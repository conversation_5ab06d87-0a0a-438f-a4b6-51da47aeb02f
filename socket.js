const socketIo = require('socket.io');
const jwt = require('jsonwebtoken');
const User = require('./models/user');
const origin = require('./utils/origin');
const mongoose = require('mongoose');

let io;

const initializeSocket = (server) => {
  const socketCorsOptions = {
    origin: process.env.NODE_ENV === 'production'
      ? origin.originProduction
      : origin.originLocal,
    methods: ['GET', 'POST'],
    credentials: true,
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With']
  };

  console.log('Socket.io CORS origins:', socketCorsOptions.origin);

  io = socketIo(server, {
    cors: socketCorsOptions,
    pingTimeout: 120000, // Increase ping timeout to 120 seconds for better reliability
    pingInterval: 25000, // Ping every 25 seconds
    transports: ['polling', 'websocket'], // Support both polling and websocket
    allowEIO3: true, // Allow Engine.IO 3 compatibility
    path: '/socket.io', // Explicitly set the socket.io path
    connectTimeout: 60000, // Increase connection timeout to 60 seconds
    maxHttpBufferSize: 1e8 // Increase buffer size for larger payloads
  });

  console.log(`Socket.io CORS configured for ${process.env.NODE_ENV || 'development'} environment`);

  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      socket.userId = decoded.user._id;
      next();
    } catch (err) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', async (socket) => {
    try {
      const userId = socket.userId;
      console.log('Socket connected for user:', userId);

      // Automatically join the user's room for direct messaging
      socket.join(userId.toString());
      console.log(`User ${userId} joined their room automatically`);

      // Also join a global room for broadcast messages
      socket.join('global');
      console.log(`User ${userId} joined global room`);

      // Update user status to online
      const user = await User.findByIdAndUpdate(
        userId,
        { isOnline: true },
        { new: true }
      );

      if (user) {
        // Broadcast to all clients that this user is online
        io.emit('userStatusChange', { userId: user._id, isOnline: true });

        // Find all conversations this user is part of and join those rooms
        try {
          const conversations = await mongoose.model('Conversation').find({
            participants: userId
          });

          if (conversations && conversations.length > 0) {
            console.log(`Found ${conversations.length} conversations for user ${userId}`);

            // Join a room for each conversation
            conversations.forEach(conversation => {
              const roomName = `conversation:${conversation._id}`;
              socket.join(roomName);
              console.log(`User ${userId} joined room for conversation: ${conversation._id}`);
            });

            // Send the list of joined rooms to the client
            socket.emit('joined_rooms', {
              rooms: conversations.map(c => `conversation:${c._id}`)
            });
          } else {
            console.log(`No conversations found for user ${userId}`);
          }
        } catch (error) {
          console.error('Error joining conversation rooms:', error);
        }
      }

      // Handle explicit join request from client
      socket.on('join', (data, callback) => {
        try {
          if (data && data.userId) {
            console.log(`User ${userId} explicitly joining room: ${data.userId}`);
            socket.join(data.userId.toString());

            // Send acknowledgment back to client
            socket.emit('joined', { userId: data.userId, success: true });

            // Also use the callback if provided
            if (typeof callback === 'function') {
              callback({ success: true, userId: data.userId });
            }
          } else if (data && data.conversationId) {
            console.log(`User ${userId} joining conversation room: ${data.conversationId}`);
            const roomName = `conversation:${data.conversationId}`;
            socket.join(roomName);

            // Send acknowledgment back to client
            socket.emit('joined', { conversationId: data.conversationId, success: true });

            // Also use the callback if provided
            if (typeof callback === 'function') {
              callback({ success: true, conversationId: data.conversationId });
            }
          }
        } catch (error) {
          console.error('Error in join handler:', error);
          if (typeof callback === 'function') {
            callback({ success: false, error: error.message });
          }
        }
      });

      // Handle message sending directly through socket
      socket.on('send_message', async (data, callback) => {
        try {
          console.log('Received send_message event:', {
            senderId: userId,
            recipientId: data.recipientId,
            conversationId: data.conversationId,
            hasContent: !!data.content
          });

          // Validate the data
          if (!data.content || !data.recipientId) {
            throw new Error('Missing required fields');
          }

          // Create a message using the controller logic
          const Message = mongoose.model('Message');
          const Conversation = mongoose.model('Conversation');

          let conversation;

          // Find or create conversation
          if (data.conversationId) {
            conversation = await Conversation.findById(data.conversationId);
            if (!conversation) {
              throw new Error('Conversation not found');
            }
          } else {
            // Look for existing conversation
            conversation = await Conversation.findOne({
              participants: { $all: [userId, data.recipientId] },
              $expr: { $eq: [{ $size: "$participants" }, 2] }
            });

            // Create new conversation if needed
            if (!conversation) {
              conversation = new Conversation({
                participants: [userId, data.recipientId],
                unreadCount: new Map()
              });
              await conversation.save();
            }
          }

          // Create and save the message
          const message = new Message({
            sender: userId,
            recipient: data.recipientId,
            content: data.content,
            conversation: conversation._id
          });

          await message.save();

          // Update conversation
          conversation.lastMessage = message._id;
          const currentCount = conversation.unreadCount.get(data.recipientId) || 0;
          conversation.unreadCount.set(data.recipientId, currentCount + 1);
          await conversation.save();

          // Populate the message
          const populatedMessage = await Message.findById(message._id)
            .populate({
              path: 'sender',
              select: 'username fullName email profilePicture role'
            });

          // Emit to both users
          io.to(data.recipientId).emit('new_message', {
            message: populatedMessage,
            conversation: conversation._id
          });

          io.to(userId).emit('new_message', {
            message: populatedMessage,
            conversation: conversation._id
          });

          // Emit to the conversation room
          io.to(`conversation:${conversation._id}`).emit('new_message', {
            message: populatedMessage,
            conversation: conversation._id
          });

          // Also broadcast to all clients
          io.emit('conversation_updated', {
            conversationId: conversation._id.toString()
          });

          // Send acknowledgment
          if (typeof callback === 'function') {
            callback({
              success: true,
              messageId: message._id,
              conversationId: conversation._id
            });
          }
        } catch (error) {
          console.error('Error in send_message handler:', error);
          if (typeof callback === 'function') {
            callback({ success: false, error: error.message });
          }
        }
      });

      // Handle disconnect
      socket.on('disconnect', async () => {
        console.log('Socket disconnected for user:', userId);
        const user = await User.findByIdAndUpdate(
          userId,
          { isOnline: false },
          { new: true }
        );

        if (user) {
          // Broadcast to all clients that this user is offline
          io.emit('userStatusChange', { userId: user._id, isOnline: false });
        }
      });
    } catch (err) {
      console.error('Socket connection error:', err);
    }
  });

  return io;
};

const getIo = () => {
  if (!io) {
    throw new Error('Socket.io not initialized');
  }
  return io;
};

module.exports = { initializeSocket, getIo };
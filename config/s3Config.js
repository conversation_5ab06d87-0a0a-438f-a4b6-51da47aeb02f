/**
 * AWS S3 Configuration for HOAFLO File Storage
 * Maintains hierarchical organization while using cloud storage
 */

const { S3Client } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const { GetObjectCommand, PutObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const multer = require('multer');
const multerS3 = require('multer-s3');
const path = require('path');

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  }
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME;

// Validate required environment variables
if (!BUCKET_NAME) {
  console.error('AWS_S3_BUCKET_NAME environment variable is required');
  process.exit(1);
}

if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
  console.error('AWS credentials (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY) are required');
  process.exit(1);
}

/**
 * Generate S3 key path maintaining HOAFLO hierarchy
 * @param {string} category - File category (documents, user_documents, hoa_documents, etc.)
 * @param {string} hoaId - HOA ID for organization
 * @param {string} filename - Original filename
 * @returns {string} S3 key path
 */
const generateS3Key = (category, hoaId, filename) => {
  const timestamp = Date.now();
  const ext = path.extname(filename);
  const base = path.basename(filename, ext);
  const safeFilename = `${timestamp}-${base.replace(/[^a-zA-Z0-9]/g, '_')}${ext}`;
  
  if (hoaId) {
    return `hoa-${hoaId}/${category}/${safeFilename}`;
  } else {
    return `global/${category}/${safeFilename}`;
  }
};

/**
 * Create multer S3 storage configuration
 * @param {string} category - File category for organization
 * @param {Array} allowedMimeTypes - Allowed MIME types
 * @param {number} maxFileSize - Maximum file size in bytes
 * @returns {Object} Multer configuration
 */
const createS3Storage = (category, allowedMimeTypes = [], maxFileSize = 10 * 1024 * 1024) => {
  const storage = multerS3({
    s3: s3Client,
    bucket: BUCKET_NAME,
    metadata: function (req, file, cb) {
      cb(null, {
        fieldName: file.fieldname,
        originalName: file.originalname,
        uploadedBy: req.user ? req.user._id.toString() : 'anonymous',
        hoaId: req.user ? (req.user.hoaId || 'global') : 'global',
        category: category,
        uploadDate: new Date().toISOString()
      });
    },
    key: function (req, file, cb) {
      let hoaId = req.user ? req.user.hoaId : null;

      // Handle case where hoaId is an array
      if (Array.isArray(hoaId)) {
        hoaId = hoaId[0];
      }

      const s3Key = generateS3Key(category, hoaId, file.originalname);
      console.log(`Uploading file to S3:`, {
        category,
        hoaId,
        originalName: file.originalname,
        s3Key
      });
      cb(null, s3Key);
    },
    contentType: multerS3.AUTO_CONTENT_TYPE
  });

  const fileFilter = (req, file, cb) => {
    console.log('File filter check:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      allowedTypes: allowedMimeTypes
    });

    if (allowedMimeTypes.length === 0) {
      // No restrictions
      cb(null, true);
    } else if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      const error = new Error(`File type ${file.mimetype} not allowed. Allowed types: ${allowedMimeTypes.join(', ')}`);
      error.code = 'INVALID_FILE_TYPE';
      cb(error, false);
    }
  };

  return multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
      fileSize: maxFileSize
    }
  });
};

/**
 * Generate signed URL for secure file access
 * @param {string} s3Key - S3 object key
 * @param {number} expiresIn - URL expiration time in seconds (default: 1 hour)
 * @param {string} fileName - Optional filename for download (extracted from s3Key if not provided)
 * @param {boolean} forceDownload - Whether to force download instead of inline viewing (default: true)
 * @returns {Promise<string>} Signed URL
 */
const generateSignedUrl = async (s3Key, expiresIn = 3600, fileName = null, forceDownload = true) => {
  try {
    // Extract filename from s3Key if not provided
    if (!fileName) {
      fileName = s3Key.split('/').pop() || 'download';
    }

    const commandParams = {
      Bucket: BUCKET_NAME,
      Key: s3Key
    };

    // Add response headers to force download
    if (forceDownload) {
      commandParams.ResponseContentDisposition = `attachment; filename="${fileName}"`;
    }

    const command = new GetObjectCommand(commandParams);
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn });

    return signedUrl;
  } catch (error) {
    console.error('Error generating signed URL:', error);
    throw error;
  }
};

/**
 * Delete file from S3
 * @param {string} s3Key - S3 object key
 * @returns {Promise<boolean>} Success status
 */
const deleteFile = async (s3Key) => {
  try {
    const command = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key
    });
    
    await s3Client.send(command);
    console.log(`File deleted from S3: ${s3Key}`);
    return true;
  } catch (error) {
    console.error('Error deleting file from S3:', error);
    return false;
  }
};

/**
 * Get file metadata from S3
 * @param {string} s3Key - S3 object key
 * @returns {Promise<Object>} File metadata
 */
const getFileMetadata = async (s3Key) => {
  try {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key
    });
    
    const response = await s3Client.send(command);
    return {
      contentType: response.ContentType,
      contentLength: response.ContentLength,
      lastModified: response.LastModified,
      metadata: response.Metadata
    };
  } catch (error) {
    console.error('Error getting file metadata:', error);
    throw error;
  }
};

// Pre-configured storage instances for different file types
const storageConfigs = {
  // User profile and verification documents
  userDocuments: createS3Storage('user_documents', [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ], 5 * 1024 * 1024), // 5MB limit

  // HOA administrative documents
  hoaDocuments: createS3Storage('hoa_documents', [
    'application/pdf', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg', 'image/png'
  ], 10 * 1024 * 1024), // 10MB limit

  // General documents
  documents: createS3Storage('documents', [], 10 * 1024 * 1024), // 10MB limit, all types

  // Task attachments
  taskAttachments: createS3Storage('task_attachments', [], 10 * 1024 * 1024),

  // Finance documents
  financeDocuments: createS3Storage('finance_documents', [
    'application/pdf', 'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg', 'image/png'
  ], 10 * 1024 * 1024),

  // Profile photos (images only)
  profilePhotos: createS3Storage('profile_photos', [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp'
  ], 2 * 1024 * 1024), // 2MB limit

  // Announcement attachments (images and documents)
  announcementAttachments: createS3Storage('announcement_attachments', [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain', 'text/csv'
  ], 10 * 1024 * 1024) // 10MB limit
};

module.exports = {
  s3Client,
  BUCKET_NAME,
  generateS3Key,
  createS3Storage,
  generateSignedUrl,
  deleteFile,
  getFileMetadata,
  storageConfigs
};

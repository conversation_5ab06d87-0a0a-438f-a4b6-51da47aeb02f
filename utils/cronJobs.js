const cron = require('node-cron');
const { checkTasksDueSoon } = require('../controllers/task/tasksController');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const Subscription = require('../models/subscription');
const HOA = require('../models/hoa');
const { sendEmail } = require('../services/emailService');

// Schedule task notifications check to run every hour
exports.startCronJobs = () => {
  // Check for tasks due soon every hour
  cron.schedule('0 1 * * *', async () => {
    console.log('Running task due soon check...');
    await checkTasksDueSoon();
    await startCheckSubscription();
    await sendEmailReminder();
  });
}; 

// Get all customers from Stripe Customer
async function getStripeCustomer() {
  try {
    let customerObject = {};
    let customerData = [];

    const subscriptions = await stripe.subscriptions.list({
      status: 'all',
      expand: ['data.default_payment_method'],
    });

    subscriptions.data.forEach(sub => {
      customerObject = {
        customerId: sub.customer,
        subscriptionId: sub.id,
        subscriptionStatus: sub.status,
        hoaId: sub.metadata.hoaId,
        currentPeriodEnd: new Date(sub.current_period_end * 1000)
      };

      customerData.push(customerObject);
    });
    console.log(customerData);
    return customerData;
  } catch (error) {
    console.error(`Error checking subscription for ${customerId}:`, error.message);
  }
}

// Check the trailing status for each subscription and adjust the status
async function checkSubscriptionStatus(hoaId, status) {
  try {
    const subscription = await Subscription.findOne({hoaId: hoaId});

    if (!subscription) {
      console.error('Subscription not found in database');
    } else {
      subscription.status = status;
      await subscription.save();
    }
  } catch (error) {
    console.error(`Error checking subscription`, error.message);
  }
}

// Send Email Notification for due subscriptions before the due date 1 week
async function sendlNotificationDueSubscription(customer) {
  try {

    const hoa = await HOA.findById(customer.hoaId);

    const emailSubject = `[REMINDER] Subscription Due for ${hoa.hoaCommunityName}`;
    const emailHtml = `
      <h2>Your HOA Subscription Next Payment Due is on ${customer.currentPeriodEnd} </h2>
      <br/>
      <p>Dear ${hoa.hoaCommunityName} Administrator,</p>
      <p>Your subscription payment will be charged automatically on ${customer.currentPeriodEnd}, 
      based on your subscription plan, and payment method you saved for your account.</p>
      <p>If we can't collect the payment by ${customer.currentPeriodEnd}, we will send you an email to notify you.</p>
      <p> If you have any questions or concerns, please contact Street HOA Team</p>
      <p>Best regards,<br>The Street HOA Team</p>
    `;
    
    await sendEmail(hoa.contactEmail, emailSubject, emailHtml);
  } catch (error) {
    console.error(`Error checking subscription`, error.message);
  }
}

const startCheckSubscription = async () => {
  // Get all customers with active subscriptions
  const customerData = await getStripeCustomer();

  customerData.forEach(async (customer) => {
    await checkSubscriptionStatus(customer.hoaId, customer.subscriptionStatus);
  })
}

const sendEmailReminder = async () => {
  // Get all customers with active subscriptions
  const customerData = await getStripeCustomer(); 

  const now = new Date();
  const targetDate = new Date(now);
  targetDate.setDate(now.getDate() + 7);

  customerData.forEach(async (customer) => {
    const dueDate = new Date(customer.currentPeriodEnd);

    // Check if dueDate is within 1 day of targetDate
    const diffInDays = Math.ceil((dueDate - targetDate) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      console.log(`Sending email for ${customer.hoaId}`);
      await sendlNotificationDueSubscription(customer);
    }

  });
}

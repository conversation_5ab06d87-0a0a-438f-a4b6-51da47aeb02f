/**
 * Logger utility for consistent logging
 */

const fs = require('fs');
const path = require('path');

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '..', 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// Log file paths
const errorLogPath = path.join(logsDir, 'error.log');
const accessLogPath = path.join(logsDir, 'access.log');

/**
 * Log levels
 */
const LogLevel = {
  ERROR: 'ERROR',
  WARN: 'WARN',
  INFO: 'INFO',
  DEBUG: 'DEBUG'
};

/**
 * Format a log message
 * @param {String} level - Log level
 * @param {String} message - Log message
 * @param {Object} meta - Additional metadata
 * @returns {String} Formatted log message
 */
const formatLogMessage = (level, message, meta = {}) => {
  const timestamp = new Date().toISOString();
  const metaString = Object.keys(meta).length > 0 ? JSON.stringify(meta) : '';
  return `[${timestamp}] [${level}] ${message} ${metaString}`.trim() + '\n';
};

/**
 * Write to log file
 * @param {String} filePath - Path to log file
 * @param {String} message - Message to log
 */
const writeToFile = (filePath, message) => {
  fs.appendFile(filePath, message, (err) => {
    if (err) {
      console.error('Error writing to log file:', err);
    }
  });
};

/**
 * Log an error message
 * @param {String} message - Error message
 * @param {Object} meta - Additional metadata
 */
const error = (message, meta = {}) => {
  const logMessage = formatLogMessage(LogLevel.ERROR, message, meta);
  console.error(logMessage);
  writeToFile(errorLogPath, logMessage);
};

/**
 * Log a warning message
 * @param {String} message - Warning message
 * @param {Object} meta - Additional metadata
 */
const warn = (message, meta = {}) => {
  const logMessage = formatLogMessage(LogLevel.WARN, message, meta);
  console.warn(logMessage);
  writeToFile(errorLogPath, logMessage);
};

/**
 * Log an info message
 * @param {String} message - Info message
 * @param {Object} meta - Additional metadata
 */
const info = (message, meta = {}) => {
  const logMessage = formatLogMessage(LogLevel.INFO, message, meta);
  console.log(logMessage);
  writeToFile(accessLogPath, logMessage);
};

/**
 * Log a debug message (only in development)
 * @param {String} message - Debug message
 * @param {Object} meta - Additional metadata
 */
const debug = (message, meta = {}) => {
  if (process.env.NODE_ENV !== 'production') {
    const logMessage = formatLogMessage(LogLevel.DEBUG, message, meta);
    console.log(logMessage);
    writeToFile(accessLogPath, logMessage);
  }
};

/**
 * Log an API request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const logRequest = (req, res, next) => {
  const startTime = Date.now();
  
  // Log when the request completes
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logData = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip || req.connection.remoteAddress,
      statusCode: res.statusCode,
      userAgent: req.headers['user-agent'],
      duration: `${duration}ms`
    };
    
    // Log at appropriate level based on status code
    if (res.statusCode >= 500) {
      error('Server error', logData);
    } else if (res.statusCode >= 400) {
      warn('Client error', logData);
    } else {
      info('Request completed', logData);
    }
  });
  
  next();
};

module.exports = {
  error,
  warn,
  info,
  debug,
  logRequest
};

/**
 * Error Logger Utility
 * Provides detailed error logging and storage for debugging purposes
 */

const fs = require('fs');
const path = require('path');

// Store recent errors in memory for quick access
const recentErrors = [];
const MAX_RECENT_ERRORS = 50;

// Error log file path
const errorLogDir = path.join(__dirname, '../logs');
const errorLogPath = path.join(errorLogDir, 'login-errors.log');

// Ensure log directory exists
if (!fs.existsSync(errorLogDir)) {
  fs.mkdirSync(errorLogDir, { recursive: true });
}

/**
 * Log a detailed error with context information
 * @param {string} category - Error category (e.g., 'login', 'registration')
 * @param {Error|string} error - The error object or message
 * @param {Object} context - Additional context information
 * @returns {string} Error ID for reference
 */
const logDetailedError = (category, error, context = {}) => {
  const timestamp = new Date();
  const errorId = `${category}-${timestamp.getTime()}-${Math.random().toString(36).substring(2, 10)}`;
  
  // Format error details
  const errorDetails = {
    id: errorId,
    category,
    timestamp: timestamp.toISOString(),
    error: error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack
    } : error,
    context
  };
  
  // Convert to string for logging
  const errorString = JSON.stringify(errorDetails, null, 2);
  
  // Log to console
  console.error(`[${category.toUpperCase()} ERROR] ${errorId}:`, errorString);
  
  // Store in recent errors
  recentErrors.unshift(errorDetails);
  if (recentErrors.length > MAX_RECENT_ERRORS) {
    recentErrors.pop();
  }
  
  // Write to log file
  try {
    fs.appendFileSync(errorLogPath, `${errorString}\n---\n`);
  } catch (fileError) {
    console.error('Failed to write to error log file:', fileError);
  }
  
  return errorId;
};

/**
 * Get recent errors
 * @param {string} [category] - Optional category filter
 * @param {number} [limit=10] - Maximum number of errors to return
 * @returns {Array} Recent errors
 */
const getRecentErrors = (category, limit = 10) => {
  let filteredErrors = recentErrors;
  
  if (category) {
    filteredErrors = filteredErrors.filter(err => err.category === category);
  }
  
  return filteredErrors.slice(0, limit);
};

/**
 * Get a specific error by ID
 * @param {string} errorId - The error ID
 * @returns {Object|null} The error object or null if not found
 */
const getErrorById = (errorId) => {
  return recentErrors.find(err => err.id === errorId) || null;
};

/**
 * Clear recent errors
 * @param {string} [category] - Optional category to clear
 */
const clearRecentErrors = (category) => {
  if (category) {
    const index = recentErrors.findIndex(err => err.category === category);
    if (index !== -1) {
      recentErrors.splice(index, 1);
    }
  } else {
    recentErrors.length = 0;
  }
};

module.exports = {
  logDetailedError,
  getRecentErrors,
  getErrorById,
  clearRecentErrors
};

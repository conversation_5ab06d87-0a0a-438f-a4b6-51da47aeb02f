# Local MongoDB Testing Environment
# Use this for complete local testing with local MongoDB instance

# Server Configuration
NODE_ENV=development
PORT=5001
JWT_SECRET=street-harmony-local-secret-key-2024-testing

# Local MongoDB Configuration
# Option 1: Local MongoDB instance (requires MongoDB installed locally)
MONGODB_URI=mongodb://localhost:27017/street-harmony-local

# Option 2: MongoDB Memory Server (for testing - install mongodb-memory-server)
# MONGODB_URI=mongodb://127.0.0.1:27017/street-harmony-test

# AWS S3 Configuration (Production - for file testing)
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=ryhqr8hQbWMKh8KT9CKVcWRtu47ZQx5gc5vl5RCj
AWS_REGION=us-east-2
AWS_S3_BUCKET_NAME=hoaflo-files-prod

# Email Configuration (Outlook Business)
OUTLOOK_SMTP_HOST=smtp-mail.outlook.com
OUTLOOK_SMTP_PORT=587
OUTLOOK_SMTP_SECURE=false
OUTLOOK_SMTP_TLS=true
OUTLOOK_EMAIL_USER=<EMAIL>
OUTLOOK_EMAIL_PASSWORD=HoaFlow@2025

# Stripe Configuration (Test Keys)
STRIPE_SECRET_KEY=sk_test_51REKUtD14Lqis0JWM7Kfdd1JkfgmT3mTmOCLFmczCu9Nw6zpSvhUek06lUbYG3AA2CXMHo1yobcLL6iF25UWQQKM00lDvyVvaW
STRIPE_PUBLISHABLE_KEY=pk_test_51REKUtD14Lqis0JWM7Kfdd1JkfgmT3mTmOCLFmczCu9Nw6zpSvhUek06lUbYG3AA2CXMHo1yobcLL6iF25UWQQKM00lDvyVvaW

# Local Development Settings
CORS_ORIGINS=http://localhost:8080,http://localhost:3000,http://localhost:5173
FRONTEND_URL=http://localhost:8080
EMAIL_SERVICE=outlook
EMAIL_FROM_NAME=HOAFLO Support (Local Testing)
EMAIL_FROM_ADDRESS=<EMAIL>

# Testing Flags
ENABLE_EMAIL_TESTING=true
ENABLE_FILE_UPLOAD_TESTING=true
ENABLE_DATABASE_SEEDING=true

/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');
const adminController = require('../../controllers/admin/adminController');
const companyAdmin = require('../../middleware/companyAdmin');

/**
 * @route   POST /api/admin
 * @desc    Create a new admin user
 * @access  Private/Admin
 */
router.post('/', [auth, admin], adminController.createAdmin);

/**
 * @route   GET /api/admin
 * @desc    Get all admin users
 * @access  Private/Admin
 */
router.get('/', [auth, admin], adminController.getAllAdmins);

/**
 * @route   PUT /api/admin/:id
 * @desc    Update an admin user
 * @access  Private/Admin
 */
router.put('/:id', [auth, admin], adminController.updateAdmin);

/**
 * @route   DELETE /api/admin/:id
 * @desc    Delete an admin user
 * @access  Private/Admin
 */
router.delete('/:id', [auth, admin], adminController.deleteAdmin);

/**
 * @route   GET /api/admin/hoa/pending
 * @desc    Get pending HOA registrations
 * @access  Private/CompanyAdmin
 */
router.get('/hoa/pending', [auth, companyAdmin], adminController.getPendingHOAs);

/**
 * @route   GET /api/admin/hoa/:id/documents
 * @desc    Get HOA verification documents
 * @access  Private/CompanyAdmin
 */
router.get('/hoa/:id/documents', [auth, companyAdmin], adminController.getHOADocuments);

/**
 * @route   GET /api/admin/stats
 * @desc    Get admin dashboard statistics
 * @access  Private/CompanyAdmin
 */
router.get('/stats', [auth, companyAdmin], adminController.getAdminStats);

/**
 * @route   GET /api/admin/user/:id/documents
 * @desc    Get user verification documents
 * @access  Private/Admin
 */
router.get('/user/:id/documents', [auth, admin], adminController.getUserDocuments);

module.exports = router;

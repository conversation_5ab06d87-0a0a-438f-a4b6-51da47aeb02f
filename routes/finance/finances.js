const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const financeController = require('../../controllers/finance/financeController');

/**
 * @route   POST /api/finances
 * @desc    Create a new finance entry
 * @access  Private (Admin, Member)
 */
router.post('/', auth, financeController.createEntry);

/**
 * @route   GET /api/finances/monthly
 * @desc    Get monthly income vs expenses data
 * @access  Private (Admin, Member)
 */
router.get('/monthly', auth, financeController.getMonthlyFinance);

/**
 * @route   GET /api/finances/breakdown
 * @desc    Get expense category breakdown
 * @access  Private (Admin, Member)
 */
router.get('/breakdown', auth, financeController.getBreakdown);

/**
 * @route   GET /api/finances
 * @desc    Get all finance entries
 * @access  Private (Admin, Member)
 */
router.get('/', auth, financeController.getAllEntries);

/**
 * @route   DELETE /api/finances/:id
 * @desc    Delete a finance entry
 * @access  Private (Admin, Entry Creator)
 */
router.delete('/:id', auth, financeController.deleteEntry);

module.exports = router;

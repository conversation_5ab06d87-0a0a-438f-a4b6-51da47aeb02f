// routes/notifications.js
const express = require('express');
const router = express.Router();
const notificationController = require('../../controllers/notification/notificationController');
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');

// Get all notifications for the authenticated user
router.get('/', auth, notificationController.getNotifications);

// Create a new notification
router.post('/', auth, notificationController.createNotification);

// Mark a notification as read
router.post('/:id/read', auth, notificationController.markAsRead);

// Create test notifications (for testing purposes)
router.post('/test', auth, notificationController.createTestNotification);

// Send admin announcement to all members (admin only)
router.post('/announcement', [auth, admin], notificationController.sendAdminAnnouncement);

module.exports = router;

const express = require('express');
const router = express.Router();
const vendorPayoutController = require('../../controllers/vendor/vendorPayoutController');
const stripeConnectController = require('../../controllers/stripe/stripeConnectController');
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');

/**
 * @route   POST /api/vendor-payouts/recurring
 * @desc    Create recurring payment to vendor
 * @access  Private/Admin
 */
router.post('/recurring', auth, admin, vendorPayoutController.createRecurringPayment);

/**
 * @route   GET /api/vendor-payouts/recurring/:hoaId
 * @desc    Get recurring payments for HOA
 * @access  Private/Admin
 */
router.get('/recurring/:hoaId', auth, admin, vendorPayoutController.getRecurringPayments);

/**
 * @route   PUT /api/vendor-payouts/recurring/:id
 * @desc    Update recurring payment
 * @access  Private/Admin
 */
router.put('/recurring/:id', auth, admin, vendorPayoutController.updateRecurringPayment);

/**
 * @route   DELETE /api/vendor-payouts/recurring/:id
 * @desc    Cancel recurring payment
 * @access  Private/Admin
 */
router.delete('/recurring/:id', auth, admin, vendorPayoutController.cancelRecurringPayment);

/**
 * @route   POST /api/vendor-payouts/stripe-connect/create-account
 * @desc    Create Stripe Connect account for HOA
 * @access  Private/Admin
 */
router.post('/stripe-connect/create-account', auth, admin, stripeConnectController.createConnectAccount);

/**
 * @route   POST /api/vendor-payouts/stripe-connect/account-link
 * @desc    Create account link for Stripe Connect onboarding
 * @access  Private/Admin
 */
router.post('/stripe-connect/account-link', auth, admin, stripeConnectController.createAccountLink);

/**
 * @route   GET /api/vendor-payouts/stripe-connect/status/:hoaId
 * @desc    Get Stripe Connect account status
 * @access  Private/Admin
 */
router.get('/stripe-connect/status/:hoaId', auth, admin, stripeConnectController.getAccountStatus);

/**
 * @route   POST /api/vendor-payouts/payment-with-fee
 * @desc    Create payment intent with platform fee
 * @access  Private/Admin
 */
router.post('/payment-with-fee', auth, admin, stripeConnectController.createPaymentIntentWithFee);

/**
 * @route   POST /api/vendor-payouts/pay-vendor
 * @desc    Make one-time payment to vendor
 * @access  Private/Admin
 */
router.post('/pay-vendor', auth, admin, stripeConnectController.payVendor);

module.exports = router;

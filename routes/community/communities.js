const express = require('express');
const router = express.Router();
const communityController = require('../../controllers/community/communityController');
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');
const companyAdmin = require('../../middleware/companyAdmin');
const hoaAccess = require('../../middleware/hoaAccess');

/**
 * @route   GET /api/communities/code/:code
 * @desc    Get community by code
 * @access  Public (needed for registration)
 */
router.get('/code/:code', communityController.getCommunityByCode);

/**
 * @route   GET /api/communities/hoa/:hoaId
 * @desc    Get communities by HOA ID
 * @access  Private (Admin, Company Admin)
 */
router.get('/hoa/:hoaId', [auth, hoaAccess({ allowSameHoaAdmins: true, allowCompanyAdmin: true })], communityController.getCommunitiesByHoa);

/**
 * @route   GET /api/communities
 * @desc    Get all communities
 * @access  Private
 */
router.get('/', auth, communityController.getAllCommunities);

/**
 * @route   GET /api/communities/:id
 * @desc    Get a single community by ID
 * @access  Private
 */
router.get('/:id', auth, communityController.getCommunity);

/**
 * @route   POST /api/communities
 * @desc    Create a new community
 * @access  Private (Admin, Company Admin)
 */
router.post('/', [auth, hoaAccess({ allowSameHoaAdmins: true, allowCompanyAdmin: true })], communityController.createCommunity);

/**
 * @route   PUT /api/communities/:id
 * @desc    Update a community
 * @access  Private (Admin, Company Admin)
 */
router.put('/:id', [auth, hoaAccess({ allowSameHoaAdmins: true, allowCompanyAdmin: true })], communityController.updateCommunity);

/**
 * @route   DELETE /api/communities/:id
 * @desc    Delete a community
 * @access  Private (Admin, Company Admin)
 */
router.delete('/:id', [auth, hoaAccess({ allowSameHoaAdmins: true, allowCompanyAdmin: true })], communityController.deleteCommunity);

module.exports = router;

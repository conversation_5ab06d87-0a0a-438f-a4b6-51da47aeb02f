const express = require('express');
const router = express.Router();
const eventsController = require('../../controllers/event/eventsController');
const auth = require('../../middleware/auth');
const operationalAdmin = require('../../middleware/operationalAdmin');

// Public routes - authentication handled manually in controller
router.get('/', eventsController.getAllEvents);

// Protected routes - operational admin only
router.post('/', auth, operationalAdmin, eventsController.createEvent);
router.delete('/:id', auth, operationalAdmin, eventsController.deleteEvent);
router.put('/:id', auth, operationalAdmin, eventsController.updateEvent);

module.exports = router;

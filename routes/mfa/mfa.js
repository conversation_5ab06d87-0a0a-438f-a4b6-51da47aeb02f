/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const mfaController = require('../../controllers/mfa/mfaController');

/**
 * @route   GET /api/mfa/status
 * @desc    Get MFA status for a user
 * @access  Private
 */
router.get('/status', auth, mfaController.getMFAStatus);

/**
 * @route   POST /api/mfa/enable
 * @desc    Enable MFA for a user
 * @access  Private
 */
router.post('/enable', auth, mfaController.enableMFA);

/**
 * @route   POST /api/mfa/disable
 * @desc    Disable MFA for a user
 * @access  Private
 */
router.post('/disable', auth, mfaController.disableMFA);

/**
 * @route   POST /api/mfa/send-verification
 * @desc    Send verification code to user's phone number
 * @access  Public
 */
router.post('/send-verification', mfaController.sendVerification);

/**
 * @route   POST /api/mfa/verify
 * @desc    Verify code and complete login
 * @access  Public
 */
router.post('/verify', mfaController.verifyLogin);

module.exports = router;

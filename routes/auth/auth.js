const express = require('express');
const router = express.Router();
const authController = require('../../controllers/auth/authController');
const upload = require('../../middleware/upload');
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');
const User = require('../../models/user');

// Register with profile & verification document uploads
router.post('/register',
  upload.fields([
    // Profile photo
    { name: 'profilePhoto', maxCount: 1 },
    // Verification documents
    { name: 'homeOwnershipDoc', maxCount: 1 },
    { name: 'utilityBillDoc', maxCount: 1 },
    { name: 'photoIdDoc', maxCount: 1 },
    // Legacy fields for backward compatibility
    { name: 'licenseFront', maxCount: 1 },
    { name: 'licenseBack', maxCount: 1 }
  ]),
  authController.register
);

// Login route
router.post('/login', authController.login);

// Protected routes (require auth)
router.post('/logout', auth, authController.logout);
router.get('/me', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .select('-password')
      .populate('hoaId');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({ user });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ message: 'Server error' });
  }
});
router.get('/pending', [auth, admin], authController.getPendingUsers);
router.get('/denied', [auth, admin], authController.getDeniedUsers);

// Test email route (remove in production)
router.post('/test-email', [auth, admin], async (req, res) => {
  try {
    const { sendEmail } = require('../../services/emailService');
    const result = await sendEmail(
      req.user.email,
      'welcomeEmail',
      { username: req.user.username }
    );
    res.json(result);
  } catch (error) {
    console.error('Test email error:', error);
    res.status(500).json({ error: 'Failed to send test email' });
  }
});

// Database diagnostic route (remove in production)
router.get('/db-status', [auth, admin], async (req, res) => {
  try {
    const mongoose = require('mongoose');
    const User = require('../../models/user');

    // Check MongoDB connection
    const connectionState = mongoose.connection.readyState;
    const connectionStates = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };

    // Get all users
    const users = await User.find({}).select('-password');

    res.json({
      dbConnection: {
        state: connectionState,
        stateText: connectionStates[connectionState],
        host: mongoose.connection.host,
        name: mongoose.connection.name
      },
      users: users.map(u => ({
        id: u._id,
        username: u.username,
        email: u.email,
        role: u.role,
        isApproved: u.isApproved,
        denied: u.denied,
        createdAt: u.createdAt
      })),
      totalUsers: users.length
    });
  } catch (error) {
    console.error('Database diagnostic error:', error);
    res.status(500).json({
      error: 'Database diagnostic failed',
      message: error.message,
      stack: error.stack
    });
  }
});

router.put('/approve/:id', auth, authController.approveUser);
router.put('/deny/:id', auth, authController.denyUser);

// Password reset routes (public)
router.post('/request-password-reset', authController.requestPasswordReset);
router.post('/reset-password', authController.resetPassword);

// Check if user exists (public) - for debugging only, remove in production
router.get('/check-user/:identifier', async (req, res) => {
  try {
    const { identifier } = req.params;
    const User = require('../../models/user');
    const mongoose = require('mongoose');

    // Check MongoDB connection
    const connectionState = mongoose.connection.readyState;
    if (connectionState !== 1) {
      return res.status(500).json({
        error: 'Database not connected',
        connectionState
      });
    }

    // Escape special characters for regex safety
    const escapeRegExp = (string) => {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    };
    const safeIdentifier = escapeRegExp(identifier);

    // Search for user
    const user = await User.findOne({
      $or: [
        { username: { $regex: new RegExp('^' + safeIdentifier + '$', 'i') } },
        { email: { $regex: new RegExp('^' + safeIdentifier + '$', 'i') } }
      ]
    }).select('-password');

    if (user) {
      res.json({
        found: true,
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          isApproved: user.isApproved,
          createdAt: user.createdAt
        }
      });
    } else {
      // Try a more flexible search
      const flexibleUser = await User.findOne({
        $or: [
          { username: { $regex: new RegExp(safeIdentifier, 'i') } },
          { email: { $regex: new RegExp(safeIdentifier, 'i') } }
        ]
      }).select('-password');

      if (flexibleUser) {
        res.json({
          found: true,
          matchType: 'partial',
          user: {
            id: flexibleUser._id,
            username: flexibleUser.username,
            email: flexibleUser.email,
            role: flexibleUser.role,
            isApproved: flexibleUser.isApproved,
            createdAt: flexibleUser.createdAt
          }
        });
      } else {
        res.json({
          found: false,
          message: 'User not found'
        });
      }
    }
  } catch (error) {
    console.error('Error checking user:', error);
    res.status(500).json({
      error: 'Failed to check user',
      message: error.message
    });
  }
});

// Update user for testing (public) - for debugging only, remove in production
router.put('/update-user/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const User = require('../models/user');
    const mongoose = require('mongoose');

    // Check MongoDB connection
    const connectionState = mongoose.connection.readyState;
    if (connectionState !== 1) {
      return res.status(500).json({
        error: 'Database not connected',
        connectionState
      });
    }

    // Find the user
    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: `No user found with ID: ${id}`
      });
    }

    console.log('Found user:', {
      _id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      isApproved: user.isApproved,
      hoaCommunityCode: user.hoaCommunityCode
    });

    // Update the user
    user.isApproved = true;
    user.role = 'admin';
    user.denied = false;
    user.verifiedAt = new Date();

    await user.save();

    console.log('User updated successfully');

    // Get the updated user
    const updatedUser = await User.findById(id);
    console.log('Updated user:', {
      _id: updatedUser._id,
      username: updatedUser.username,
      email: updatedUser.email,
      role: updatedUser.role,
      isApproved: updatedUser.isApproved,
      hoaCommunityCode: updatedUser.hoaCommunityCode
    });

    res.json({
      message: 'User updated successfully',
      user: {
        id: updatedUser._id,
        username: updatedUser.username,
        email: updatedUser.email,
        role: updatedUser.role,
        isApproved: updatedUser.isApproved,
        hoaCommunityCode: updatedUser.hoaCommunityCode
      }
    });
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({
      error: 'Failed to update user',
      message: error.message
    });
  }
});

// Login error diagnostics (admin only) - remove in production
router.get('/login-errors', [auth, admin], async (req, res) => {
  try {
    const errorLogger = require('../../utils/errorLogger');
    const { category, limit } = req.query;

    const errors = errorLogger.getRecentErrors(category || 'login', parseInt(limit) || 20);

    res.json({
      count: errors.length,
      errors
    });
  } catch (error) {
    console.error('Error retrieving login errors:', error);
    res.status(500).json({
      error: 'Failed to retrieve login errors',
      message: error.message
    });
  }
});

// Get specific error by ID (admin only) - remove in production
router.get('/error/:errorId', [auth, admin], async (req, res) => {
  try {
    const errorLogger = require('../../utils/errorLogger');
    const { errorId } = req.params;

    const error = errorLogger.getErrorById(errorId);

    if (!error) {
      return res.status(404).json({
        error: 'Error not found',
        message: `No error found with ID: ${errorId}`
      });
    }

    res.json(error);
  } catch (error) {
    console.error('Error retrieving error details:', error);
    res.status(500).json({
      error: 'Failed to retrieve error details',
      message: error.message
    });
  }
});

// Error handling middleware
router.use((err, req, res, next) => {
  console.error('Auth route error:', err);

  // Log the error with our detailed logger
  const errorLogger = require('../../utils/errorLogger');
  const errorId = errorLogger.logDetailedError('auth-route', err, {
    path: req.path,
    method: req.method,
    query: req.query,
    body: req.body
  });

  res.status(500).json({
    error: 'Authentication error',
    message: err.message,
    errorId
  });
});

module.exports = router;

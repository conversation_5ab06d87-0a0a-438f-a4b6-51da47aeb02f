/**
 * Subscription Routes
 * Handles subscription management for HOAs
 */
const express = require('express');
const router = express.Router();
const subscriptionController = require('../../controllers/subscription/subscriptionController');
const auth = require('../../middleware/auth');
const companyAdmin = require('../../middleware/companyAdmin');
const hoaAccess = require('../../middleware/hoaAccess');

/**
 * @route   POST /api/subscriptions
 * @desc    Create a new subscription
 * @access  Private/CompanyAdmin, HOA Admin
 */
router.post('/', auth, subscriptionController.createSubscription);

/**
 * @route   GET /api/subscriptions/hoa/:hoaId
 * @desc    Get subscription by HOA ID
 * @access  Private/CompanyAdmin, HOA Admin
 */
router.get('/hoa/:hoaId', auth, hoaAccess({ allowSameHoaAdmins: true, allowMembers: false }), subscriptionController.getSubscriptionByHoaId);

/**
 * @route   GET /api/subscriptions/hoa/:hoaId/payments
 * @desc    Get subscription payments for an HOA
 * @access  Private/CompanyAdmin, HOA Admin
 */
router.get('/hoa/:hoaId/payments', auth, hoaAccess({ allowSameHoaAdmins: true, allowMembers: false }), subscriptionController.getSubscriptionPayments);

/**
 * @route   GET /api/subscriptions/by-community/:communityId
 * @desc    Get subscription by community ID
 * @access  Private/CompanyAdmin, HOA Admin
 */
router.get('/by-community/:communityId', auth, subscriptionController.getSubscriptionByCommunityId);

/**
 * @route   GET /api/subscriptions/:id?communityId=communityId
 * @desc    Get subscription details by subscription ID or community ID
 * @access  Private/CompanyAdmin, HOA Admin
 * @query   communityId - Optional community ID to find HOA and then subscription
 */
router.get('/:id', auth, subscriptionController.getSubscriptionDetails);

/**
 * @route   PUT /api/subscriptions/:id
 * @desc    Update subscription
 * @access  Private/CompanyAdmin, HOA Admin
 */
router.put('/:id', auth, subscriptionController.updateSubscription);

/**
 * @route   DELETE /api/subscriptions/:id
 * @desc    Cancel subscription
 * @access  Private/CompanyAdmin, HOA Admin
 */
router.delete('/:id', auth, subscriptionController.cancelSubscription);

/**
 * @route   GET /api/subscriptions/verify-token/:token
 * @desc    Verify subscription activation token
 * @access  Public
 */
router.get('/verify-token/:token', subscriptionController.verifyToken);

/**
 * @route   POST /api/subscriptions/create-checkout
 * @desc    Create Stripe checkout session for subscription
 * @access  Public (with valid token)
 */
router.post('/create-checkout', subscriptionController.createCheckoutSession);

router.get('/retrieve-checkout/:sessionId', subscriptionController.retrieveCheckoutSession);

/**
 * @route   POST /api/subscriptions/webhook
 * @desc    Handle Stripe webhook events
 * @access  Public
 */
router.post('/webhook', subscriptionController.handleStripeWebhook);

module.exports = router;

const express = require('express');
const router = express.Router();
const plaidController = require('../../../controllers/payments/plaid/plaidController');
const auth = require('../../../middleware/auth');

// Create a Plaid link token
router.post('/create-link-token', auth, plaidController.createLinkToken);

// Exchange a public token for an access token
router.post('/exchange-public-token', auth, plaidController.exchangePublicToken);

// Charge a payment
router.post('/charge', auth, plaidController.chargePayment);

module.exports = router;
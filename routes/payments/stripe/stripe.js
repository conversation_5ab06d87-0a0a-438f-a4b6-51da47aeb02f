// routes/payments.js
const express = require('express');
const router = express.Router();
const stripeController = require('../../../controllers/payments/stripe/stripeController');
const auth = require('../../../middleware/auth');

// Create a payment intent (for Stripe)
router.post('/create-intent', auth, stripeController.createPaymentIntent);

// Process a successful payment
router.post('/process', auth, stripeController.processPayment);

// Create setup intent (for Stripe)
router.post('/create-setup-intent', auth, stripeController.createSetupIntent);

// Create Subscription
router.post('/create-subscription', auth, stripeController.createSubscription);

// Get payment history for the current user
router.get('/history', auth, stripeController.getPaymentHistory);

// Get monthly due amount
router.get('/monthly-due', auth, stripeController.getMonthlyDueAmount);

// Get refresh onboarding link
router.get('/refresh-onboarding-link', stripeController.refreshOnboardingLink);

module.exports = router;

/**
 * HOA Revenue Routes
 *
 * These routes provide access to HOA revenue data for company admins.
 */

const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const companyAdmin = require('../../middleware/companyAdmin');
const hoaRevenueController = require('../../controllers/hoaRevenue/hoaRevenueController');

/**
 * @route   GET /api/hoa-revenue/summary
 * @desc    Get revenue summary for all HOAs
 * @access  Private/CompanyAdmin
 */
router.get('/summary', [auth, companyAdmin], hoaRevenueController.getHOARevenueSummary);

/**
 * @route   GET /api/hoa-revenue/debug/:hoaId
 * @desc    Debug endpoint to check finance entries for a specific HOA
 * @access  Private/CompanyAdmin
 */
router.get('/debug/:hoaId', [auth, companyAdmin], hoaRevenueController.debugHOAFinances);

/**
 * @route   GET /api/hoa-revenue/:hoaId
 * @desc    Get detailed revenue data for a specific HOA
 * @access  Private/CompanyAdmin
 */
router.get('/:hoaId', [auth, companyAdmin], hoaRevenueController.getHOARevenueDetail);

module.exports = router;

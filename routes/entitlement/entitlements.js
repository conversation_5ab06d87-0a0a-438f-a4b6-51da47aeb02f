/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const Entitlement = require('../../models/entitlement');

/**
 * @route   GET /api/entitlement
 * @desc    Get all entitlements
 * @access  Member
 */
router.get('/', auth, async (req, res) => {
  try {
    const entitlements = await Entitlement.findOne({role: req.user.role});
    res.status(200).json({
      success: true,
      count: entitlements.length,
      data: entitlements,
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

module.exports = router;
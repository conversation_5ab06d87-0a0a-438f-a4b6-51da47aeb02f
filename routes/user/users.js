const express = require('express');
const router = express.Router();
const User = require('../../models/user');
const authenticateUser = require('../../middleware/authenticateUser');
const userController = require('../../controllers/user/userController'); // Also make sure this is here
const auth = require('../../middleware/auth');
const upload = require('../../middleware/upload');


// Get all users who are not yet approved
router.get('/pending', auth, async (req, res) => {
  try {
    const pendingUsers = await User.find({ isApproved: false });
    res.json(pendingUsers);
  } catch (err) {
    res.status(500).json({ message: 'Error fetching pending users' });
  }
});

// Approve a user by ID
router.patch('/:id/approve', auth, async (req, res) => {
  try {
    const updated = await User.findByIdAndUpdate(
      req.params.id,
      { isApproved: true },
      { new: true }
    );
    res.json(updated);
  } catch (err) {
    res.status(500).json({ message: 'Error approving user' });
  }
});

// Update user settings
router.put('/settings',
  auth,
  upload.single('profilePhoto'),
  userController.updateSettings
);

// Delete profile photo
router.delete('/profile-photo',
  auth,
  userController.deleteProfilePhoto
);

// Set password for first-time login users
router.post('/set-password',
  auth,
  userController.setPassword
);

// Login route moved to auth.js

router.post('/logout', async (req, res) => {
  const userId = req.user.id; // Extract user ID from JWT
  const user = await User.findById(userId);

  if (user) {
    user.isOnline = false; // Set user as offline
    await user.save();
  }

  res.status(200).json({ message: 'Logged out successfully' });
});

// Admin routes for user management
// Get all users
router.get('/', auth, userController.getAllUsers);

// Get user by ID
router.get('/:id', auth, userController.getUserById);

// Update user
router.put('/:id', auth, userController.updateUser);

// Delete user
router.delete('/:id', auth, userController.deleteUser);

module.exports = router;

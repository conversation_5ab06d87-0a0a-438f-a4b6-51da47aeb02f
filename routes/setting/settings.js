const express = require('express');
const router = express.Router();
const Settings = require('../../models/settings');
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');

// Get settings
router.get('/', auth, async (req, res) => {
  try {
    console.log('Fetching settings for user:', req.user);
    let settings = await Settings.findOne();
    
    if (!settings) {
      settings = await Settings.create({
        dueDate: {
          day: 15,
          reminderDays: 7,
          gracePeriod: 3
        },
        lastModifiedBy: req.user.email || 'System'
      });
    }
    
    console.log('Returning settings:', settings);
    res.json(settings);
  } catch (err) {
    console.error('Error fetching settings:', err);
    res.status(500).json({ error: 'Failed to fetch settings', details: err.message });
  }
});

// Update settings (admin only)
router.put('/', [auth, admin], async (req, res) => {
  try {
    console.log('Update settings request from user:', req.user);
    console.log('Request body:', req.body);
    
    const { dueDate } = req.body;
    
    // Validate dueDate structure
    if (!dueDate || typeof dueDate.day !== 'number') {
      return res.status(400).json({ error: 'Invalid due date format' });
    }

    let settings = await Settings.findOne();
    if (!settings) {
      settings = new Settings();
    }
    
    // Update settings with the new dueDate
    settings.dueDate = {
      day: dueDate.day,
      reminderDays: dueDate.reminderDays || 7,
      gracePeriod: dueDate.gracePeriod || 3
    };
    settings.lastModifiedBy = req.user.email || 'Admin';
    settings.lastModifiedAt = new Date();
    
    await settings.save();
    console.log('Updated settings:', settings);
    res.json(settings);
  } catch (err) {
    console.error('Error updating settings:', err);
    res.status(500).json({ error: 'Failed to update settings', details: err.message });
  }
});

module.exports = router; 
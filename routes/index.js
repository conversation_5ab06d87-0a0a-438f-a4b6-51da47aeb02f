// Routes
const taskRoutes = require('./task/tasks');
const financeRoutes = require('./finance/finances');
const notificationRoutes = require('./notification/notifications');
const memberRoutes = require('./member/members');
const authRoutes = require('./auth/auth');
const userRoutes = require('./user/users');
const eventRoutes = require('./event/events');
const settingsRoutes = require('./setting/settings');
const passwordRoutes = require('./password/password');
const documentRoutes = require('./document/documents');
const hoaRoutes = require('./hoa/hoa');
const adminRoutes = require('./admin/admin');
const mfaRoutes = require('./mfa/mfa');
const messageRoutes = require('./message/messageRoutes');
const testRoutes = require('./test/test');
const stripePaymentRoutes = require('./payments/stripe/stripe');
const plaidPaymentRoutes = require('./payments/plaid/plaid');
const maintenanceRoutes = require('./maintenance/maintenance');
const entitlementRoutes = require('./entitlement/entitlements');
const subscriptionRoutes = require('./subscription/subscriptions');
const propertiesRoutes = require('./property/properties');
const budgetRoutes = require('./budget/budgets');
const communityRoutes = require('./community/communities');
const hoaRevenueRoutes = require('./hoaRevenue/hoaRevenue');
const propertyRoutes = require('./property/properties');
const announcementRoutes = require('./announcement/announcements');

module.exports = {
    taskRoutes,
    financeRoutes,
    notificationRoutes,
    memberRoutes,
    authRoutes,
    userRoutes,
    eventRoutes,
    settingsRoutes,
    passwordRoutes,
    documentRoutes,
    hoaRoutes,
    adminRoutes,
    mfaRoutes,
    messageRoutes,
    testRoutes,
    stripePaymentRoutes,
    plaidPaymentRoutes,
    maintenanceRoutes,
    entitlementRoutes,
    subscriptionRoutes,
    propertiesRoutes,
    budgetRoutes,
    communityRoutes,
    hoaRevenueRoutes,
    propertyRoutes,
    announcementRoutes,
};
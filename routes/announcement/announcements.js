const express = require('express');
const router = express.Router();
const announcementController = require('../../controllers/announcement/announcementController');
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');
const companyAdmin = require('../../middleware/companyAdmin');
const adminOrCompanyAdmin = require('../../middleware/adminOrCompanyAdmin');
const { announcementAttachments } = require('../../middleware/upload');

/**
 * Enhanced Announcement Routes
 * Supports role-based targeting for HOA admins and company admins
 */

// HOA Admin Routes (street-level targeting)
// Get streets that the HOA admin can target
router.get('/streets', [auth, admin], announcementController.getStreetsForAdmin);

// Get residents for a specific street
router.get('/streets/:streetId/residents', [auth, admin], announcementController.getResidentsForStreet);

// Send announcement to street (HOA admin or company admin) with file attachments
router.post('/send/street', [auth, adminOrCompanyAdmin, announcementAttachments.array('attachments', 5)], announcementController.sendStreetAnnouncement);

// Company Admin Routes (HOA-level targeting)
// Get HOAs that the company admin can target
router.get('/hoas', [auth, companyAdmin], announcementController.getHOAsForCompanyAdmin);

// Get targeting options for a specific HOA
router.get('/hoas/:hoaId/targeting', [auth, companyAdmin], announcementController.getHOATargetingOptions);

// Company admin can also access street-level residents (no HOA restrictions)
router.get('/company-admin/streets/:streetId/residents', [auth, companyAdmin], announcementController.getResidentsForStreetCompanyAdmin);

// Send announcement to HOA (company admin) with file attachments
router.post('/send/hoa', [auth, companyAdmin, announcementAttachments.array('attachments', 5)], announcementController.sendHOAAnnouncement);

module.exports = router;

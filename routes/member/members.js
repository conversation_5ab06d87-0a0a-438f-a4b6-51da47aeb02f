// routes/members.js
const express = require('express');
const router = express.Router();
const membersController = require('../../controllers/member/membersController');
const auth = require('../../middleware/auth');
const operationalAdmin = require('../../middleware/operationalAdmin');

// Company admin can view members for oversight, but not manage them
router.get('/', auth, membersController.getAllMembers);
router.get('/approved', auth, membersController.getApprovedUsers);

// Only operational admins can manage members (not company admin)
router.post('/', auth, operationalAdmin, membersController.addMember);
router.put('/:id', auth, operationalAdmin, membersController.updateMember);
router.delete('/:id', auth, operationalAdmin, membersController.deleteMember);

module.exports = router;

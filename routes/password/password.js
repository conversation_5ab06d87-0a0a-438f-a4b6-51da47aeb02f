const express = require('express');
const router = express.Router();
const passwordController = require('../../controllers/password/passwordController');
const auth = require('../../middleware/auth');

// Handle CORS preflight requests
router.options('*', (req, res) => {
  // Set CORS headers
  const allowedOrigins = process.env.NODE_ENV === 'production'
    ? [
        'https://hoa-front.vercel.app',
        'https://www.hoa-front.vercel.app',
        'https://hoa-front-git-main-pelicanapps-projects.vercel.app'
      ]
    : ['http://localhost:8080', 'http://127.0.0.1:8080', 'http://localhost:8082', 'http://127.0.0.1:8082'];

  const origin = req.headers.origin;
  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else {
    // Fallback to the first allowed origin
    res.setHeader('Access-Control-Allow-Origin', allowedOrigins[0]);
  }

  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(204);
});

// Request password reset (public)
router.post('/request-reset', passwordController.requestPasswordReset);

// Reset password with token (public)
router.post('/reset', passwordController.resetPassword);

// Change password (protected)
router.post('/change', auth, passwordController.changePassword);

module.exports = router;
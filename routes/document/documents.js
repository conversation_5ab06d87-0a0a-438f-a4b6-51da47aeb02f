/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// routes/documents.js
const express = require('express');
const router = express.Router();
const enhancedDocumentController = require('../../controllers/document/enhancedDocumentController');
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');
const hoaAccess = require('../../middleware/hoaAccess');
const { documents } = require('../../middleware/upload');

// Enhanced Routes with S3 Integration and Access Control

// Get all documents with enhanced filtering and pagination
router.get('/', auth, enhancedDocumentController.getDocuments);

// Get document statistics
router.get('/stats', auth, enhancedDocumentController.getDocumentStats);

// Upload a document with S3 storage and enhanced metadata
router.post('/upload', auth, documents.single('document'), enhancedDocumentController.uploadDocument);

// Download document with token verification (no auth middleware needed)
router.get('/download/:id', enhancedDocumentController.downloadDocument);

// Update document metadata
router.patch('/:id', auth, enhancedDocumentController.updateDocument);

// Update document category (specific route for category updates)
router.patch('/:id/category', auth, enhancedDocumentController.updateDocumentCategory);

// Delete a document (soft delete by default, hard delete with ?hard=true)
router.delete('/:id', auth, enhancedDocumentController.deleteDocument);

// Get documents by category
router.get('/category/:category', auth, enhancedDocumentController.getDocuments);

module.exports = router;

const express = require('express');
const router = express.Router();
const messageController = require('../../controllers/message/messageController');
const auth = require('../../middleware/auth');
const socketMiddleware = require('../../middleware/socketMiddleware');

// Apply auth middleware to all routes
router.use(auth);

// Apply socket middleware to all routes
router.use(socketMiddleware);

// Get all conversations for the current user
router.get('/conversations', messageController.getConversations);

// Get messages for a specific conversation
router.get('/conversations/:conversationId', messageController.getMessages);

// Get conversation details (by ID or by otherUserId query param)
router.get('/conversations/:conversationId?/details', messageController.getConversationDetails);

// Send a new message (all authenticated users including company admin)
router.post('/send', messageController.sendMessage);

// Edit a message (all authenticated users including company admin)
router.put('/:messageId', messageController.editMessage);

// Delete a message (all authenticated users including company admin)
router.delete('/:messageId', messageController.deleteMessage);

// Hide a conversation (all authenticated users including company admin)
router.post('/conversations/:conversationId/hide', messageController.hideConversation);

// Unhide a conversation (all authenticated users including company admin)
router.post('/conversations/:conversationId/unhide', messageController.unhideConversation);

module.exports = router;

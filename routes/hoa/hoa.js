/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const HOA = require('../../models/hoa');
const companyAdmin = require('../../middleware/companyAdmin');
const hoaAccess = require('../../middleware/hoaAccess');
const admin = require('../../middleware/admin');
const multer = require('multer');
const hoaController = require('../../controllers/hoa/hoaController');


// Configure multer for document uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, 'uploads/hoa_documents');
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, uniqueSuffix + '-' + file.originalname);
    }
  }),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

/**
 * @route   GET /api/hoa/code/:code
 * @desc    Get HOA by community code
 * @access  Public (needed for registration)
 */
// Handle OPTIONS requests for CORS preflight
router.options('/code/:code', (req, res) => {
  const origin = req.headers.origin;
  const allowedOrigins = [
    'https://hoa-front.vercel.app',
    'https://www.hoa-front.vercel.app',
    'https://street-harmony.vercel.app',
    'https://www.street-harmony.vercel.app',
    'http://localhost:8080',
    'http://localhost:8082'
  ];

  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Origin, X-Requested-With');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
  }

  res.status(204).end();
});

router.get('/code/:code', hoaController.getHOAByCode);

/**
 * @route   POST /api/hoa/register
 * @desc    Register a new HOA (public)
 * @access  Public
 */
// Handle OPTIONS requests for CORS preflight
router.options('/register', (req, res) => {
  const origin = req.headers.origin;
  const allowedOrigins = [
    'https://hoa-front.vercel.app',
    'https://www.hoa-front.vercel.app',
    'https://street-harmony.vercel.app',
    'https://www.street-harmony.vercel.app',
    'http://localhost:8080',
    'http://localhost:8082'
  ];

  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Origin, X-Requested-With');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
  }

  res.status(204).end();
});

router.post('/register', upload.fields([
  { name: 'registrationDoc', maxCount: 1 },
  { name: 'einDoc', maxCount: 1 }
]), hoaController.registerHOA);

/**
 * @route   GET /api/hoa/test
 * @desc    Create a test HOA (for development only)
 * @access  Public (for testing only)
 */
// Skip auth middleware for this route
router.get('/test', hoaController.createTestHOA);

/**
 * @route   GET /api/hoa
 * @desc    Get HOAs (Company Admin for all, Admin/Member for accessible HOAs)
 * @access  Private (Company Admin, Admin, Member)
 * @query   communityId - Filter HOAs by community ID (for Admin/Member)
 * @query   status - Filter HOAs by verification status
 */
router.get('/', [auth], hoaController.getAllHOAs);

/**
 * @route   GET /api/hoa/by-community/:communityId
 * @desc    Get HOA by Community ID (dedicated route for community-based lookup)
 * @access  Private/Admin for their HOA, Company Admin for any HOA
 * @param   communityId - Single community ID to find HOA
 * @example GET /api/hoa/by-community/6861f3dd03d638bc5774c23b
 */
router.get('/by-community/:communityId', [auth, hoaAccess({ allowSameHoaAdmins: true, allowMembers: true })], hoaController.getHOAById);

/**
 * @route   GET /api/hoa/:id
 * @desc    Get HOA by Community ID or HOA ID
 * @access  Private/Admin for their HOA, Company Admin for any HOA
 * @query   communityId - Single community ID to find HOA
 * @example GET /api/hoa/undefined?communityId=6861f3dd03d638bc5774c23b
 * @example GET /api/hoa/hoaId (traditional HOA ID lookup)
 */
router.get('/:id', [auth, hoaAccess({ allowSameHoaAdmins: true, allowMembers: true })], hoaController.getHOAById);

/**
 * @route   POST /api/hoa
 * @desc    Create a new HOA (company admin only)
 * @access  Private/CompanyAdmin
 */
router.post('/', [auth, companyAdmin], hoaController.createHOA);

/**
 * @route   PUT /api/hoa/:id
 * @desc    Update HOA (operational management - HOA admins only)
 * @access  Private/Admin for their HOA only (Company Admin excluded for operational updates)
 */
router.put('/:id', [auth, hoaAccess({ allowSameHoaAdmins: true, allowCompanyAdmin: false, allowMembers: false })], hoaController.updateHOA);

/**
 * @route   POST /api/hoa/:id/documents
 * @desc    Upload HOA verification documents
 * @access  Private (Admin for their HOA, Company Admin for any HOA)
 */
router.post('/:id/documents', [
  auth,
  hoaAccess({ allowSameHoaAdmins: true, allowMembers: false }),
  upload.fields([
    { name: 'registrationDoc', maxCount: 1 },
    { name: 'einDoc', maxCount: 1 }
  ])
], hoaController.uploadHOADocuments);

/**
 * @route   PUT /api/hoa/:id/verify
 * @desc    Approve or reject HOA registration
 * @access  Private (Company Admin only)
 */
router.put('/:id/verify', [auth, companyAdmin], hoaController.verifyHOA);

module.exports = router;

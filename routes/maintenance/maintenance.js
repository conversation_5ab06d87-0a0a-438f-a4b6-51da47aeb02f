const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');
const hoaAccess = require('../../middleware/hoaAccess');
const maintenanceController = require('../../controllers/maintenance/maintenanceController');

/**
 * @route   GET /api/maintenance
 * @desc    Get all maintenance requests for an HOA
 * @access  Private
 */
router.get('/', auth, hoaAccess({ allowMembers: true }), maintenanceController.getMaintenanceRequests);

/**
 * @route   GET /api/maintenance/:id
 * @desc    Get a single maintenance request
 * @access  Private
 */
router.get('/:id', auth, hoaAccess({ allowMembers: true }), maintenanceController.getMaintenanceRequest);

/**
 * @route   POST /api/maintenance
 * @desc    Create a new maintenance request
 * @access  Private
 */
router.post('/', auth, hoaAccess({ allowMembers: true }), maintenanceController.createMaintenanceRequest);

/**
 * @route   PUT /api/maintenance/:id
 * @desc    Update a maintenance request
 * @access  Private/Admin
 */
router.put('/:id', auth, hoaAccess({ allowSameHoaAdmins: true }), maintenanceController.updateMaintenanceRequest);

/**
 * @route   DELETE /api/maintenance/:id
 * @desc    Delete a maintenance request
 * @access  Private/Admin
 */
router.delete('/:id', auth, hoaAccess({ allowSameHoaAdmins: true }), maintenanceController.deleteMaintenanceRequest);

module.exports = router;

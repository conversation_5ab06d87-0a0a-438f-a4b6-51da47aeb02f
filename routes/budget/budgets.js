/**
 * Street Harmony HOA Management System
 * Budget Routes
 */
const express = require('express');
const router = express.Router();
const budgetController = require('../../controllers/budget/budgetController');
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');
const operationalAdmin = require('../../middleware/operationalAdmin');

/**
 * @route   POST /api/budgets
 * @desc    Create a new budget
 * @access  Private/OperationalAdmin (not company admin)
 */
router.post('/', auth, operationalAdmin, budgetController.createBudget);

/**
 * @route   GET /api/budgets
 * @desc    Get all budgets (company admin only)
 * @access  Private/Admin
 */
router.get('/', auth, admin, budgetController.getAllBudgets);

/**
 * @route   GET /api/budgets/hoa/:hoaId
 * @desc    Get all budgets for an HOA
 * @access  Private
 */
router.get('/hoa/:hoaId', auth, budgetController.getBudgetsByHoa);

/**
 * @route   GET /api/budgets/:id
 * @desc    Get a budget by ID
 * @access  Private
 */
router.get('/:id', auth, budgetController.getBudgetById);

/**
 * @route   PUT /api/budgets/:id
 * @desc    Update a budget
 * @access  Private/OperationalAdmin (not company admin)
 */
router.put('/:id', auth, operationalAdmin, budgetController.updateBudget);

/**
 * @route   DELETE /api/budgets/:id
 * @desc    Delete a budget
 * @access  Private/OperationalAdmin (not company admin)
 */
router.delete('/:id', auth, operationalAdmin, budgetController.deleteBudget);

/**
 * @route   POST /api/budgets/:id/update-actuals
 * @desc    Update budget actuals from finance entries
 * @access  Private/OperationalAdmin (not company admin)
 */
router.post('/:id/update-actuals', auth, operationalAdmin, budgetController.updateBudgetActuals);

module.exports = router;

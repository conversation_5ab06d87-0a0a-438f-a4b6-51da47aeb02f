/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// routes/test.js - Test routes for debugging
const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const multer = require('multer');

// Create test directory if it doesn't exist
const testDir = path.join(__dirname, '../uploads/test');
if (!fs.existsSync(testDir)) {
  fs.mkdirSync(testDir, { recursive: true, mode: 0o777 });
  console.log('Created test directory at:', testDir);
}

// Configure multer for test uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, testDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  }
});

const upload = multer({ storage: storage });

// Test route to check if uploads are working
router.post('/upload', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    console.log('Test file uploaded:', req.file);

    // Return file details
    res.status(200).json({
      message: 'File uploaded successfully',
      file: {
        filename: req.file.filename,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        path: req.file.path
      }
    });
  } catch (err) {
    console.error('Error in test upload:', err);
    res.status(500).json({ message: 'Error uploading file', error: err.message });
  }
});

// Test route to check file system
router.get('/fs-check', (req, res) => {
  try {
    const uploadsDir = path.join(__dirname, '../uploads');
    const documentsDir = path.join(uploadsDir, 'documents');
    
    // Check if directories exist
    const uploadsExists = fs.existsSync(uploadsDir);
    const documentsExists = fs.existsSync(documentsDir);
    
    // Try to create directories if they don't exist
    if (!uploadsExists) {
      try {
        fs.mkdirSync(uploadsDir, { recursive: true, mode: 0o777 });
        console.log('Created uploads directory');
      } catch (err) {
        console.error('Error creating uploads directory:', err);
      }
    }
    
    if (!documentsExists) {
      try {
        fs.mkdirSync(documentsDir, { recursive: true, mode: 0o777 });
        console.log('Created documents directory');
      } catch (err) {
        console.error('Error creating documents directory:', err);
      }
    }
    
    // Check write permissions
    let uploadsWritable = false;
    let documentsWritable = false;
    
    try {
      fs.accessSync(uploadsDir, fs.constants.W_OK);
      uploadsWritable = true;
    } catch (err) {
      console.error('Uploads directory is not writable:', err);
    }
    
    try {
      fs.accessSync(documentsDir, fs.constants.W_OK);
      documentsWritable = true;
    } catch (err) {
      console.error('Documents directory is not writable:', err);
    }
    
    // Return directory status
    res.status(200).json({
      message: 'File system check completed',
      directories: {
        uploads: {
          path: uploadsDir,
          exists: uploadsExists || fs.existsSync(uploadsDir),
          writable: uploadsWritable
        },
        documents: {
          path: documentsDir,
          exists: documentsExists || fs.existsSync(documentsDir),
          writable: documentsWritable
        }
      }
    });
  } catch (err) {
    console.error('Error checking file system:', err);
    res.status(500).json({ message: 'Error checking file system', error: err.message });
  }
});

module.exports = router;

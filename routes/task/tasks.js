const express = require('express');
const router = express.Router();
const tasksController = require('../../controllers/task/tasksController');
const auth = require('../../middleware/auth');
const operationalAdmin = require('../../middleware/operationalAdmin');

// GET all tasks
router.get('/', auth, tasksController.getAllTasks);

// POST create new task - operational admin only
router.post('/', auth, operationalAdmin, tasksController.createTask);

// PUT update task - operational admin only
router.put('/:id', auth, operationalAdmin, tasksController.updateTask);

// DELETE task - operational admin only
router.delete('/:id', auth, operationalAdmin, tasksController.deleteTask);

// POST close task - operational admin only
router.post('/:id/close', auth, operationalAdmin, tasksController.closeTask);

// PATCH vote on task - members can vote, keep auth only
router.patch('/:id/vote', auth, tasksController.voteTask);

// POST add comment to task - members can comment, keep auth only
router.post('/:id/comments', auth, tasksController.addComment);

// DELETE comment from task - operational admin only
router.delete('/:taskId/comments/:commentId', auth, operationalAdmin, tasksController.deleteComment);

module.exports = router;

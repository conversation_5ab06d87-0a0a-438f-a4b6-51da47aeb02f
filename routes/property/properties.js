const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const hoaAccess = require('../../middleware/hoaAccess');
const propertyController = require('../../controllers/property/propertyController');

/**
 * @route   GET /api/properties
 * @desc    Get all properties for an HOA or specific communities
 * @access  Private
 * @query   communityId - Array of community IDs or single community ID to filter properties
 * @example GET /api/properties?communityId=["id1","id2"]
 * @example GET /api/properties?communityId=id1,id2
 * @example GET /api/properties?communityId=id1
 * @example GET /api/properties (shows all properties user has access to)
 */
router.get('/', auth, propertyController.getProperties);



/**
 * @route   GET /api/properties/:id
 * @desc    Get a single property
 * @access  Private
 */
router.get('/:id', auth, hoaAccess({ allowMembers: true }), propertyController.getProperty);

/**
 * @route   POST /api/properties
 * @desc    Create a new property
 * @access  Private/Admin
 */
router.post('/', auth, hoaAccess({ allowSameHoaAdmins: true }), propertyController.createProperty);

/**
 * @route   PUT /api/properties/:id
 * @desc    Update a property
 * @access  Private/Admin
 */
router.put('/:id', auth, hoaAccess({ allowSameHoaAdmins: true }), propertyController.updateProperty);

/**
 * @route   DELETE /api/properties/:id
 * @desc    Delete a property
 * @access  Private/Admin
 */
router.delete('/:id', auth, hoaAccess({ allowSameHoaAdmins: true }), propertyController.deleteProperty);

/**
 * @route   POST /api/properties/:id/documents
 * @desc    Add a document to a property
 * @access  Private/Admin
 */
router.post('/:id/documents', auth, hoaAccess({ allowSameHoaAdmins: true }), propertyController.addPropertyDocument);

/**
 * @route   DELETE /api/properties/:id/documents/:documentId
 * @desc    Delete a document from a property
 * @access  Private/Admin
 */
router.delete('/:id/documents/:documentId', auth, hoaAccess({ allowSameHoaAdmins: true }), propertyController.deletePropertyDocument);

router.get('/notifyOwner/:userId', auth, propertyController.notifyOwner);

module.exports = router;

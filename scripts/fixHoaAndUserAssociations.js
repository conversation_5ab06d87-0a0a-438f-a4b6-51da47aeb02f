const mongoose = require('mongoose');
require('dotenv').config();

// MongoDB URI for street-harmony
const MONGO_URI = 'mongodb+srv://frankiebruno:<EMAIL>/street-harmony?retryWrites=true&w=majority&appName=HOA-Data';

async function fixHoaAndUserAssociations() {
  let connection = null;
  
  try {
    // Connect to database
    connection = await mongoose.createConnection(MONGO_URI);
    console.log('Connected to street-harmony database');
    
    // 1. Remove HOA association from fbruno (company_admin)
    console.log('\nUpdating fbruno user to remove HOA association...');
    const fbrunoResult = await connection.collection('users').updateOne(
      { username: 'fbruno' },
      { 
        $unset: { 
          hoaId: "",
          hoaCommunityCode: ""
        }
      }
    );
    
    console.log(`Modified ${fbrunoResult.modifiedCount} user(s)`);
    
    // 2. Update both HOAs to have proper verification and subscription status
    console.log('\nUpdating HOAs with proper verification and subscription status...');
    const hoaResult = await connection.collection('hoas').updateMany(
      {}, // Update all HOAs
      { 
        $set: { 
          verificationStatus: "approved",
          "subscription.status": "active",
          "subscription.plan": "basic",
          "subscription.startDate": new Date(),
          "subscription.endDate": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          "subscription.stripeSubscriptionId": "sub_test_" + Math.random().toString(36).substring(2, 15)
        }
      }
    );
    
    console.log(`Updated ${hoaResult.modifiedCount} HOA(s)`);
    
    // 3. Delete the "Deraney ln" HOA since it's not needed
    console.log('\nDeleting the "Deraney ln" HOA...');
    const deleteResult = await connection.collection('hoas').deleteOne(
      { hoaCommunityCode: "1FWXFS" }
    );
    
    console.log(`Deleted ${deleteResult.deletedCount} HOA(s)`);
    
    // Verify the updates
    console.log('\n=== VERIFICATION ===');
    
    // Check fbruno user
    const fbrunoUser = await connection.collection('users').findOne({ username: 'fbruno' });
    console.log('\nfbruno user details:');
    console.log('- Username:', fbrunoUser.username);
    console.log('- Role:', fbrunoUser.role);
    console.log('- HOA ID:', fbrunoUser.hoaId || 'None');
    console.log('- HOA Community Code:', fbrunoUser.hoaCommunityCode || 'None');
    
    // Check remaining HOAs
    const hoas = await connection.collection('hoas').find({}).toArray();
    console.log(`\nRemaining HOAs: ${hoas.length}`);
    
    hoas.forEach((hoa, index) => {
      console.log(`\nHOA ${index + 1}:`);
      console.log('- Name:', hoa.hoaCommunityName);
      console.log('- Code:', hoa.hoaCommunityCode);
      console.log('- Verification Status:', hoa.verificationStatus);
      console.log('- Subscription Status:', hoa.subscription?.status);
      console.log('- Subscription Plan:', hoa.subscription?.plan);
      console.log('- ID:', hoa._id);
    });
    
  } catch (err) {
    console.error('Error:', err.message);
  } finally {
    // Close connection
    if (connection) await connection.close();
    console.log('\nDatabase connection closed');
  }
}

// Run the function
fixHoaAndUserAssociations();

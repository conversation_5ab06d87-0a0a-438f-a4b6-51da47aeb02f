/**
 * Test script to create a community
 */
require('dotenv').config({ path: '.env.development' });
const mongoose = require('mongoose');
const Community = require('../models/community');
const HOA = require('../models/hoa');

// Connect to MongoDB
const MONGO_URI = process.env.MONGO_URI;
console.log(`Using MongoDB URI: ${MONGO_URI ? MONGO_URI.substring(0, 20) + '...' : 'undefined'}`);

mongoose.connect(MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected for testing'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function createTestCommunity() {
  try {
    // Find an existing HOA
    const hoa = await HOA.findOne();

    if (!hoa) {
      console.log('No HOA found. Creating a test HOA...');

      // Create a test HOA
      const newHoa = new HOA({
        hoaCommunityName: 'Test HOA',
        hoaCommunityCode: 'TEST123',
        hoaStreetAddress: '123 Test Street',
        hoaCity: 'Test City',
        hoaState: 'TS',
        hoaZipCode: '12345',
        verificationStatus: 'approved',
        subscription: {
          tier: 'basic',
          status: 'active'
        }
      });

      await newHoa.save();
      console.log('Test HOA created:', newHoa);

      // Create a test community
      const communityCode = await Community.generateCommunityCode(newHoa.hoaCommunityCode);

      const newCommunity = new Community({
        name: 'Test Community',
        description: 'A test community',
        streetAddress: '456 Test Avenue',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        communityCode,
        hoaId: newHoa._id,
        type: 'single-family',
        status: 'active',
        visibility: 'public',
        permissions: {
          memberVisibility: true,
          financeVisibility: false,
          documentVisibility: true
        }
      });

      await newCommunity.save();
      console.log('Test community created:', newCommunity);
    } else {
      console.log('Existing HOA found:', hoa);

      // Check if community already exists
      const existingCommunity = await Community.findOne({ hoaId: hoa._id });

      if (existingCommunity) {
        console.log('Existing community found:', existingCommunity);
      } else {
        // Create a test community
        const communityCode = await Community.generateCommunityCode(hoa.hoaCommunityCode);

        const newCommunity = new Community({
          name: `${hoa.hoaCommunityName} - Main`,
          description: `Default community for ${hoa.hoaCommunityName}`,
          streetAddress: hoa.hoaStreetAddress,
          city: hoa.hoaCity,
          state: hoa.hoaState,
          zipCode: hoa.hoaZipCode,
          communityCode,
          hoaId: hoa._id,
          type: 'mixed',
          status: 'active',
          visibility: 'public',
          permissions: {
            memberVisibility: true,
            financeVisibility: false,
            documentVisibility: true
          }
        });

        await newCommunity.save();
        console.log('Test community created:', newCommunity);
      }
    }

    // List all communities
    const communities = await Community.find().populate('hoaId', 'hoaCommunityName');
    console.log('All communities:', communities);

  } catch (error) {
    console.error('Error creating test data:', error);
  } finally {
    // Close MongoDB connection
    mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

// Run the test
createTestCommunity();

/**
 * Quick Local Testing Script
 * Tests the backend with existing production data
 */

const mongoose = require('mongoose');
const fs = require('fs');

// Load environment - prioritize production environment for testing
let envFile = '.env.local.production';
if (fs.existsSync('.env.local.active')) {
  envFile = '.env.local.active';
} else if (!fs.existsSync('.env.local.production') && fs.existsSync('.env.local')) {
  envFile = '.env.local';
} else if (!fs.existsSync('.env.local.production') && !fs.existsSync('.env.local')) {
  envFile = '.env';
}

require('dotenv').config({ path: envFile });
console.log(`🔧 Using environment: ${envFile}`);

// Import models
const User = require('../models/user');
const HOA = require('../models/hoa');
const Community = require('../models/community');
const Document = require('../models/document');
const Notification = require('../models/notification');

const quickTest = async () => {
  try {
    console.log('🧪 Starting quick local testing with existing data...\n');
    
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || process.env.MONGO_URI);
    console.log('✅ Connected to database');

    // Test data retrieval
    const userCount = await User.countDocuments();
    const hoaCount = await HOA.countDocuments();
    const communityCount = await Community.countDocuments();
    const documentCount = await Document.countDocuments();
    const notificationCount = await Notification.countDocuments();

    console.log('\n📊 Database Statistics:');
    console.log(`   • Users: ${userCount}`);
    console.log(`   • HOAs: ${hoaCount}`);
    console.log(`   • Communities: ${communityCount}`);
    console.log(`   • Documents: ${documentCount}`);
    console.log(`   • Notifications: ${notificationCount}`);

    // Get sample data for testing
    const sampleUsers = await User.find().limit(3).select('username email role hoaId');
    const sampleHOAs = await HOA.find().limit(2).select('hoaCommunityName hoaCommunityCode');
    const sampleCommunities = await Community.find().limit(3).select('name code hoaId');

    if (sampleUsers.length > 0) {
      console.log('\n👥 Sample Users for Testing:');
      sampleUsers.forEach(user => {
        console.log(`   • ${user.username} (${user.email}) - Role: ${user.role}`);
      });
    }

    if (sampleHOAs.length > 0) {
      console.log('\n🏘️ Sample HOAs:');
      sampleHOAs.forEach(hoa => {
        console.log(`   • ${hoa.hoaCommunityName} (${hoa.hoaCommunityCode})`);
      });
    }

    if (sampleCommunities.length > 0) {
      console.log('\n🏠 Sample Communities:');
      sampleCommunities.forEach(community => {
        console.log(`   • ${community.name} (${community.code})`);
      });
    }

    // Test enhanced features
    console.log('\n🔧 Testing Enhanced Features:');
    
    // Test document model enhancements
    const documentsWithFiles = await Document.find({ 'file.s3Key': { $exists: true } }).limit(3);
    console.log(`   • Documents with S3 integration: ${documentsWithFiles.length}`);

    // Test announcement attachments
    const AnnouncementAttachment = require('../models/announcementAttachment');
    const attachmentCount = await AnnouncementAttachment.countDocuments();
    console.log(`   • Announcement attachments: ${attachmentCount}`);

    console.log('\n✅ Quick test completed successfully!');
    console.log('\n🚀 Your local testing environment is ready!');
    console.log('\n📋 Next Steps:');
    console.log('   1. Start backend: npm run local:start');
    console.log('   2. Start frontend: cd ../HOA-FRONT && npm run dev:local');
    console.log('   3. Open browser: http://localhost:8080 or http://localhost:8081');
    console.log('   4. Login with existing user credentials');

  } catch (error) {
    console.error('❌ Quick test failed:', error.message);
    console.log('\n🔍 Troubleshooting:');
    console.log('   • Check database connection string');
    console.log('   • Verify environment variables are set');
    console.log('   • Ensure MongoDB is accessible');
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from database');
    process.exit(0);
  }
};

// Run quick test if called directly
if (require.main === module) {
  quickTest();
}

module.exports = quickTest;

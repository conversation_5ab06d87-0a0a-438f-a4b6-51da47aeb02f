/**
 * Local Setup Testing Script
 * Tests all enhanced features locally to ensure everything works
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001';
let authToken = null;

// Test credentials
const testCredentials = {
  companyAdmin: { email: '<EMAIL>', password: 'password123' },
  hoaAdmin: { email: '<EMAIL>', password: 'password123' },
  user: { email: '<EMAIL>', password: 'password123' }
};

const testLocalSetup = async () => {
  console.log('🧪 Starting comprehensive local testing...\n');

  try {
    // Test 1: Server Health
    console.log('1️⃣ Testing server health...');
    const healthResponse = await axios.get(`${BASE_URL}/`);
    console.log('✅ Server is running:', healthResponse.data);

    // Test 2: Authentication
    console.log('\n2️⃣ Testing authentication...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, testCredentials.companyAdmin);
    authToken = loginResponse.data.token;
    console.log('✅ Authentication successful');

    // Test 3: Enhanced Document Endpoints
    console.log('\n3️⃣ Testing enhanced document management...');
    
    // Test document stats
    const statsResponse = await axios.get(`${BASE_URL}/api/documents/stats`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Document stats:', statsResponse.data);

    // Test document listing
    const documentsResponse = await axios.get(`${BASE_URL}/api/documents`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Document listing successful, found:', documentsResponse.data.documents?.length || 0, 'documents');

    // Test 4: Announcement System
    console.log('\n4️⃣ Testing announcement system...');
    const announcementsResponse = await axios.get(`${BASE_URL}/api/notifications`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Announcements retrieved:', announcementsResponse.data.notifications?.length || 0, 'announcements');

    // Test 5: User Management
    console.log('\n5️⃣ Testing user management...');
    const usersResponse = await axios.get(`${BASE_URL}/api/users`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Users retrieved:', usersResponse.data.users?.length || 0, 'users');

    // Test 6: HOA Management
    console.log('\n6️⃣ Testing HOA management...');
    const hoasResponse = await axios.get(`${BASE_URL}/api/hoas`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ HOAs retrieved:', hoasResponse.data.hoas?.length || 0, 'HOAs');

    // Test 7: Community Management
    console.log('\n7️⃣ Testing community management...');
    const communitiesResponse = await axios.get(`${BASE_URL}/api/communities`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Communities retrieved:', communitiesResponse.data.communities?.length || 0, 'communities');

    // Test 8: Role-based Access Control
    console.log('\n8️⃣ Testing role-based access control...');
    
    // Test HOA Admin access
    const hoaAdminLogin = await axios.post(`${BASE_URL}/api/auth/login`, testCredentials.hoaAdmin);
    const hoaAdminToken = hoaAdminLogin.data.token;
    
    const hoaAdminDocs = await axios.get(`${BASE_URL}/api/documents`, {
      headers: { Authorization: `Bearer ${hoaAdminToken}` }
    });
    console.log('✅ HOA Admin access test passed');

    // Test User access
    const userLogin = await axios.post(`${BASE_URL}/api/auth/login`, testCredentials.user);
    const userToken = userLogin.data.token;
    
    const userDocs = await axios.get(`${BASE_URL}/api/documents`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ User access test passed');

    console.log('\n🎉 All local tests passed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('✅ Server Health: PASS');
    console.log('✅ Authentication: PASS');
    console.log('✅ Enhanced Documents: PASS');
    console.log('✅ Announcement System: PASS');
    console.log('✅ User Management: PASS');
    console.log('✅ HOA Management: PASS');
    console.log('✅ Community Management: PASS');
    console.log('✅ Role-based Access: PASS');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    console.log('\n🔍 Troubleshooting tips:');
    console.log('1. Make sure the backend server is running on port 5001');
    console.log('2. Ensure the database is seeded with test data');
    console.log('3. Check that all environment variables are set correctly');
  }
};

// Run tests if called directly
if (require.main === module) {
  testLocalSetup();
}

module.exports = testLocalSetup;

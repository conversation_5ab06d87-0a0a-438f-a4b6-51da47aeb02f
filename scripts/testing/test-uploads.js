/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// Script to test file upload functionality
require('dotenv').config();
const fs = require('fs');
const path = require('path');

// Check if uploads directory exists
const uploadsDir = path.join(__dirname, '../uploads');
console.log('Checking uploads directory:', uploadsDir);

if (!fs.existsSync(uploadsDir)) {
  console.log('Creating uploads directory...');
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('Uploads directory created successfully');
} else {
  console.log('Uploads directory exists');
  
  // Check if the directory is writable
  try {
    const testFile = path.join(uploadsDir, 'test-write.txt');
    fs.writeFileSync(testFile, 'Test write permission');
    console.log('Successfully wrote test file');
    
    // Clean up test file
    fs.unlinkSync(testFile);
    console.log('Successfully deleted test file');
    
    console.log('Uploads directory is writable');
  } catch (error) {
    console.error('Error testing write permissions:', error);
    console.log('Uploads directory may not be writable');
  }
}

// Check permissions on the uploads directory
try {
  const stats = fs.statSync(uploadsDir);
  console.log('Uploads directory permissions:', stats.mode.toString(8));
} catch (error) {
  console.error('Error checking directory permissions:', error);
}

console.log('Upload test complete');

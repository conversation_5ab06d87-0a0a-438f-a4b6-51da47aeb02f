#!/usr/bin/env node

/**
 * Deployment script for different environments
 * 
 * Usage:
 * - Deploy to staging: node scripts/deploy.js staging
 * - Deploy to production: node scripts/deploy.js prod
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get the environment from command line arguments
const args = process.argv.slice(2);
const env = args[0]?.toLowerCase();

if (!env || (env !== 'staging' && env !== 'prod')) {
  console.error('Please specify environment: staging or prod');
  console.log('Usage:');
  console.log('  node scripts/deploy.js staging  # Deploy to staging environment');
  console.log('  node scripts/deploy.js prod     # Deploy to production environment');
  process.exit(1);
}

const deployConfig = {
  staging: {
    herokuApp: 'hoa-backend-dev',
    herokuRemote: 'heroku-dev',
    envFile: '.env.staging'
  },
  prod: {
    herokuApp: 'hoa-management-app-dad2f9d126ae',
    herokuRemote: 'heroku-prod', 
    envFile: '.env.production'
  }
};

const config = deployConfig[env];

console.log(`🚀 Deploying to ${env.toUpperCase()} environment...`);
console.log(`📱 Heroku App: ${config.herokuApp}`);
console.log(`🔧 Environment File: ${config.envFile}`);

try {
  // 1. Switch to the correct environment
  console.log('\n1️⃣ Switching environment configuration...');
  execSync(`node scripts/toggle-env.js ${env}`, { stdio: 'inherit' });

  // 2. Build the frontend
  console.log('\n2️⃣ Building frontend...');
  execSync('npm run build', { stdio: 'inherit' });

  // 3. Deploy to Heroku (if this is a backend deployment)
  // Note: This assumes you're deploying the backend. 
  // For frontend, you'd typically deploy to Vercel or similar
  console.log('\n3️⃣ Deploying to Heroku...');
  
  // Check if the Heroku remote exists
  try {
    execSync(`git remote get-url ${config.herokuRemote}`, { stdio: 'pipe' });
  } catch (error) {
    console.log(`Adding Heroku remote: ${config.herokuRemote}`);
    execSync(`heroku git:remote -a ${config.herokuApp} -r ${config.herokuRemote}`, { stdio: 'inherit' });
  }

  // Deploy to Heroku
  execSync(`git push ${config.herokuRemote} main`, { stdio: 'inherit' });

  console.log(`\n✅ Successfully deployed to ${env.toUpperCase()}!`);
  console.log(`🌐 App URL: https://${config.herokuApp}.herokuapp.com`);

} catch (error) {
  console.error(`\n❌ Deployment failed:`, error.message);
  process.exit(1);
}

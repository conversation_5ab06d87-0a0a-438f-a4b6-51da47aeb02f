const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.development' });

// Models
const User = require('../models/user');

async function cleanupExceptTestUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB Connected to:', process.env.MONGO_URI);

    // Find all users except our test users
    const result = await User.deleteMany({
      username: { 
        $nin: ['fbruno', 'VuDoan3112', 'testmember'] 
      }
    });
    
    console.log(`Deleted ${result.deletedCount} users`);
    
    // Verify remaining users
    const remainingUsers = await User.find({}).select('username email role');
    
    console.log('\nRemaining users:');
    remainingUsers.forEach((user, index) => {
      console.log(`\nUser ${index + 1}:`);
      console.log('Username:', user.username);
      console.log('Email:', user.email);
      console.log('Role:', user.role);
    });

  } catch (err) {
    console.error('Error:', err.message);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('\nMongoDB Disconnected');
  }
}

// Run the function
cleanupExceptTestUsers();

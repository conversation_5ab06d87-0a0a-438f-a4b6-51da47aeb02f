/**
 * <PERSON><PERSON><PERSON> to view MongoDB database structure and contents
 * 
 * This script:
 * 1. Connects to the MongoDB database
 * 2. Lists all collections
 * 3. Shows sample documents from key collections
 * 4. Displays user information
 * 
 * Usage:
 * - Run this script with: node scripts/viewDatabase.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('../models/user');
const HOA = require('../models/hoa');
const Community = require('../models/community');
const Property = require('../models/property');

// Connect to MongoDB
const MONGO_URI = process.env.MONGO_URI || 'mongodb+srv://frankiebruno:<EMAIL>/street-harmony-dev?retryWrites=true&w=majority&appName=HOA-Data';
console.log(`Using MongoDB URI: ${MONGO_URI.substring(0, 20)}...`);

mongoose.connect(MONGO_URI)
  .then(() => {
    console.log('MongoDB connected successfully');
    viewDatabase();
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

async function viewDatabase() {
  try {
    console.log('\n=== DATABASE OVERVIEW ===');
    
    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\nCollections in database:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    // Count documents in each collection
    console.log('\nDocument counts:');
    for (const collection of collections) {
      const count = await mongoose.connection.db.collection(collection.name).countDocuments();
      console.log(`- ${collection.name}: ${count} documents`);
    }
    
    // View users
    console.log('\n=== USERS ===');
    const users = await User.find().select('-password');
    console.log(`Total users: ${users.length}`);
    
    if (users.length > 0) {
      console.log('\nUser details:');
      users.forEach(user => {
        console.log(`- Username: ${user.username}`);
        console.log(`  Email: ${user.email}`);
        console.log(`  Role: ${user.role}`);
        console.log(`  Approved: ${user.isApproved}`);
        console.log(`  HOA ID: ${user.hoaId || 'None'}`);
        console.log(`  HOA Community Code: ${user.hoaCommunityCode || 'None'}`);
        console.log('  ---');
      });
    }
    
    // View HOAs
    console.log('\n=== HOAs ===');
    const hoas = await HOA.find();
    console.log(`Total HOAs: ${hoas.length}`);
    
    if (hoas.length > 0) {
      console.log('\nHOA details:');
      hoas.forEach(hoa => {
        console.log(`- Name: ${hoa.hoaCommunityName}`);
        console.log(`  Code: ${hoa.hoaCommunityCode}`);
        console.log(`  Verification Status: ${hoa.verificationStatus}`);
        console.log(`  Subscription Status: ${hoa.subscription?.status || 'None'}`);
        console.log('  ---');
      });
    }
    
    // View Communities
    console.log('\n=== Communities ===');
    const communities = await Community.find();
    console.log(`Total Communities: ${communities.length}`);
    
    if (communities.length > 0) {
      console.log('\nCommunity details:');
      communities.forEach(community => {
        console.log(`- Name: ${community.name}`);
        console.log(`  Code: ${community.communityCode}`);
        console.log(`  HOA ID: ${community.hoaId}`);
        console.log(`  Status: ${community.status}`);
        console.log('  ---');
      });
    }
    
    // View Properties
    console.log('\n=== Properties ===');
    const properties = await Property.find();
    console.log(`Total Properties: ${properties.length}`);
    
    if (properties.length > 0) {
      console.log('\nProperty details:');
      properties.forEach(property => {
        console.log(`- Address: ${property.address}`);
        console.log(`  Community ID: ${property.communityId}`);
        console.log(`  HOA ID: ${property.hoaId}`);
        console.log(`  Owner: ${property.owner || 'None'}`);
        console.log('  ---');
      });
    }
    
    console.log('\nDatabase overview completed.');
    mongoose.connection.close();
    
  } catch (error) {
    console.error('Error viewing database:', error);
    process.exit(1);
  }
}

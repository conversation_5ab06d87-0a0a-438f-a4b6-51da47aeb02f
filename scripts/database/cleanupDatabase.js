/**
 * <PERSON><PERSON><PERSON> to clean up the database while preserving specific users
 * 
 * This script:
 * 1. Keeps the specified user accounts (f<PERSON><PERSON><PERSON> and VuDoan3112)
 * 2. Removes all other users
 * 3. Removes all HOAs, communities, properties, and related data
 * 4. Preserves the system configuration
 * 
 * Usage:
 * - Run this script with: node scripts/cleanupDatabase.js [database-name]
 * - Example: node scripts/cleanupDatabase.js street-harmony
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Get database name from command line arguments
const dbName = process.argv[2] || 'street-harmony';
if (!dbName) {
  console.error('Please provide a database name as an argument');
  console.error('Example: node scripts/cleanupDatabase.js street-harmony');
  process.exit(1);
}

// Usernames to keep
const USERNAMES_TO_KEEP = ['fbruno', 'VuDoan3112'];

// Base MongoDB URI without database name
const BASE_MONGO_URI = 'mongodb+srv://frankiebruno:<EMAIL>/';
// Full MongoDB URI with database name
const MONGO_URI = `${BASE_MONGO_URI}${dbName}?retryWrites=true&w=majority&appName=HOA-Data`;

console.log(`Connecting to database: ${dbName}`);
console.log(`Using MongoDB URI: ${BASE_MONGO_URI}${dbName}?...`);

// Connect to MongoDB
mongoose.connect(MONGO_URI)
  .then(() => {
    console.log(`MongoDB connected successfully to ${dbName}`);
    cleanupDatabase();
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

async function cleanupDatabase() {
  try {
    console.log(`\n=== STARTING DATABASE CLEANUP: ${dbName} ===`);
    
    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\nCollections in database:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    // Find users to keep
    const usersCollection = mongoose.connection.db.collection('users');
    const usersToKeep = await usersCollection.find({ username: { $in: USERNAMES_TO_KEEP } }).toArray();
    
    if (usersToKeep.length === 0) {
      console.error('No users found to keep. Aborting cleanup.');
      process.exit(1);
    }
    
    console.log(`\nFound ${usersToKeep.length} users to keep:`);
    usersToKeep.forEach(user => {
      console.log(`- ${user.username} (${user.email}): ${user.role}`);
    });
    
    // Get IDs of users to keep
    const userIdsToKeep = usersToKeep.map(user => user._id);
    
    // Delete all other users
    const deleteUsersResult = await usersCollection.deleteMany({
      _id: { $nin: userIdsToKeep }
    });
    console.log(`Deleted ${deleteUsersResult.deletedCount} users`);
    
    // Get HOA IDs associated with kept users
    const hoaIdsToKeep = usersToKeep
      .filter(user => user.hoaId)
      .map(user => {
        // Convert string IDs to ObjectId if needed
        if (typeof user.hoaId === 'string') {
          return new mongoose.Types.ObjectId(user.hoaId);
        }
        return user.hoaId;
      });
    
    // Delete all HOAs except those associated with kept users
    if (collections.some(c => c.name === 'hoas')) {
      const hoasCollection = mongoose.connection.db.collection('hoas');
      const deleteHOAsResult = await hoasCollection.deleteMany({
        _id: { $nin: hoaIdsToKeep }
      });
      console.log(`Deleted ${deleteHOAsResult.deletedCount} HOAs`);
    }
    
    // Delete all communities
    if (collections.some(c => c.name === 'communities')) {
      const communitiesCollection = mongoose.connection.db.collection('communities');
      const deleteCommunitiesResult = await communitiesCollection.deleteMany({});
      console.log(`Deleted ${deleteCommunitiesResult.deletedCount} communities`);
    }
    
    // Delete all properties
    if (collections.some(c => c.name === 'properties')) {
      const propertiesCollection = mongoose.connection.db.collection('properties');
      const deletePropertiesResult = await propertiesCollection.deleteMany({});
      console.log(`Deleted ${deletePropertiesResult.deletedCount} properties`);
    }
    
    // Delete all documents
    if (collections.some(c => c.name === 'documents')) {
      const documentsCollection = mongoose.connection.db.collection('documents');
      const deleteDocumentsResult = await documentsCollection.deleteMany({});
      console.log(`Deleted ${deleteDocumentsResult.deletedCount} documents`);
    }
    
    // Delete all tasks
    if (collections.some(c => c.name === 'tasks')) {
      const tasksCollection = mongoose.connection.db.collection('tasks');
      const deleteTasksResult = await tasksCollection.deleteMany({});
      console.log(`Deleted ${deleteTasksResult.deletedCount} tasks`);
    }
    
    // Delete all finances
    if (collections.some(c => c.name === 'finances')) {
      const financesCollection = mongoose.connection.db.collection('finances');
      const deleteFinancesResult = await financesCollection.deleteMany({});
      console.log(`Deleted ${deleteFinancesResult.deletedCount} finance records`);
    }
    
    // Delete all messages
    if (collections.some(c => c.name === 'messages')) {
      const messagesCollection = mongoose.connection.db.collection('messages');
      const deleteMessagesResult = await messagesCollection.deleteMany({});
      console.log(`Deleted ${deleteMessagesResult.deletedCount} messages`);
    }
    
    // Delete all conversations
    if (collections.some(c => c.name === 'conversations')) {
      const conversationsCollection = mongoose.connection.db.collection('conversations');
      const deleteConversationsResult = await conversationsCollection.deleteMany({});
      console.log(`Deleted ${deleteConversationsResult.deletedCount} conversations`);
    }
    
    // Delete all notifications
    if (collections.some(c => c.name === 'notifications')) {
      const notificationsCollection = mongoose.connection.db.collection('notifications');
      const deleteNotificationsResult = await notificationsCollection.deleteMany({});
      console.log(`Deleted ${deleteNotificationsResult.deletedCount} notifications`);
    }
    
    // Delete all subscriptions
    if (collections.some(c => c.name === 'subscriptions')) {
      const subscriptionsCollection = mongoose.connection.db.collection('subscriptions');
      const deleteSubscriptionsResult = await subscriptionsCollection.deleteMany({});
      console.log(`Deleted ${deleteSubscriptionsResult.deletedCount} subscriptions`);
    }
    
    // Delete all budgets
    if (collections.some(c => c.name === 'budgets')) {
      const budgetsCollection = mongoose.connection.db.collection('budgets');
      const deleteBudgetsResult = await budgetsCollection.deleteMany({});
      console.log(`Deleted ${deleteBudgetsResult.deletedCount} budgets`);
    }
    
    // Delete all maintenance requests
    if (collections.some(c => c.name === 'maintenancerequests')) {
      const maintenanceCollection = mongoose.connection.db.collection('maintenancerequests');
      const deleteMaintenanceResult = await maintenanceCollection.deleteMany({});
      console.log(`Deleted ${deleteMaintenanceResult.deletedCount} maintenance requests`);
    }
    
    console.log('\nDatabase cleanup completed successfully!');
    console.log(`The system has been reset with only the users ${USERNAMES_TO_KEEP.join(', ')} remaining.`);
    
    // Close the database connection
    mongoose.connection.close();
    
  } catch (error) {
    console.error('Error during database cleanup:', error);
    process.exit(1);
  }
}

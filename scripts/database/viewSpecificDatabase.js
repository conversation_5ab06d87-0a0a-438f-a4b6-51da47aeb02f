/**
 * <PERSON><PERSON><PERSON> to view a specific MongoDB database
 * 
 * This script:
 * 1. Connects to the specified MongoDB database
 * 2. Lists all collections
 * 3. Shows sample documents from key collections
 * 4. Displays user information
 * 
 * Usage:
 * - Run this script with: node scripts/viewSpecificDatabase.js [database-name]
 * - Example: node scripts/viewSpecificDatabase.js street-harmony
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Get database name from command line arguments
const dbName = process.argv[2] || 'street-harmony';
if (!dbName) {
  console.error('Please provide a database name as an argument');
  console.error('Example: node scripts/viewSpecificDatabase.js street-harmony');
  process.exit(1);
}

// Base MongoDB URI without database name
const BASE_MONGO_URI = 'mongodb+srv://frankiebruno:<EMAIL>/';
// Full MongoDB URI with database name
const MONGO_URI = `${BASE_MONGO_URI}${dbName}?retryWrites=true&w=majority&appName=HOA-Data`;

console.log(`Connecting to database: ${dbName}`);
console.log(`Using MongoDB URI: ${BASE_MONGO_URI}${dbName}?...`);

// Connect to MongoDB
mongoose.connect(MONGO_URI)
  .then(() => {
    console.log(`MongoDB connected successfully to ${dbName}`);
    viewDatabase();
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

async function viewDatabase() {
  try {
    console.log(`\n=== DATABASE OVERVIEW: ${dbName} ===`);
    
    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\nCollections in database:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    // Count documents in each collection
    console.log('\nDocument counts:');
    for (const collection of collections) {
      const count = await mongoose.connection.db.collection(collection.name).countDocuments();
      console.log(`- ${collection.name}: ${count} documents`);
    }
    
    // View users
    console.log('\n=== USERS ===');
    const users = await mongoose.connection.db.collection('users').find({}).toArray();
    console.log(`Total users: ${users.length}`);
    
    if (users.length > 0) {
      console.log('\nUser details:');
      users.forEach(user => {
        console.log(`- Username: ${user.username}`);
        console.log(`  Email: ${user.email}`);
        console.log(`  Role: ${user.role}`);
        console.log(`  Approved: ${user.isApproved}`);
        console.log(`  HOA ID: ${user.hoaId || 'None'}`);
        console.log(`  HOA Community Code: ${user.hoaCommunityCode || 'None'}`);
        console.log('  ---');
      });
    }
    
    // View HOAs
    if (collections.some(c => c.name === 'hoas')) {
      console.log('\n=== HOAs ===');
      const hoas = await mongoose.connection.db.collection('hoas').find({}).toArray();
      console.log(`Total HOAs: ${hoas.length}`);
      
      if (hoas.length > 0) {
        console.log('\nHOA details:');
        hoas.forEach(hoa => {
          console.log(`- Name: ${hoa.hoaCommunityName}`);
          console.log(`  Code: ${hoa.hoaCommunityCode}`);
          console.log(`  Verification Status: ${hoa.verificationStatus}`);
          console.log(`  Subscription Status: ${hoa.subscription?.status || 'None'}`);
          console.log(`  ID: ${hoa._id}`);
          console.log('  ---');
        });
      }
    }
    
    // View Communities
    if (collections.some(c => c.name === 'communities')) {
      console.log('\n=== Communities ===');
      const communities = await mongoose.connection.db.collection('communities').find({}).toArray();
      console.log(`Total Communities: ${communities.length}`);
      
      if (communities.length > 0) {
        console.log('\nCommunity details:');
        communities.forEach(community => {
          console.log(`- Name: ${community.name}`);
          console.log(`  Code: ${community.communityCode}`);
          console.log(`  HOA ID: ${community.hoaId}`);
          console.log(`  Status: ${community.status}`);
          console.log('  ---');
        });
      }
    }
    
    // Look for the specific user 'fbruno'
    console.log('\n=== SEARCHING FOR USER: fbruno ===');
    const fbrunoUser = await mongoose.connection.db.collection('users').findOne({ username: 'fbruno' });
    
    if (fbrunoUser) {
      console.log('Found user fbruno:');
      console.log(`- Username: ${fbrunoUser.username}`);
      console.log(`- Email: ${fbrunoUser.email}`);
      console.log(`- Role: ${fbrunoUser.role}`);
      console.log(`- Full Name: ${fbrunoUser.fullName}`);
      console.log(`- HOA ID: ${fbrunoUser.hoaId || 'None'}`);
      console.log(`- HOA Community Code: ${fbrunoUser.hoaCommunityCode || 'None'}`);
      
      // If fbruno has an HOA ID, look up that HOA
      if (fbrunoUser.hoaId) {
        const fbrunoHOA = await mongoose.connection.db.collection('hoas').findOne({ _id: fbrunoUser.hoaId });
        if (fbrunoHOA) {
          console.log('\nfbruno\'s HOA:');
          console.log(`- Name: ${fbrunoHOA.hoaCommunityName}`);
          console.log(`- Code: ${fbrunoHOA.hoaCommunityCode}`);
        }
      }
    } else {
      console.log('User fbruno not found in this database');
    }
    
    console.log('\nDatabase overview completed.');
    mongoose.connection.close();
    
  } catch (error) {
    console.error('Error viewing database:', error);
    process.exit(1);
  }
}

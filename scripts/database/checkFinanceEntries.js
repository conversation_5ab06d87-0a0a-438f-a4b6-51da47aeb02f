/**
 * <PERSON><PERSON><PERSON> to check all finance entries in the database
 * This will help identify if the $360 entry exists or was deleted
 */
require('dotenv').config({ path: '.env.development' });
const mongoose = require('mongoose');
const Finance = require('../models/finance');
const HOA = require('../models/hoa');

async function checkFinanceEntries() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to database');

    // Get all finance entries
    const allEntries = await Finance.find({})
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
      .populate('createdBy', 'username fullName')
      .sort({ createdAt: -1 });

    console.log(`\n=== TOTAL FINANCE ENTRIES: ${allEntries.length} ===\n`);

    if (allEntries.length === 0) {
      console.log('No finance entries found in the database.');
      return;
    }

    // Group by HOA
    const entriesByHOA = {};
    const entriesWithoutHOA = [];

    allEntries.forEach(entry => {
      if (entry.hoaId) {
        const hoaName = entry.hoaId.hoaCommunityName || 'Unknown HOA';
        if (!entriesByHOA[hoaName]) {
          entriesByHOA[hoaName] = [];
        }
        entriesByHOA[hoaName].push(entry);
      } else {
        entriesWithoutHOA.push(entry);
      }
    });

    // Display entries by HOA
    Object.keys(entriesByHOA).forEach(hoaName => {
      const entries = entriesByHOA[hoaName];
      console.log(`\n--- ${hoaName} (${entries.length} entries) ---`);

      entries.forEach(entry => {
        console.log(`  ${entry.type.toUpperCase()}: $${entry.amount} - ${entry.category}`);
        console.log(`    Created: ${entry.createdAt.toLocaleDateString()}`);
        console.log(`    By: ${entry.createdBy?.username || 'Unknown'}`);
        if (entry.note) console.log(`    Note: ${entry.note}`);
        console.log('');
      });
    });

    // Display entries without HOA
    if (entriesWithoutHOA.length > 0) {
      console.log(`\n--- ENTRIES WITHOUT HOA (${entriesWithoutHOA.length} entries) ---`);
      entriesWithoutHOA.forEach(entry => {
        console.log(`  ${entry.type.toUpperCase()}: $${entry.amount} - ${entry.category}`);
        console.log(`    Created: ${entry.createdAt.toLocaleDateString()}`);
        console.log(`    By: ${entry.createdBy?.username || 'Unknown'}`);
        if (entry.note) console.log(`    Note: ${entry.note}`);
        console.log('');
      });
    }

    // Look specifically for $360 entries
    const entries360 = allEntries.filter(entry => entry.amount === 360);
    if (entries360.length > 0) {
      console.log(`\n=== FOUND ${entries360.length} ENTRIES WITH $360 ===`);
      entries360.forEach(entry => {
        console.log(`  ${entry.type.toUpperCase()}: $${entry.amount} - ${entry.category}`);
        console.log(`    HOA: ${entry.hoaId?.hoaCommunityName || 'No HOA'}`);
        console.log(`    Created: ${entry.createdAt.toLocaleDateString()}`);
        console.log(`    By: ${entry.createdBy?.username || 'Unknown'}`);
        if (entry.note) console.log(`    Note: ${entry.note}`);
        console.log('');
      });
    } else {
      console.log('\n=== NO $360 ENTRIES FOUND ===');
    }

    // Summary by type
    const incomeEntries = allEntries.filter(e => e.type === 'income');
    const expenseEntries = allEntries.filter(e => e.type === 'expense');
    const totalIncome = incomeEntries.reduce((sum, e) => sum + e.amount, 0);
    const totalExpenses = expenseEntries.reduce((sum, e) => sum + e.amount, 0);

    console.log('\n=== SUMMARY ===');
    console.log(`Total Income Entries: ${incomeEntries.length} ($${totalIncome})`);
    console.log(`Total Expense Entries: ${expenseEntries.length} ($${totalExpenses})`);
    console.log(`Net Balance: $${totalIncome - totalExpenses}`);

  } catch (error) {
    console.error('Error checking finance entries:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkFinanceEntries();

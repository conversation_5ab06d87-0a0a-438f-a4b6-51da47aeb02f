/**
 * <PERSON><PERSON><PERSON> to check all data in the database that might contain $360
 * This includes tasks, payments, subscription payments, and any other collections
 */
require('dotenv').config({ path: '.env.development' });
const mongoose = require('mongoose');
const Finance = require('../models/finance');
const Task = require('../models/task');
const Payment = require('../models/payment');
const SubscriptionPayment = require('../models/subscriptionPayment');
const HOA = require('../models/hoa');

async function checkAllData() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to database');

    console.log('\n=== CHECKING ALL DATA FOR $360 AMOUNTS ===\n');

    // Check Finance entries
    const financeEntries = await Finance.find({});
    console.log(`Finance Entries: ${financeEntries.length} total`);
    const finance360 = financeEntries.filter(entry => entry.amount === 360);
    if (finance360.length > 0) {
      console.log(`  Found ${finance360.length} finance entries with $360`);
      finance360.forEach(entry => {
        console.log(`    - ${entry.type}: $${entry.amount} (${entry.category})`);
      });
    } else {
      console.log('  No $360 finance entries found');
    }

    // Check Tasks (might have budget amounts)
    const tasks = await Task.find({});
    console.log(`\nTasks: ${tasks.length} total`);
    const tasks360 = tasks.filter(task => 
      (task.budget && task.budget === 360) || 
      (task.estimatedCost && task.estimatedCost === 360) ||
      (task.actualCost && task.actualCost === 360)
    );
    if (tasks360.length > 0) {
      console.log(`  Found ${tasks360.length} tasks with $360`);
      tasks360.forEach(task => {
        console.log(`    - ${task.title}: Budget: $${task.budget || 0}, Estimated: $${task.estimatedCost || 0}, Actual: $${task.actualCost || 0}`);
      });
    } else {
      console.log('  No $360 tasks found');
    }

    // Check Payments
    const payments = await Payment.find({});
    console.log(`\nPayments: ${payments.length} total`);
    const payments360 = payments.filter(payment => payment.amount === 360);
    if (payments360.length > 0) {
      console.log(`  Found ${payments360.length} payments with $360`);
      payments360.forEach(payment => {
        console.log(`    - Payment: $${payment.amount} (${payment.paymentType})`);
      });
    } else {
      console.log('  No $360 payments found');
    }

    // Check Subscription Payments
    const subscriptionPayments = await SubscriptionPayment.find({});
    console.log(`\nSubscription Payments: ${subscriptionPayments.length} total`);
    const subPayments360 = subscriptionPayments.filter(payment => payment.amount === 360);
    if (subPayments360.length > 0) {
      console.log(`  Found ${subPayments360.length} subscription payments with $360`);
      subPayments360.forEach(payment => {
        console.log(`    - Subscription Payment: $${payment.amount}`);
      });
    } else {
      console.log('  No $360 subscription payments found');
    }

    // Check HOAs (might have subscription amounts)
    const hoas = await HOA.find({});
    console.log(`\nHOAs: ${hoas.length} total`);
    const hoas360 = hoas.filter(hoa => 
      (hoa.subscription && hoa.subscription.totalPrice === 360) ||
      (hoa.subscription && hoa.subscription.pricePerUnit === 360)
    );
    if (hoas360.length > 0) {
      console.log(`  Found ${hoas360.length} HOAs with $360 subscription amounts`);
      hoas360.forEach(hoa => {
        console.log(`    - ${hoa.hoaCommunityName}: Total: $${hoa.subscription?.totalPrice || 0}, Per Unit: $${hoa.subscription?.pricePerUnit || 0}`);
      });
    } else {
      console.log('  No $360 HOA subscription amounts found');
    }

    // Summary
    console.log('\n=== SUMMARY ===');
    const total360Items = finance360.length + tasks360.length + payments360.length + subPayments360.length + hoas360.length;
    if (total360Items === 0) {
      console.log('❌ NO $360 AMOUNTS FOUND IN ANY COLLECTION');
      console.log('The $360 you saw was likely test data that was deleted during database cleanup.');
    } else {
      console.log(`✅ Found ${total360Items} items with $360 amounts`);
    }

    // Show recent deletions or changes
    console.log('\n=== RECENT ACTIVITY ===');
    const recentFinance = await Finance.find({}).sort({ createdAt: -1 }).limit(5);
    const recentTasks = await Task.find({}).sort({ createdAt: -1 }).limit(5);
    
    console.log('Most recent finance entries:');
    if (recentFinance.length === 0) {
      console.log('  No finance entries exist');
    } else {
      recentFinance.forEach(entry => {
        console.log(`  - ${entry.type}: $${entry.amount} (${entry.createdAt.toLocaleDateString()})`);
      });
    }

    console.log('Most recent tasks:');
    if (recentTasks.length === 0) {
      console.log('  No tasks exist');
    } else {
      recentTasks.forEach(task => {
        console.log(`  - ${task.title} (${task.createdAt.toLocaleDateString()})`);
      });
    }

  } catch (error) {
    console.error('Error checking data:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkAllData();

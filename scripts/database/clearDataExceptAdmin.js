/**
 * <PERSON><PERSON><PERSON> to clear all data except for a specific company admin account
 *
 * This script:
 * 1. Keeps the specified company admin account (fbruno)
 * 2. Removes all other users
 * 3. Removes all HOAs, communities, properties, and related data
 * 4. Preserves the system configuration
 *
 * Usage:
 * - Run this script with: node scripts/clearDataExceptAdmin.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('../models/user');
const HOA = require('../models/hoa');
const Community = require('../models/community');
const Property = require('../models/property');
const Document = require('../models/document');
const Task = require('../models/task');
const Finance = require('../models/finance');
const Message = require('../models/message');
const Conversation = require('../models/conversation');
const Notification = require('../models/notification');
const Subscription = require('../models/subscription');
const Budget = require('../models/budget');

// Username of the admin to keep
const ADMIN_USERNAME = 'fbruno';

// Connect to MongoDB
const MONGO_URI = process.env.MONGO_URI || 'mongodb+srv://frankiebruno:<EMAIL>/street-harmony-dev?retryWrites=true&w=majority&appName=HOA-Data';
console.log(`Using MongoDB URI: ${MONGO_URI.substring(0, 20)}...`);

mongoose.connect(MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  console.log('MongoDB connected successfully');
  clearData();
})
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function clearData() {
  try {
    console.log('Starting data cleanup process...');

    // Find the admin user to keep
    let adminUser = await User.findOne({ username: ADMIN_USERNAME });

    if (!adminUser) {
      console.log(`Admin user '${ADMIN_USERNAME}' not found. Creating new company admin user...`);

      // Create a new company admin user
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('Archvile@1991!', 10);

      adminUser = new User({
        username: ADMIN_USERNAME,
        password: hashedPassword,
        email: '<EMAIL>',
        fullName: 'Frankie Bruno',
        propertyAddress: 'Company Headquarters',
        role: 'company_admin',
        isApproved: true,
        denied: false,
        verifiedAt: new Date()
      });

      await adminUser.save();
      console.log(`Created new company admin user: ${adminUser.username} (${adminUser.email})`);
    } else {
      console.log(`Found admin user: ${adminUser.username} (${adminUser.email})`);

      // Ensure the admin user has company_admin role
      if (adminUser.role !== 'company_admin') {
        console.log(`Updating ${adminUser.username} role to company_admin`);
        adminUser.role = 'company_admin';
        await adminUser.save();
      }
    }

    // Delete all users except the admin
    const deleteUsersResult = await User.deleteMany({
      _id: { $ne: adminUser._id }
    });
    console.log(`Deleted ${deleteUsersResult.deletedCount} users`);

    // Delete all HOAs
    const deleteHOAsResult = await HOA.deleteMany({});
    console.log(`Deleted ${deleteHOAsResult.deletedCount} HOAs`);

    // Delete all communities
    const deleteCommunitiesResult = await Community.deleteMany({});
    console.log(`Deleted ${deleteCommunitiesResult.deletedCount} communities`);

    // Delete all properties
    const deletePropertiesResult = await Property.deleteMany({});
    console.log(`Deleted ${deletePropertiesResult.deletedCount} properties`);

    // Delete all documents
    const deleteDocumentsResult = await Document.deleteMany({});
    console.log(`Deleted ${deleteDocumentsResult.deletedCount} documents`);

    // Delete all tasks
    const deleteTasksResult = await Task.deleteMany({});
    console.log(`Deleted ${deleteTasksResult.deletedCount} tasks`);

    // Delete all finances
    const deleteFinancesResult = await Finance.deleteMany({});
    console.log(`Deleted ${deleteFinancesResult.deletedCount} finance records`);

    // Delete all messages
    const deleteMessagesResult = await Message.deleteMany({});
    console.log(`Deleted ${deleteMessagesResult.deletedCount} messages`);

    // Delete all conversations
    const deleteConversationsResult = await Conversation.deleteMany({});
    console.log(`Deleted ${deleteConversationsResult.deletedCount} conversations`);

    // Delete all notifications
    const deleteNotificationsResult = await Notification.deleteMany({});
    console.log(`Deleted ${deleteNotificationsResult.deletedCount} notifications`);

    // Delete all subscriptions
    const deleteSubscriptionsResult = await Subscription.deleteMany({});
    console.log(`Deleted ${deleteSubscriptionsResult.deletedCount} subscriptions`);

    // Delete all budgets
    const deleteBudgetsResult = await Budget.deleteMany({});
    console.log(`Deleted ${deleteBudgetsResult.deletedCount} budgets`);

    console.log('\nData cleanup completed successfully!');
    console.log(`The system has been reset with only the admin user '${ADMIN_USERNAME}' remaining.`);

    // Close the database connection
    mongoose.connection.close();
    console.log('MongoDB connection closed');

  } catch (error) {
    console.error('Error during data cleanup:', error);
    process.exit(1);
  }
}

/**
 * <PERSON><PERSON><PERSON> to update a user directly in the database
 */
require('dotenv').config({ path: '.env.development' });
const mongoose = require('mongoose');

// Connect to MongoDB
const MONGO_URI = process.env.MONGO_URI;
console.log(`Using MongoDB URI: ${MONGO_URI ? MONGO_URI.substring(0, 20) + '...' : 'undefined'}`);

mongoose.connect(process.env.MONGO_URI)
  .then(async () => {
    console.log('MongoDB connected');
    
    try {
      // Get the database connection
      const db = mongoose.connection.db;
      
      // Get the user
      const userCollection = db.collection('users');
      const user = await userCollection.findOne({ username: 'testuser1' });
      
      if (!user) {
        console.log('User not found');
        return;
      }
      
      console.log('Found user:', {
        _id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        isApproved: user.isApproved,
        hoaCommunityCode: user.hoaCommunityCode
      });
      
      // Update the user
      const result = await userCollection.updateOne(
        { _id: user._id },
        {
          $set: {
            isApproved: true,
            role: 'admin',
            denied: false,
            verifiedAt: new Date()
          }
        }
      );
      
      console.log('Update result:', result);
      
      // Check if the update was successful
      if (result.modifiedCount > 0) {
        console.log('User updated successfully');
        
        // Get the updated user
        const updatedUser = await userCollection.findOne({ _id: user._id });
        console.log('Updated user:', {
          _id: updatedUser._id,
          username: updatedUser.username,
          email: updatedUser.email,
          role: updatedUser.role,
          isApproved: updatedUser.isApproved,
          hoaCommunityCode: updatedUser.hoaCommunityCode
        });
      } else {
        console.log('User not updated');
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      // Close MongoDB connection
      mongoose.connection.close();
      console.log('MongoDB connection closed');
    }
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

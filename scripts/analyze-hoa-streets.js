/**
 * <PERSON>ript to Analyze HOA Streets and Communities
 * 
 * This script checks which HOA admin has the most streets/communities
 * for testing the announcement targeting features.
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env' });

console.log(`Analyzing HOA streets and communities`);

// Connect to MongoDB
const connectDB = async () => {
  try {
    // Connect to the main street-harmony database
    const baseUri = 'mongodb+srv://frankiebruno:<EMAIL>/street-harmony?retryWrites=true&w=majority&appName=HOA-Data';
    
    await mongoose.connect(baseUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected to street-harmony database');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

// Define schemas
const userSchema = new mongoose.Schema({}, { strict: false });
const hoaSchema = new mongoose.Schema({}, { strict: false });
const communitySchema = new mongoose.Schema({}, { strict: false });

const User = mongoose.model('User', userSchema);
const HOA = mongoose.model('HOA', hoaSchema);
const Community = mongoose.model('Community', communitySchema);

// Analyze HOA streets and communities
const analyzeHOAStreets = async () => {
  try {
    console.log('\n🔍 ANALYZING HOA STREETS AND COMMUNITIES...\n');

    // Get all HOAs
    const hoas = await HOA.find({});
    console.log(`Found ${hoas.length} HOAs in database`);

    // Get all communities/streets
    const communities = await Community.find({});
    console.log(`Found ${communities.length} communities/streets in database`);

    // Get all admin users
    const adminUsers = await User.find({ role: 'admin' });
    console.log(`Found ${adminUsers.length} admin users\n`);

    // Analyze each HOA and its streets
    for (const hoa of hoas) {
      console.log(`🏘️ HOA: ${hoa.hoaCommunityName || 'Unnamed HOA'}`);
      console.log(`   - ID: ${hoa._id}`);
      console.log(`   - Code: ${hoa.hoaCommunityCode}`);
      console.log(`   - Location: ${hoa.hoaCity || 'Unknown'}, ${hoa.hoaState || 'Unknown'}`);

      // Find communities/streets for this HOA
      const hoaStreets = communities.filter(community => 
        community.hoaCommunityCode === hoa.hoaCommunityCode
      );

      console.log(`   - Streets/Communities: ${hoaStreets.length}`);
      
      if (hoaStreets.length > 0) {
        hoaStreets.forEach((street, index) => {
          console.log(`     ${index + 1}. ${street.name} (${street.code})`);
          if (street.description) {
            console.log(`        Description: ${street.description}`);
          }
        });
      } else {
        console.log(`     ❌ No streets found for this HOA`);
      }

      // Find admin users for this HOA
      const hoaAdmins = adminUsers.filter(user => 
        user.hoaCommunityCode === hoa.hoaCommunityCode
      );

      console.log(`   - Admin Users: ${hoaAdmins.length}`);
      hoaAdmins.forEach((admin, index) => {
        console.log(`     ${index + 1}. ${admin.email} (${admin.username || 'No username'})`);
      });

      console.log(''); // Empty line for readability
    }

    // Find the HOA with the most streets
    let maxStreets = 0;
    let bestHOA = null;
    let bestAdmins = [];

    for (const hoa of hoas) {
      const hoaStreets = communities.filter(community => 
        community.hoaCommunityCode === hoa.hoaCommunityCode
      );
      
      if (hoaStreets.length > maxStreets) {
        maxStreets = hoaStreets.length;
        bestHOA = hoa;
        bestAdmins = adminUsers.filter(user => 
          user.hoaCommunityCode === hoa.hoaCommunityCode
        );
      }
    }

    console.log('🎯 RECOMMENDATION FOR TESTING:\n');
    
    if (bestHOA && maxStreets > 0) {
      console.log(`✅ Best HOA for testing: ${bestHOA.hoaCommunityName || 'Unnamed HOA'}`);
      console.log(`   - HOA Code: ${bestHOA.hoaCommunityCode}`);
      console.log(`   - Number of Streets: ${maxStreets}`);
      console.log(`   - Admin Users Available:`);
      
      bestAdmins.forEach((admin, index) => {
        console.log(`     ${index + 1}. ${admin.email}`);
        console.log(`        - Username: ${admin.username || 'No username'}`);
        console.log(`        - Full Name: ${admin.fullName || 'No full name'}`);
        console.log(`        - ID: ${admin._id}`);
      });

      console.log(`\n📋 STREETS IN THIS HOA:`);
      const bestHOAStreets = communities.filter(community => 
        community.hoaCommunityCode === bestHOA.hoaCommunityCode
      );
      
      bestHOAStreets.forEach((street, index) => {
        console.log(`   ${index + 1}. ${street.name} (Code: ${street.code})`);
        if (street.description) {
          console.log(`      Description: ${street.description}`);
        }
      });

      console.log(`\n🧪 TESTING INSTRUCTIONS:`);
      console.log(`1. Login with any of the admin users listed above`);
      console.log(`2. Navigate to announcements section`);
      console.log(`3. Create new announcement`);
      console.log(`4. You should see ${maxStreets} streets in the dropdown`);
      console.log(`5. Test selecting different streets and residents`);

    } else {
      console.log(`❌ No HOA found with streets. You may need to create street/community data.`);
    }

    // Also check for users with residents
    console.log(`\n👥 CHECKING FOR RESIDENTS IN COMMUNITIES...\n`);
    
    for (const community of communities) {
      const residents = await User.find({ 
        communityId: community._id,
        role: { $in: ['member', 'user'] }
      });
      
      if (residents.length > 0) {
        console.log(`🏠 ${community.name} (${community.code}):`);
        console.log(`   - Residents: ${residents.length}`);
        residents.forEach((resident, index) => {
          console.log(`     ${index + 1}. ${resident.email} (${resident.fullName || 'No name'})`);
        });
        console.log('');
      }
    }

  } catch (error) {
    console.error('❌ Error analyzing HOA streets:', error);
    throw error;
  }
};

// Main execution
const runAnalysis = async () => {
  try {
    await connectDB();
    await analyzeHOAStreets();
    
    console.log('\n✅ Analysis completed!');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
    process.exit(0);
  }
};

// Run the analysis
if (require.main === module) {
  runAnalysis();
}

module.exports = { analyzeHOAStreets };

/**
 * <PERSON><PERSON>t to Find Admin Users
 * 
 * This script finds all admin users in the database to identify the correct user ID
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env' });

console.log(`Finding admin users in street-harmony database`);

// Connect to MongoDB
const connectDB = async () => {
  try {
    // Connect to the main street-harmony database
    const baseUri = 'mongodb+srv://frankiebruno:<EMAIL>/street-harmony?retryWrites=true&w=majority&appName=HOA-Data';

    await mongoose.connect(baseUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected to street-harmony database');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

// Define schemas
const userSchema = new mongoose.Schema({}, { strict: false });
const User = mongoose.model('User', userSchema);

// Find admin users
const findAdminUsers = async () => {
  try {
    console.log('\n🔍 SEARCHING FOR ALL USERS...\n');

    // Find all users first
    const allUsers = await User.find({});
    console.log(`Total users in database: ${allUsers.length}`);

    // Find all admin users
    const adminUsers = await User.find({ role: 'admin' });

    console.log(`Found ${adminUsers.length} admin users:`);

    adminUsers.forEach((user, index) => {
      console.log(`\n${index + 1}. ADMIN USER:`);
      console.log(`   - ID: ${user._id}`);
      console.log(`   - Email: ${user.email}`);
      console.log(`   - Username: ${user.username || 'No username'}`);
      console.log(`   - Full Name: ${user.fullName || 'No full name'}`);
      console.log(`   - Role: ${user.role}`);
      console.log(`   - Approved: ${user.isApproved}`);
      console.log(`   - HOA ID: ${user.hoaId || 'NOT SET'}`);
      console.log(`   - Community ID: ${user.communityId || 'NOT SET'}`);
      console.log(`   - HOA Community Code: ${user.hoaCommunityCode || 'NOT SET'}`);
    });

    // Show all users with their roles
    console.log(`\n📋 ALL USERS BY ROLE:`);
    const usersByRole = {};
    allUsers.forEach(user => {
      const role = user.role || 'no_role';
      if (!usersByRole[role]) usersByRole[role] = [];
      usersByRole[role].push(user);
    });

    Object.keys(usersByRole).forEach(role => {
      console.log(`\n${role.toUpperCase()} (${usersByRole[role].length}):`);
      usersByRole[role].forEach(user => {
        console.log(`   - ${user.email} (ID: ${user._id})`);
      });
    });

    // Also find company admin users
    const companyAdmins = await User.find({ role: 'company_admin' });
    
    if (companyAdmins.length > 0) {
      console.log(`\n\nFound ${companyAdmins.length} company admin users:`);
      
      companyAdmins.forEach((user, index) => {
        console.log(`\n${index + 1}. COMPANY ADMIN USER:`);
        console.log(`   - ID: ${user._id}`);
        console.log(`   - Email: ${user.email}`);
        console.log(`   - Username: ${user.username || 'No username'}`);
        console.log(`   - Full Name: ${user.fullName || 'No full name'}`);
        console.log(`   - Role: ${user.role}`);
        console.log(`   - Approved: ${user.isApproved}`);
      });
    }

    // Find the specific user that was mentioned
    const specificUser = await User.findOne({ email: '<EMAIL>' });
    if (specificUser) {
      console.log(`\n\n🎯 FOUND SPECIFIC USER (<EMAIL>):`);
      console.log(`   - ID: ${specificUser._id}`);
      console.log(`   - Email: ${specificUser.email}`);
      console.log(`   - Username: ${specificUser.username || 'No username'}`);
      console.log(`   - Role: ${specificUser.role}`);
      console.log(`   - HOA Association: ${specificUser.hoaCommunityCode || 'NONE'}`);
    }

    return adminUsers;

  } catch (error) {
    console.error('❌ Error finding admin users:', error);
    throw error;
  }
};

// Main execution
const runSearch = async () => {
  try {
    await connectDB();
    await findAdminUsers();
    
    console.log('\n✅ Search completed!');
    
  } catch (error) {
    console.error('❌ Search failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
    process.exit(0);
  }
};

// Run the search
if (require.main === module) {
  runSearch();
}

module.exports = { findAdminUsers };

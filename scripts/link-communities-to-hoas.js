/**
 * <PERSON>ript to Link Communities to HOAs
 * 
 * This script fixes the missing links between communities/streets and HOAs
 * so that HOA admins can see streets in the announcement targeting dropdown.
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env' });

console.log(`Linking communities to HOAs`);

// Connect to MongoDB
const connectDB = async () => {
  try {
    const baseUri = 'mongodb+srv://frankiebruno:<EMAIL>/street-harmony?retryWrites=true&w=majority&appName=HOA-Data';
    
    await mongoose.connect(baseUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected to street-harmony database');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

// Define schemas
const userSchema = new mongoose.Schema({}, { strict: false });
const hoaSchema = new mongoose.Schema({}, { strict: false });
const communitySchema = new mongoose.Schema({}, { strict: false });

const User = mongoose.model('User', userSchema);
const HOA = mongoose.model('HOA', hoaSchema);
const Community = mongoose.model('Community', communitySchema);

// Link communities to HOAs
const linkCommunitiesToHOAs = async () => {
  try {
    console.log('\n🔗 LINKING COMMUNITIES TO HOAs...\n');

    // Get all HOAs
    const hoas = await HOA.find({});
    console.log(`Found ${hoas.length} HOAs`);

    // Get all communities
    const communities = await Community.find({});
    console.log(`Found ${communities.length} communities`);

    // Show current state
    console.log('\n📋 CURRENT STATE:');
    hoas.forEach(hoa => {
      console.log(`HOA: ${hoa.hoaCommunityName} (${hoa.hoaCommunityCode})`);
      const linkedCommunities = communities.filter(c => c.hoaCommunityCode === hoa.hoaCommunityCode);
      console.log(`   Linked communities: ${linkedCommunities.length}`);
    });

    // Find communities without HOA links
    const unlinkedCommunities = communities.filter(c => !c.hoaCommunityCode);
    console.log(`\n🔍 Found ${unlinkedCommunities.length} unlinked communities`);

    if (unlinkedCommunities.length === 0) {
      console.log('✅ All communities are already linked to HOAs');
      return;
    }

    // Strategy: Distribute communities evenly among HOAs, with preference for HOAs that have admin users
    const hoasWithAdmins = [];
    for (const hoa of hoas) {
      const adminCount = await User.countDocuments({ 
        hoaCommunityCode: hoa.hoaCommunityCode, 
        role: 'admin' 
      });
      hoasWithAdmins.push({ hoa, adminCount });
    }

    // Sort HOAs by admin count (descending) so HOAs with more admins get more communities
    hoasWithAdmins.sort((a, b) => b.adminCount - a.adminCount);

    console.log('\n🎯 HOA PRIORITY (by admin count):');
    hoasWithAdmins.forEach((item, index) => {
      console.log(`${index + 1}. ${item.hoa.hoaCommunityName} - ${item.adminCount} admins`);
    });

    // Distribute communities
    let communityIndex = 0;
    const updates = [];

    for (const community of unlinkedCommunities) {
      // Cycle through HOAs, giving preference to those with more admins
      const targetHOA = hoasWithAdmins[communityIndex % hoasWithAdmins.length].hoa;
      
      updates.push({
        community: community,
        targetHOA: targetHOA,
        update: {
          hoaCommunityCode: targetHOA.hoaCommunityCode
        }
      });

      communityIndex++;
    }

    // Show planned updates
    console.log('\n📝 PLANNED UPDATES:');
    const updatesByHOA = {};
    updates.forEach(update => {
      const hoaName = update.targetHOA.hoaCommunityName;
      if (!updatesByHOA[hoaName]) updatesByHOA[hoaName] = [];
      updatesByHOA[hoaName].push(update.community.name);
    });

    Object.keys(updatesByHOA).forEach(hoaName => {
      console.log(`${hoaName}:`);
      updatesByHOA[hoaName].forEach(communityName => {
        console.log(`   + ${communityName}`);
      });
    });

    // Ask for confirmation (in a real scenario)
    console.log('\n🚀 APPLYING UPDATES...');

    // Apply updates
    let successCount = 0;
    for (const update of updates) {
      try {
        await Community.findByIdAndUpdate(
          update.community._id,
          { $set: update.update },
          { new: true }
        );
        console.log(`✅ Linked ${update.community.name} to ${update.targetHOA.hoaCommunityName}`);
        successCount++;
      } catch (error) {
        console.error(`❌ Failed to link ${update.community.name}:`, error.message);
      }
    }

    console.log(`\n🎉 LINKING COMPLETED!`);
    console.log(`✅ Successfully linked ${successCount} communities`);
    console.log(`❌ Failed to link ${updates.length - successCount} communities`);

    // Show final state
    console.log('\n📊 FINAL STATE:');
    for (const hoa of hoas) {
      const linkedCommunities = await Community.find({ hoaCommunityCode: hoa.hoaCommunityCode });
      const adminUsers = await User.find({ hoaCommunityCode: hoa.hoaCommunityCode, role: 'admin' });
      
      console.log(`\n🏘️ ${hoa.hoaCommunityName} (${hoa.hoaCommunityCode}):`);
      console.log(`   - Streets/Communities: ${linkedCommunities.length}`);
      console.log(`   - Admin Users: ${adminUsers.length}`);
      
      if (adminUsers.length > 0) {
        console.log(`   - Test with these admins:`);
        adminUsers.forEach(admin => {
          console.log(`     • ${admin.email}`);
        });
      }
      
      if (linkedCommunities.length > 0) {
        console.log(`   - Available streets:`);
        linkedCommunities.slice(0, 5).forEach(community => {
          console.log(`     • ${community.name} (${community.code || 'No code'})`);
        });
        if (linkedCommunities.length > 5) {
          console.log(`     ... and ${linkedCommunities.length - 5} more`);
        }
      }
    }

    // Find the best HOA for testing
    let bestHOA = null;
    let maxScore = 0;

    for (const hoa of hoas) {
      const streetCount = await Community.countDocuments({ hoaCommunityCode: hoa.hoaCommunityCode });
      const adminCount = await User.countDocuments({ hoaCommunityCode: hoa.hoaCommunityCode, role: 'admin' });
      const score = streetCount * adminCount; // Score based on streets × admins

      if (score > maxScore) {
        maxScore = score;
        bestHOA = hoa;
      }
    }

    if (bestHOA) {
      const bestStreets = await Community.find({ hoaCommunityCode: bestHOA.hoaCommunityCode });
      const bestAdmins = await User.find({ hoaCommunityCode: bestHOA.hoaCommunityCode, role: 'admin' });

      console.log(`\n🎯 BEST HOA FOR TESTING ANNOUNCEMENTS:`);
      console.log(`HOA: ${bestHOA.hoaCommunityName}`);
      console.log(`Code: ${bestHOA.hoaCommunityCode}`);
      console.log(`Streets: ${bestStreets.length}`);
      console.log(`Admins: ${bestAdmins.length}`);
      console.log(`\n🧪 RECOMMENDED TEST USER:`);
      if (bestAdmins.length > 0) {
        console.log(`Login with: ${bestAdmins[0].email}`);
        console.log(`You should see ${bestStreets.length} streets in the announcement dropdown!`);
      }
    }

  } catch (error) {
    console.error('❌ Error linking communities to HOAs:', error);
    throw error;
  }
};

// Main execution
const runLinking = async () => {
  try {
    await connectDB();
    await linkCommunitiesToHOAs();
    
    console.log('\n✅ Linking completed successfully!');
    
  } catch (error) {
    console.error('❌ Linking failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
    process.exit(0);
  }
};

// Run the linking
if (require.main === module) {
  runLinking();
}

module.exports = { linkCommunitiesToHOAs };

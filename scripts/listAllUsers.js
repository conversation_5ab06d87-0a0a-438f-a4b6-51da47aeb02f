const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.development' });

// Models
const User = require('../models/user');

async function listAllUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB Connected to:', process.env.MONGO_URI);

    // Find all users
    const users = await User.find({}).select('username email role hoaId hoaCommunityCode isApproved');

    console.log('Total users found:', users.length);

    // Check for specific users
    const fbrunoUser = users.find(user => user.username === 'fbruno');
    const vuDoanUser = users.find(user => user.username === 'VuDoan3112');

    console.log('\n--- Important Users Check ---');
    if (fbrunoUser) {
      console.log('✅ fbruno exists with role:', fbrunoUser.role);
    } else {
      console.log('❌ fbruno does not exist');
    }

    if (vuDoanUser) {
      console.log('✅ VuDoan3112 exists with role:', vuDoanUser.role);
    } else {
      console.log('❌ VuDoan3112 does not exist');
    }

    // Check for regular member users
    const memberUsers = users.filter(user => user.role === 'member');
    if (memberUsers.length > 0) {
      console.log(`✅ Found ${memberUsers.length} member users`);
    } else {
      console.log('❌ No member users found');
    }

    console.log('\n--- All Users ---');
    // Display all users
    users.forEach((user, index) => {
      console.log(`\nUser ${index + 1}:`);
      console.log('Username:', user.username);
      console.log('Email:', user.email);
      console.log('Role:', user.role);
      console.log('HOA ID:', user.hoaId);
      console.log('HOA Community Code:', user.hoaCommunityCode);
      console.log('Is Approved:', user.isApproved);
    });

  } catch (err) {
    console.error('Error:', err.message);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('\nMongoDB Disconnected');
  }
}

// Run the function
listAllUsers();

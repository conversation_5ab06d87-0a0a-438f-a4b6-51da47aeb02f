const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.development' });

// Models
const HOA = require('../models/hoa');

async function listAllHoas() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB Connected to:', process.env.MONGO_URI);

    // Find all HOAs
    const hoas = await HOA.find({}).select('hoaCommunityName hoaCommunityCode verificationStatus');
    
    console.log('Total HOAs found:', hoas.length);
    
    // Display all HOAs
    hoas.forEach((hoa, index) => {
      console.log(`\nHOA ${index + 1}:`);
      console.log('Name:', hoa.hoaCommunityName);
      console.log('Code:', hoa.hoaCommunityCode);
      console.log('Status:', hoa.verificationStatus);
      console.log('ID:', hoa._id);
    });

  } catch (err) {
    console.error('Error:', err.message);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('\nMongoDB Disconnected');
  }
}

// Run the function
listAllHoas();

/**
 * <PERSON><PERSON><PERSON> to update all users with HOA codes to have the 'admin' role
 * 
 * This script:
 * 1. Finds all users who have an HOA code but don't have the 'admin' role
 * 2. Updates their role to 'admin'
 * 3. Logs the changes made
 * 
 * Usage:
 * - Run this script on your production server with:
 *   node scripts/update_hoa_users_to_admin.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import the User model
const User = require('../models/user');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  console.log('MongoDB connected successfully');
  updateUsers();
})
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function updateUsers() {
  try {
    console.log('Starting user role update process...');
    
    // Find all users who have an HOA code but don't have the 'admin' role
    const users = await User.find({
      hoaCommunityCode: { $exists: true, $ne: null, $ne: '' },
      role: { $ne: 'admin' },
      isApproved: true
    });
    
    console.log(`Found ${users.length} users with HOA codes who are not admins`);
    
    if (users.length === 0) {
      console.log('No users need to be updated. Exiting.');
      process.exit(0);
    }
    
    // Log the users that will be updated
    console.log('\nUsers to be updated:');
    users.forEach(user => {
      console.log(`- ${user.username} (${user.email}): Current role: ${user.role}, HOA Code: ${user.hoaCommunityCode}`);
    });
    
    // Update each user
    console.log('\nUpdating users...');
    let updatedCount = 0;
    
    for (const user of users) {
      console.log(`Updating ${user.username} (${user.email}) from '${user.role}' to 'admin'`);
      
      user.role = 'admin';
      await user.save();
      
      updatedCount++;
      console.log(`✓ Updated ${user.username}`);
    }
    
    console.log(`\nUpdate complete. ${updatedCount} users updated to 'admin' role.`);
    
    // Verify the updates
    const verifyUsers = await User.find({
      hoaCommunityCode: { $exists: true, $ne: null, $ne: '' },
      role: 'admin'
    });
    
    console.log(`\nVerification: ${verifyUsers.length} users now have HOA codes and 'admin' role.`);
    
    mongoose.connection.close();
    console.log('MongoDB connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Error updating users:', error);
    mongoose.connection.close();
    process.exit(1);
  }
}

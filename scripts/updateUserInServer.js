/**
 * <PERSON><PERSON>t to update a user directly in the server's database
 */
const mongoose = require('mongoose');
const User = require('../models/user');

// Connect to MongoDB
mongoose.connect('mongodb+srv://frankiecyber:<EMAIL>/hoa-management-app?retryWrites=true&w=majority')
  .then(async () => {
    console.log('MongoDB connected');
    
    try {
      // Get the user
      const user = await User.findById('682ccedc23986dd82de3bc43');
      
      if (!user) {
        console.log('User not found');
        return;
      }
      
      console.log('Found user:', {
        _id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        isApproved: user.isApproved,
        hoaCommunityCode: user.hoaCommunityCode
      });
      
      // Update the user
      user.isApproved = true;
      user.role = 'admin';
      user.denied = false;
      user.verifiedAt = new Date();
      
      await user.save();
      
      console.log('User updated successfully');
      
      // Get the updated user
      const updatedUser = await User.findById('682ccedc23986dd82de3bc43');
      console.log('Updated user:', {
        _id: updatedUser._id,
        username: updatedUser.username,
        email: updatedUser.email,
        role: updatedUser.role,
        isApproved: updatedUser.isApproved,
        hoaCommunityCode: updatedUser.hoaCommunityCode
      });
    } catch (error) {
      console.error('Error:', error);
    } finally {
      // Close MongoDB connection
      mongoose.connection.close();
      console.log('MongoDB connection closed');
    }
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

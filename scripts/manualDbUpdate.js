/**
 * <PERSON><PERSON><PERSON> to manually update the database
 */
require('dotenv').config({ path: '.env.development' });
const mongoose = require('mongoose');

// Connect to MongoDB
const MONGO_URI = process.env.MONGO_URI;
console.log(`Using MongoDB URI: ${MONGO_URI ? MONGO_URI.substring(0, 20) + '...' : 'undefined'}`);
console.log('Full MongoDB URI:', MONGO_URI);

mongoose.connect(MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  console.log('MongoDB connected for testing');
  // Run the script after connection is established
  updateDatabase();
})
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function updateDatabase() {
  try {
    // Get the database connection
    const db = mongoose.connection.db;

    // List all collections
    const collections = await db.listCollections().toArray();
    console.log('Collections:', collections.map(c => c.name));

    // Check if the HOA collection exists
    if (collections.some(c => c.name === 'hoas')) {
      // Get all HOAs
      const hoas = await db.collection('hoas').find().toArray();
      console.log('HOAs:', hoas.map(h => ({
        _id: h._id,
        name: h.hoaCommunityName,
        code: h.hoaCommunityCode,
        status: h.verificationStatus
      })));

      // Update the HOA with ID 682cca7852353ebedd791076
      const hoaId = '682cca7852353ebedd791076';

      // Try to find the HOA by ID
      const hoaObjectId = new mongoose.Types.ObjectId(hoaId);
      const hoa = await db.collection('hoas').findOne({ _id: hoaObjectId });

      if (hoa) {
        console.log('Found HOA:', {
          _id: hoa._id,
          name: hoa.hoaCommunityName,
          code: hoa.hoaCommunityCode,
          status: hoa.verificationStatus
        });

        // Update the HOA
        const result = await db.collection('hoas').updateOne(
          { _id: hoaObjectId },
          {
            $set: {
              verificationStatus: 'approved',
              verificationNotes: 'Approved manually',
              verifiedAt: new Date(),
              'subscription.status': 'active'
            }
          }
        );

        console.log('Update result:', result);

        // Check if the update was successful
        if (result.modifiedCount > 0) {
          console.log('HOA updated successfully');

          // Get the updated HOA
          const updatedHoa = await db.collection('hoas').findOne({ _id: hoaObjectId });
          console.log('Updated HOA:', {
            _id: updatedHoa._id,
            name: updatedHoa.hoaCommunityName,
            code: updatedHoa.hoaCommunityCode,
            status: updatedHoa.verificationStatus,
            subscription: updatedHoa.subscription
          });
        } else {
          console.log('HOA not updated');
        }
      } else {
        console.log('HOA not found with ID:', hoaId);

        // Try to find the HOA by name
        const hoaByName = await db.collection('hoas').findOne({ hoaCommunityName: 'Test HOA 4' });

        if (hoaByName) {
          console.log('Found HOA by name:', {
            _id: hoaByName._id,
            name: hoaByName.hoaCommunityName,
            code: hoaByName.hoaCommunityCode,
            status: hoaByName.verificationStatus
          });

          // Update the HOA
          const result = await db.collection('hoas').updateOne(
            { _id: hoaByName._id },
            {
              $set: {
                verificationStatus: 'approved',
                verificationNotes: 'Approved manually',
                verifiedAt: new Date(),
                'subscription.status': 'active'
              }
            }
          );

          console.log('Update result:', result);

          // Check if the update was successful
          if (result.modifiedCount > 0) {
            console.log('HOA updated successfully');

            // Get the updated HOA
            const updatedHoa = await db.collection('hoas').findOne({ _id: hoaByName._id });
            console.log('Updated HOA:', {
              _id: updatedHoa._id,
              name: updatedHoa.hoaCommunityName,
              code: updatedHoa.hoaCommunityCode,
              status: updatedHoa.verificationStatus,
              subscription: updatedHoa.subscription
            });
          } else {
            console.log('HOA not updated');
          }
        } else {
          console.log('HOA not found by name: Test HOA 4');
        }
      }
    } else {
      console.log('HOA collection does not exist');
    }
  } catch (error) {
    console.error('Error updating database:', error);
  } finally {
    // Close MongoDB connection
    mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

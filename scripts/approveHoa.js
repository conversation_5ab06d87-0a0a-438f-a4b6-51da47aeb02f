/**
 * <PERSON><PERSON>t to approve an HOA
 */
require('dotenv').config({ path: '.env.development' });
const mongoose = require('mongoose');
const HOA = require('../models/hoa');

// Connect to MongoDB
const MONGO_URI = process.env.MONGO_URI;
console.log(`Using MongoDB URI: ${MONGO_URI ? MONGO_URI.substring(0, 20) + '...' : 'undefined'}`);

mongoose.connect(MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected for testing'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function approveHoa() {
  try {
    // Find the HOA
    const hoaId = '682cca7852353ebedd791076';
    console.log('Looking for HOA with ID:', hoaId);

    const hoa = await HOA.findById(hoaId);

    if (!hoa) {
      console.log('HOA not found');
      return;
    }

    console.log('Found HOA:', {
      _id: hoa._id,
      name: hoa.hoaCommunityName,
      code: hoa.hoaCommunityCode,
      status: hoa.verificationStatus
    });

    // Update the HOA
    hoa.verificationStatus = 'approved';
    hoa.verificationNotes = 'Approved manually';
    hoa.verifiedAt = new Date();
    hoa.subscription.status = 'active';

    await hoa.save();

    console.log('HOA approved successfully');

    // Find the updated HOA
    const updatedHoa = await HOA.findById(hoaId);

    console.log('Updated HOA:', {
      _id: updatedHoa._id,
      name: updatedHoa.hoaCommunityName,
      code: updatedHoa.hoaCommunityCode,
      status: updatedHoa.verificationStatus,
      subscription: updatedHoa.subscription
    });
  } catch (error) {
    console.error('Error approving HOA:', error);
  } finally {
    // Close MongoDB connection
    mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

// Run the script
approveHoa();

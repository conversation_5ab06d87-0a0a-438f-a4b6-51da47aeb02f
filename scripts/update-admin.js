/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// Script to update admin user information
require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/user');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected successfully'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function updateAdminUser() {
  try {
    // Find the admin user by username
    const adminUser = await User.findOne({ username: 'fbruno' });

    if (!adminUser) {
      console.error('Admin user fbruno not found');
      process.exit(1);
    }

    console.log('Found admin user:', adminUser.username);

    // Update the user with the missing information
    adminUser.fullName = 'Frankie Bruno';
    adminUser.propertyAddress = '117 deraney ln, Lafayette LA';

    // Save the updated user
    await adminUser.save();

    console.log('Admin user updated successfully');
    console.log('Updated information:');
    console.log('- Full Name:', adminUser.fullName);
    console.log('- Property Address:', adminUser.propertyAddress);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('MongoDB disconnected');

  } catch (error) {
    console.error('Error updating admin user:', error);
    process.exit(1);
  }
}

// Run the update function
updateAdminUser();

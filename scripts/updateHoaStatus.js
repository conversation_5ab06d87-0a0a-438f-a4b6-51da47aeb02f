const mongoose = require('mongoose');
require('dotenv').config();

// MongoDB URI for street-harmony
const MONGO_URI = 'mongodb+srv://frankiebruno:<EMAIL>/street-harmony?retryWrites=true&w=majority&appName=HOA-Data';

async function updateHoaStatus() {
  let connection = null;
  
  try {
    // Connect to database
    connection = await mongoose.createConnection(MONGO_URI);
    console.log('Connected to street-harmony database');
    
    // Update all HOAs to have verificationStatus = "approved" and subscription status = "active"
    const result = await connection.collection('hoas').updateMany(
      {}, // Update all HOAs
      { 
        $set: { 
          verificationStatus: "approved",
          "subscription.status": "active",
          "subscription.plan": "basic",
          "subscription.startDate": new Date(),
          "subscription.endDate": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          "subscription.stripeSubscriptionId": "sub_test_" + Math.random().toString(36).substring(2, 15)
        }
      }
    );
    
    console.log(`Updated ${result.modifiedCount} HOAs`);
    
    // Verify the update
    const hoas = await connection.collection('hoas').find({}).toArray();
    
    console.log('\nUpdated HOA details:');
    hoas.forEach((hoa, index) => {
      console.log(`\nHOA ${index + 1}:`);
      console.log('Name:', hoa.hoaCommunityName);
      console.log('Code:', hoa.hoaCommunityCode);
      console.log('Verification Status:', hoa.verificationStatus);
      console.log('Subscription Status:', hoa.subscription?.status);
      console.log('Subscription Plan:', hoa.subscription?.plan);
    });
    
  } catch (err) {
    console.error('Error:', err.message);
  } finally {
    // Close connection
    if (connection) await connection.close();
    console.log('\nDatabase connection closed');
  }
}

// Run the function
updateHoaStatus();

/**
 * <PERSON><PERSON><PERSON> to update a specific user to have the 'admin' role
 * 
 * This script:
 * 1. Finds the user with the specified email
 * 2. Updates their role to 'admin'
 * 3. Logs the changes made
 * 
 * Usage:
 * - Run this script on your production server with:
 *   node scripts/update_specific_user.js <EMAIL>
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import the User model
const User = require('../models/user');

// Get the email from command line arguments
const email = process.argv[2];

if (!email) {
  console.error('Please provide an email address');
  console.error('Usage: node scripts/update_specific_user.js <email>');
  process.exit(1);
}

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  console.log('MongoDB connected successfully');
  updateUser(email);
})
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function updateUser(email) {
  try {
    console.log(`Looking for user with email: ${email}`);
    
    // Find the user
    const user = await User.findOne({ email: email });
    
    if (!user) {
      console.error(`User with email ${email} not found`);
      mongoose.connection.close();
      process.exit(1);
    }
    
    console.log('User found:');
    console.log(`- Username: ${user.username}`);
    console.log(`- Email: ${user.email}`);
    console.log(`- Current role: ${user.role}`);
    console.log(`- HOA Code: ${user.hoaCommunityCode}`);
    console.log(`- Approved: ${user.isApproved}`);
    
    // Update the user's role
    console.log(`\nUpdating ${user.username} (${user.email}) from '${user.role}' to 'admin'`);
    
    user.role = 'admin';
    await user.save();
    
    console.log(`✓ Updated ${user.username} to 'admin' role`);
    
    // Verify the update
    const updatedUser = await User.findOne({ email: email });
    console.log('\nVerification:');
    console.log(`- Username: ${updatedUser.username}`);
    console.log(`- Email: ${updatedUser.email}`);
    console.log(`- New role: ${updatedUser.role}`);
    
    mongoose.connection.close();
    console.log('MongoDB connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Error updating user:', error);
    mongoose.connection.close();
    process.exit(1);
  }
}

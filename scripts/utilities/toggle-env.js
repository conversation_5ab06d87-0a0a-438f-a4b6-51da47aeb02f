/**
 * Environment Toggle Script
 * 
 * This script toggles between local development and production environments
 * by copying the appropriate .env file to .env
 */

const fs = require('fs');
const path = require('path');

// Get the environment from command line arguments
const env = process.argv[2];

if (!env || (env !== 'local' && env !== 'prod')) {
  console.error('Please specify environment: node toggle-env.js [local|prod]');
  process.exit(1);
}

const rootDir = path.resolve(__dirname, '..');
const sourceFile = env === 'local' 
  ? path.join(rootDir, '.env.development')
  : path.join(rootDir, '.env.production');
const targetFile = path.join(rootDir, '.env');

// Check if source file exists
if (!fs.existsSync(sourceFile)) {
  console.error(`Source file ${sourceFile} does not exist`);
  process.exit(1);
}

// Copy the file
try {
  fs.copyFileSync(sourceFile, targetFile);
  console.log(`Successfully switched to ${env === 'local' ? 'local development' : 'production'} environment`);
  console.log(`Copied ${sourceFile} to ${targetFile}`);
} catch (error) {
  console.error(`Error switching environment: ${error.message}`);
  process.exit(1);
}

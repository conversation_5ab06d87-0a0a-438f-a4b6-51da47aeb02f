/**
 * Local Database Seeding Script
 * Seeds local MongoDB with test data for comprehensive local testing
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const fs = require('fs');

// Load appropriate environment file
let envFile = '.env.local';
if (fs.existsSync('.env.local.active')) {
  envFile = '.env.local.active';
} else if (fs.existsSync('.env.local.production')) {
  envFile = '.env.local.production';
}

require('dotenv').config({ path: envFile });
console.log(`🔧 Using environment: ${envFile}`);

// Import models
const User = require('../models/user');
const HOA = require('../models/hoa');
const Community = require('../models/community');
const Document = require('../models/document');
const Notification = require('../models/notification');

const seedDatabase = async () => {
  try {
    console.log('🌱 Starting local database seeding...');
    
    // Connect to local MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to local MongoDB');

    // Clear existing data
    await Promise.all([
      User.deleteMany({}),
      HOA.deleteMany({}),
      Community.deleteMany({}),
      Document.deleteMany({}),
      Notification.deleteMany({})
    ]);
    console.log('🧹 Cleared existing data');

    // Create test HOA
    const testHOA = await HOA.create({
      hoaCommunityName: 'Test Harmony HOA',
      hoaCommunityCode: 'TEST001',
      address: '123 Test Street, Test City, TC 12345',
      contactEmail: '<EMAIL>',
      contactPhone: '555-0123',
      establishedDate: new Date('2020-01-01'),
      totalUnits: 100,
      monthlyFee: 150.00,
      amenities: ['Pool', 'Gym', 'Clubhouse', 'Tennis Court'],
      rules: ['No loud music after 10 PM', 'Keep common areas clean'],
      boardMembers: ['John Smith - President', 'Jane Doe - Secretary'],
      isActive: true
    });
    console.log('🏘️ Created test HOA');

    // Create test communities (streets)
    const communities = await Community.create([
      {
        name: 'Maple Street',
        code: 'MAPLE',
        hoaId: testHOA._id,
        description: 'Beautiful tree-lined street with family homes',
        totalUnits: 25,
        isActive: true
      },
      {
        name: 'Oak Avenue',
        code: 'OAK',
        hoaId: testHOA._id,
        description: 'Quiet residential area with modern townhomes',
        totalUnits: 30,
        isActive: true
      },
      {
        name: 'Pine Boulevard',
        code: 'PINE',
        hoaId: testHOA._id,
        description: 'Premium location with luxury condos',
        totalUnits: 45,
        isActive: true
      }
    ]);
    console.log('🏠 Created test communities');

    // Create test users
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    const users = await User.create([
      {
        username: 'companyadmin',
        email: '<EMAIL>',
        password: hashedPassword,
        fullName: 'Company Administrator',
        role: 'company_admin',
        isActive: true,
        emailVerified: true
      },
      {
        username: 'hoaadmin',
        email: '<EMAIL>',
        password: hashedPassword,
        fullName: 'HOA Administrator',
        role: 'admin',
        hoaId: testHOA._id,
        isActive: true,
        emailVerified: true
      },
      {
        username: 'testuser1',
        email: '<EMAIL>',
        password: hashedPassword,
        fullName: 'Test User One',
        role: 'user',
        hoaId: testHOA._id,
        communityId: communities[0]._id,
        unitNumber: '101',
        isActive: true,
        emailVerified: true
      },
      {
        username: 'testuser2',
        email: '<EMAIL>',
        password: hashedPassword,
        fullName: 'Test User Two',
        role: 'user',
        hoaId: testHOA._id,
        communityId: communities[1]._id,
        unitNumber: '201',
        isActive: true,
        emailVerified: true
      }
    ]);
    console.log('👥 Created test users');

    // Create test documents
    const documents = await Document.create([
      {
        title: 'HOA Bylaws 2024',
        description: 'Updated community bylaws and regulations',
        category: 'rules',
        fileName: 'hoa-bylaws-2024.pdf',
        fileType: 'application/pdf',
        fileSize: 1024000,
        uploadedBy: users[1]._id,
        hoaId: testHOA._id,
        isPublic: true,
        status: 'active'
      },
      {
        title: 'Monthly Financial Report',
        description: 'December 2024 financial summary',
        category: 'financial',
        fileName: 'financial-report-dec-2024.pdf',
        fileType: 'application/pdf',
        fileSize: 512000,
        uploadedBy: users[1]._id,
        hoaId: testHOA._id,
        isPublic: true,
        status: 'active'
      }
    ]);
    console.log('📄 Created test documents');

    // Create test announcements
    const announcements = await Notification.create([
      {
        title: 'Pool Maintenance Schedule',
        message: 'The community pool will be closed for maintenance from January 15-17, 2025. We apologize for any inconvenience.',
        type: 'announcement',
        priority: 'medium',
        senderId: users[1]._id,
        hoaId: testHOA._id,
        targetAudience: 'all',
        isActive: true,
        scheduledFor: new Date()
      },
      {
        title: 'Maple Street Parking Reminder',
        message: 'Please remember that overnight parking on the street is not permitted. Use designated parking areas.',
        type: 'announcement',
        priority: 'low',
        senderId: users[1]._id,
        hoaId: testHOA._id,
        communityId: communities[0]._id,
        targetAudience: 'community',
        isActive: true,
        scheduledFor: new Date()
      }
    ]);
    console.log('📢 Created test announcements');

    console.log('\n🎉 Local database seeding completed successfully!');
    console.log('\n📋 Test Accounts Created:');
    console.log('Company Admin: <EMAIL> / password123');
    console.log('HOA Admin: <EMAIL> / password123');
    console.log('Test User 1: <EMAIL> / password123');
    console.log('Test User 2: <EMAIL> / password123');
    console.log('\n🏘️ Test HOA: Test Harmony HOA (TEST001)');
    console.log('🏠 Test Communities: Maple Street, Oak Avenue, Pine Boulevard');
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
};

// Run seeding if called directly
if (require.main === module) {
  seedDatabase();
}

module.exports = seedDatabase;

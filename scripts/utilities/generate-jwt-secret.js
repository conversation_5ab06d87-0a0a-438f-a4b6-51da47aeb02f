/**
 * <PERSON><PERSON>t to generate a secure JWT secret
 * Run with: node scripts/generate-jwt-secret.js
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Generate a secure random string
const generateSecret = (length = 64) => {
  return crypto.randomBytes(length).toString('hex');
};

// Main function
const main = () => {
  try {
    const secret = generateSecret();
    console.log('\n=== JWT Secret Generator ===\n');
    console.log('Generated JWT Secret:');
    console.log('\x1b[32m%s\x1b[0m', secret);
    console.log('\nAdd this to your .env file as:');
    console.log('\x1b[33m%s\x1b[0m', `JWT_SECRET=${secret}`);
    
    // Check if .env file exists
    const envPath = path.join(__dirname, '..', '.env');
    if (fs.existsSync(envPath)) {
      console.log('\nWould you like to update your .env file automatically? (y/n)');
      process.stdin.once('data', (data) => {
        const input = data.toString().trim().toLowerCase();
        if (input === 'y' || input === 'yes') {
          try {
            let envContent = fs.readFileSync(envPath, 'utf8');
            
            // Check if JWT_SECRET already exists
            if (envContent.includes('JWT_SECRET=')) {
              // Replace existing JWT_SECRET
              envContent = envContent.replace(/JWT_SECRET=.*(\r?\n|$)/, `JWT_SECRET=${secret}$1`);
            } else {
              // Add JWT_SECRET to the end
              envContent += `\nJWT_SECRET=${secret}\n`;
            }
            
            fs.writeFileSync(envPath, envContent);
            console.log('\x1b[32m%s\x1b[0m', '\n.env file updated successfully!\n');
          } catch (err) {
            console.error('\x1b[31m%s\x1b[0m', `\nError updating .env file: ${err.message}\n`);
          }
        } else {
          console.log('\nNo changes made to .env file.\n');
        }
        process.exit(0);
      });
    } else {
      console.log('\n.env file not found. Create one and add the JWT_SECRET manually.\n');
      process.exit(0);
    }
  } catch (err) {
    console.error('\x1b[31m%s\x1b[0m', `\nError generating JWT secret: ${err.message}\n`);
    process.exit(1);
  }
};

// Run the script
main();

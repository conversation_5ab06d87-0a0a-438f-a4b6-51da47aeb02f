#!/usr/bin/env node

/**
 * Simple Local Testing Startup Script
 * Starts the backend with production database for local testing
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting HOAFLO Local Testing Environment...\n');

// Copy production environment for local testing
const sourceEnv = '.env.local.production';
const targetEnv = '.env.local.active';

if (fs.existsSync(sourceEnv)) {
  fs.copyFileSync(sourceEnv, targetEnv);
  console.log('✅ Environment configured for local testing with production database');
} else {
  console.log('⚠️  Production environment file not found, using default local environment');
}

console.log('\n🔧 Configuration:');
console.log('   • Backend: http://localhost:5001');
console.log('   • Database: Production MongoDB (read/write)');
console.log('   • S3: Production bucket');
console.log('   • Email: Outlook Business');
console.log('\n📋 Test Accounts (if available):');
console.log('   • Company Admin: <EMAIL> / password123');
console.log('   • HOA Admin: <EMAIL> / password123');
console.log('   • Test User: <EMAIL> / password123');

console.log('\n🎯 Starting backend server...\n');

// Start the backend server
const server = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true,
  env: { ...process.env, NODE_ENV: 'development' }
});

server.on('close', (code) => {
  console.log(`\n🔌 Backend server exited with code ${code}`);
});

server.on('error', (error) => {
  console.error('❌ Failed to start backend server:', error);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down local testing environment...');
  server.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down local testing environment...');
  server.kill('SIGTERM');
  process.exit(0);
});

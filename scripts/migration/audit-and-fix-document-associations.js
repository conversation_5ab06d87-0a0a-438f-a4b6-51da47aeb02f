/**
 * HOAFLO Document Association Audit & Migration Script
 * 
 * This script audits and fixes document associations to ensure proper HOA and community isolation.
 * It addresses missing hoaId and communityId references in existing documents.
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env' });

console.log('Starting HOAFLO Document Association Audit & Migration');

// Connect to MongoDB
const connectDB = async () => {
  try {
    const baseUri = 'mongodb+srv://frankiebruno:<EMAIL>/street-harmony?retryWrites=true&w=majority&appName=HOA-Data';
    
    await mongoose.connect(baseUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected to street-harmony database');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

// Define schemas
const documentSchema = new mongoose.Schema({}, { strict: false });
const userSchema = new mongoose.Schema({}, { strict: false });
const hoaSchema = new mongoose.Schema({}, { strict: false });
const communitySchema = new mongoose.Schema({}, { strict: false });
const notificationSchema = new mongoose.Schema({}, { strict: false });

const Document = mongoose.model('Document', documentSchema);
const User = mongoose.model('User', userSchema);
const HOA = mongoose.model('HOA', hoaSchema);
const Community = mongoose.model('Community', communitySchema);
const Notification = mongoose.model('Notification', notificationSchema);

// Audit and fix document associations
const auditAndFixDocuments = async () => {
  try {
    console.log('\n🔍 AUDITING DOCUMENT ASSOCIATIONS...\n');

    // Get all documents
    const allDocuments = await Document.find({});
    console.log(`Found ${allDocuments.length} total documents`);

    // Get all users with HOA associations
    const usersWithHOA = await User.find({ 
      $or: [
        { hoaId: { $exists: true, $ne: null } },
        { hoaCommunityCode: { $exists: true, $ne: null } }
      ]
    });
    console.log(`Found ${usersWithHOA.length} users with HOA associations`);

    // Get all HOAs
    const allHOAs = await HOA.find({});
    console.log(`Found ${allHOAs.length} HOAs`);

    // Get all communities
    const allCommunities = await Community.find({});
    console.log(`Found ${allCommunities.length} communities`);

    // Audit results
    const auditResults = {
      documentsWithHOA: 0,
      documentsWithoutHOA: 0,
      documentsWithCommunity: 0,
      documentsWithoutCommunity: 0,
      orphanedDocuments: [],
      fixableDocuments: [],
      unfixableDocuments: []
    };

    console.log('\n📊 AUDIT RESULTS:\n');

    for (const doc of allDocuments) {
      // Check HOA association
      if (doc.hoaId) {
        auditResults.documentsWithHOA++;
      } else {
        auditResults.documentsWithoutHOA++;
      }

      // Check community association
      if (doc.communityId) {
        auditResults.documentsWithCommunity++;
      } else {
        auditResults.documentsWithoutCommunity++;
      }

      // Check if document can be fixed
      if (!doc.hoaId && doc.uploadedBy) {
        const uploader = usersWithHOA.find(u => u._id.toString() === doc.uploadedBy.toString());
        if (uploader && (uploader.hoaId || uploader.hoaCommunityCode)) {
          auditResults.fixableDocuments.push({
            documentId: doc._id,
            uploadedBy: doc.uploadedBy,
            uploaderHOAId: uploader.hoaId,
            uploaderHOACode: uploader.hoaCommunityCode,
            uploaderCommunityId: uploader.communityId,
            fileName: doc.fileName,
            category: doc.category
          });
        } else {
          auditResults.unfixableDocuments.push({
            documentId: doc._id,
            uploadedBy: doc.uploadedBy,
            fileName: doc.fileName,
            reason: 'Uploader has no HOA association'
          });
        }
      } else if (!doc.hoaId && !doc.uploadedBy) {
        auditResults.orphanedDocuments.push({
          documentId: doc._id,
          fileName: doc.fileName,
          category: doc.category,
          reason: 'No uploader and no HOA association'
        });
      }
    }

    // Display audit results
    console.log(`✅ Documents with HOA: ${auditResults.documentsWithHOA}`);
    console.log(`❌ Documents without HOA: ${auditResults.documentsWithoutHOA}`);
    console.log(`✅ Documents with Community: ${auditResults.documentsWithCommunity}`);
    console.log(`❌ Documents without Community: ${auditResults.documentsWithoutCommunity}`);
    console.log(`🔧 Fixable documents: ${auditResults.fixableDocuments.length}`);
    console.log(`⚠️ Unfixable documents: ${auditResults.unfixableDocuments.length}`);
    console.log(`🚫 Orphaned documents: ${auditResults.orphanedDocuments.length}`);

    // Show fixable documents
    if (auditResults.fixableDocuments.length > 0) {
      console.log('\n🔧 FIXABLE DOCUMENTS:');
      auditResults.fixableDocuments.forEach((doc, index) => {
        console.log(`${index + 1}. ${doc.fileName} (${doc.category})`);
        console.log(`   - Document ID: ${doc.documentId}`);
        console.log(`   - Uploader: ${doc.uploadedBy}`);
        console.log(`   - Will assign HOA: ${doc.uploaderHOAId || 'via code lookup'}`);
        console.log(`   - Will assign Community: ${doc.uploaderCommunityId || 'none'}`);
      });
    }

    // Show problematic documents
    if (auditResults.orphanedDocuments.length > 0) {
      console.log('\n🚫 ORPHANED DOCUMENTS:');
      auditResults.orphanedDocuments.forEach((doc, index) => {
        console.log(`${index + 1}. ${doc.fileName} (${doc.category})`);
        console.log(`   - Document ID: ${doc.documentId}`);
        console.log(`   - Reason: ${doc.reason}`);
      });
    }

    return auditResults;

  } catch (error) {
    console.error('❌ Error during audit:', error);
    throw error;
  }
};

// Fix document associations
const fixDocumentAssociations = async (auditResults) => {
  try {
    console.log('\n🔧 FIXING DOCUMENT ASSOCIATIONS...\n');

    let fixedCount = 0;
    let errorCount = 0;

    for (const docInfo of auditResults.fixableDocuments) {
      try {
        const updateData = {};

        // Add HOA association
        if (docInfo.uploaderHOAId) {
          updateData.hoaId = docInfo.uploaderHOAId;
        } else if (docInfo.uploaderHOACode) {
          // Find HOA by code
          const hoa = await HOA.findOne({ hoaCommunityCode: docInfo.uploaderHOACode });
          if (hoa) {
            updateData.hoaId = hoa._id;
          }
        }

        // Add community association if available
        if (docInfo.uploaderCommunityId) {
          updateData.communityId = docInfo.uploaderCommunityId;
        }

        // Update the document
        if (Object.keys(updateData).length > 0) {
          await Document.findByIdAndUpdate(docInfo.documentId, { $set: updateData });
          console.log(`✅ Fixed: ${docInfo.fileName}`);
          console.log(`   - Added HOA: ${updateData.hoaId}`);
          if (updateData.communityId) {
            console.log(`   - Added Community: ${updateData.communityId}`);
          }
          fixedCount++;
        }

      } catch (error) {
        console.error(`❌ Error fixing document ${docInfo.documentId}:`, error.message);
        errorCount++;
      }
    }

    console.log(`\n📊 FIXING RESULTS:`);
    console.log(`✅ Successfully fixed: ${fixedCount} documents`);
    console.log(`❌ Errors: ${errorCount} documents`);

    return { fixedCount, errorCount };

  } catch (error) {
    console.error('❌ Error during fixing:', error);
    throw error;
  }
};

// Handle orphaned documents
const handleOrphanedDocuments = async (auditResults) => {
  try {
    console.log('\n🚫 HANDLING ORPHANED DOCUMENTS...\n');

    if (auditResults.orphanedDocuments.length === 0) {
      console.log('✅ No orphaned documents found');
      return;
    }

    console.log('📋 ORPHANED DOCUMENT STRATEGIES:');
    console.log('1. Assign to a default "System" HOA');
    console.log('2. Mark as global documents (no HOA association)');
    console.log('3. Delete if they are test/invalid documents');
    console.log('4. Manual review required');

    // For now, we'll mark them for manual review
    console.log('\n⚠️ RECOMMENDATION: Manual review required for orphaned documents');
    console.log('These documents should be reviewed and either:');
    console.log('- Assigned to appropriate HOAs manually');
    console.log('- Marked as global system documents');
    console.log('- Deleted if they are invalid/test data');

    auditResults.orphanedDocuments.forEach((doc, index) => {
      console.log(`\n${index + 1}. Document: ${doc.fileName}`);
      console.log(`   - ID: ${doc.documentId}`);
      console.log(`   - Category: ${doc.category}`);
      console.log(`   - Action needed: Manual review`);
    });

  } catch (error) {
    console.error('❌ Error handling orphaned documents:', error);
    throw error;
  }
};

// Verify fixes
const verifyFixes = async () => {
  try {
    console.log('\n✅ VERIFYING FIXES...\n');

    const documentsWithHOA = await Document.countDocuments({ hoaId: { $exists: true, $ne: null } });
    const documentsWithoutHOA = await Document.countDocuments({ 
      $or: [
        { hoaId: { $exists: false } },
        { hoaId: null }
      ]
    });
    const documentsWithCommunity = await Document.countDocuments({ communityId: { $exists: true, $ne: null } });

    console.log(`📊 POST-FIX STATISTICS:`);
    console.log(`✅ Documents with HOA: ${documentsWithHOA}`);
    console.log(`❌ Documents without HOA: ${documentsWithoutHOA}`);
    console.log(`✅ Documents with Community: ${documentsWithCommunity}`);

    // Check HOA distribution
    const hoaDistribution = await Document.aggregate([
      { $match: { hoaId: { $exists: true, $ne: null } } },
      { $group: { _id: '$hoaId', count: { $sum: 1 } } },
      { $lookup: { from: 'hoas', localField: '_id', foreignField: '_id', as: 'hoa' } },
      { $unwind: '$hoa' },
      { $project: { hoaName: '$hoa.hoaCommunityName', count: 1 } }
    ]);

    console.log(`\n🏘️ DOCUMENTS BY HOA:`);
    hoaDistribution.forEach(item => {
      console.log(`   - ${item.hoaName}: ${item.count} documents`);
    });

  } catch (error) {
    console.error('❌ Error during verification:', error);
    throw error;
  }
};

// Main execution
const runAuditAndFix = async () => {
  try {
    await connectDB();
    
    console.log('🚀 Starting document association audit and migration...\n');
    
    // Step 1: Audit current state
    const auditResults = await auditAndFixDocuments();
    
    // Step 2: Fix fixable documents
    const fixResults = await fixDocumentAssociations(auditResults);
    
    // Step 3: Handle orphaned documents
    await handleOrphanedDocuments(auditResults);
    
    // Step 4: Verify fixes
    await verifyFixes();
    
    console.log('\n🎉 DOCUMENT ASSOCIATION AUDIT & MIGRATION COMPLETED!');
    console.log(`📊 Summary: Fixed ${fixResults.fixedCount} documents with ${fixResults.errorCount} errors`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
    process.exit(0);
  }
};

// Run the migration
if (require.main === module) {
  runAuditAndFix();
}

module.exports = { auditAndFixDocuments, fixDocumentAssociations, verifyFixes };

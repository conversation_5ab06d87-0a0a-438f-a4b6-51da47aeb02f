const mongoose = require('mongoose');
const path = require('path');

// Load environment variables - try multiple locations
const envFiles = [
  path.join(__dirname, '..', '.env.production'),
  path.join(__dirname, '..', '.env.development'),
  path.join(__dirname, '..', '.env')
];

for (const envFile of envFiles) {
  try {
    require('dotenv').config({ path: envFile });
    console.log(`📁 Loaded environment from: ${envFile}`);
    break;
  } catch (error) {
    // Continue to next file
  }
}

/**
 * SAFE MIGRATION SCRIPT: Add Email Configuration to Existing HOAs
 * 
 * This script safely adds email configuration fields to existing HOA records
 * without breaking any existing data or functionality.
 * 
 * SAFETY FEATURES:
 * - Only adds fields if they don't already exist
 * - Uses MongoDB $set with $exists: false to prevent overwrites
 * - Maintains all existing HOA data unchanged
 * - Provides detailed logging and rollback information
 * - Dry-run mode available for testing
 */

// Connect to MongoDB
const connectDB = async () => {
  try {
    if (!process.env.MONGODB_URI) {
      console.error('❌ MONGODB_URI environment variable not found');
      console.log('Available environment variables:', Object.keys(process.env).filter(key => key.includes('MONGO')));
      process.exit(1);
    }

    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Import HOA model after connection
let HOA;

const initializeModel = () => {
  try {
    HOA = require('../models/hoa');
    console.log('✅ HOA model loaded successfully');
  } catch (error) {
    console.error('❌ Failed to load HOA model:', error);
    process.exit(1);
  }
};

/**
 * Safely migrate HOA email configuration
 */
const migrateHOAEmailConfig = async (dryRun = true) => {
  try {
    console.log('\n🚀 Starting HOA Email Configuration Migration...');
    console.log(`📋 Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE MIGRATION'}`);
    console.log('⏰ Started at:', new Date().toISOString());

    // Get all HOAs that don't have email configuration
    const hoasWithoutEmailConfig = await HOA.find({
      'emailConfig.configured': { $exists: false }
    }).select('hoaCommunityName hoaCommunityCode contactEmail');

    console.log(`\n📊 Found ${hoasWithoutEmailConfig.length} HOAs without email configuration`);

    if (hoasWithoutEmailConfig.length === 0) {
      console.log('✅ All HOAs already have email configuration. No migration needed.');
      return { success: true, migrated: 0, skipped: 0 };
    }

    let migratedCount = 0;
    let skippedCount = 0;
    const errors = [];

    // Process each HOA
    for (const hoa of hoasWithoutEmailConfig) {
      try {
        console.log(`\n🏘️  Processing HOA: ${hoa.hoaCommunityName} (${hoa.hoaCommunityCode})`);

        // Prepare email configuration with safe defaults
        const emailConfig = {
          configured: false,
          provider: 'gmail',
          email: '', // Empty by default - HOA admin will configure
          password: '', // Empty by default - HOA admin will configure
          displayName: hoa.hoaCommunityName || 'HOA Administration',
          smtpConfig: {
            host: '',
            port: 587,
            secure: false
          },
          verificationStatus: 'not_verified',
          lastVerified: null,
          configuredAt: null,
          configuredBy: null
        };

        if (!dryRun) {
          // Use $set with $exists: false to only add if field doesn't exist
          const result = await HOA.updateOne(
            { 
              _id: hoa._id,
              'emailConfig.configured': { $exists: false }
            },
            { 
              $set: { emailConfig: emailConfig }
            }
          );

          if (result.modifiedCount > 0) {
            console.log(`   ✅ Successfully added email configuration`);
            migratedCount++;
          } else {
            console.log(`   ⚠️  Skipped (already has email config or not found)`);
            skippedCount++;
          }
        } else {
          console.log(`   📝 Would add email configuration:`, {
            configured: false,
            provider: 'gmail',
            displayName: emailConfig.displayName,
            verificationStatus: 'not_verified'
          });
          migratedCount++;
        }

      } catch (error) {
        console.error(`   ❌ Error processing HOA ${hoa.hoaCommunityCode}:`, error.message);
        errors.push({
          hoaId: hoa._id,
          hoaCode: hoa.hoaCommunityCode,
          error: error.message
        });
        skippedCount++;
      }
    }

    // Summary
    console.log('\n📊 MIGRATION SUMMARY:');
    console.log(`   ✅ Successfully processed: ${migratedCount}`);
    console.log(`   ⚠️  Skipped: ${skippedCount}`);
    console.log(`   ❌ Errors: ${errors.length}`);

    if (errors.length > 0) {
      console.log('\n❌ ERRORS:');
      errors.forEach(error => {
        console.log(`   - HOA ${error.hoaCode}: ${error.error}`);
      });
    }

    console.log(`\n⏰ Completed at: ${new Date().toISOString()}`);

    if (dryRun) {
      console.log('\n🔍 DRY RUN COMPLETE - No changes were made to the database');
      console.log('💡 To perform the actual migration, run: node migrateHOAEmailConfig.js --live');
    } else {
      console.log('\n✅ LIVE MIGRATION COMPLETE');
      console.log('🎯 HOAs can now configure their email settings through the admin panel');
    }

    return {
      success: true,
      migrated: migratedCount,
      skipped: skippedCount,
      errors: errors.length
    };

  } catch (error) {
    console.error('\n❌ MIGRATION FAILED:', error);
    console.error('Stack trace:', error.stack);
    return { success: false, error: error.message };
  }
};

/**
 * Verify migration results
 */
const verifyMigration = async () => {
  try {
    console.log('\n🔍 Verifying migration results...');

    const totalHOAs = await HOA.countDocuments({});
    const hoasWithEmailConfig = await HOA.countDocuments({
      'emailConfig.configured': { $exists: true }
    });
    const configuredHOAs = await HOA.countDocuments({
      'emailConfig.configured': true
    });

    console.log(`📊 VERIFICATION RESULTS:`);
    console.log(`   Total HOAs: ${totalHOAs}`);
    console.log(`   HOAs with email config structure: ${hoasWithEmailConfig}`);
    console.log(`   HOAs with configured email: ${configuredHOAs}`);
    console.log(`   HOAs ready for email configuration: ${hoasWithEmailConfig - configuredHOAs}`);

    if (hoasWithEmailConfig === totalHOAs) {
      console.log('✅ All HOAs have email configuration structure');
    } else {
      console.log(`⚠️  ${totalHOAs - hoasWithEmailConfig} HOAs still need email configuration structure`);
    }

  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
};

/**
 * Main execution
 */
const main = async () => {
  try {
    await connectDB();
    initializeModel();

    // Check command line arguments
    const args = process.argv.slice(2);
    const isLive = args.includes('--live');
    const isVerify = args.includes('--verify');

    if (isVerify) {
      await verifyMigration();
    } else {
      const result = await migrateHOAEmailConfig(!isLive);
      
      if (result.success && !isLive) {
        console.log('\n🔄 To run verification: node migrateHOAEmailConfig.js --verify');
      }
    }

  } catch (error) {
    console.error('❌ Script execution failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
};

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Script interrupted by user');
  await mongoose.connection.close();
  process.exit(0);
});

process.on('unhandledRejection', async (error) => {
  console.error('❌ Unhandled rejection:', error);
  await mongoose.connection.close();
  process.exit(1);
});

// Run the script
main();

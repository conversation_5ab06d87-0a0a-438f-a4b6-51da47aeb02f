/**
 * <PERSON>ript to Fix Admin User HOA Association
 * 
 * This script diagnoses and fixes the HOA association issue for admin users
 * so they can access the announcement targeting features.
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
const environment = process.env.NODE_ENV || 'production';
const envFile = environment === 'production' ? '.env.production' : '.env.development';
dotenv.config({ path: envFile });

console.log(`Running HOA association fix in ${environment} environment`);

// Connect to MongoDB
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGO_URI || process.env.MONGODB_URI;
    if (!mongoUri) {
      console.error('❌ No MongoDB URI found in environment variables');
      console.log('Available env vars:', Object.keys(process.env).filter(key => key.includes('MONGO')));
      process.exit(1);
    }

    await mongoose.connect(mongo<PERSON><PERSON>, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for HOA association fix');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

// Define schemas (simplified for this script)
const userSchema = new mongoose.Schema({}, { strict: false });
const hoaSchema = new mongoose.Schema({}, { strict: false });
const communitySchema = new mongoose.Schema({}, { strict: false });

const User = mongoose.model('User', userSchema);
const HOA = mongoose.model('HOA', hoaSchema);
const Community = mongoose.model('Community', communitySchema);

// Main diagnostic and fix function
const fixAdminHOAAssociation = async () => {
  try {
    console.log('\n🔍 DIAGNOSING HOA ASSOCIATION ISSUE...\n');

    // 1. Find the problematic admin user
    const adminUserId = '6854cc7997ffcf63ee79ef9d';
    const adminUser = await User.findById(adminUserId);
    
    if (!adminUser) {
      console.log('❌ Admin user not found!');
      return;
    }

    console.log('👤 ADMIN USER DETAILS:');
    console.log(`- ID: ${adminUser._id}`);
    console.log(`- Email: ${adminUser.email}`);
    console.log(`- Role: ${adminUser.role}`);
    console.log(`- HOA ID: ${adminUser.hoaId || 'NOT SET'}`);
    console.log(`- Community ID: ${adminUser.communityId || 'NOT SET'}`);
    console.log(`- HOA Community Code: ${adminUser.hoaCommunityCode || 'NOT SET'}`);

    // 2. Check available HOAs
    const hoas = await HOA.find({});
    console.log(`\n🏘️ AVAILABLE HOAs (${hoas.length}):`);
    hoas.forEach((hoa, index) => {
      console.log(`${index + 1}. ${hoa.hoaCommunityName || hoa.name || 'Unnamed HOA'}`);
      console.log(`   - ID: ${hoa._id}`);
      console.log(`   - Code: ${hoa.hoaCommunityCode || 'No code'}`);
      console.log(`   - Location: ${hoa.hoaCity || 'No location'}, ${hoa.hoaState || ''}`);
    });

    // 3. Check available communities/streets
    const communities = await Community.find({});
    console.log(`\n🏠 AVAILABLE COMMUNITIES/STREETS (${communities.length}):`);
    communities.forEach((community, index) => {
      console.log(`${index + 1}. ${community.name}`);
      console.log(`   - ID: ${community._id}`);
      console.log(`   - Code: ${community.code}`);
      console.log(`   - HOA Code: ${community.hoaCommunityCode || 'No HOA code'}`);
    });

    // 4. Determine the fix needed
    if (hoas.length === 0) {
      console.log('\n❌ NO HOAs FOUND - Need to create HOA data first');
      console.log('\n📋 SOLUTION: Create HOA and community data');
      await createSampleHOAData();
    } else if (communities.length === 0) {
      console.log('\n❌ NO COMMUNITIES/STREETS FOUND - Need to create community data');
      console.log('\n📋 SOLUTION: Create community/street data');
      await createSampleCommunityData(hoas[0]);
    } else {
      console.log('\n✅ HOA and Community data exists - Fixing user association');
      await associateAdminWithHOA(adminUser, hoas[0], communities);
    }

  } catch (error) {
    console.error('❌ Error during diagnosis:', error);
    throw error;
  }
};

// Create sample HOA data if none exists
const createSampleHOAData = async () => {
  try {
    console.log('\n🏗️ Creating sample HOA data...');

    const sampleHOA = new HOA({
      hoaCommunityName: 'Test HOA Community',
      hoaCommunityCode: 'TEST001',
      hoaStreetAddress: '123 Main Street',
      hoaCity: 'Test City',
      hoaState: 'CA',
      hoaZipCode: '12345',
      hoaPaymentInfo: {
        paymentMethod: 'check',
        paymentInstructions: 'Mail checks to HOA office',
        dueDate: 1
      },
      isApproved: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    const savedHOA = await sampleHOA.save();
    console.log(`✅ Created HOA: ${savedHOA.hoaCommunityName} (${savedHOA.hoaCommunityCode})`);

    // Create sample communities for this HOA
    await createSampleCommunityData(savedHOA);

    return savedHOA;
  } catch (error) {
    console.error('❌ Error creating HOA data:', error);
    throw error;
  }
};

// Create sample community/street data
const createSampleCommunityData = async (hoa) => {
  try {
    console.log('\n🏠 Creating sample community/street data...');

    const sampleCommunities = [
      {
        name: 'Main Street',
        code: 'MAIN',
        description: 'Main residential street',
        hoaCommunityCode: hoa.hoaCommunityCode
      },
      {
        name: 'Oak Avenue',
        code: 'OAK',
        description: 'Oak tree lined avenue',
        hoaCommunityCode: hoa.hoaCommunityCode
      },
      {
        name: 'Pine Drive',
        code: 'PINE',
        description: 'Pine tree residential drive',
        hoaCommunityCode: hoa.hoaCommunityCode
      }
    ];

    const createdCommunities = [];
    for (const communityData of sampleCommunities) {
      const community = new Community(communityData);
      const savedCommunity = await community.save();
      createdCommunities.push(savedCommunity);
      console.log(`✅ Created community: ${savedCommunity.name} (${savedCommunity.code})`);
    }

    // Now associate the admin with the HOA
    const adminUser = await User.findById('6854cc7997ffcf63ee79ef9d');
    await associateAdminWithHOA(adminUser, hoa, createdCommunities);

    return createdCommunities;
  } catch (error) {
    console.error('❌ Error creating community data:', error);
    throw error;
  }
};

// Associate admin user with HOA
const associateAdminWithHOA = async (adminUser, hoa, communities) => {
  try {
    console.log('\n🔗 Associating admin user with HOA...');

    const updateData = {
      hoaId: hoa._id,
      hoaCommunityCode: hoa.hoaCommunityCode,
      // Associate with the first community as default
      communityId: communities.length > 0 ? communities[0]._id : null
    };

    const updatedUser = await User.findByIdAndUpdate(
      adminUser._id,
      { $set: updateData },
      { new: true }
    );

    console.log('✅ ADMIN USER UPDATED:');
    console.log(`- HOA ID: ${updatedUser.hoaId}`);
    console.log(`- HOA Community Code: ${updatedUser.hoaCommunityCode}`);
    console.log(`- Community ID: ${updatedUser.communityId}`);

    console.log('\n🎉 SUCCESS! Admin user is now associated with HOA');
    console.log('📋 The admin can now:');
    console.log('   ✅ Access announcement targeting features');
    console.log('   ✅ See street dropdown options');
    console.log('   ✅ Select residents for announcements');

  } catch (error) {
    console.error('❌ Error associating admin with HOA:', error);
    throw error;
  }
};

// Main execution
const runFix = async () => {
  try {
    await connectDB();
    await fixAdminHOAAssociation();
    
    console.log('\n🎉 HOA ASSOCIATION FIX COMPLETED SUCCESSFULLY!');
    console.log('🧪 You can now test the announcement targeting features.');
    
  } catch (error) {
    console.error('❌ Fix failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
    process.exit(0);
  }
};

// Run the fix
if (require.main === module) {
  runFix();
}

module.exports = { fixAdminHOAAssociation };

/**
 * Fix Community-HOA Association Issues
 * 
 * This script fixes mismatched community-HOA associations in documents
 * to ensure proper data integrity.
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env' });

console.log('Fixing Community-HOA Association Issues');

// Connect to MongoDB
const connectDB = async () => {
  try {
    const baseUri = 'mongodb+srv://frankiebruno:<EMAIL>/street-harmony?retryWrites=true&w=majority&appName=HOA-Data';
    
    await mongoose.connect(baseUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected to street-harmony database');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

// Use actual models
const Document = require('../models/document');
const HOA = require('../models/hoa');
const Community = require('../models/community');

// Fix community-HOA associations
const fixCommunityHOAAssociations = async () => {
  try {
    console.log('\n🔧 FIXING COMMUNITY-HOA ASSOCIATIONS...\n');

    // Get all documents with both HOA and community associations
    const docsWithBoth = await Document.find({
      hoaId: { $exists: true, $ne: null },
      communityId: { $exists: true, $ne: null }
    }).populate(['hoaId', 'communityId']);

    console.log(`Found ${docsWithBoth.length} documents with both HOA and community associations`);

    let fixedCount = 0;
    let errorCount = 0;

    for (const doc of docsWithBoth) {
      try {
        if (!doc.hoaId || !doc.communityId) {
          console.log(`Skipping document ${doc._id} - missing populated data`);
          continue;
        }

        const hoa = doc.hoaId;
        const community = doc.communityId;

        console.log(`\nChecking document: ${doc.title || doc.fileName}`);
        console.log(`  HOA: ${hoa.hoaCommunityName} (${hoa.hoaCommunityCode})`);
        console.log(`  Community: ${community.name} (${community.hoaCommunityCode || 'No HOA code'})`);

        // Check if community belongs to the HOA
        if (community.hoaCommunityCode && community.hoaCommunityCode !== hoa.hoaCommunityCode) {
          console.log(`  ❌ MISMATCH: Community ${community.name} belongs to ${community.hoaCommunityCode}, not ${hoa.hoaCommunityCode}`);
          
          // Option 1: Remove community association (keep HOA-wide)
          await Document.findByIdAndUpdate(doc._id, { $unset: { communityId: 1 } });
          console.log(`  ✅ FIXED: Removed community association, keeping HOA-wide document`);
          fixedCount++;
          
        } else if (!community.hoaCommunityCode) {
          // Community has no HOA code - update it to match the document's HOA
          await Community.findByIdAndUpdate(community._id, { 
            hoaCommunityCode: hoa.hoaCommunityCode 
          });
          console.log(`  ✅ FIXED: Updated community ${community.name} to belong to HOA ${hoa.hoaCommunityCode}`);
          fixedCount++;
          
        } else {
          console.log(`  ✅ OK: Community and HOA are properly associated`);
        }

      } catch (error) {
        console.error(`❌ Error processing document ${doc._id}:`, error.message);
        errorCount++;
      }
    }

    console.log(`\n📊 FIXING RESULTS:`);
    console.log(`✅ Successfully fixed: ${fixedCount} associations`);
    console.log(`❌ Errors: ${errorCount} associations`);

    // Verify the fixes
    console.log('\n🔍 VERIFYING FIXES...');
    
    const remainingMismatches = await Document.aggregate([
      {
        $match: {
          hoaId: { $exists: true, $ne: null },
          communityId: { $exists: true, $ne: null }
        }
      },
      {
        $lookup: {
          from: 'hoas',
          localField: 'hoaId',
          foreignField: '_id',
          as: 'hoa'
        }
      },
      {
        $lookup: {
          from: 'communities',
          localField: 'communityId',
          foreignField: '_id',
          as: 'community'
        }
      },
      {
        $unwind: '$hoa'
      },
      {
        $unwind: '$community'
      },
      {
        $match: {
          $expr: {
            $and: [
              { $ne: ['$community.hoaCommunityCode', null] },
              { $ne: ['$community.hoaCommunityCode', '$hoa.hoaCommunityCode'] }
            ]
          }
        }
      }
    ]);

    if (remainingMismatches.length === 0) {
      console.log('✅ All community-HOA associations are now correct!');
    } else {
      console.log(`⚠️ ${remainingMismatches.length} mismatches still remain`);
      remainingMismatches.forEach(doc => {
        console.log(`  - Document: ${doc.title || doc.fileName}`);
        console.log(`    HOA: ${doc.hoa.hoaCommunityName} (${doc.hoa.hoaCommunityCode})`);
        console.log(`    Community: ${doc.community.name} (${doc.community.hoaCommunityCode})`);
      });
    }

  } catch (error) {
    console.error('❌ Error fixing community-HOA associations:', error);
    throw error;
  }
};

// Main execution
const runFix = async () => {
  try {
    await connectDB();
    await fixCommunityHOAAssociations();
    
    console.log('\n🎉 Community-HOA association fix completed!');
    
  } catch (error) {
    console.error('❌ Fix failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
    process.exit(0);
  }
};

// Run the fix
if (require.main === module) {
  runFix();
}

module.exports = { fixCommunityHOAAssociations };

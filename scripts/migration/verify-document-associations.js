/**
 * Script to Verify Document Database Associations
 * 
 * This script verifies that documents uploaded through the enhanced controller
 * have proper database associations (hoaId, communityId, uploadedBy).
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env' });

console.log(`Verifying document database associations`);

// Connect to MongoDB
const connectDB = async () => {
  try {
    const baseUri = 'mongodb+srv://frankiebruno:<EMAIL>/street-harmony?retryWrites=true&w=majority&appName=HOA-Data';
    
    await mongoose.connect(baseUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected to street-harmony database');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

// Define schemas
const documentSchema = new mongoose.Schema({}, { strict: false });
const userSchema = new mongoose.Schema({}, { strict: false });
const hoaSchema = new mongoose.Schema({}, { strict: false });
const communitySchema = new mongoose.Schema({}, { strict: false });

const Document = mongoose.model('Document', documentSchema);
const User = mongoose.model('User', userSchema);
const HOA = mongoose.model('HOA', hoaSchema);
const Community = mongoose.model('Community', communitySchema);

// Verify document associations
const verifyDocumentAssociations = async () => {
  try {
    console.log('\n🔍 VERIFYING DOCUMENT DATABASE ASSOCIATIONS...\n');

    // Get recent documents (last 10)
    const recentDocuments = await Document.find({})
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('uploadedBy', 'username email role')
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
      .populate('communityId', 'name');

    console.log(`📄 Found ${recentDocuments.length} recent documents\n`);

    for (let i = 0; i < recentDocuments.length; i++) {
      const doc = recentDocuments[i];
      console.log(`${i + 1}. Document: ${doc.title}`);
      console.log(`   ID: ${doc._id}`);
      console.log(`   Category: ${doc.category}`);
      console.log(`   Created: ${doc.createdAt}`);
      
      // Check associations
      console.log(`   📋 ASSOCIATIONS:`);
      
      // HOA Association
      if (doc.hoaId) {
        console.log(`   ✅ HOA: ${doc.hoaId.hoaCommunityName} (${doc.hoaId.hoaCommunityCode})`);
      } else {
        console.log(`   ❌ HOA: Missing`);
      }
      
      // Community Association
      if (doc.communityId) {
        console.log(`   ✅ Community: ${doc.communityId.name}`);
      } else {
        console.log(`   ⚠️  Community: Not specified`);
      }
      
      // User Association
      if (doc.uploadedBy) {
        console.log(`   ✅ Uploaded By: ${doc.uploadedBy.username} (${doc.uploadedBy.email}) - ${doc.uploadedBy.role}`);
      } else {
        console.log(`   ❌ Uploaded By: Missing`);
      }
      
      // File Information
      if (doc.file && doc.file.s3Key) {
        console.log(`   📁 File: ${doc.file.originalName} (${doc.file.s3Key})`);
      } else if (doc.s3Key) {
        console.log(`   📁 File: ${doc.fileName} (${doc.s3Key})`);
      } else {
        console.log(`   ❌ File: Missing S3 information`);
      }
      
      console.log(`   🔒 Public: ${doc.isPublic ? 'Yes' : 'No'}`);
      console.log(`   📊 Status: ${doc.status || 'active'}`);
      console.log('');
    }

    // Check for documents missing critical associations
    const documentsWithMissingHOA = await Document.countDocuments({ hoaId: { $exists: false } });
    const documentsWithMissingUploader = await Document.countDocuments({ uploadedBy: { $exists: false } });
    
    console.log(`📊 ASSOCIATION SUMMARY:`);
    console.log(`   Total documents: ${await Document.countDocuments({})}`);
    console.log(`   Missing HOA association: ${documentsWithMissingHOA}`);
    console.log(`   Missing uploader association: ${documentsWithMissingUploader}`);
    
    // Check <EMAIL> specifically
    const testAdmin = await User.findOne({ email: '<EMAIL>' });
    if (testAdmin) {
      console.log(`\n👤 <EMAIL> VERIFICATION:`);
      console.log(`   User ID: ${testAdmin._id}`);
      console.log(`   Role: ${testAdmin.role}`);
      console.log(`   HOA ID: ${testAdmin.hoaId}`);
      console.log(`   Community ID: ${testAdmin.communityId}`);
      console.log(`   HOA Community Code: ${testAdmin.hoaCommunityCode}`);
      
      // Check documents uploaded by this user
      const userDocuments = await Document.find({ uploadedBy: testAdmin._id });
      console.log(`   Documents uploaded: ${userDocuments.length}`);
      
      if (userDocuments.length > 0) {
        console.log(`   Recent uploads:`);
        userDocuments.slice(-3).forEach((doc, index) => {
          console.log(`     ${index + 1}. ${doc.title} (${doc.category}) - ${doc.createdAt}`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Error verifying document associations:', error);
    throw error;
  }
};

// Main execution
const runVerification = async () => {
  try {
    await connectDB();
    await verifyDocumentAssociations();
    
    console.log('\n🎉 Verification completed successfully!');
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
    process.exit(0);
  }
};

// Run the verification
if (require.main === module) {
  runVerification();
}

module.exports = { verifyDocumentAssociations };

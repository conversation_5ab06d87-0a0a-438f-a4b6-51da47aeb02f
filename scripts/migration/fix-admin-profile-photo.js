/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// Script to fix admin profile photo
require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/user');
const fs = require('fs');
const path = require('path');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
.then(() => console.log('MongoDB connected successfully'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Create a default profile photo for the admin user
async function fixAdminProfilePhoto() {
  try {
    // Find the admin user by username
    const adminUser = await User.findOne({ username: 'fbruno' });
    
    if (!adminUser) {
      console.error('Admin user fbruno not found');
      process.exit(1);
    }
    
    console.log('Found admin user:', adminUser.username);
    
    // Check if the user already has a profile photo
    if (adminUser.profilePhoto) {
      console.log('Admin user already has a profile photo:', adminUser.profilePhoto);
      
      // Check if the file exists
      const photoPath = path.join(__dirname, '../uploads', adminUser.profilePhoto);
      if (fs.existsSync(photoPath)) {
        console.log('Profile photo file exists at:', photoPath);
      } else {
        console.log('Profile photo file does not exist, will reset the profile photo field');
        adminUser.profilePhoto = null;
        await adminUser.save();
        console.log('Reset profile photo field to null');
      }
    } else {
      console.log('Admin user does not have a profile photo');
    }
    
    // Ensure the uploads directory exists
    const uploadsDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log('Created uploads directory');
    }
    
    console.log('Admin profile photo check complete');
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('MongoDB disconnected');
    
  } catch (error) {
    console.error('Error fixing admin profile photo:', error);
    process.exit(1);
  }
}

// Run the function
fixAdminProfilePhoto();

/**
 * Migration script to update existing data to work with the new schema changes
 * This script ensures backward compatibility with existing data
 *
 * Run this script once after deploying the updated models
 */
require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/user');
const HOA = require('../models/hoa');
const Community = require('../models/community');

// Set to true to perform actual updates, false to just simulate (dry run)
const PERFORM_UPDATES = true;

// Load environment variables
const environment = process.env.NODE_ENV || 'development';
const envFile = environment === 'production' ? '.env.production' : '.env.development';
console.log(`Loading environment: ${environment} from ${envFile}`);
require('dotenv').config({ path: envFile });

// Get MongoDB URI
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/hoa-management';
console.log(`Using MongoDB URI: ${MONGODB_URI.substring(0, 20)}...`);

// Connect to MongoDB
mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected for migration'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function migrateHOAs() {
  console.log('\n=== Migrating HOAs ===');

  try {
    // Get all existing HOAs
    const hoas = await HOA.find({});
    console.log(`Found ${hoas.length} HOAs to migrate`);

    let updated = 0;

    for (const hoa of hoas) {
      // Check if HOA already has verification status
      if (!hoa.verificationStatus) {
        console.log(`Updating HOA: ${hoa.hoaCommunityName} (${hoa._id})`);

        // Set default values for new fields
        const updates = {
          verificationStatus: 'approved', // Assume existing HOAs are approved
          subscription: {
            tier: 'basic',
            status: 'active',
            unitCount: 0
          }
        };

        if (PERFORM_UPDATES) {
          await HOA.updateOne({ _id: hoa._id }, { $set: updates });
          updated++;
        } else {
          console.log('  [DRY RUN] Would update with:', updates);
          updated++;
        }
      } else {
        console.log(`HOA already migrated: ${hoa.hoaCommunityName}`);
      }
    }

    console.log(`Migration completed. ${updated} HOAs updated.`);
  } catch (error) {
    console.error('Error migrating HOAs:', error);
  }
}

async function createDefaultCommunities() {
  console.log('\n=== Creating Default Communities ===');

  try {
    // Get all HOAs
    const hoas = await HOA.find({});
    console.log(`Found ${hoas.length} HOAs to create default communities for`);

    let created = 0;

    for (const hoa of hoas) {
      // Check if HOA already has a default community
      const existingCommunity = await Community.findOne({ hoaId: hoa._id });

      if (!existingCommunity) {
        console.log(`Creating default community for HOA: ${hoa.hoaCommunityName} (${hoa._id})`);

        // Generate a community code based on HOA code
        const communityCode = await Community.generateCommunityCode(hoa.hoaCommunityCode);

        // Create a default community for this HOA
        const newCommunity = new Community({
          name: `${hoa.hoaCommunityName} - Main`,
          description: `Default community for ${hoa.hoaCommunityName}`,
          streetAddress: hoa.hoaStreetAddress,
          city: hoa.hoaCity,
          state: hoa.hoaState,
          zipCode: hoa.hoaZipCode,
          communityCode,
          hoaId: hoa._id,
          type: 'mixed',
          status: 'active',
          visibility: 'public',
          permissions: {
            memberVisibility: true,
            financeVisibility: false,
            documentVisibility: true
          }
        });

        if (PERFORM_UPDATES) {
          await newCommunity.save();
          created++;
        } else {
          console.log('  [DRY RUN] Would create community:', newCommunity);
          created++;
        }
      } else {
        console.log(`HOA already has a community: ${hoa.hoaCommunityName}`);
      }
    }

    console.log(`Default communities creation completed. ${created} communities created.`);
  } catch (error) {
    console.error('Error creating default communities:', error);
  }
}

async function migrateUsers() {
  console.log('\n=== Migrating Users ===');

  try {
    // Get all users
    const users = await User.find({});
    console.log(`Found ${users.length} users to migrate`);

    let updated = 0;

    for (const user of users) {
      // Check if user needs migration (doesn't have propertyType)
      if (!user.propertyType) {
        console.log(`Updating user: ${user.username} (${user._id})`);

        // Set default values for new fields
        const updates = {
          propertyType: 'owner'
        };

        // If user has an HOA, try to find the default community
        if (user.hoaId) {
          const community = await Community.findOne({ hoaId: user.hoaId });
          if (community) {
            updates.communityId = community._id;
            console.log(`  Assigning to community: ${community.name}`);
          }
        }

        if (PERFORM_UPDATES) {
          await User.updateOne({ _id: user._id }, { $set: updates });
          updated++;
        } else {
          console.log('  [DRY RUN] Would update with:', updates);
          updated++;
        }
      } else {
        console.log(`User already migrated: ${user.username}`);
      }
    }

    console.log(`Migration completed. ${updated} users updated.`);
  } catch (error) {
    console.error('Error migrating users:', error);
  }
}

async function runMigration() {
  console.log('Starting migration...');
  console.log(`Mode: ${PERFORM_UPDATES ? 'LIVE' : 'DRY RUN'}`);

  try {
    // Run migrations in sequence
    await migrateHOAs();
    await createDefaultCommunities();
    await migrateUsers();

    console.log('\nMigration completed successfully!');

    if (!PERFORM_UPDATES) {
      console.log('\nThis was a DRY RUN. No actual changes were made.');
      console.log('To perform actual updates, set PERFORM_UPDATES to true and run again.');
    }
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // Close MongoDB connection
    mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

// Run the migration
runMigration();

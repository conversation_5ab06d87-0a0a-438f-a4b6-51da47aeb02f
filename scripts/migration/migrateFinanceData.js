/**
 * Migration script to update existing finance entries with a default creator
 * Run this script once after deploying the updated code
 *
 * This script includes safety checks and a dry-run mode to prevent accidental data changes
 */
require('dotenv').config();
const mongoose = require('mongoose');
const Finance = require('../models/finance');
const User = require('../models/user');

// Set to true to perform actual updates, false to just simulate (dry run)
const PERFORM_UPDATES = false;

async function migrateFinanceData() {
  try {
    console.log('\n=== Finance Data Migration Script ===');
    console.log(`Mode: ${PERFORM_UPDATES ? 'LIVE UPDATE' : 'DRY RUN (no changes will be made)'}`);
    console.log('======================================\n');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Find an admin user to set as the default creator
    const adminUser = await User.findOne({ role: 'admin' });

    if (!adminUser) {
      console.error('❌ No admin user found. Cannot proceed with migration.');
      process.exit(1);
    }

    console.log(`✅ Using admin user ${adminUser.username} (${adminUser._id}) as default creator`);

    // Find all finance entries that don't have a createdBy field
    const entriesToUpdate = await Finance.find({ createdBy: { $exists: false } });
    console.log(`✅ Found ${entriesToUpdate.length} finance entries without a creator`);

    if (entriesToUpdate.length === 0) {
      console.log('✅ No entries to update. Migration complete.');
      process.exit(0);
    }

    // Show sample of entries that would be updated
    if (entriesToUpdate.length > 0) {
      const sample = entriesToUpdate.slice(0, Math.min(3, entriesToUpdate.length));
      console.log('\nSample entries that will be updated:');
      sample.forEach((entry, i) => {
        console.log(`${i+1}. ID: ${entry._id}, Type: ${entry.type}, Amount: ${entry.amount}, Category: ${entry.category}`);
      });
      console.log('');
    }

    if (!PERFORM_UPDATES) {
      console.log('\n⚠️ DRY RUN COMPLETE - No changes were made');
      console.log('To perform actual updates, set PERFORM_UPDATES = true in the script and run again.');
      process.exit(0);
    }

    // Confirm before proceeding with updates
    console.log(`\n⚠️ About to update ${entriesToUpdate.length} finance entries with creator: ${adminUser.username}`);
    console.log('Press Ctrl+C now to abort, or wait 5 seconds to continue...');

    // Wait for 5 seconds to allow cancellation
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Update all entries with the admin user as creator
    const updateResult = await Finance.updateMany(
      { createdBy: { $exists: false } },
      { $set: { createdBy: adminUser._id } }
    );

    console.log(`\n✅ Updated ${updateResult.modifiedCount} finance entries`);
    console.log('✅ Migration complete');

    process.exit(0);
  } catch (error) {
    console.error('\n❌ Migration error:', error);
    process.exit(1);
  }
}

migrateFinanceData();

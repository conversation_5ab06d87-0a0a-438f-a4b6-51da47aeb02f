/**
 * Simple Migration Runner for Production
 * This script can be run on Heroku to safely migrate existing HOAs
 */

const mongoose = require('mongoose');

// Use existing database connection from environment
const connectDB = async () => {
  const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI;

  if (!mongoUri) {
    console.error('❌ MongoDB URI not found (checked MONGODB_URI and MONGO_URI)');
    process.exit(1);
  }

  try {
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Import HOA model
const HOA = require('../models/hoa');

const migrateHOAs = async () => {
  try {
    console.log('\n🚀 Starting HOA Email Configuration Migration...');
    
    // Find HOAs without email configuration
    const hoasToMigrate = await HOA.find({
      'emailConfig.configured': { $exists: false }
    });
    
    console.log(`📊 Found ${hoasToMigrate.length} HOAs to migrate`);
    
    if (hoasToMigrate.length === 0) {
      console.log('✅ All HOAs already have email configuration');
      return;
    }
    
    let migrated = 0;
    
    // Migrate each HOA
    for (const hoa of hoasToMigrate) {
      try {
        await HOA.updateOne(
          { 
            _id: hoa._id,
            'emailConfig.configured': { $exists: false }
          },
          {
            $set: {
              emailConfig: {
                configured: false,
                provider: 'gmail',
                email: '',
                password: '',
                displayName: hoa.hoaCommunityName || 'HOA Administration',
                smtpConfig: {
                  host: '',
                  port: 587,
                  secure: false
                },
                verificationStatus: 'not_verified',
                lastVerified: null,
                configuredAt: null,
                configuredBy: null
              }
            }
          }
        );
        
        console.log(`✅ Migrated: ${hoa.hoaCommunityName} (${hoa.hoaCommunityCode})`);
        migrated++;
        
      } catch (error) {
        console.error(`❌ Failed to migrate ${hoa.hoaCommunityCode}:`, error.message);
      }
    }
    
    console.log(`\n📊 Migration complete: ${migrated}/${hoasToMigrate.length} HOAs migrated`);
    
    // Verify results
    const totalHOAs = await HOA.countDocuments({});
    const hoasWithConfig = await HOA.countDocuments({
      'emailConfig.configured': { $exists: true }
    });
    
    console.log(`\n🔍 Verification:`);
    console.log(`   Total HOAs: ${totalHOAs}`);
    console.log(`   HOAs with email config: ${hoasWithConfig}`);
    console.log(`   Success rate: ${Math.round((hoasWithConfig / totalHOAs) * 100)}%`);
    
    if (hoasWithConfig === totalHOAs) {
      console.log('✅ All HOAs successfully migrated!');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
};

const main = async () => {
  try {
    await connectDB();
    await migrateHOAs();
    console.log('\n🎉 Migration completed successfully!');
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { migrateHOAs };

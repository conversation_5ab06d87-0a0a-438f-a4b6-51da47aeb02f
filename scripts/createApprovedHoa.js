/**
 * <PERSON><PERSON><PERSON> to create an approved HOA
 */
require('dotenv').config({ path: '.env.development' });
const mongoose = require('mongoose');
const HOA = require('../models/hoa');
const Community = require('../models/community');

// Connect to MongoDB
const MONGO_URI = process.env.MONGO_URI;
console.log(`Using MongoDB URI: ${MONGO_URI ? MONGO_URI.substring(0, 20) + '...' : 'undefined'}`);

mongoose.connect(MONGO_URI)
  .then(async () => {
    console.log('MongoDB connected');
    
    try {
      // Generate a unique HOA community code
      const generateHoaCommunityCode = async () => {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let code;
        let isUnique = false;
        
        while (!isUnique) {
          code = '';
          for (let i = 0; i < 6; i++) {
            code += characters.charAt(Math.floor(Math.random() * characters.length));
          }
          
          // Check if code already exists
          const existingHoa = await HOA.findOne({ hoaCommunityCode: code });
          if (!existingHoa) {
            isUnique = true;
          }
        }
        
        return code;
      };
      
      // Generate a unique community code
      const generateCommunityCode = async (hoaCode) => {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let code;
        let isUnique = false;
        
        while (!isUnique) {
          code = '';
          for (let i = 0; i < 4; i++) {
            code += characters.charAt(Math.floor(Math.random() * characters.length));
          }
          
          const fullCode = `${hoaCode}-${code}`;
          
          // Check if code already exists
          const existingCommunity = await Community.findOne({ communityCode: fullCode });
          if (!existingCommunity) {
            isUnique = true;
          }
        }
        
        return `${hoaCode}-${code}`;
      };
      
      // Create a new HOA
      const hoaCommunityCode = await generateHoaCommunityCode();
      
      const newHoa = new HOA({
        hoaCommunityName: 'Test HOA 5',
        hoaCommunityCode,
        hoaStreetAddress: '123 Test Street',
        hoaCity: 'Test City',
        hoaState: 'TS',
        hoaZipCode: '12345',
        contactEmail: '<EMAIL>',
        contactPhone: '************',
        einNumber: '12-3456789',
        verificationStatus: 'approved',
        verificationNotes: 'Approved automatically',
        verifiedAt: new Date(),
        subscription: {
          tier: 'basic',
          unitCount: 50,
          status: 'active'
        },
        hoaPaymentInfo: {
          paymentMethod: 'online',
          dueDate: 1
        }
      });
      
      const savedHoa = await newHoa.save();
      console.log('HOA created successfully:', {
        _id: savedHoa._id,
        name: savedHoa.hoaCommunityName,
        code: savedHoa.hoaCommunityCode,
        status: savedHoa.verificationStatus
      });
      
      // Create a default community for this HOA
      const communityCode = await generateCommunityCode(hoaCommunityCode);
      
      const newCommunity = new Community({
        name: `${savedHoa.hoaCommunityName} - Main`,
        description: `Default community for ${savedHoa.hoaCommunityName}`,
        streetAddress: savedHoa.hoaStreetAddress,
        city: savedHoa.hoaCity,
        state: savedHoa.hoaState,
        zipCode: savedHoa.hoaZipCode,
        communityCode,
        hoaId: savedHoa._id,
        type: 'mixed',
        status: 'active',
        visibility: 'public',
        permissions: {
          memberVisibility: true,
          financeVisibility: false,
          documentVisibility: true
        }
      });
      
      const savedCommunity = await newCommunity.save();
      console.log('Community created successfully:', {
        _id: savedCommunity._id,
        name: savedCommunity.name,
        code: savedCommunity.communityCode,
        hoaId: savedCommunity.hoaId
      });
    } catch (error) {
      console.error('Error:', error);
    } finally {
      // Close MongoDB connection
      mongoose.connection.close();
      console.log('MongoDB connection closed');
    }
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

# HOA Management App Scripts

This directory contains utility scripts for managing the HOA Management App.

## Available Scripts

### 1. Update HOA Users to Admin Role

This script finds all users who have an HOA code but don't have the 'admin' role, and updates their role to 'admin'.

**Usage:**
```bash
# Run locally
node scripts/update_hoa_users_to_admin.js

# Run on Heroku
heroku run node scripts/update_hoa_users_to_admin.js --app hoa-management-app-dad2f9d126ae
```

### 2. Update Specific User to Admin Role

This script updates a specific user's role to 'admin' based on their email address.

**Usage:**
```bash
# Run locally
node scripts/update_specific_user.js <EMAIL>

# Run on Heroku
heroku run node scripts/update_specific_user.js <EMAIL> --app hoa-management-app-dad2f9d126ae
```

## Running Scripts on Heroku

To run these scripts on your Heroku production environment:

1. Make sure you have the Heroku CLI installed and are logged in
2. Push these scripts to your Heroku app (they should be included in your git repository)
3. Run the script using the `heroku run` command as shown above

## Important Notes

- These scripts will modify your database, so make sure you understand what they do before running them
- It's recommended to back up your database before running these scripts
- The scripts will log all changes they make, so you can review what was updated
- If you encounter any issues, check the logs for error messages

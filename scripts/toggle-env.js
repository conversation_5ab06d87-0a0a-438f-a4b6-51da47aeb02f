/**
 * Script to toggle between different environments
 *
 * Usage:
 * - To use local environment: node scripts/toggle-env.js local
 * - To use staging environment: node scripts/toggle-env.js staging
 * - To use production environment: node scripts/toggle-env.js prod
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const ENV_DEV_PATH = path.resolve(__dirname, '../.env.development');
const ENV_STAGING_PATH = path.resolve(__dirname, '../.env.staging');
const ENV_PROD_PATH = path.resolve(__dirname, '../.env.production');

// Check if .env.development exists, if not create it
if (!fs.existsSync(ENV_DEV_PATH)) {
  fs.writeFileSync(ENV_DEV_PATH,
    'VITE_API_URL=http://localhost:5001\n' +
    'VITE_SOCKET_URL=http://localhost:5001'
  );
  console.log('Created .env.development file with local URLs');
}

// Get the environment to use from command line arguments
const args = process.argv.slice(2);
const env = args[0]?.toLowerCase();

if (env === 'local') {
  try {
    if (fs.existsSync(path.resolve(__dirname, '../.env'))) {
      fs.unlinkSync(path.resolve(__dirname, '../.env'));
    }
    fs.copyFileSync(ENV_DEV_PATH, path.resolve(__dirname, '../.env'));
    console.log('Switched to LOCAL environment');
    console.log('API URL: http://localhost:5001');
  } catch (error) {
    console.error('Error switching to local environment:', error);
  }
} else if (env === 'staging') {
  try {
    if (fs.existsSync(path.resolve(__dirname, '../.env'))) {
      fs.unlinkSync(path.resolve(__dirname, '../.env'));
    }
    fs.copyFileSync(ENV_STAGING_PATH, path.resolve(__dirname, '../.env'));
    console.log('Switched to STAGING environment');

    // Read the staging URL from .env.staging
    const stagingEnv = fs.readFileSync(ENV_STAGING_PATH, 'utf8');
    const apiUrl = stagingEnv.match(/VITE_API_URL=(.*)/)?.[1];
    console.log(`API URL: ${apiUrl}`);
  } catch (error) {
    console.error('Error switching to staging environment:', error);
  }
} else if (env === 'prod') {
  try {
    if (fs.existsSync(path.resolve(__dirname, '../.env'))) {
      fs.unlinkSync(path.resolve(__dirname, '../.env'));
    }
    fs.copyFileSync(ENV_PROD_PATH, path.resolve(__dirname, '../.env'));
    console.log('Switched to PRODUCTION environment');

    // Read the production URL from .env.production
    const prodEnv = fs.readFileSync(ENV_PROD_PATH, 'utf8');
    const apiUrl = prodEnv.match(/VITE_API_URL=(.*)/)?.[1];
    console.log(`API URL: ${apiUrl}`);
  } catch (error) {
    console.error('Error switching to production environment:', error);
  }
} else {
  console.log('Please specify an environment: local, staging, or prod');
  console.log('Usage:');
  console.log('  node scripts/toggle-env.js local    # Switch to local environment');
  console.log('  node scripts/toggle-env.js staging  # Switch to staging environment');
  console.log('  node scripts/toggle-env.js prod     # Switch to production environment');
}

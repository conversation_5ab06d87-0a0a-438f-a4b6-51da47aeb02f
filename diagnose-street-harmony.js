/**
 * Diagnose street-harmony <NAME_EMAIL> and Deraney Lane
 */

require('dotenv').config({ path: '.env.local.active' });
const mongoose = require('mongoose');
const User = require('./models/user');
const Community = require('./models/community');
const HOA = require('./models/hoa');

async function diagnoseStreetHarmony() {
  console.log('🔍 DIAGNOSING STREET-HARMONY DATABASE');
  console.log('====================================');
  console.log('MongoDB URI:', process.env.MONGODB_URI);
  
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    console.log('Database:', mongoose.connection.db.databaseName);

    // 1. Get <EMAIL> exact data
    console.log('\n1️⃣ <EMAIL> DATA:');
    console.log('--------------------------------');
    const admin = await User.findOne({ email: '<EMAIL>' });
    if (!admin) {
      console.log('❌ <EMAIL> not found');
      return;
    }
    
    console.log('Admin User Fields:');
    console.log('- _id:', admin._id);
    console.log('- email:', admin.email);
    console.log('- role:', admin.role);
    console.log('- hoaId:', admin.hoaId);
    console.log('- communityId:', admin.communityId);
    console.log('- hoaCommunityCode:', admin.hoaCommunityCode);
    console.log('- communityCode:', admin.communityCode);

    // 2. Search for Deraney Lane in street-harmony database
    console.log('\n2️⃣ SEARCHING FOR DERANEY LANE IN STREET-HARMONY:');
    console.log('--------------------------------------------------');
    
    // Try different variations
    const deraneyVariations = [
      { name: 'Deraney Lane', query: { name: 'Deraney Lane' } },
      { name: 'deraney (case insensitive)', query: { name: { $regex: /deraney/i } } },
      { name: 'Deraney Ln', query: { name: 'Deraney Ln' } },
      { name: 'contains "lane"', query: { name: { $regex: /lane/i } } }
    ];
    
    for (const variation of deraneyVariations) {
      const communities = await Community.find(variation.query);
      console.log(`\n${variation.name}: Found ${communities.length} communities`);
      communities.forEach((community, index) => {
        console.log(`  ${index + 1}. "${community.name}" (ID: ${community._id})`);
        console.log(`     - communityCode: ${community.communityCode}`);
        console.log(`     - hoaCommunityCode: ${community.hoaCommunityCode}`);
        console.log(`     - hoaId: ${community.hoaId}`);
      });
    }

    // 3. Check communities that match admin's HOA codes
    console.log('\n3️⃣ COMMUNITIES MATCHING ADMIN HOA CODES:');
    console.log('------------------------------------------');
    const adminHoaCodes = admin.hoaCommunityCode || [];
    console.log('Admin HOA Codes:', adminHoaCodes);
    
    for (const hoaCode of adminHoaCodes) {
      const matchingCommunities = await Community.find({
        hoaCommunityCode: hoaCode
      });
      
      console.log(`\nCommunities for HOA Code "${hoaCode}" (${matchingCommunities.length} found):`);
      matchingCommunities.forEach((community, index) => {
        console.log(`  ${index + 1}. "${community.name}" (ID: ${community._id})`);
        console.log(`     - communityCode: ${community.communityCode}`);
        console.log(`     - hoaId: ${community.hoaId}`);
      });
    }

    // 4. Check users with admin's HOA codes
    console.log('\n4️⃣ USERS WITH ADMIN HOA CODES:');
    console.log('--------------------------------');
    for (const hoaCode of adminHoaCodes) {
      const usersWithHoaCode = await User.find({
        hoaCommunityCode: hoaCode,
        isDeleted: { $ne: true }
      }).select('email fullName hoaCommunityCode verificationStatus');
      
      console.log(`\nUsers with HOA Code "${hoaCode}" (${usersWithHoaCode.length} found):`);
      usersWithHoaCode.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.fullName} (${user.email})`);
        console.log(`     - verificationStatus: ${user.verificationStatus}`);
      });
    }

    // 5. Check what verification statuses exist
    console.log('\n5️⃣ VERIFICATION STATUSES IN STREET-HARMONY:');
    console.log('--------------------------------------------');
    const verificationStatuses = await User.distinct('verificationStatus');
    console.log('Available verification statuses:', verificationStatuses);
    
    // Count users by status
    for (const status of verificationStatuses) {
      const count = await User.countDocuments({ verificationStatus: status });
      console.log(`- ${status}: ${count} users`);
    }

    // 6. Test the exact query that should work
    console.log('\n6️⃣ TESTING EXACT ANNOUNCEMENT QUERY:');
    console.log('-------------------------------------');
    
    // Find a community to test with
    const testCommunity = await Community.findOne({
      hoaCommunityCode: { $in: adminHoaCodes }
    });
    
    if (testCommunity) {
      console.log(`Testing with community: "${testCommunity.name}"`);
      console.log(`Community hoaCommunityCode: ${testCommunity.hoaCommunityCode}`);
      
      // Test the current query
      const currentQuery = {
        hoaCommunityCode: testCommunity.hoaCommunityCode,
        isDeleted: { $ne: true }
      };
      
      const residents = await User.find(currentQuery).select('email fullName verificationStatus');
      console.log(`\nCurrent query results: ${residents.length} users found`);
      residents.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.fullName} (${user.email}) - Status: ${user.verificationStatus}`);
      });
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the diagnosis
diagnoseStreetHarmony();

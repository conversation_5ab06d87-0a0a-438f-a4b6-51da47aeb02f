# Local Testing with Production Database
# Use this to test locally while connected to production database

# Server Configuration
NODE_ENV=development
PORT=5001
JWT_SECRET=street-harmony-secret-key-2024

# Production MongoDB Configuration (Read-only testing recommended)
MONGODB_URI=mongodb+srv://frankiebruno:<EMAIL>/street-harmony?retryWrites=true&w=majority&appName=HOA-Data

# AWS S3 Configuration (Production)
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=ryhqr8hQbWMKh8KT9CKVcWRtu47ZQx5gc5vl5RCj
AWS_REGION=us-east-2
AWS_S3_BUCKET_NAME=hoaflo-files-prod

# Email Configuration (Outlook Business - Production)
OUTLOOK_SMTP_HOST=smtp-mail.outlook.com
OUTLOOK_SMTP_PORT=587
OUTLOOK_SMTP_SECURE=false
OUTLOOK_SMTP_TLS=true
OUTLOOK_EMAIL_USER=<EMAIL>
OUTLOOK_EMAIL_PASSWORD=HoaFlow@2025

# Azure OAuth Configuration for Outlook Business
AZURE_CLIENT_SECRET=****************************************
AZURE_CLIENT_SECRET_ID=e4d72c27-8c9a-4cb4-a2ae-103f9488fcce
AZURE_TENANT_ID=8cae2d00-6f54-4211-9e39-9a239124b6b5
AZURE_CLIENT_ID=86068cd6-10f0-4d7b-a142-de1371ba45e4

# Stripe Configuration (Test Keys for Local Testing)
STRIPE_SECRET_KEY=sk_test_51REKUtD14Lqis0JWM7Kfdd1JkfgmT3mTmOCLFmczCu9Nw6zpSvhUek06lUbYG3AA2CXMHo1yobcLL6iF25UWQQKM00lDvyVvaW
STRIPE_PUBLISHABLE_KEY=pk_test_51REKUtD14Lqis0JWM7Kfdd1JkfgmT3mTmOCLFmczCu9Nw6zpSvhUek06lUbYG3AA2CXMHo1yobcLL6iF25UWQQKM00lDvyVvaW

# Local Development Settings
CORS_ORIGINS=http://localhost:8080,http://localhost:3000,http://localhost:5173
FRONTEND_URL=http://localhost:8080
EMAIL_SERVICE=outlook
EMAIL_FROM_NAME=HOAFLO Support (Local Testing)
EMAIL_FROM_ADDRESS=<EMAIL>

# Safety Settings for Production Database Testing
ENABLE_DATA_MODIFICATION=false
ENABLE_EMAIL_TESTING=true
ENABLE_FILE_UPLOAD_TESTING=true
READ_ONLY_MODE=false

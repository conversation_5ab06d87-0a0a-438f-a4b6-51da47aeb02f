# HOAFLO Technical Implementation Guide

## Code Changes Summary

This document provides detailed technical information about all code modifications made in the `feature/side_panel/documents/announcements` branch.

## Backend Changes (HOA-BACKEND)

### 1. Members System Enhancement

#### Modified Files:

**`controllers/member/membersController.js`** (Lines 12-286)
- **Purpose:** Enhanced member access control with street-level filtering
- **Key Changes:**
  - Added role-based query filtering for regular users (lines 40-65)
  - Implemented community-based member isolation
  - Enhanced company admin oversight capabilities
  - Added comprehensive logging for debugging

**`routes/member/members.js`** (Lines 8-16)
- **Purpose:** Role-based route protection
- **Key Changes:**
  - Added `operationalAdmin` middleware for member management
  - Separated company admin oversight from operational management

#### New Database Queries:
```javascript
// Regular users - street-level filtering
query.communityId = req.user.communityId;
query.hoaId = req.user.hoaId;

// Company admin - cross-HOA access
query = { role: 'member' }; // No HOA restrictions
```

### 2. Document System Overhaul

#### New Files Created:

**`controllers/document/enhancedDocumentController.js`** (Complete new implementation)
- **Purpose:** AWS S3 integrated document management
- **Key Features:**
  - S3 file upload with metadata storage (lines 140-180)
  - Signed URL generation for secure downloads
  - Role-based access control for document viewing
  - File size tracking and display

**`config/s3Config.js`** (Lines 1-250)
- **Purpose:** AWS S3 configuration and utilities
- **Key Features:**
  - Multiple storage configurations for different file types
  - Hierarchical file organization by HOA
  - Pre-configured multer-s3 instances
  - Signed URL generation utilities

**`models/document.js`** (Enhanced schema)
- **Purpose:** Standardized file reference with backward compatibility
- **Key Changes:**
  - New standardized `file` schema (lines 19-50)
  - Legacy field support for migration
  - Enhanced metadata and versioning
  - Role-based permission system

#### S3 Integration Details:
```javascript
// File upload configuration
const s3Storage = multerS3({
  s3: s3Client,
  bucket: process.env.AWS_S3_BUCKET_NAME,
  key: (req, file, cb) => {
    const hoaId = req.user.hoaId || 'default';
    const key = `hoa-${hoaId}/documents/${Date.now()}-${file.originalname}`;
    cb(null, key);
  }
});
```

### 3. Announcement System Implementation

#### New Files Created:

**`routes/announcements.js`** (Complete new routing system)
- **Purpose:** Role-based announcement routing
- **Key Routes:**
  - `/streets` - Get available streets for HOA admin
  - `/send/street` - Send street-level announcements
  - `/send/hoa` - Send HOA-wide announcements (company admin)
  - File attachment support with multer middleware

**`controllers/announcementController.js`** (Lines 1-900+)
- **Purpose:** Comprehensive announcement logic
- **Key Functions:**
  - `getStreetsForAdmin()` - Street targeting for HOA admins
  - `sendStreetAnnouncement()` - Street-level announcement sending
  - `sendHOAAnnouncement()` - HOA-wide announcement sending
  - Email integration with attachment support

**`models/announcementAttachment.js`** (New model)
- **Purpose:** Persistent storage for announcement attachments
- **Key Features:**
  - Standardized file schema matching document model
  - HOA-scoped access control
  - Download tracking and analytics
  - Automatic cleanup capabilities

#### Email Integration:
```javascript
// Email template for street announcements
const emailTemplate = {
  subject: `📢 ${subject} - ${streetName}`,
  html: createEmailLayout(content, subject),
  attachments: attachmentData
};
```

### 4. Enhanced Middleware & Security

#### Modified Files:

**`middleware/upload.js`** (Updated for S3)
- **Purpose:** S3-integrated file upload middleware
- **Key Changes:**
  - Multiple storage configurations
  - File type validation
  - Size limits per file type
  - Error handling improvements

**`services/emailService.js`** (Enhanced templates)
- **Purpose:** Rich email templates with attachment support
- **Key Changes:**
  - Street announcement templates
  - HOA announcement templates
  - Attachment handling in emails
  - Outlook Business integration

## Frontend Changes (HOA-FRONT)

### 1. Members Directory Enhancement

#### Modified Files:

**`src/pages/Members.tsx`** (Lines 14-30)
- **Purpose:** Enhanced member directory interface
- **Key Changes:**
  - Dynamic title based on user role ("Neighbors" vs "Members")
  - Role-based action buttons
  - Responsive design implementation

**`src/components/StreetSelector.tsx`** (Lines 55-193)
- **Purpose:** Community selection with HOA filtering
- **Key Changes:**
  - HOA-based community filtering
  - Subscription status validation
  - Enhanced error handling and loading states

### 2. Document Management Interface

#### Modified Files:

**`src/pages/Documents.tsx`** (Lines 22-118)
- **Purpose:** Enhanced document management interface
- **Key Changes:**
  - Company admin HOA selector (lines 58-75)
  - Responsive grid layout
  - Role-based upload restrictions

**`src/components/DocumentUpload.tsx`** (Lines 35-225)
- **Purpose:** Advanced file upload component
- **Key Changes:**
  - Drag-and-drop file upload
  - File type validation
  - Progress indicators
  - S3 integration with metadata

**`src/components/DocumentList.tsx`** (Enhanced display)
- **Purpose:** Responsive document listing
- **Key Changes:**
  - File size display instead of "unknown size"
  - Download functionality with signed URLs
  - Responsive card layouts
  - Role-based access controls

### 3. Announcement System Interface

#### New Files Created:

**`src/components/announcements/AnnouncementManager.tsx`** (Lines 60-187)
- **Purpose:** Main announcement interface
- **Key Features:**
  - Role-based UI adaptation
  - Quick stats display
  - Recent announcements view
  - Responsive design

**`src/components/announcements/EnhancedAnnouncementForm.tsx`** (Lines 360-388)
- **Purpose:** Comprehensive announcement composition
- **Key Features:**
  - Rich text editor
  - File attachment support
  - Recipient selection with checkboxes
  - Email confirmation options

#### Targeting Logic:
```javascript
// HOA Admin targeting
if (userRole === 'admin') {
  // Show streets within user's HOA
  targetOptions = userStreets;
} else if (userRole === 'company_admin') {
  // Show all HOAs and their streets
  targetOptions = allHOAs;
}
```

### 4. Responsive Sidebar Navigation

#### Modified Files:

**`src/components/Sidebar.tsx`** (Lines 1-311)
- **Purpose:** Mobile-first responsive sidebar
- **Key Changes:**
  - Mobile overlay with hamburger menu (lines 224-243)
  - Role-based navigation items (lines 168-219)
  - Touch-friendly 44px minimum targets
  - Collapsible design for desktop

**`src/components/Layout.tsx`** (Lines 8-25)
- **Purpose:** Main layout with responsive sidebar
- **Key Changes:**
  - Flex-based layout system
  - Mobile-safe spacing
  - Responsive container sizing

#### Navigation Structure:
```javascript
const coreLinks = [
  { name: 'Dashboard', icon: <Home />, path: '/dashboard' },
  { name: 'Documents', icon: <FileText />, path: '/documents' },
  { name: 'Members', icon: <Users />, path: '/admin/members' }
];
```

### 5. Responsive Design System

#### New Files Created:

**`RESPONSIVE_DESIGN_GUIDE.md`** (Comprehensive guide)
- **Purpose:** Design system documentation
- **Key Features:**
  - Breakpoint system definition
  - Touch target guidelines
  - Component-specific patterns
  - Accessibility requirements

**`src/styles/responsive.css`** (Custom utilities)
- **Purpose:** Custom responsive utilities
- **Key Features:**
  - Extra small breakpoint (475px)
  - Document card responsive layouts
  - Mobile-specific optimizations

## Database Schema Changes

### 1. Enhanced User Model

```javascript
// New community relationship fields
hoaId: [{ type: Schema.Types.ObjectId, ref: 'HOA' }],
communityId: [{ type: Schema.Types.ObjectId, ref: 'Community' }],
hoaCommunityCode: [{ type: String, trim: true, uppercase: true }]
```

### 2. Standardized File Schema

```javascript
// Reusable file reference schema
const fileSchema = new Schema({
  s3Key: { type: String, required: true, index: true },
  s3Bucket: { type: String, required: true },
  originalName: { type: String, required: true },
  mimetype: { type: String, required: true },
  size: { type: Number, required: true, min: 0 },
  uploadedAt: { type: Date, default: Date.now }
});
```

### 3. New AnnouncementAttachment Model

```javascript
const announcementAttachmentSchema = new Schema({
  file: fileSchema,
  announcementId: { type: Schema.Types.ObjectId, ref: 'Announcement' },
  hoaId: { type: Schema.Types.ObjectId, ref: 'HOA' },
  uploadedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  status: { type: String, enum: ['active', 'deleted'], default: 'active' }
});
```

## API Endpoints Added/Modified

### Members API:
- `GET /api/members` - Enhanced with community filtering
- `GET /api/members/approved` - Street-level access control

### Documents API:
- `POST /api/documents/upload` - S3 integration
- `GET /api/documents/download/:id` - Signed URL downloads
- `GET /api/documents/stats` - Document statistics

### Announcements API:
- `GET /api/announcements/streets` - Available streets for admin
- `POST /api/announcements/send/street` - Street announcements
- `POST /api/announcements/send/hoa` - HOA announcements
- `GET /api/announcements/hoas` - Company admin HOA list

## Configuration Changes

### Environment Variables Added:
```bash
# AWS S3
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=ryhqr8hQbWMKh8KT9CKVcWRtu47ZQx5gc5vl5RCj
AWS_REGION=us-east-2
AWS_S3_BUCKET_NAME=hoaflo-files-prod

# Email Configuration
EMAIL_SERVICE=outlook
EMAIL_FROM_ADDRESS=<EMAIL>
OUTLOOK_APP_PASSWORD=[Azure App Password]
```

### Package Dependencies Added:

**Backend:**
- `@aws-sdk/client-s3` - S3 client
- `@aws-sdk/s3-request-presigner` - Signed URLs
- `multer-s3` - S3 upload middleware

**Frontend:**
- Enhanced Radix UI components
- Additional responsive utilities

## Testing & Validation

### Test Scripts Available:
- `npm run local:test` - Comprehensive system testing
- `npm run local:production` - Production database testing
- `npm run local:check` - Quick validation

### Critical Test Cases:
1. **Member Access Control:** Verify street-level filtering
2. **File Upload/Download:** Test S3 integration
3. **Email Delivery:** Validate announcement emails
4. **Responsive Design:** Test across device sizes
5. **Role-Based Access:** Verify permission systems

## Performance Optimizations

### Frontend:
- Code splitting for large components
- Lazy loading of announcement features
- Optimized bundle sizes with Vite
- Responsive image loading

### Backend:
- Database query optimization with indexes
- S3 signed URL caching
- Batch email processing
- Connection pooling for MongoDB

## Security Enhancements

### Access Control:
- JWT token validation on all routes
- Role-based middleware protection
- HOA-scoped data isolation
- File access through signed URLs

### Data Protection:
- Input validation with Joi schemas
- File type and size restrictions
- CORS configuration
- SQL injection prevention

---

*This technical guide covers all major code changes in the feature branch. For specific line-by-line changes, refer to the git diff between main and feature branches.*

# HOAFLO Backend File Structure Guide

## Overview
This document outlines the organized file structure of the HOAFLO backend after reorganization for improved maintainability and developer experience.

## Directory Structure

```
HOA-BACKEND/
├── config/                     # Configuration files
│   └── s3Config.js             # AWS S3 configuration
├── controllers/                # Business logic controllers
│   ├── admin/                  # Admin-related controllers
│   ├── announcement/           # Announcement system controllers
│   │   └── announcementController.js
│   ├── auth/                   # Authentication controllers
│   ├── budget/                 # Budget management controllers
│   ├── community/              # Community management controllers
│   ├── document/               # Document management controllers
│   ├── event/                  # Event management controllers
│   ├── finance/                # Financial controllers
│   ├── hoa/                    # HOA management controllers
│   ├── hoaRevenue/             # HOA revenue controllers
│   ├── maintenance/            # Maintenance request controllers
│   ├── member/                 # Member management controllers
│   ├── message/                # Messaging controllers
│   ├── mfa/                    # Multi-factor authentication controllers
│   ├── notification/           # Notification controllers
│   ├── password/               # Password management controllers
│   ├── payments/               # Payment processing controllers
│   ├── property/               # Property management controllers
│   ├── setting/                # Settings controllers
│   ├── stripe/                 # Stripe-specific controllers
│   │   └── stripeConnectController.js
│   ├── subscription/           # Subscription controllers
│   ├── task/                   # Task management controllers
│   ├── uploads/                # Upload handling controllers
│   ├── user/                   # User management controllers
│   └── vendor/                 # Vendor management controllers
│       └── vendorPayoutController.js
├── docs/                       # Documentation
│   ├── api/                    # API documentation
│   │   └── API_DOCUMENTATION.md
│   ├── deployment/             # Deployment guides
│   │   └── S3_SETUP_GUIDE.md
│   ├── project/                # Project documentation
│   │   ├── HOAFLO_DEVELOPMENT_REPORT.md
│   │   ├── TECHNICAL_IMPLEMENTATION_GUIDE.md
│   │   ├── FILE_CHANGES_INVENTORY.md
│   │   └── DEVELOPER_HANDOFF_CHECKLIST.md
│   └── SECURITY.md             # Security documentation
├── logs/                       # Application logs
├── middleware/                 # Express middleware
├── migrations/                 # Database migrations
├── models/                     # Mongoose data models
├── routes/                     # Express route definitions
│   ├── admin/                  # Admin routes
│   ├── announcement/           # Announcement routes
│   │   └── announcements.js
│   ├── auth/                   # Authentication routes
│   ├── budget/                 # Budget routes
│   ├── community/              # Community routes
│   ├── document/               # Document routes
│   ├── entitlement/            # Entitlement routes
│   ├── event/                  # Event routes
│   ├── finance/                # Finance routes
│   ├── hoa/                    # HOA routes
│   ├── hoaRevenue/             # HOA revenue routes
│   ├── maintenance/            # Maintenance routes
│   ├── member/                 # Member routes
│   ├── message/                # Message routes
│   ├── mfa/                    # MFA routes
│   ├── notification/           # Notification routes
│   ├── password/               # Password routes
│   ├── payments/               # Payment routes
│   ├── property/               # Property routes
│   ├── setting/                # Settings routes
│   ├── subscription/           # Subscription routes
│   ├── task/                   # Task routes
│   ├── test/                   # Test routes
│   │   └── test.js
│   ├── user/                   # User routes
│   ├── vendor/                 # Vendor routes
│   │   └── vendorPayouts.js
│   └── index.js                # Route aggregator
├── scripts/                    # Utility and maintenance scripts
│   ├── database/               # Database management scripts
│   │   ├── cleanupDatabase.js
│   │   ├── cleanupExceptTestUsers.js
│   │   ├── clearDataExceptAdmin.js
│   │   ├── checkAllData.js
│   │   ├── checkFinanceEntries.js
│   │   ├── viewDatabase.js
│   │   └── viewSpecificDatabase.js
│   ├── migration/              # Data migration scripts
│   │   ├── migrateFinanceData.js
│   │   ├── migrateHOAEmailConfig.js
│   │   ├── migrateModels.js
│   │   ├── runMigration.js
│   │   ├── audit-and-fix-document-associations.js
│   │   ├── fix-admin-hoa-association.js
│   │   ├── fix-admin-profile-photo.js
│   │   ├── fix-community-hoa-associations.js
│   │   ├── fixHoaAndUserAssociations.js
│   │   └── verify-document-associations.js
│   ├── testing/                # Testing and validation scripts
│   │   ├── test-document-management-system.js
│   │   ├── test-enhanced-document-system.js
│   │   ├── test-uploads.js
│   │   ├── testCommunity.js
│   │   ├── testLocalSetup.js
│   │   └── quickTest.js
│   ├── utilities/              # General utility scripts
│   │   ├── generate-jwt-secret.js
│   │   ├── toggle-env.js
│   │   ├── startLocal.js
│   │   └── seedLocalDatabase.js
│   └── README.md               # Scripts documentation
├── services/                   # External service integrations
├── uploads/                    # Local file uploads (legacy)
├── utils/                      # Utility functions
├── server.js                   # Main application entry point
├── socket.js                   # Socket.io configuration
├── db.js                       # Database connection
└── package.json                # Dependencies and scripts
```

## Key Changes Made

### 1. Controllers Organization
- **Moved loose controllers** into appropriate subdirectories:
  - `announcementController.js` → `controllers/announcement/`
  - `stripeConnectController.js` → `controllers/stripe/`
  - `vendorPayoutController.js` → `controllers/vendor/`

### 2. Routes Organization
- **Moved loose routes** into appropriate subdirectories:
  - `announcements.js` → `routes/announcement/`
  - `test.js` → `routes/test/`
  - `vendorPayouts.js` → `routes/vendor/`

### 3. Documentation Structure
- **Created organized documentation hierarchy**:
  - `docs/api/` - API documentation
  - `docs/deployment/` - Deployment guides
  - `docs/project/` - Project documentation

### 4. Scripts Organization
- **Organized scripts by function**:
  - `scripts/database/` - Database management
  - `scripts/migration/` - Data migrations
  - `scripts/testing/` - Testing scripts
  - `scripts/utilities/` - General utilities

## Updated Import Paths

### Routes Index (`routes/index.js`)
```javascript
// Updated paths
const testRoutes = require('./test/test');
const announcementRoutes = require('./announcement/announcements');
```

### Announcement Controller (`controllers/announcement/announcementController.js`)
```javascript
// Updated relative paths
const User = require('../../models/user');
const HOA = require('../../models/hoa');
// ... other imports with updated paths
```

### Announcement Routes (`routes/announcement/announcements.js`)
```javascript
// Updated relative paths
const announcementController = require('../../controllers/announcement/announcementController');
const auth = require('../../middleware/auth');
// ... other imports with updated paths
```

## Updated NPM Scripts

### Package.json Scripts
```json
{
  "scripts": {
    "generate-jwt": "node scripts/utilities/generate-jwt-secret.js",
    "local:seed": "cp .env.local.production .env.local.active && node scripts/utilities/seedLocalDatabase.js",
    "local:test": "node scripts/testing/testLocalSetup.js",
    "local:start": "node scripts/utilities/startLocal.js",
    "local:check": "node scripts/testing/quickTest.js"
  }
}
```

## Benefits of New Structure

### 1. **Improved Maintainability**
- Logical grouping of related files
- Easier to locate specific functionality
- Reduced cognitive load for developers

### 2. **Better Scalability**
- Clear structure for adding new features
- Consistent organization patterns
- Easier onboarding for new developers

### 3. **Enhanced Documentation**
- Centralized documentation structure
- Clear separation of concerns
- Better project overview

### 4. **Simplified Navigation**
- Intuitive file organization
- Reduced root directory clutter
- Easier IDE navigation

## Migration Notes

### Backward Compatibility
- All existing functionality preserved
- Import paths updated correctly
- NPM scripts updated to new locations

### Testing Verification
After reorganization, verify:
1. Application starts successfully
2. All routes function correctly
3. NPM scripts execute properly
4. Import statements resolve correctly

## Future Considerations

### Additional Organization Opportunities
- Consider organizing models by domain
- Group related middleware together
- Create feature-based organization for larger modules

### Maintenance
- Keep documentation updated with structure changes
- Maintain consistent naming conventions
- Regular cleanup of unused files

---

*This structure was implemented to improve developer experience and code maintainability while preserving all existing functionality.*

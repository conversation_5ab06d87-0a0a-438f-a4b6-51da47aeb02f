# AWS S3 Setup Guide for HOAFLO

## Required Environment Variables

Add these environment variables to your Heroku app and local development environment:

### AWS S3 Configuration
```bash
# AWS Credentials
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=hoaflo-files

# Optional: For custom S3 endpoint (if using S3-compatible service)
# AWS_ENDPOINT_URL=https://s3.amazonaws.com
```

## Setting Up AWS S3

### 1. Create S3 Bucket
1. Log into AWS Console
2. Navigate to S3 service
3. Create a new bucket named `hoaflo-files` (or your preferred name)
4. Choose your preferred region (e.g., `us-east-1`)
5. Configure bucket settings:
   - **Block Public Access**: Keep enabled for security
   - **Versioning**: Optional (recommended for document management)
   - **Encryption**: Enable server-side encryption

### 2. Create IAM User for HOAFLO
1. Navigate to IAM service in AWS Console
2. Create a new user named `hoaflo-s3-user`
3. Select "Programmatic access" (for API keys)
4. Attach the following policy:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::hoaflo-files",
                "arn:aws:s3:::hoaflo-files/*"
            ]
        }
    ]
}
```

5. Save the Access Key ID and Secret Access Key

### 3. Configure CORS for S3 Bucket
Add this CORS configuration to your S3 bucket:

```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
        "AllowedOrigins": [
            "http://localhost:3000",
            "https://your-frontend-domain.com",
            "https://hoa-backend-dev.herokuapp.com"
        ],
        "ExposeHeaders": ["ETag"]
    }
]
```

## Heroku Configuration

### Set Environment Variables in Heroku
```bash
heroku config:set AWS_ACCESS_KEY_ID=your_access_key_here
heroku config:set AWS_SECRET_ACCESS_KEY=your_secret_key_here
heroku config:set AWS_REGION=us-east-1
heroku config:set AWS_S3_BUCKET_NAME=hoaflo-files
```

### Or use Heroku Dashboard
1. Go to your Heroku app dashboard
2. Navigate to Settings tab
3. Click "Reveal Config Vars"
4. Add each environment variable

## Local Development Setup

### Create .env file in HOA-BACKEND directory:
```bash
# Copy your existing .env and add these S3 variables
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=hoaflo-files-dev
```

**Note**: Use a separate bucket for development (e.g., `hoaflo-files-dev`)

## File Organization in S3

Your files will be organized in S3 with this hierarchy:

```
hoaflo-files/
├── hoa-{hoaId}/
│   ├── documents/
│   ├── user_documents/
│   ├── hoa_documents/
│   ├── task_attachments/
│   ├── finance_documents/
│   └── profile_photos/
└── global/
    ├── documents/
    ├── user_documents/
    └── system/
```

## Migration from Local Storage

### Automatic Fallback
The system includes automatic fallback to local files during migration:
- New uploads go to S3
- Existing file requests try S3 first, then fall back to local storage
- This allows gradual migration without breaking existing functionality

### Manual Migration (Optional)
To migrate existing files to S3, you can:
1. Use AWS CLI to sync local uploads directory to S3
2. Update database records to include S3 keys
3. Remove local files after verification

## Security Features

### Signed URLs
- All file access uses signed URLs with 1-hour expiration
- No direct public access to S3 objects
- Maintains your existing role-based access controls

### Access Control
- Files are organized by HOA ID for isolation
- Role-based access controls are preserved
- Admin users can access all HOA files
- Members can only access their HOA's files

## Troubleshooting

### Common Issues

1. **Access Denied Errors**
   - Check IAM policy permissions
   - Verify bucket name in environment variables
   - Ensure AWS credentials are correct

2. **CORS Errors**
   - Update S3 bucket CORS configuration
   - Add your frontend domain to allowed origins

3. **File Not Found**
   - Check if file exists in S3 bucket
   - Verify S3 key format matches expected pattern
   - Check fallback to local storage logs

### Testing S3 Connection
Add this test endpoint to verify S3 connection:

```javascript
// Test S3 connection
app.get('/test-s3', async (req, res) => {
  try {
    const { s3Client, BUCKET_NAME } = require('./config/s3Config');
    const { ListObjectsV2Command } = require('@aws-sdk/client-s3');
    
    const command = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      MaxKeys: 1
    });
    
    await s3Client.send(command);
    res.json({ status: 'S3 connection successful', bucket: BUCKET_NAME });
  } catch (error) {
    res.status(500).json({ status: 'S3 connection failed', error: error.message });
  }
});
```

## Cost Optimization

### S3 Storage Classes
- Use Standard storage for frequently accessed files
- Consider Standard-IA for older documents
- Use Intelligent Tiering for automatic optimization

### Lifecycle Policies
Consider setting up lifecycle policies for:
- Moving old files to cheaper storage classes
- Deleting temporary files after a certain period

## Monitoring

### CloudWatch Metrics
Monitor these S3 metrics:
- Request count
- Error rate
- Storage usage
- Data transfer

### Application Logs
The application logs S3 operations for debugging:
- File upload success/failure
- Signed URL generation
- Fallback to local storage events

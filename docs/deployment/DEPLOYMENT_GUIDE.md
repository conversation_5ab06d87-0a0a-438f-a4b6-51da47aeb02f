# Multi-Environment Deployment Guide

This guide explains how to set up and deploy your HOA app to multiple Heroku environments.

## Environment Structure

### 🏠 Local Development
- **Frontend**: `http://localhost:8080`
- **Backend**: `http://localhost:5001`
- **Database**: `street-harmony-dev`
- **Stripe**: Test keys

### 🧪 Staging/Development (Heroku)
- **Frontend**: Deployed to Vercel (staging)
- **Backend**: `https://hoa-backend-dev.herokuapp.com`
- **Database**: `street-harmony-dev`
- **Stripe**: Test keys

### 🚀 Production (Heroku)
- **Frontend**: Deployed to Vercel (production)
- **Backend**: `https://hoa-management-app-dad2f9d126ae.herokuapp.com`
- **Database**: `street-harmony-prod`
- **Stripe**: Live keys

## Setup Instructions

### 1. Create Heroku Apps

```bash
# Create development/staging app
heroku create hoa-backend-dev

# Add git remotes for both apps
git remote add heroku-dev https://git.heroku.com/hoa-backend-dev.git
git remote add heroku-prod https://git.heroku.com/hoa-management-app-dad2f9d126ae.git
```

### 2. Configure Environment Variables

#### Development App (hoa-backend-dev)
```bash
heroku config:set NODE_ENV=development --app hoa-backend-dev
heroku config:set STRIPE_SECRET_KEY=sk_test_51REKV1RnGmqLuZ16MZYbXyEd0277AEN8QVJheBb7OAhTQjJZ8G3wFemwuC35o2eVVHOvS14BtSbzeUHmO4gU1eD700Y4HyBR40 --app hoa-backend-dev
heroku config:set MONGO_URI="mongodb+srv://frankiebruno:<EMAIL>/street-harmony-dev?retryWrites=true&w=majority&appName=HOA-Data" --app hoa-backend-dev
heroku config:set JWT_SECRET=street-harmony-secret-key-2024-development --app hoa-backend-dev
heroku config:set FRONTEND_URL=https://hoa-front-staging.vercel.app --app hoa-backend-dev
```

#### Production App (hoa-management-app-dad2f9d126ae)
```bash
heroku config:set NODE_ENV=production --app hoa-management-app-dad2f9d126ae
heroku config:set STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_KEY --app hoa-management-app-dad2f9d126ae
heroku config:set MONGO_URI="mongodb+srv://frankiebruno:<EMAIL>/street-harmony-prod?retryWrites=true&w=majority&appName=HOA-Data" --app hoa-management-app-dad2f9d126ae
heroku config:set JWT_SECRET=street-harmony-secret-key-2024-production --app hoa-management-app-dad2f9d126ae
heroku config:set FRONTEND_URL=https://hoa-front.vercel.app --app hoa-management-app-dad2f9d126ae
```

## Usage Commands

### Environment Switching
```bash
# Switch to local development
npm run use:local

# Switch to staging environment
npm run use:staging

# Switch to production environment
npm run use:prod
```

### Development
```bash
# Run locally
npm run dev:local

# Run against staging backend
npm run dev:staging

# Run against production backend (not recommended)
npm run dev:prod
```

### Building
```bash
# Build for staging
npm run build:staging

# Build for production
npm run build:prod
```

### Deployment
```bash
# Deploy to staging
npm run deploy:staging

# Deploy to production
npm run deploy:prod
```

## Workflow Recommendations

### 1. Development Workflow
1. Work locally: `npm run dev:local`
2. Test against staging: `npm run dev:staging`
3. Deploy to staging: `npm run deploy:staging`
4. Test staging thoroughly
5. Deploy to production: `npm run deploy:prod`

### 2. Testing Payments
- **Local/Staging**: Use Stripe test cards
- **Production**: Use real cards for final testing (small amounts, then refund)

### 3. Database Strategy
- **Development**: Use `street-harmony-dev` database
- **Production**: Use `street-harmony-prod` database
- Keep databases separate to avoid data corruption

## Environment Files

- `.env.development` - Local development
- `.env.staging` - Staging/development Heroku
- `.env.production` - Production Heroku

## Important Notes

1. **Never use live Stripe keys in development**
2. **Always test in staging before production**
3. **Keep databases separate between environments**
4. **Use environment-specific URLs and keys**
5. **Test payment flows thoroughly in each environment**

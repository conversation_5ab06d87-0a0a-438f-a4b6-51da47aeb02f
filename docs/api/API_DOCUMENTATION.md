# Street Harmony API Documentation

This document provides detailed information about the Street Harmony API endpoints, request/response formats, and authentication requirements.

## Base URL

```
http://localhost:5000
```

## Authentication

Most endpoints require authentication using a JWT token. Include the token in the Authorization header:

```
Authorization: Bearer <your_token>
```

### Authentication Endpoints

#### Login

```
POST /api/auth/login
```

Request body:
```json
{
  "email": "<EMAIL>",
  "password": "yourpassword"
}
```

Response:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "_id": "user_id",
      "username": "username",
      "email": "<EMAIL>",
      "fullName": "Full Name",
      "role": "member"
    },
    "token": "jwt_token"
  }
}
```

#### Register

```
POST /api/auth/register
```

Request body:
```json
{
  "username": "username",
  "email": "<EMAIL>",
  "password": "yourpassword",
  "fullName": "Full Name",
  "propertyAddress": "123 Main St"
}
```

Response:
```json
{
  "success": true,
  "message": "Registration successful. Awaiting admin approval.",
  "data": {
    "user": {
      "_id": "user_id",
      "username": "username",
      "email": "<EMAIL>",
      "fullName": "Full Name",
      "role": "pending"
    }
  }
}
```

## Member Endpoints

### Get All Members

```
GET /api/members
```

Response:
```json
{
  "success": true,
  "message": "Members retrieved successfully",
  "data": [
    {
      "_id": "member_id",
      "username": "username",
      "email": "<EMAIL>",
      "fullName": "Full Name",
      "role": "member",
      "propertyAddress": "123 Main St",
      "profilePhoto": "photo_url"
    }
  ]
}
```

### Get Member by ID

```
GET /api/members/:id
```

Response:
```json
{
  "success": true,
  "message": "Member retrieved successfully",
  "data": {
    "_id": "member_id",
    "username": "username",
    "email": "<EMAIL>",
    "fullName": "Full Name",
    "role": "member",
    "propertyAddress": "123 Main St",
    "profilePhoto": "photo_url"
  }
}
```

## Task Endpoints

### Get All Tasks

```
GET /api/tasks
```

Response:
```json
{
  "success": true,
  "message": "Tasks retrieved successfully",
  "data": {
    "tasks": [
      {
        "_id": "task_id",
        "title": "Task Title",
        "description": "Task Description",
        "status": "Not Started",
        "priority": "Medium",
        "dueDate": "2023-12-31T00:00:00.000Z",
        "createdBy": {
          "userId": "user_id",
          "timestamp": "2023-01-01T00:00:00.000Z"
        },
        "assignee": {
          "userId": "user_id",
          "username": "username",
          "email": "<EMAIL>"
        }
      }
    ],
    "closed": [],
    "activeTasks": 1
  }
}
```

### Create Task

```
POST /api/tasks
```

Request body:
```json
{
  "title": "Task Title",
  "description": "Task Description",
  "priority": "Medium",
  "dueDate": "2023-12-31T00:00:00.000Z",
  "budget": 100
}
```

Response:
```json
{
  "success": true,
  "message": "Task created successfully",
  "data": {
    "_id": "task_id",
    "title": "Task Title",
    "description": "Task Description",
    "status": "Not Started",
    "priority": "Medium",
    "dueDate": "2023-12-31T00:00:00.000Z",
    "budget": 100,
    "createdBy": {
      "userId": "user_id",
      "timestamp": "2023-01-01T00:00:00.000Z"
    }
  }
}
```

## Finance Endpoints

### Get All Financial Entries

```
GET /api/finances
```

Response:
```json
{
  "success": true,
  "message": "Financial entries retrieved successfully",
  "data": [
    {
      "_id": "entry_id",
      "type": "income",
      "category": "Dues",
      "amount": 1000,
      "note": "Monthly dues",
      "createdAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### Create Financial Entry

```
POST /api/finances
```

Request body:
```json
{
  "type": "income",
  "category": "Dues",
  "amount": 1000,
  "note": "Monthly dues"
}
```

Response:
```json
{
  "success": true,
  "message": "Financial entry created successfully",
  "data": {
    "_id": "entry_id",
    "type": "income",
    "category": "Dues",
    "amount": 1000,
    "note": "Monthly dues",
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

## Event Endpoints

### Get All Events

```
GET /api/events
```

Response:
```json
{
  "success": true,
  "message": "Events retrieved successfully",
  "data": [
    {
      "_id": "event_id",
      "title": "Community Meeting",
      "date": "2023-12-31",
      "createdBy": {
        "userId": "user_id",
        "username": "username"
      },
      "createdAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### Create Event

```
POST /api/events
```

Request body:
```json
{
  "title": "Community Meeting",
  "date": "2023-12-31"
}
```

Response:
```json
{
  "success": true,
  "message": "Event created successfully",
  "data": {
    "_id": "event_id",
    "title": "Community Meeting",
    "date": "2023-12-31",
    "createdBy": {
      "userId": "user_id",
      "username": "username"
    },
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

## Notification Endpoints

### Get All Notifications

```
GET /api/notifications
```

Response:
```json
{
  "success": true,
  "message": "Notifications retrieved successfully",
  "data": [
    {
      "_id": "notification_id",
      "type": "task",
      "title": "New Task Assigned",
      "description": "You have been assigned a new task",
      "read": false,
      "createdAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### Mark Notification as Read

```
PUT /api/notifications/:id/read
```

Response:
```json
{
  "success": true,
  "message": "Notification marked as read",
  "data": {
    "_id": "notification_id",
    "read": true,
    "readAt": "2023-01-02T00:00:00.000Z"
  }
}
```

## Error Responses

All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Error message",
  "statusCode": 400,
  "errors": ["Detailed error information"]
}
```

Common status codes:
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Server Error

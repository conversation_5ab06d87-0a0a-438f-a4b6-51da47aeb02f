/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// server.js
const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const connectDB = require('./db');
const fs = require('fs');
const mongoose = require('mongoose');
const { startCronJobs } = require('./utils/cronJobs');
const http = require('http');
const { initializeSocket } = require('./socket');
const logger = require('./utils/logger');
const { originProduction, originLocal } = require('./utils/origin');
const mimeTypes = require('./utils/mimeTypes');

// Import middleware
const {
  notFound,
  errorHandler,
  handleValidationError,
  handleCastError,
  handleDuplicateKeyError
} = require('./middleware/errorHandler');

const {
  rateLimit,
  securityHeaders
} = require('./middleware/security');

const {
  csrfProtection,
  provideCsrfToken
} = require('./middleware/csrf');

// Routes
const indexRoutes = require('./routes/index');
const testRoutes = require('./routes/test/test');

// Load env variables based on environment
const environment = process.env.NODE_ENV || 'development';

// Determine which environment file to load
let envFile = '.env';
if (environment === 'production') {
  envFile = '.env.production';
} else if (fs.existsSync('.env.local.active')) {
  // Use active local environment if it exists
  envFile = '.env.local.active';
  console.log('🧪 Using active local testing environment');
} else if (fs.existsSync('.env.local')) {
  // Fallback to .env.local if it exists
  envFile = '.env.local';
  console.log('🏠 Using local development environment');
}

dotenv.config({ path: envFile });
console.log(`Loading environment: ${environment} from ${envFile}`);

// Verify JWT_SECRET is loaded
console.log('JWT_SECRET status:', process.env.JWT_SECRET ? 'Loaded' : 'MISSING');

// Initialize express
const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 5001;

// CORS configuration
const corsOptions = {
  origin: function(origin, callback) {
    // List of allowed origins
    const allowedOrigins = process.env.NODE_ENV === 'production'
      ? originProduction
      : originLocal;

    // Allow requests with no origin (like mobile apps, curl requests, etc)
    if (!origin) {
      console.log('Request with no origin allowed');
      return callback(null, true);
    }

    console.log('Request origin:', origin);

    // Allow requests with no origin (mobile apps, curl, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With', 'X-CSRF-Token'],
  exposedHeaders: ['Content-Range', 'X-Content-Range', 'Content-Disposition'],
  preflightContinue: false,
  optionsSuccessStatus: 204
};

app.use(cors(corsOptions));
console.log(`CORS configured for ${process.env.NODE_ENV || 'development'} environment with origins:`, corsOptions.origin);

// Add CORS headers for static files
app.use('/uploads', (req, res, next) => {
  // Set CORS headers specifically for the uploads directory
  const allowedOrigins = process.env.NODE_ENV === 'production'
    ? originProduction
    : originLocal;

  console.log('Upload request from origin:', req.headers.origin);

  const origin = req.headers.origin;

  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else if (!origin) {
    // For requests with no origin (direct access, mobile apps, etc.)
    res.setHeader('Access-Control-Allow-Origin', '*');
  }

  // Add Vary header to tell browsers that the response varies based on Origin
  res.setHeader('Vary', 'Origin');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Origin, X-Requested-With, X-CSRF-Token');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition, Content-Length, Content-Type');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(204).end();
  }

  next();
});

// Apply security headers
app.use(securityHeaders);

// Apply rate limiting only in production
if (process.env.NODE_ENV === 'production') {
  app.use(rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 300 // limit each IP to 300 requests per minute
  }));
} else {
  console.log('Rate limiting disabled in development mode');
}

// Special route for Stripe webhooks (must come before JSON parser)
app.post('/api/subscriptions/webhook', express.raw({ type: 'application/json' }), (req, res, next) => {
  if (req.originalUrl === '/api/subscriptions/webhook') {
    next();
  } else {
    express.json()(req, res, next);
  }
});

// Middleware for parsing JSON and urlencoded data - must come before CSRF
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Log all requests
app.use(logger.logRequest);

// Apply CSRF protection after parsing middleware but before routes
// Exempt certain public endpoints from CSRF protection
app.use((req, res, next) => {
  // List of paths that should be exempt from CSRF protection
  const csrfExemptPaths = [
    '/api/hoa/register',
    '/api/hoa/code',
    '/api/auth/login',
    '/api/auth/register',
    '/api/password/reset',
    '/api/subscriptions/webhook'
  ];

  // Check if the current path should be exempt
  const isExempt = csrfExemptPaths.some(path => req.path.startsWith(path));

  if (isExempt) {
    console.log('CSRF protection bypassed for exempt path:', req.path);
    next();
  } else {
    csrfProtection(req, res, next);
  }
});

// Add CSRF token to response headers for authenticated users
app.use(provideCsrfToken);

// S3-based file serving middleware
const { generateSignedUrl } = require('./config/s3Config');

app.use('/uploads', async (req, res, next) => {
  try {
    // Decode the URL path to handle spaces and special characters
    const decodedPath = decodeURIComponent(req.path);
    console.log('Requested S3 file path:', req.path);
    console.log('Decoded S3 file path:', decodedPath);

    // Remove leading slash from path to create S3 key
    const s3Key = decodedPath.startsWith('/') ? decodedPath.substring(1) : decodedPath;

    // For backward compatibility, handle old local file paths
    // Convert old paths like "/documents/filename" to proper S3 keys
    let finalS3Key = s3Key;

    // If the path doesn't contain a HOA prefix, try to construct it
    if (!s3Key.includes('hoa-') && !s3Key.includes('global/')) {
      // This might be an old-style path, try to find the file in global or user's HOA
      const pathParts = s3Key.split('/');
      if (pathParts.length >= 2) {
        const category = pathParts[0];
        const filename = pathParts.slice(1).join('/');
        finalS3Key = `global/${category}/${filename}`;
      }
    }

    console.log('Looking for S3 object with key:', finalS3Key);

    // Generate signed URL for secure access
    const signedUrl = await generateSignedUrl(finalS3Key, 3600); // 1 hour expiration

    console.log('Generated signed URL for S3 object');

    // Set CORS headers for the response
    const allowedOrigins = process.env.NODE_ENV === 'production'
      ? origin.originProduction
      : origin.originLocal;

    const origin = req.headers.origin;
    if (origin) {
      res.setHeader('Access-Control-Allow-Origin', origin);
    } else {
      res.setHeader('Access-Control-Allow-Origin', '*');
    }

    res.setHeader('Vary', 'Origin');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Origin, X-Requested-With, X-CSRF-Token');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours

    // Redirect to signed URL
    res.redirect(signedUrl);

  } catch (error) {
    console.error('Error serving file from S3:', error);

    // Try fallback to local files for backward compatibility during migration
    const localFilePath = path.join(__dirname, 'uploads', req.path);
    if (fs.existsSync(localFilePath)) {
      console.log('Falling back to local file:', localFilePath);
      next(); // Let the next middleware handle local files
    } else {
      res.status(404).json({
        error: 'File not found',
        message: 'The requested file could not be found in cloud storage'
      });
    }
  }
});

// Fallback to local file serving during migration period
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Initialize socket.io
initializeSocket(server);

// Connect to MongoDB
connectDB();

// Create required directories if they don't exist
const directories = [
  'uploads',
  'uploads/documents',
  'uploads/hoa_documents',
  'uploads/user_documents',
  'uploads/task_attachments',
  'uploads/finance_documents',
  'uploads/test'
];

directories.forEach(dir => {
  const fullPath = path.join(__dirname, dir);
  try {
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`Created directory at: ${fullPath}`);
    }
    fs.accessSync(fullPath, fs.constants.W_OK);
    console.log(`Directory exists and is writable: ${fullPath}`);
  } catch (err) {
    console.error(`Error with directory ${fullPath}: ${err.message}`);
  }
});

// Health check route
app.get('/', (req, res) => {
  res.send('HOA backend is running ✅');
});

// Database health check route
app.get('/db-health', async (req, res) => {
  try {
    // Check MongoDB connection
    const connectionState = mongoose.connection.readyState;
    const connectionStates = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };

    // Try to count users as a simple database operation
    const userCount = await mongoose.model('User').countDocuments();

    res.json({
      status: 'ok',
      database: {
        connection: connectionStates[connectionState],
        host: mongoose.connection.host,
        userCount: userCount
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Database health check failed:', error);
    res.status(500).json({
      status: 'error',
      message: 'Database health check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Mount routes
// Note: These routes are mounted with '/api' prefix
// Frontend should use these paths without the '/api' prefix when using axios
app.use('/api/auth', indexRoutes.authRoutes);
app.use('/api/members', indexRoutes.memberRoutes);
app.use('/api/tasks', indexRoutes.taskRoutes);
app.use('/api/finances', indexRoutes.financeRoutes);
app.use('/api/notifications', indexRoutes.notificationRoutes);
app.use('/api/users', indexRoutes.userRoutes);
app.use('/api/events', indexRoutes.eventRoutes);
app.use('/api/settings', indexRoutes.settingsRoutes);
app.use('/api/password', indexRoutes.passwordRoutes);
app.use('/api/documents', indexRoutes.documentRoutes);
app.use('/api/hoa', indexRoutes.hoaRoutes);
app.use('/api/admin', indexRoutes.adminRoutes);
app.use('/api/mfa', indexRoutes.mfaRoutes);
app.use('/api/messages', indexRoutes.messageRoutes);
app.use('/api/test', testRoutes);
app.use('/api/stripe-payment/', indexRoutes.stripePaymentRoutes);
app.use('/api/plaid-payment/', indexRoutes.plaidPaymentRoutes);
app.use('/api/properties', indexRoutes.propertiesRoutes);
app.use('/api/maintenance', indexRoutes.maintenanceRoutes);
app.use('/api/maintenance-requests', indexRoutes.maintenanceRoutes); // Alias for frontend compatibility
app.use('/api/entitlements', indexRoutes.entitlementRoutes);
app.use('/api/communities', indexRoutes.communityRoutes);
app.use('/api/subscriptions', indexRoutes.subscriptionRoutes);
app.use('/api/budgets', indexRoutes.budgetRoutes);
app.use('/api/hoa-revenue', indexRoutes.hoaRevenueRoutes);
app.use('/api/announcements', indexRoutes.announcementRoutes);

// Test S3 connection endpoint
app.get('/test-s3', async (req, res) => {
  try {
    const { s3Client, BUCKET_NAME } = require('./config/s3Config');
    const { ListObjectsV2Command } = require('@aws-sdk/client-s3');

    const command = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      MaxKeys: 1
    });

    await s3Client.send(command);
    res.json({
      status: 'S3 connection successful',
      bucket: BUCKET_NAME,
      region: process.env.AWS_REGION,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('S3 connection test failed:', error);
    res.status(500).json({
      status: 'S3 connection failed',
      error: error.message,
      bucket: process.env.AWS_S3_BUCKET_NAME,
      region: process.env.AWS_REGION
    });
  }
});

// Error handling middleware
app.use(handleValidationError);
app.use(handleCastError);
app.use(handleDuplicateKeyError);
app.use(notFound);
app.use(errorHandler);

// Start server
server.listen(PORT, () => {
  const serverUrl = process.env.API_URL || `http://localhost:${PORT}`;
  console.log(`Server running on ${serverUrl}`);
  console.log(`Port: ${PORT}`);
  // Start cron jobs
  startCronJobs();
});

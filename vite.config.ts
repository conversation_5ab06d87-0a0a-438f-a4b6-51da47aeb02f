import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import fs from 'fs';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  console.log(`Building for mode: ${mode}`);

  // Load environment variables based on mode (.env.development or .env.production)
  const env = loadEnv(mode, process.cwd(), '');

  // Log which environment file is being used
  const envFile = mode === 'production' ? '.env.production' : '.env.development';
  console.log(`Using environment file: ${envFile}`);

  // Get API URL from environment variables
  const apiUrl = env.VITE_API_URL || 'http://localhost:5001';
  const socketUrl = env.VITE_SOCKET_URL || 'http://localhost:5001';

  console.log(`API URL: ${apiUrl}`);
  console.log(`Socket URL: ${socketUrl}`);

  // Check if we're in production mode
  const isProd = mode === 'production';
  console.log(`Environment: ${isProd ? 'Production' : 'Development'}`);

  return {
    server: {
      host: '0.0.0.0',
      port: 8080,
      proxy: {
        '/api': {
          target: apiUrl,
          changeOrigin: true,
          secure: true,
          ws: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      },
      cors: true
    },
    plugins: [
      react(),
      mode === 'development' && componentTagger(),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            // Split vendor libraries into separate chunks
            'react-vendor': ['react', 'react-dom', 'react-router-dom'],
            'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select'],
            'chart-vendor': ['recharts', 'd3-scale', 'd3-array'],
            'form-vendor': ['react-hook-form', 'zod'],
            'utils-vendor': ['axios', 'date-fns', 'lodash']
          }
        }
      },
      chunkSizeWarningLimit: 1000, // Increase warning limit to 1000kb
      target: 'esnext',
      minify: 'esbuild'
    },
    // No need to manually define environment variables as they're loaded from the appropriate .env file
  };
});

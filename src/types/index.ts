export interface Task {
  _id: string;
  title: string;
  description: string;
  status: 'Not Started' | 'In Progress' | 'Completed' | 'Closed';
  priority: 'Low' | 'Medium' | 'High';
  dueDate: string;
  budget?: number;
  createdBy: {
    userId: {
      _id: string;
      username: string;
      email: string;
    };
    timestamp: string;
  };
  permissions: {
    canView: string[];
    canEdit: string[];
    canDelete: string[];
  };
  isArchived: boolean;
  closedAt?: string;
  closedBy?: {
    userId: {
      _id: string;
      username: string;
      email: string;
    };
    timestamp: string;
  };
} 
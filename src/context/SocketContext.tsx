import React, { createContext, useContext, useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from '@/hooks/useAuth';

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
}

export const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
});

export const useSocket = () => useContext(SocketContext);

export const SocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { user, token } = useAuth();

  useEffect(() => {
    // Don't attempt to connect if there's no token
    if (!token) {
      if (socket) {
        try {
          socket.disconnect();
          socket.close();
        } catch (e) {
          console.error('Error disconnecting socket:', e);
        }
        setSocket(null);
        setIsConnected(false);
      }
      return;
    }

    // Only create a new socket if we don't already have one
    if (socket && isConnected) {
      return;
    }

    try {
      // Always use the VITE_SOCKET_URL environment variable, with no localhost fallback
      const socketUrl = import.meta.env.VITE_SOCKET_URL;
      console.log('Connecting to socket server at:', socketUrl);

      // Add more detailed logging for debugging
      console.log('Socket connection details:', {
        url: socketUrl,
        isProd: import.meta.env.PROD,
        hasToken: !!token,
        tokenLength: token ? token.length : 0
      });

      // For production debugging - log to console if we're in production
      if (import.meta.env.PROD) {
        console.log('PRODUCTION MODE: Using socket URL:', socketUrl);

        // Check if the URL is accessible
        try {
          fetch(socketUrl, {
            method: 'HEAD',
            mode: 'cors',
            credentials: 'omit',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          })
            .then(response => console.log('Socket server is reachable:', response.status))
            .catch(error => console.error('Socket server might be unreachable:', error));
        } catch (e) {
          console.error('Error checking socket server availability:', e);
        }

        // Log global environment variables if available
        if (typeof window !== 'undefined' && window.__ENV__) {
          console.log('Global environment variables:', window.__ENV__);
        }
      }

      // PRODUCTION FIX: Create a new socket with minimal configuration
      // Using only essential options to minimize potential issues
      const newSocket = io(socketUrl, {
        auth: { token },
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: Infinity, // Never stop trying to reconnect
        reconnectionDelay: 1000,
        timeout: 20000, // Increase timeout for better reliability
        transports: ['polling', 'websocket'], // Try both transports
        forceNew: true,
        withCredentials: false,
        path: '/socket.io'
      });

      // Log the socket configuration
      console.log('Socket configuration:', {
        url: socketUrl,
        transports: ['polling', 'websocket'],
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: Infinity,
        path: '/socket.io'
      });

      newSocket.on('connect', () => {
        console.log('Socket connected successfully');
        console.log('Socket ID:', newSocket.id);
        console.log('Socket transport:', newSocket.io.engine.transport.name);

        // Get current user info to join the correct room
        try {
          const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
          if (currentUser && currentUser._id) {
            console.log('Joining room for user:', currentUser._id);

            // Explicitly join the user's room to receive messages
            newSocket.emit('join', { userId: currentUser._id }, (response: any) => {
              if (response && response.success) {
                console.log('Successfully joined user room:', currentUser._id);
              } else {
                console.error('Failed to join user room:', response?.error || 'Unknown error');
              }
            });

            // Also join the global room
            newSocket.emit('join', { roomName: 'global' }, (response: any) => {
              if (response && response.success) {
                console.log('Successfully joined global room');
              } else {
                console.error('Failed to join global room:', response?.error || 'Unknown error');
              }
            });
          }
        } catch (e) {
          console.error('Error joining user room:', e);
        }

        setIsConnected(true);
      });

      // Handle joined_rooms event
      newSocket.on('joined_rooms', (data: { rooms: string[] }) => {
        console.log('Joined rooms:', data.rooms);
      });

      // Handle conversation_updated event
      newSocket.on('conversation_updated', (data: { conversationId: string }) => {
        console.log('Conversation updated:', data.conversationId);

        // Trigger a refresh of the conversations list
        try {
          const event = new CustomEvent('conversation_updated', {
            detail: { conversationId: data.conversationId }
          });
          window.dispatchEvent(event);
        } catch (e) {
          console.error('Error dispatching conversation_updated event:', e);
        }
      });

      newSocket.on('disconnect', (reason) => {
        console.log('Socket disconnected, reason:', reason);
        setIsConnected(false);

        // Handle various disconnect reasons
        if (reason === 'io server disconnect') {
          // The server has forcefully disconnected the socket
          // We need to manually reconnect
          console.log('Server disconnected the socket, attempting manual reconnect...');
          setTimeout(() => {
            if (newSocket) {
              newSocket.connect();
            }
          }, 3000);
        } else if (reason === 'transport close' || reason === 'ping timeout') {
          // Transport closed or ping timeout - likely a network issue
          console.log('Transport closed or ping timeout, attempting to reconnect...');
          setTimeout(() => {
            if (newSocket && !newSocket.connected) {
              // Try to reconnect with a clean slate
              newSocket.io.opts.transports = ['polling', 'websocket'];
              newSocket.connect();
            }
          }, 3000);
        } else {
          // For any other reason, let the automatic reconnection handle it
          console.log(`Socket disconnected due to ${reason}, automatic reconnection will be attempted`);
        }
      });

      newSocket.on('error', (error) => {
        console.error('Socket error:', error);
        setIsConnected(false);
      });

      newSocket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        setIsConnected(false);

        // Try to recover from connection errors by switching transports
        if (newSocket.io.opts.transports.includes('websocket')) {
          console.log('Switching to polling transport after connection error');
          newSocket.io.opts.transports = ['polling'];
        } else {
          console.log('Switching back to websocket after polling connection error');
          newSocket.io.opts.transports = ['websocket', 'polling'];
        }

        // Attempt to reconnect after a delay
        setTimeout(() => {
          if (newSocket && !newSocket.connected) {
            console.log('Attempting manual reconnect after connection error');
            newSocket.connect();
          }
        }, 5000);
      });

      newSocket.on('reconnect', (attemptNumber) => {
        console.log(`Socket reconnected after ${attemptNumber} attempts`);
        setIsConnected(true);
      });

      newSocket.on('reconnect_attempt', (attemptNumber) => {
        console.log(`Socket reconnection attempt #${attemptNumber}`);

        // Switch transport strategy on certain reconnection attempts
        if (attemptNumber % 2 === 0) {
          console.log('Switching transport strategy for this reconnection attempt');
          if (newSocket.io.opts.transports[0] === 'websocket') {
            newSocket.io.opts.transports = ['polling', 'websocket'];
          } else {
            newSocket.io.opts.transports = ['websocket', 'polling'];
          }
        }
      });

      newSocket.on('reconnect_error', (error) => {
        console.error('Socket reconnection error:', error);
      });

      newSocket.on('reconnect_failed', () => {
        console.error('Socket reconnection failed after all attempts');
        setIsConnected(false);

        // Create a completely new socket after reconnection failure
        console.log('Creating a new socket instance after reconnection failure');
        setTimeout(() => {
          try {
            newSocket.close();
            // Use the same socket URL as before
            const freshSocketUrl = socketUrl;
            console.log('Creating fresh socket connection to:', freshSocketUrl);

            const freshSocket = io(freshSocketUrl, {
              auth: { token },
              autoConnect: true,
              reconnection: true,
              reconnectionAttempts: Infinity,
              reconnectionDelay: 2000,
              reconnectionDelayMax: 10000,
              timeout: 20000,
              transports: ['polling', 'websocket'],
              forceNew: true,
              withCredentials: false, // Don't include credentials in cross-origin requests (causes CORS issues)
              path: '/socket.io' // Explicitly set the socket.io path
            });
            setSocket(freshSocket);
          } catch (e) {
            console.error('Error creating fresh socket:', e);
          }
        }, 10000);
      });

      setSocket(newSocket);
    } catch (e) {
      console.error('Error creating socket connection:', e);
    }

    return () => {
      if (socket) {
        try {
          socket.disconnect();
          socket.close();
        } catch (e) {
          console.error('Error cleaning up socket:', e);
        }
      }
    };
  }, [token]);

  return (
    <SocketContext.Provider value={{ socket, isConnected }}>
      {children}
    </SocketContext.Provider>
  );
};
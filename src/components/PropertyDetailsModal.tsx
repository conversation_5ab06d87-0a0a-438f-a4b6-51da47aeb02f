import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Building2,
  Home,
  Users,
  Calendar,
  ClipboardList,
  Wrench,
  MapPin,
  FileText,
  Plus
} from 'lucide-react';
import PropertyDocumentUpload from './PropertyDocumentUpload';
import PropertyPhotoGallery from './PropertyPhotoGallery';
import { Property } from '@/services/propertyService';
import { MaintenanceRequest } from '@/services/maintenanceService';
import PaymentHistory from './payments/PaymentHistory';

interface PropertyDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  property: Property | null;
  maintenanceRequest?: MaintenanceRequest | null;
}

const PropertyDetailsModal: React.FC<PropertyDetailsModalProps> = ({
  isOpen,
  onClose,
  property,
  maintenanceRequest
}) => {
  // State for showing document upload form
  const [showDocumentUpload, setShowDocumentUpload] = useState(false);

  // Determine if we're showing a property or maintenance request
  const isProperty = !!property;
  const item = property || maintenanceRequest;

  // If no item is provided or the modal isn't open, don't render anything
  if (!item || !isOpen) return null;

  // Add error boundary to catch rendering errors
  try {

  // Check if user is admin
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isAdmin = user?.role === 'admin' || user?.role === 'company_admin' || user?.role === 'hoa_admin';

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'occupied':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Occupied</Badge>;
      case 'vacant':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Vacant</Badge>;
      case 'maintenance':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Maintenance</Badge>;
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>;
      case 'in_progress':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">In Progress</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Completed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">High</Badge>;
      case 'medium':
        return <Badge variant="secondary">Medium</Badge>;
      case 'low':
        return <Badge variant="outline">Low</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex justify-between items-start">
            <div>
              <DialogTitle className="text-xl">
                {isProperty ? property?.address || 'Property Details' : `Maintenance Request: ${maintenanceRequest?.property || 'Unknown'}`}
              </DialogTitle>
              <DialogDescription className="flex items-center gap-2 mt-1">
                {isProperty ? (
                  <>
                    <Home className="h-4 w-4" />
                    <span>{property?.type || 'Unknown Type'}</span>
                    {property?.status && getStatusBadge(property.status)}
                  </>
                ) : (
                  <>
                    <Wrench className="h-4 w-4" />
                    <span>Submitted on {maintenanceRequest?.dateSubmitted ? new Date(maintenanceRequest.dateSubmitted).toLocaleDateString() : 'Unknown Date'}</span>
                    {maintenanceRequest?.priority && getPriorityBadge(maintenanceRequest.priority)}
                    {maintenanceRequest?.status && getStatusBadge(maintenanceRequest.status)}
                  </>
                )}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {isProperty ? (
          <Tabs defaultValue="details">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="hoa-transactions">HOA Transaction</TabsTrigger>
              <TabsTrigger value="photos">Photos</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="maintenance">Maintenance History</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h3 className="font-medium">Property Information</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Address:</span>
                      <span className="text-sm">{property?.address || 'N/A'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Type:</span>
                      <span className="text-sm">{property?.type || 'N/A'}</span>
                    </div>
                    {property?.yearBuilt && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Year Built:</span>
                        <span className="text-sm">{property.yearBuilt}</span>
                      </div>
                    )}
                    {property.squareFeet && (
                      <div className="flex items-center gap-2">
                        <Home className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Square Feet:</span>
                        <span className="text-sm">{property.squareFeet}</span>
                      </div>
                    )}
                    {(property.bedrooms || property.bathrooms) && (
                      <div className="flex items-center gap-2">
                        <Home className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Bedrooms/Bathrooms:</span>
                        <span className="text-sm">{property.bedrooms || 0} bed / {property.bathrooms || 0} bath</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-medium">Status Information</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Status:</span>
                      <span className="text-sm">{getStatusBadge(property.status)}</span>
                    </div>
                    {property.resident && (
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Resident:</span>
                        <span className="text-sm">{property.resident.fullName}</span>
                      </div>
                    )}
                    {property.nextInspection && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Next Inspection:</span>
                        <span className="text-sm">{new Date(property.nextInspection).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {property?.amenities && property.amenities.length > 0 && (
                <div className="mt-4">
                  <h3 className="font-medium mb-2">Amenities</h3>
                  <div className="flex flex-wrap gap-2">
                    {property.amenities.map((amenity, index) => (
                      <Badge key={index} variant="outline">{amenity}</Badge>
                    ))}
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value='hoa-transactions' className="space-y-4 mt-4">
              <PaymentHistory userId={property.resident?._id} fromPropertyTab={true}></PaymentHistory>
            </TabsContent>

            <TabsContent value="photos" className="mt-4">
              <PropertyPhotoGallery property={property} isAdmin={isAdmin} />
            </TabsContent>

            <TabsContent value="documents" className="space-y-4 mt-4">
              {isAdmin && (
                <div className="flex justify-end mb-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowDocumentUpload(!showDocumentUpload)}
                  >
                    {showDocumentUpload ? 'Cancel' : (
                      <>
                        <Plus className="h-4 w-4 mr-1" />
                        Add Document
                      </>
                    )}
                  </Button>
                </div>
              )}

              {showDocumentUpload && isAdmin && (
                <div className="mb-6">
                  <PropertyDocumentUpload propertyId={property._id} />
                </div>
              )}

              {property.documents && property.documents.length > 0 ? (
                <div className="space-y-2">
                  {property.documents.map((doc) => (
                    <div key={doc._id} className="flex items-center justify-between p-3 border rounded-md">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{doc.name}</p>
                          <p className="text-xs text-muted-foreground">{doc.fileType} • {new Date(doc.uploadDate).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">View</Button>
                        {isAdmin && (
                          <Button variant="destructive" size="sm">Delete</Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No documents available for this property
                </div>
              )}
            </TabsContent>

            <TabsContent value="maintenance" className="space-y-4 mt-4">
              {property.maintenanceHistory && property.maintenanceHistory.length > 0 ? (
                <div className="space-y-3">
                  {property.maintenanceHistory.map((item) => (
                    <div key={item._id} className="p-3 border rounded-md">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">{item.description}</p>
                          <p className="text-xs text-muted-foreground">{new Date(item.date).toLocaleDateString()}</p>
                        </div>
                        {getStatusBadge(item.status)}
                      </div>
                      {item.cost && (
                        <div className="mt-2 text-sm">
                          <span className="font-medium">Cost:</span> ${item.cost.toFixed(2)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No maintenance history available for this property
                </div>
              )}
            </TabsContent>
          </Tabs>
        ) : (
          // Maintenance request details
          <div className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <h3 className="font-medium">Request Details</h3>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <Wrench className="h-4 w-4 text-muted-foreground mt-1" />
                    <span className="text-sm font-medium">Description:</span>
                    <span className="text-sm">{maintenanceRequest?.description}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Date Submitted:</span>
                    <span className="text-sm">{new Date(maintenanceRequest?.dateSubmitted || '').toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Property:</span>
                    <span className="text-sm">{maintenanceRequest?.property.address}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Reported By:</span>
                    <span className="text-sm">{maintenanceRequest?.resident.fullName}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="font-medium">Status Information</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <ClipboardList className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Status:</span>
                    <span className="text-sm">{maintenanceRequest?.status && getStatusBadge(maintenanceRequest.status)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ClipboardList className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Priority:</span>
                    <span className="text-sm">{maintenanceRequest?.priority && getPriorityBadge(maintenanceRequest.priority)}</span>
                  </div>
                  {maintenanceRequest?.assignedTo && (
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Assigned To:</span>
                      <span className="text-sm">{maintenanceRequest.assignedTo}</span>
                    </div>
                  )}
                  {maintenanceRequest?.estimatedCompletion && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Est. Completion:</span>
                      <span className="text-sm">{maintenanceRequest.estimatedCompletion}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {maintenanceRequest?.notes && (
              <div className="mt-4">
                <h3 className="font-medium mb-2">Notes</h3>
                <div className="p-3 bg-muted rounded-md text-sm">
                  {maintenanceRequest.notes}
                </div>
              </div>
            )}

            {maintenanceRequest?.images && maintenanceRequest.images.length > 0 && (
              <div className="mt-4">
                <h3 className="font-medium mb-2">Images</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {maintenanceRequest.images.map((image, index) => (
                    <div key={index} className="aspect-square bg-muted rounded-md flex items-center justify-center">
                      <img src={image} alt={`Maintenance image ${index + 1}`} className="max-h-full max-w-full object-contain" />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        <DialogFooter className="flex justify-between items-center mt-6">
          <Button variant="outline" onClick={onClose}>Close</Button>
          {isProperty ? (
            property?.status === 'vacant' ? (
              <Button>Mark as Occupied</Button>
            ) : (
              <Button>Schedule Inspection</Button>
            )
          ) : (
            maintenanceRequest?.status !== 'completed' && (
              <Button>
                {maintenanceRequest?.status === 'pending' ? 'Start Work' : 'Mark Complete'}
              </Button>
            )
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
  } catch (error) {
    console.error("Error rendering PropertyDetailsModal:", error);
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Error Loading Details</DialogTitle>
            <DialogDescription>
              There was an error loading the details. Please try again.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={onClose}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }
};

export default PropertyDetailsModal;

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertCircle, Shield, Smartphone } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { getMFAStatus, enableMFA, disableMFA } from '@/services/mfaService';

const MFASettings = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isEnableDialogOpen, setIsEnableDialogOpen] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');

  // Fetch MFA status
  const { data: mfaStatus, isLoading, isError, error } = useQuery({
    queryKey: ['mfaStatus'],
    queryFn: getMFAStatus,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Update phone number when MFA status is loaded
  useEffect(() => {
    if (mfaStatus?.phoneNumber) {
      setPhoneNumber(mfaStatus.phoneNumber);
    }
  }, [mfaStatus]);

  // Enable MFA mutation
  const enableMFAMutation = useMutation({
    mutationFn: enableMFA,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mfaStatus'] });
      toast({
        title: 'MFA Enabled',
        description: 'Multi-factor authentication has been enabled for your account.',
        variant: 'default',
      });
      setIsEnableDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to Enable MFA',
        description: error.response?.data?.message || 'An error occurred while enabling MFA.',
        variant: 'destructive',
      });
    },
  });

  // Disable MFA mutation
  const disableMFAMutation = useMutation({
    mutationFn: disableMFA,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mfaStatus'] });
      toast({
        title: 'MFA Disabled',
        description: 'Multi-factor authentication has been disabled for your account.',
        variant: 'default',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to Disable MFA',
        description: error.response?.data?.message || 'An error occurred while disabling MFA.',
        variant: 'destructive',
      });
    },
  });

  const handleEnableMFA = (e: React.FormEvent) => {
    e.preventDefault();
    enableMFAMutation.mutate(phoneNumber);
  };

  const handleToggleMFA = (checked: boolean) => {
    if (checked) {
      setIsEnableDialogOpen(true);
    } else {
      disableMFAMutation.mutate();
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-3/4 mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {(error as any)?.message || 'Failed to load MFA settings. Please try again later.'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          <CardTitle>Multi-Factor Authentication</CardTitle>
        </div>
        <CardDescription>
          Enhance your account security with multi-factor authentication
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between py-4">
          <div className="flex flex-col space-y-1">
            <span className="font-medium">Enable MFA</span>
            <span className="text-sm text-muted-foreground">
              Require a verification code when logging in
            </span>
          </div>
          <Switch
            checked={mfaStatus?.mfaEnabled || false}
            onCheckedChange={handleToggleMFA}
            disabled={enableMFAMutation.isPending || disableMFAMutation.isPending}
          />
        </div>

        {mfaStatus?.mfaEnabled && (
          <div className="mt-4 p-4 border rounded-md bg-muted/50">
            <div className="flex items-start gap-3">
              <Smartphone className="h-5 w-5 mt-0.5 text-primary" />
              <div>
                <h4 className="font-medium">Registered Phone Number</h4>
                <p className="text-sm text-muted-foreground mt-1">{mfaStatus.phoneNumber}</p>
                <p className="text-sm mt-3">
                  When MFA is enabled, you'll receive a verification code via email (simulating SMS) when you log in.
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      {/* Enable MFA Dialog */}
      <Dialog open={isEnableDialogOpen} onOpenChange={setIsEnableDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Enable Multi-Factor Authentication</DialogTitle>
            <DialogDescription>
              Enter your phone number to receive verification codes when logging in.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEnableMFA}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="phoneNumber">Phone Number</Label>
                <Input
                  id="phoneNumber"
                  placeholder="(*************"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  required
                />
                <p className="text-sm text-muted-foreground">
                  For this demo, verification codes will be sent to your email instead of SMS.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEnableDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={enableMFAMutation.isPending}>
                {enableMFAMutation.isPending ? 'Enabling...' : 'Enable MFA'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default MFASettings;

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import api from '@/lib/axios';

interface DueDate {
  day: number;
  reminderDays: number;
  gracePeriod: number;
}

interface Settings {
  dueDate: DueDate;
  lastModifiedBy: string;
  lastModifiedAt: string;
  _id: string;
}

const DueDateSettings = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [date, setDate] = useState<Date | null>(null);
  const [reminderDays, setReminderDays] = useState(7);
  const [gracePeriod, setGracePeriod] = useState(3);
  const [isOpen, setIsOpen] = useState(false);

  // Fetch current settings
  const { data: settings, isLoading, isError, error: queryError } = useQuery({
    queryKey: ['settings'],
    queryFn: async () => {
      try {
        console.log('Fetching settings...');
        const response = await api.get<Settings>('/api/settings');
        console.log('Settings response:', response.data);

        // Initialize state with fetched data
        if (response.data?.dueDate) {
          const currentDate = new Date();
          currentDate.setDate(response.data.dueDate.day);
          setDate(currentDate);
          setReminderDays(response.data.dueDate.reminderDays);
          setGracePeriod(response.data.dueDate.gracePeriod);
        }

        return response.data;
      } catch (error) {
        console.error('Error fetching settings:', error);
        throw error;
      }
    }
  });

  // Update settings mutation
  const mutation = useMutation({
    mutationFn: async () => {
      if (!date) {
        throw new Error('Please select a due date');
      }
      const dueDate = {
        day: date.getDate(),
        reminderDays,
        gracePeriod
      };
      console.log('Sending update:', { dueDate });
      const response = await api.put<Settings>('/api/settings', { dueDate });
      console.log('Update response:', response.data);
      return response.data;
    },
    onSuccess: (data) => {
      console.log('Settings updated successfully:', data);
      queryClient.invalidateQueries({ queryKey: ['settings'] });
      toast({
        title: 'Settings Updated',
        description: 'Due date settings have been saved successfully.',
        variant: 'default',
      });
    },
    onError: (error: any) => {
      console.error('Update error:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to update settings',
        variant: 'destructive',
      });
    },
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Due Date Settings</CardTitle>
          <CardDescription>Loading settings...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Due Date Settings</CardTitle>
          <CardDescription className="text-red-500">
            Error loading settings: {(queryError as Error)?.message || 'Please try again'}
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Due Date Settings</CardTitle>
        <CardDescription>
          Configure when HOA dues are due and set up reminders
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Monthly Due Date</Label>
            <div className="flex gap-4">
              <Popover open={isOpen} onOpenChange={setIsOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[240px] justify-start text-left font-normal",
                      !date && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "d 'of each month'") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={(newDate) => {
                      if (newDate) {
                        setDate(newDate);
                        setIsOpen(false);
                      }
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Reminder Days Before Due Date</Label>
            <div className="flex items-center gap-4">
              <Slider
                value={[reminderDays]}
                onValueChange={(value) => setReminderDays(value[0])}
                max={14}
                min={1}
                step={1}
                className="w-[60%]"
              />
              <span className="w-[40%] text-sm">{reminderDays} days before</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Grace Period</Label>
            <div className="flex items-center gap-4">
              <Slider
                value={[gracePeriod]}
                onValueChange={(value) => setGracePeriod(value[0])}
                max={14}
                min={0}
                step={1}
                className="w-[60%]"
              />
              <span className="w-[40%] text-sm">{gracePeriod} days after</span>
            </div>
          </div>

          <Button
            onClick={() => mutation.mutate()}
            disabled={!date || mutation.isPending}
            className="mt-4"
          >
            {mutation.isPending ? "Saving..." : "Save Settings"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default DueDateSettings; 
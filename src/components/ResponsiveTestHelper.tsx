import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Monitor, Smartphone, Tablet, Eye, EyeOff } from 'lucide-react';

interface ResponsiveTestHelperProps {
  showHelper?: boolean;
}

const ResponsiveTestHelper: React.FC<ResponsiveTestHelperProps> = ({ 
  showHelper = process.env.NODE_ENV === 'development' 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 });
  const [breakpoint, setBreakpoint] = useState('');

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      setScreenSize({ width, height });

      // Determine current breakpoint
      if (width < 475) {
        setBreakpoint('Mobile (< 475px)');
      } else if (width < 640) {
        setBreakpoint('XS (475px - 640px)');
      } else if (width < 768) {
        setBreakpoint('SM (640px - 768px)');
      } else if (width < 1024) {
        setBreakpoint('MD (768px - 1024px)');
      } else if (width < 1280) {
        setBreakpoint('LG (1024px - 1280px)');
      } else if (width < 1536) {
        setBreakpoint('XL (1280px - 1536px)');
      } else {
        setBreakpoint('2XL (≥ 1536px)');
      }
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  if (!showHelper) return null;

  const getBreakpointColor = () => {
    if (screenSize.width < 475) return 'bg-red-500';
    if (screenSize.width < 640) return 'bg-orange-500';
    if (screenSize.width < 768) return 'bg-yellow-500';
    if (screenSize.width < 1024) return 'bg-green-500';
    if (screenSize.width < 1280) return 'bg-blue-500';
    if (screenSize.width < 1536) return 'bg-indigo-500';
    return 'bg-purple-500';
  };

  const getDeviceIcon = () => {
    if (screenSize.width < 768) return <Smartphone className="h-4 w-4" />;
    if (screenSize.width < 1024) return <Tablet className="h-4 w-4" />;
    return <Monitor className="h-4 w-4" />;
  };

  return (
    <>
      {/* Toggle Button */}
      <Button
        variant="outline"
        size="sm"
        className="fixed bottom-4 right-4 z-50 bg-background shadow-lg"
        onClick={() => setIsVisible(!isVisible)}
      >
        {isVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
        <span className="ml-2 hidden sm:inline">
          {isVisible ? 'Hide' : 'Show'} Responsive Helper
        </span>
      </Button>

      {/* Responsive Info Panel */}
      {isVisible && (
        <Card className="fixed bottom-16 right-4 z-40 w-80 bg-background shadow-xl border-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              {getDeviceIcon()}
              Responsive Design Helper
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* Current Breakpoint */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Breakpoint:</span>
              <Badge className={`${getBreakpointColor()} text-white`}>
                {breakpoint}
              </Badge>
            </div>

            {/* Screen Dimensions */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Dimensions:</span>
              <span className="text-sm font-mono">
                {screenSize.width} × {screenSize.height}
              </span>
            </div>

            {/* Touch Target Guidelines */}
            <div className="border-t pt-3">
              <div className="text-sm font-medium mb-2">Touch Target Guidelines:</div>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Minimum:</span>
                  <span className="font-mono">44px × 44px</span>
                </div>
                <div className="flex justify-between">
                  <span>Recommended:</span>
                  <span className="font-mono">48px × 48px</span>
                </div>
              </div>
            </div>

            {/* Breakpoint Reference */}
            <div className="border-t pt-3">
              <div className="text-sm font-medium mb-2">Breakpoint Reference:</div>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Mobile:</span>
                  <span className="font-mono">< 475px</span>
                </div>
                <div className="flex justify-between">
                  <span>XS:</span>
                  <span className="font-mono">475px+</span>
                </div>
                <div className="flex justify-between">
                  <span>SM:</span>
                  <span className="font-mono">640px+</span>
                </div>
                <div className="flex justify-between">
                  <span>MD:</span>
                  <span className="font-mono">768px+</span>
                </div>
                <div className="flex justify-between">
                  <span>LG:</span>
                  <span className="font-mono">1024px+</span>
                </div>
                <div className="flex justify-between">
                  <span>XL:</span>
                  <span className="font-mono">1280px+</span>
                </div>
                <div className="flex justify-between">
                  <span>2XL:</span>
                  <span className="font-mono">1536px+</span>
                </div>
              </div>
            </div>

            {/* Common Device Sizes */}
            <div className="border-t pt-3">
              <div className="text-sm font-medium mb-2">Common Device Sizes:</div>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>iPhone SE:</span>
                  <span className="font-mono">375 × 667</span>
                </div>
                <div className="flex justify-between">
                  <span>iPhone 12/13/14:</span>
                  <span className="font-mono">390 × 844</span>
                </div>
                <div className="flex justify-between">
                  <span>iPad:</span>
                  <span className="font-mono">768 × 1024</span>
                </div>
                <div className="flex justify-between">
                  <span>iPad Pro:</span>
                  <span className="font-mono">1024 × 1366</span>
                </div>
                <div className="flex justify-between">
                  <span>Desktop:</span>
                  <span className="font-mono">1920 × 1080</span>
                </div>
              </div>
            </div>

            {/* Responsive Design Tips */}
            <div className="border-t pt-3">
              <div className="text-sm font-medium mb-2">Quick Tips:</div>
              <div className="space-y-1 text-xs text-muted-foreground">
                <div>• Use min-h-[44px] for touch targets</div>
                <div>• Test with real devices when possible</div>
                <div>• Consider landscape orientation</div>
                <div>• Use responsive text utilities</div>
                <div>• Test with different zoom levels</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Breakpoint Indicator (Always Visible) */}
      <div className="fixed top-4 right-4 z-30">
        <Badge 
          className={`${getBreakpointColor()} text-white text-xs px-2 py-1`}
          title={`Current breakpoint: ${breakpoint} (${screenSize.width}px)`}
        >
          {screenSize.width < 640 ? 'Mobile' : 
           screenSize.width < 1024 ? 'Tablet' : 'Desktop'}
        </Badge>
      </div>
    </>
  );
};

export default ResponsiveTestHelper;

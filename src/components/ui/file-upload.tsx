import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { X, Upload, File, Image, FileText, FileSpreadsheet, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface FileWithPreview extends File {
  id: string;
  preview?: string;
  uploadProgress?: number;
  error?: string;
}

interface FileUploadProps {
  files: FileWithPreview[];
  onFilesChange: (files: FileWithPreview[]) => void;
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedTypes?: string[];
  disabled?: boolean;
  className?: string;
}

const ACCEPTED_FILE_TYPES = {
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/gif': ['.gif'],
  'image/webp': ['.webp'],
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'text/plain': ['.txt'],
  'text/csv': ['.csv']
};

const DEFAULT_MAX_SIZE = 25 * 1024 * 1024; // 25MB

const getFileIcon = (file: File) => {
  if (file.type.startsWith('image/')) {
    return <Image className="h-4 w-4" />;
  } else if (file.type.includes('pdf')) {
    return <FileText className="h-4 w-4 text-red-500" />;
  } else if (file.type.includes('sheet') || file.type.includes('excel')) {
    return <FileSpreadsheet className="h-4 w-4 text-green-500" />;
  } else if (file.type.includes('word') || file.type.includes('document')) {
    return <FileText className="h-4 w-4 text-blue-500" />;
  }
  return <File className="h-4 w-4" />;
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const FileUpload: React.FC<FileUploadProps> = ({
  files,
  onFilesChange,
  maxFiles = 5,
  maxSize = DEFAULT_MAX_SIZE,
  acceptedTypes = Object.keys(ACCEPTED_FILE_TYPES),
  disabled = false,
  className
}) => {
  const [dragActive, setDragActive] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      console.warn('Some files were rejected:', rejectedFiles);
    }

    // Process accepted files
    const newFiles: FileWithPreview[] = acceptedFiles.map((file) => {
      const fileWithPreview: FileWithPreview = Object.assign(file, {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        uploadProgress: 0
      });

      // Create preview for images
      if (file.type.startsWith('image/')) {
        fileWithPreview.preview = URL.createObjectURL(file);
      }

      return fileWithPreview;
    });

    // Check if adding new files would exceed maxFiles
    const totalFiles = files.length + newFiles.length;
    if (totalFiles > maxFiles) {
      const allowedNewFiles = newFiles.slice(0, maxFiles - files.length);
      onFilesChange([...files, ...allowedNewFiles]);
    } else {
      onFilesChange([...files, ...newFiles]);
    }
  }, [files, maxFiles, onFilesChange]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => {
      acc[type] = ACCEPTED_FILE_TYPES[type as keyof typeof ACCEPTED_FILE_TYPES] || [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize,
    maxFiles: maxFiles - files.length,
    disabled: disabled || files.length >= maxFiles,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
    onDropAccepted: () => setDragActive(false),
    onDropRejected: () => setDragActive(false)
  });

  const removeFile = (fileId: string) => {
    const updatedFiles = files.filter(file => file.id !== fileId);
    // Revoke object URLs to prevent memory leaks
    files.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });
    onFilesChange(updatedFiles);
  };

  const canAddMoreFiles = files.length < maxFiles && !disabled;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Drop Zone */}
      {canAddMoreFiles && (
        <Card className={cn(
          "border-2 border-dashed transition-colors cursor-pointer",
          isDragActive || dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25",
          disabled && "opacity-50 cursor-not-allowed"
        )}>
          <CardContent className="p-6">
            <div {...getRootProps()} className="text-center">
              <input {...getInputProps()} />
              <Upload className={cn(
                "mx-auto h-12 w-12 mb-4",
                isDragActive ? "text-primary" : "text-muted-foreground"
              )} />
              <div className="space-y-2">
                <p className="text-sm font-medium">
                  {isDragActive ? "Drop files here" : "Drag & drop files here, or click to select"}
                </p>
                <p className="text-xs text-muted-foreground">
                  Supports: Images, PDFs, Word docs, Excel files, Text files
                </p>
                <p className="text-xs text-muted-foreground">
                  Max {maxFiles} files, {formatFileSize(maxSize)} each
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Attached Files ({files.length}/{maxFiles})</h4>
          {files.map((file) => (
            <Card key={file.id} className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  {getFileIcon(file)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                  {file.preview && (
                    <img
                      src={file.preview}
                      alt={file.name}
                      className="h-10 w-10 object-cover rounded border"
                    />
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  {file.error && (
                    <AlertCircle className="h-4 w-4 text-destructive" />
                  )}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(file.id)}
                    disabled={disabled}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {file.uploadProgress !== undefined && file.uploadProgress > 0 && file.uploadProgress < 100 && (
                <Progress value={file.uploadProgress} className="mt-2" />
              )}
              {file.error && (
                <p className="text-xs text-destructive mt-1">{file.error}</p>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default FileUpload;

import React, { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { registerHOA } from '@/services/hoaService';
import { useMutation } from '@tanstack/react-query';
import { Loader2, Building2, CreditCard, Upload, ChevronDown } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { US_STATES } from '@/utils/state';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';

const PublicHOARegistration = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    hoaCommunityName: '',
    hoaStreetAddress: '',
    hoaCity: '',
    hoaState: '',
    hoaZipCode: '',
    contactEmail: '',
    contactPhone: '',
    einNumber: '',
    description: '',
    subscription: {
      tier: 'basic',
      unitCount: 50,
      totalPrice: 150,
      
    }
  });

  const [files, setFiles] = useState({
    registrationDoc: null as File | null,
    einDoc: null as File | null
  });

  const [submissionSuccess, setSubmissionSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  // Subscription tier information
  const TIERS = {
    basic: {
      name: 'Basic',
      description: 'For small HOAs with up to 50 units',
      price: 150,
      maxUnits: 50,
      features: [
        'All core features',
        'Up to 50 units',
        'Basic support',
        'Document management',
        'Task management'
      ]
    },
    standard: {
      name: 'Standard',
      description: 'For medium HOAs with up to 150 units',
      price: 350,
      maxUnits: 150,
      features: [
        'All Basic features',
        'Up to 150 units',
        'Priority support',
        'Advanced reporting',
        'Community management'
      ]
    },
    premium: {
      name: 'Premium',
      description: 'For large HOAs with up to 300 units',
      price: 600,
      maxUnits: 300,
      features: [
        'All Standard features',
        'Up to 300 units',
        'Premium support',
        'Advanced analytics',
        'Custom branding'
      ]
    },
    enterprise: {
      name: 'Enterprise',
      description: 'For very large HOAs with unlimited units',
      price: 800,
      maxUnits: 1000,
      features: [
        'All Premium features',
        'Unlimited units',
        'Dedicated support',
        'Custom integrations',
        'White-label solution'
      ]
    }
  };

  const registerHOAMutation = useMutation({
    mutationFn: async () => {
      const formDataToSend = new FormData();

      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'subscription') {
          formDataToSend.append(key, JSON.stringify(value));
        } else {
          formDataToSend.append(key, value as string);
        }
      });

      // Add files
      if (files.registrationDoc) {
        formDataToSend.append('registrationDoc', files.registrationDoc);
      }

      if (files.einDoc) {
        formDataToSend.append('einDoc', files.einDoc);
      }

      return registerHOA(formDataToSend);
    },
    onSuccess: () => {
      setSubmissionSuccess(true);
      toast({
        title: 'HOA Registration Submitted',
        description: 'Your HOA registration has been submitted successfully and is pending approval by a company administrator.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to submit HOA registration',
        variant: 'destructive',
      });
    }
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, files: fileList } = e.target;
    if (fileList && fileList[0]) {
      setFiles(prev => ({ ...prev, [name]: fileList[0] }));
    }
  };

  const handleTierChange = (tier: string) => {
    setFormData({
      ...formData,
      subscription: {
        ...formData.subscription,
        tier,
        unitCount: TIERS[tier as keyof typeof TIERS].maxUnits / 2,
        totalPrice: TIERS[tier as keyof typeof TIERS].price
      }
    });
  };

  const handleUnitCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const unitCount = parseInt(e.target.value, 10);
    setFormData({
      ...formData,
      subscription: {
        ...formData.subscription,
        unitCount: isNaN(unitCount) ? 0 : unitCount
      }
    });
  };

    const handleStateChange = (selectedCode: string) => {
      setFormData({
        ...formData,
        hoaState: selectedCode
      });
    }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.hoaCommunityName || !formData.contactEmail || !formData.einNumber) {
      toast({
        title: 'Missing Required Fields',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    // Validate required files
    if (!files.registrationDoc || !files.einDoc) {
      toast({
        title: 'Missing Required Documents',
        description: 'Please upload all required documents.',
        variant: 'destructive',
      });
      return;
    }

    registerHOAMutation.mutate();
  };

  if (submissionSuccess) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-700">
            <Building2 className="h-6 w-6" />
            HOA Registration Submitted
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="mb-6 bg-green-50 border-green-200">
            <AlertTitle className="text-green-800">Registration Successful</AlertTitle>
            <AlertDescription className="text-green-700">
              <p>Your HOA registration has been submitted successfully and is pending approval by a company administrator.</p>
              <p className="mt-4">Once approved, you will receive an email with your HOA community code that you can share with your HOA administrators.</p>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-6 w-6" />
          Register New HOA Community
        </CardTitle>
        <CardDescription>
          Submit your HOA registration for approval by a company administrator
        </CardDescription>
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md text-sm">
          <p className="font-medium mb-1">HOA Registration Process:</p>
          <ol className="list-decimal pl-5 space-y-1">
            <li>Fill out this form to register your HOA</li>
            <li>Your registration will be reviewed by company administrators</li>
            <li>Once approved, you'll receive a community code to share with your HOA administrators</li>
            <li>HOA administrators can then register using the resident registration form with this code</li>
          </ol>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <Tabs value={activeTab}
            onValueChange={(newTab) => {
              if (newTab === 'documents') {
                if (!formData.hoaCommunityName || !formData.contactEmail || !formData.einNumber) {
                  toast({
                    title: 'Incomplete HOA Details',
                    description: 'Please fill in all required HOA details before proceeding to upload documents.',
                    variant: 'destructive',
                  });
                  return;
                }
              }

              if (newTab === 'subscription') {
                if (!files.registrationDoc || !files.einDoc) {
                  toast({
                    title: 'Documents Missing',
                    description: 'Please upload both required documents before selecting a subscription.',
                    variant: 'destructive',
                  });
                  return;
                }
              }

              // Allow tab change
              setActiveTab(newTab);
            }}
            className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="details">
                <Building2 className="h-4 w-4 mr-2" />
                HOA Details
              </TabsTrigger>
              <TabsTrigger value="documents">
                <Upload className="h-4 w-4 mr-2" />
                Documents
              </TabsTrigger>
              <TabsTrigger value="subscription">
                <CreditCard className="h-4 w-4 mr-2" />
                Subscription
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="hoaCommunityName">
                    HOA Community Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="hoaCommunityName"
                    name="hoaCommunityName"
                    value={formData.hoaCommunityName}
                    onChange={handleInputChange}
                    placeholder="Enter HOA community name"
                    required
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="hoaStreetAddress">Street Address</Label>
                  <Input
                    id="hoaStreetAddress"
                    name="hoaStreetAddress"
                    value={formData.hoaStreetAddress}
                    onChange={handleInputChange}
                    placeholder="Enter street address"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="hoaCity">City</Label>
                    <Input
                      id="hoaCity"
                      name="hoaCity"
                      value={formData.hoaCity}
                      onChange={handleInputChange}
                      placeholder="Enter city"
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="hoaState">State</Label>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-between"
                        >
                          {formData.hoaState
                            ? US_STATES.find(
                                (state) => state.code === formData.hoaState
                              )?.name
                            : 'Select a state'}
                          <ChevronDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-[var(--radix-popover-trigger-width)] max-h-60 overflow-y-auto">
                        {US_STATES.map((state) => (
                          <DropdownMenuItem
                            key={state.code}
                            onClick={() => handleStateChange(state.code)}
                          >
                            {state.name}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="hoaZipCode">Zip Code</Label>
                    <Input
                      id="hoaZipCode"
                      name="hoaZipCode"
                      value={formData.hoaZipCode}
                      onChange={handleInputChange}
                      placeholder="Enter zip code"
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="contactEmail">
                    Contact Email <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="contactEmail"
                    name="contactEmail"
                    type="email"
                    value={formData.contactEmail}
                    onChange={handleInputChange}
                    placeholder="Enter contact email"
                    required
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="contactPhone">Contact Phone</Label>
                  <Input
                    id="contactPhone"
                    name="contactPhone"
                    value={formData.contactPhone}
                    onChange={handleInputChange}
                    placeholder="Enter contact phone"
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="einNumber">
                    EIN Number <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="einNumber"
                    name="einNumber"
                    value={formData.einNumber}
                    onChange={handleInputChange}
                    placeholder="Enter EIN number (XX-XXXXXXX)"
                    required
                  />
                  <p className="text-sm text-muted-foreground">
                    The Employer Identification Number (EIN) for your HOA
                  </p>
                </div>

                <div className="flex justify-end">
                  <Button
                    type="button"
                    onClick={() => setActiveTab('documents')}
                    disabled={
                      !formData.hoaCommunityName ||
                      !formData.contactEmail ||
                      !formData.einNumber
                    }
                  >
                    Next: Upload Documents
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="documents" className="space-y-4">
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="registrationDoc">
                    HOA Registration Document <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="registrationDoc"
                    name="registrationDoc"
                    type="file"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    onChange={handleFileChange}
                    className="cursor-pointer"
                    required
                  />
                  <p className="text-sm text-muted-foreground">
                    Upload your HOA's official registration document
                  </p>
                  {files.registrationDoc && (
                    <p className="text-sm text-green-600">
                      File selected: {files.registrationDoc.name}
                    </p>
                  )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="einDoc">
                    EIN Document <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="einDoc"
                    name="einDoc"
                    type="file"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    onChange={handleFileChange}
                    className="cursor-pointer"
                    required
                  />
                  <p className="text-sm text-muted-foreground">
                    Upload your HOA's EIN document or tax exemption letter
                  </p>
                  {files.einDoc && (
                    <p className="text-sm text-green-600">
                      File selected: {files.einDoc.name}
                    </p>
                  )}
                </div>

                <div className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setActiveTab('details')}
                  >
                    Back to Details
                  </Button>
                  <Button
                    type="button"
                    onClick={() => setActiveTab('subscription')}
                    disabled={!files.registrationDoc || !files.einDoc}
                  >
                    Next: Choose Subscription
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="subscription" className="space-y-4">
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="subscriptionTier">Subscription Tier</Label>
                  <Select
                    value={formData.subscription.tier}
                    onValueChange={handleTierChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a subscription tier" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(TIERS).map(([key, tier]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex items-center justify-between w-full">
                            <span>{tier.name}</span>
                            <Badge variant="outline">${tier.price}/month</Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    {TIERS[formData.subscription.tier as keyof typeof TIERS].description}
                  </p>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="unitCount">Number of Units</Label>
                  <Input
                    id="unitCount"
                    name="unitCount"
                    type="number"
                    min="1"
                    max={TIERS[formData.subscription.tier as keyof typeof TIERS].maxUnits}
                    value={formData.subscription.unitCount}
                    onChange={handleUnitCountChange}
                  />
                  <p className="text-sm text-muted-foreground">
                    Maximum {TIERS[formData.subscription.tier as keyof typeof TIERS].maxUnits} units for {TIERS[formData.subscription.tier as keyof typeof TIERS].name} tier
                  </p>
                </div>

                <div className="mt-6 p-4 bg-gray-50 rounded-md">
                  <h3 className="font-medium mb-2">Features included:</h3>
                  <ul className="space-y-1">
                    {TIERS[formData.subscription.tier as keyof typeof TIERS].features.map((feature, index) => (
                      <li key={index} className="text-sm flex items-center">
                        <span className="mr-2 text-green-500">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="flex items-center space-x-2 mt-4">
                  <input
                    id="acceptedTerms"
                    type="checkbox"
                    checked={acceptedTerms}
                    onChange={(e) => setAcceptedTerms(e.target.checked)}
                    className="accent-blue-600 w-4 h-4"
                    required
                    />
                    <label htmlFor="acceptedTerms" className="text-sm text-muted-foreground">
                    I accept the <a href="/privacy-policy" className="underline text-blue-600" target="_blank" rel="noopener noreferrer">Privacy Policy</a>, <a href="/terms-of-use" className="underline text-blue-600" target="_blank" rel="noopener noreferrer">Terms of Use</a>, and <a href="/cookie-policy" className="underline text-blue-600" target="_blank" rel="noopener noreferrer">Cookie Policy</a>
                  </label>
                </div>
                <div className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setActiveTab('documents')}
                  >
                    Back to Documents
                  </Button>
                  <Button
                    type="submit"
                    disabled={!acceptedTerms || registerHOAMutation.isPending}
                  >
                    {registerHOAMutation.isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Submit HOA Registration
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </form>
      </CardContent>
    </Card>
  );
};

export default PublicHOARegistration;

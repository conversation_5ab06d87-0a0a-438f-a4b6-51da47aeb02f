import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ChevronLeft, ChevronRight, X, Download } from 'lucide-react';
import { getProfilePhotoUrl } from '@/utils/imageUtils';

interface PropertyPhoto {
  _id: string;
  name: string;
  description?: string;
  fileUrl: string;
  uploadDate: string;
  category: string;
}

interface PropertyPhotoSlideshowProps {
  photos: PropertyPhoto[];
  initialPhotoIndex?: number;
  isOpen: boolean;
  onClose: () => void;
}

const PropertyPhotoSlideshow: React.FC<PropertyPhotoSlideshowProps> = ({
  photos,
  initialPhotoIndex = 0,
  isOpen,
  onClose
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialPhotoIndex);
  const [isLoading, setIsLoading] = useState(true);

  // Reset current index when photos change
  useEffect(() => {
    setCurrentIndex(initialPhotoIndex);
  }, [photos, initialPhotoIndex]);

  // Reset loading state when current index changes
  useEffect(() => {
    setIsLoading(true);
  }, [currentIndex]);

  const currentPhoto = photos[currentIndex];

  const handlePrevious = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? photos.length - 1 : prevIndex - 1));
  };

  const handleNext = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prevIndex) => (prevIndex === photos.length - 1 ? 0 : prevIndex + 1));
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'ArrowLeft') {
      setCurrentIndex((prevIndex) => (prevIndex === 0 ? photos.length - 1 : prevIndex - 1));
    } else if (e.key === 'ArrowRight') {
      setCurrentIndex((prevIndex) => (prevIndex === photos.length - 1 ? 0 : prevIndex + 1));
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  // Add keyboard event listeners
  useEffect(() => {
    if (isOpen) {
      window.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, photos.length]);

  // Handle download
  const handleDownload = () => {
    if (!currentPhoto) return;
    
    const link = document.createElement('a');
    link.href = getProfilePhotoUrl(currentPhoto.fileUrl) || '';
    link.download = currentPhoto.name || 'property-photo.jpg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!currentPhoto) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[90vw] max-h-[90vh] p-0 overflow-hidden bg-black/90 border-none">
        <div className="relative flex flex-col h-full">
          {/* Close button */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 z-50 text-white bg-black/50 hover:bg-black/70"
            onClick={onClose}
          >
            <X className="h-5 w-5" />
          </Button>
          
          {/* Download button */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-12 z-50 text-white bg-black/50 hover:bg-black/70"
            onClick={handleDownload}
          >
            <Download className="h-5 w-5" />
          </Button>

          {/* Navigation buttons */}
          {photos.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="icon"
                className="absolute left-2 top-1/2 transform -translate-y-1/2 z-50 text-white bg-black/50 hover:bg-black/70"
                onClick={handlePrevious}
              >
                <ChevronLeft className="h-8 w-8" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 z-50 text-white bg-black/50 hover:bg-black/70"
                onClick={handleNext}
              >
                <ChevronRight className="h-8 w-8" />
              </Button>
            </>
          )}

          {/* Main image */}
          <div className="flex-1 flex items-center justify-center p-4">
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
              </div>
            )}
            <img
              src={getProfilePhotoUrl(currentPhoto.fileUrl) || ''}
              alt={currentPhoto.name}
              className="max-w-full max-h-[calc(90vh-120px)] object-contain"
              onLoad={() => setIsLoading(false)}
              onError={(e) => {
                setIsLoading(false);
                (e.target as HTMLImageElement).src = 'https://placehold.co/800x600?text=Error+Loading+Image';
              }}
            />
          </div>

          {/* Caption */}
          <div className="bg-black/80 p-4 text-white">
            <h3 className="text-lg font-medium">
              {currentPhoto.name.split('_').slice(1).join('_') || 'Property Photo'}
            </h3>
            {currentPhoto.description && (
              <p className="text-sm text-gray-300 mt-1">{currentPhoto.description}</p>
            )}
            <div className="flex justify-between items-center mt-2">
              <p className="text-sm text-gray-400">
                Category: {currentPhoto.category.charAt(0).toUpperCase() + currentPhoto.category.slice(1)}
              </p>
              <p className="text-sm text-gray-400">
                {currentIndex + 1} of {photos.length}
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PropertyPhotoSlideshow;

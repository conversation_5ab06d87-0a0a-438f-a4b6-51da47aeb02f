/**
 * HOA Revenue Selector Component
 *
 * This component renders a dropdown selector for HOAs with revenue information.
 * It's specifically designed for company admins to monitor HOA activity and financial contributions.
 */
import React, { useEffect, useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useQuery } from '@tanstack/react-query';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { DollarSign, TrendingUp, AlertCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { getHOARevenueSummary, HOARevenueSummary } from '@/services/hoaRevenueService';
import { Skeleton } from '@/components/ui/skeleton';

// Custom component for rendering the selected HOA in the trigger
interface SelectedHOADisplayProps {
  selectedHOAId: string | null;
  hoaRevenue: HOARevenueSummary[];
  getStatusBadgeColor: (status: string) => string;
  getTierBadgeColor: (tier: string) => string;
  formatCurrency: (amount: number) => string;
}

const SelectedHOADisplay: React.FC<SelectedHOADisplayProps> = ({
  selectedHOAId,
  hoaRevenue,
  getStatusBadgeColor,
  getTierBadgeColor,
  formatCurrency
}) => {
  if (!selectedHOAId || selectedHOAId === 'all') {
    // Calculate total revenue from all HOAs
    const totalRevenue = hoaRevenue.reduce((total, hoa) => total + (hoa.totalRevenue || 0), 0);

    return (
      <div className="flex flex-col">
        <div className="flex items-center gap-2">
          <span className="font-medium">All HOAs</span>
          <span className="text-sm font-semibold text-green-600">
            ${totalRevenue.toLocaleString()}
          </span>
        </div>
      </div>
    );
  }

  const selectedHOA = hoaRevenue.find(hoa => hoa._id === selectedHOAId);

  if (!selectedHOA) {
    return <span>Select an HOA</span>;
  }

  return (
    <div className="flex flex-col">
      <div className="flex items-center gap-2">
        <span className="font-medium">{selectedHOA.name}</span>
        <span className="text-sm font-semibold text-green-600">
          ${selectedHOA.totalRevenue.toLocaleString()}
        </span>
      </div>
      <div className="flex items-center gap-3 mt-1">
        <span className={`hoa-status-badge ${getStatusBadgeColor(selectedHOA.subscriptionStatus)}`}>
          {selectedHOA.subscriptionStatus}
        </span>
        <span className={`hoa-tier-badge ${getTierBadgeColor(selectedHOA.subscriptionTier)}`}>
          {selectedHOA.subscriptionTier}
        </span>
      </div>
    </div>
  );
};

interface HOARevenueSelectorProps {
  onHOAChange: (hoaId: string | null) => void;
  selectedHOAId?: string | null;
}

const HOARevenueSelector: React.FC<HOARevenueSelectorProps> = ({ onHOAChange, selectedHOAId }) => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isCompanyAdmin = user?.role === 'company_admin';

  // Fetch HOA revenue summary
  const { data: hoaRevenue, isLoading, error } = useQuery({
    queryKey: ['hoa-revenue-summary'],
    queryFn: getHOARevenueSummary,
    enabled: isCompanyAdmin,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Handle HOA selection
  const handleHOAChange = (value: string) => {
    if (value === 'all') {
      onHOAChange(null);
    } else {
      onHOAChange(value);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Get subscription status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border border-green-200';
      case 'trialing':
        return 'bg-blue-100 text-blue-800 border border-blue-200';
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
      case 'canceled':
        return 'bg-red-100 text-red-800 border border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
  };

  // Add CSS for badges
  React.useEffect(() => {
    // Add CSS for badges if not already present
    if (!document.getElementById('hoa-badge-styles')) {
      const style = document.createElement('style');
      style.id = 'hoa-badge-styles';
      style.innerHTML = `
        .hoa-status-badge, .hoa-tier-badge {
          display: inline-block;
          padding: 0.125rem 0.5rem;
          font-size: 0.75rem;
          font-weight: 500;
          border-radius: 0.375rem;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  // Get tier badge color
  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case 'basic':
        return 'bg-blue-100 text-blue-800 border border-blue-200';
      case 'standard':
        return 'bg-purple-100 text-purple-800 border border-purple-200';
      case 'premium':
        return 'bg-amber-100 text-amber-800 border border-amber-200';
      case 'enterprise':
        return 'bg-indigo-100 text-indigo-800 border border-indigo-200';
      default:
        return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
  };

  if (!isCompanyAdmin) {
    return null;
  }

  return (
    <div className="flex flex-col space-y-1.5">
      <div className="flex items-center gap-2">
        <Label htmlFor="hoa-select" className="font-medium">HOA Watchdog</Label>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">Monitor HOA activity and financial contributions across the platform</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {isLoading ? (
        <Skeleton className="h-10 w-full" />
      ) : error ? (
        <div className="text-sm text-destructive">Error loading HOA data</div>
      ) : hoaRevenue && hoaRevenue.length > 0 ? (
        <Select
          value={selectedHOAId || 'all'}
          onValueChange={handleHOAChange}
        >
          <SelectTrigger id="hoa-select" className="w-full min-h-[60px]">
            <SelectedHOADisplay
              selectedHOAId={selectedHOAId}
              hoaRevenue={hoaRevenue}
              getStatusBadgeColor={getStatusBadgeColor}
              getTierBadgeColor={getTierBadgeColor}
              formatCurrency={formatCurrency}
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all" className="font-medium">All HOAs</SelectItem>

            {hoaRevenue.map((hoa: HOARevenueSummary) => (
              <SelectItem key={hoa._id} value={hoa._id} className="py-2 px-1">
                <div className="flex flex-col gap-1">
                  <div className="flex items-center justify-between w-full">
                    <span className="font-medium">{hoa.name}</span>
                    <div className="flex items-center gap-1">
                      <span className="text-sm font-semibold text-green-600">
                        ${hoa.totalRevenue.toLocaleString()}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center mt-0.5">
                    <span className={`hoa-status-badge ${getStatusBadgeColor(hoa.subscriptionStatus)}`}>
                      {hoa.subscriptionStatus}
                    </span>
                    <span className={`hoa-tier-badge ${getTierBadgeColor(hoa.subscriptionTier)}`}>
                      {hoa.subscriptionTier}
                    </span>
                    <span className="text-xs text-muted-foreground ml-auto">{hoa.code}</span>
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : (
        <div className="text-sm text-muted-foreground">No HOAs found</div>
      )}
    </div>
  );
};

export default HOARevenueSelector;

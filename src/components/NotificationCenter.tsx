import React, { useState } from 'react';
import { Card } from './ui/card';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { formatTimeAgo } from '../utils/date';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from '@/lib/utils';
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "@/components/ui/use-toast";
import api from '@/lib/axios';
import {
  Bell,
  Clock,
  DollarSign,
  CheckCircle,
  AlertTriangle,
  Calendar,
  Ticket,
  User,
  Settings,
  X,
  Trash2
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Notification {
  _id: string;
  id?: string; // Some responses might use id instead of _id
  type: string;
  title: string;
  description: string;
  createdAt?: string;
  time?: string; // Formatted time string from backend
  read: boolean;
  metadata?: any;
}

interface NotificationCenterProps {
  limit?: number;
  hoaId?: string | null;
  communityId?: string | null;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  limit = 5,
  hoaId,
  communityId
}) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isMarkingRead, setIsMarkingRead] = useState(false);

  const { data: notifications = [], isLoading, isError } = useQuery<Notification[]>({
    queryKey: ['notifications', hoaId, communityId],
    queryFn: async () => {
      try {
        console.log('Fetching notifications...');

        // Build endpoint with filters
        let endpoint = '/api/notifications';
        const params = [];

        if (hoaId) {
          params.push(`hoaId=${hoaId}`);
        }

        if (communityId) {
          params.push(`communityId=${communityId}`);
        }

        if (params.length > 0) {
          endpoint += `?${params.join('&')}`;
        }

        console.log('Fetching notifications from:', endpoint);
        const response = await api.get(endpoint);
        console.log('Notifications response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Error fetching notifications:', error);
        return [];
      }
    },
    refetchInterval: 30000, // Reduced frequency to 30 seconds
    refetchOnWindowFocus: false, // Don't refetch on window focus
    staleTime: 10000, // Consider data fresh for 10 seconds
    retry: 1, // Only retry once if the request fails
  });

  // Function to mark a notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      setIsMarkingRead(true);
      await api.post(`/api/notifications/${notificationId}/read`);

      // Update the cache to mark this notification as read
      queryClient.setQueryData(['notifications', hoaId, communityId], (oldData: Notification[] | undefined) => {
        if (!oldData) return [];
        return oldData.map(notification => {
          if (notification._id === notificationId || notification.id === notificationId) {
            return { ...notification, read: true };
          }
          return notification;
        });
      });

      toast({
        title: "Notification dismissed",
        description: "The notification has been marked as read.",
        duration: 3000
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast({
        title: "Error",
        description: "Failed to dismiss notification. Please try again.",
        variant: "destructive",
        duration: 3000
      });
    } finally {
      setIsMarkingRead(false);
    }
  };

  // Create a copy of notifications to avoid modifying the original data
  const recentNotifications = [...notifications]
    .filter(n => !n.read)
    // Sort by id which should be in chronological order (newest first)
    .sort((a, b) => {
      // Try to extract timestamp from MongoDB ObjectId
      const getTimestamp = (id: string) => {
        try {
          // MongoDB ObjectId has timestamp in first 4 bytes
          return parseInt(id.substring(0, 8), 16) * 1000;
        } catch (e) {
          return 0;
        }
      };
      return getTimestamp(b._id) - getTimestamp(a._id);
    })
    .slice(0, limit);

  const getNotificationIcon = (type: string) => {
    if (!type) return <Bell className="h-5 w-5 text-gray-500" />;

    switch (type.toLowerCase()) {
      case 'payment':
        return <DollarSign className="h-5 w-5 text-green-500" />;
      case 'task':
        return <Ticket className="h-5 w-5 text-blue-500" />;
      case 'alert':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'event':
        return <Calendar className="h-5 w-5 text-purple-500" />;
      case 'member':
        return <User className="h-5 w-5 text-indigo-500" />;
      case 'settings':
        return <Settings className="h-5 w-5 text-gray-500" />;
      case 'info':
        return <CheckCircle className="h-5 w-5 text-teal-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  if (isLoading) {
    return (
      <Card className="p-4">
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card className="p-4">
        <div className="text-center text-red-500">
          Failed to load notifications
        </div>
      </Card>
    );
  }

  if (recentNotifications.length === 0) {
    return (
      <Card className="p-4">
        <div className="text-center text-gray-500">
          No new notifications
        </div>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Notifications</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/notifications')}
          >
            View All
          </Button>
        </div>

        <ScrollArea className="h-[300px] pr-3">
          <div className="space-y-4">
            {recentNotifications.map((notification) => (
              <div
                key={notification._id}
                className="flex items-start gap-4 p-4 bg-card hover:bg-accent/10 transition-colors rounded-lg cursor-pointer relative group"
                onClick={() => {
                  if (notification.metadata?.link) {
                    navigate(notification.metadata.link);
                  }
                }}
              >
                <div className="shrink-0">
                  {getNotificationIcon(notification.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2">
                    <h3 className="font-medium truncate">{notification.title}</h3>
                    <span className="text-sm text-muted-foreground whitespace-nowrap">
                      {notification.time || formatTimeAgo(notification.createdAt || '')}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {notification.description}
                  </p>
                </div>

                {/* Dismiss button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6"
                  onClick={(e) => {
                    e.stopPropagation();
                    markAsRead(notification._id || notification.id || '');
                  }}
                  disabled={isMarkingRead}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}

            {recentNotifications.length === 0 && (
              <div className="text-center text-muted-foreground p-4">
                No new notifications
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </Card>
  );
};

export default NotificationCenter;

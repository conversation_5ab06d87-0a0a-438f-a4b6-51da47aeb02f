import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, Shield, Smartphone } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { sendVerificationCode, verifyCode } from '@/services/mfaService';

interface MFAVerificationProps {
  phoneNumber: string;
  onVerificationSuccess: (token: string, user: any) => void;
  onCancel: () => void;
}

const MFAVerification = ({ phoneNumber, onVerificationSuccess, onCancel }: MFAVerificationProps) => {
  const { toast } = useToast();
  const [verificationCode, setVerificationCode] = useState('');
  const [isCodeSent, setIsCodeSent] = useState(false);

  // Send verification code mutation
  const sendCodeMutation = useMutation({
    mutationFn: sendVerificationCode,
    onSuccess: () => {
      setIsCodeSent(true);
      toast({
        title: 'Verification Code Sent',
        description: 'A verification code has been sent to your email (simulating SMS).',
        variant: 'default',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to Send Code',
        description: error.response?.data?.message || 'An error occurred while sending the verification code.',
        variant: 'destructive',
      });
    },
  });

  // Verify code mutation
  const verifyCodeMutation = useMutation({
    mutationFn: ({ phoneNumber, code }: { phoneNumber: string; code: string }) => 
      verifyCode(phoneNumber, code),
    onSuccess: (data) => {
      toast({
        title: 'Verification Successful',
        description: 'You have successfully verified your identity.',
        variant: 'default',
      });
      onVerificationSuccess(data.token, data.user);
    },
    onError: (error: any) => {
      toast({
        title: 'Verification Failed',
        description: error.response?.data?.message || 'Invalid or expired verification code.',
        variant: 'destructive',
      });
    },
  });

  const handleSendCode = () => {
    sendCodeMutation.mutate(phoneNumber);
  };

  const handleVerifyCode = (e: React.FormEvent) => {
    e.preventDefault();
    verifyCodeMutation.mutate({ phoneNumber, code: verificationCode });
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-primary" />
          <CardTitle>Two-Factor Authentication</CardTitle>
        </div>
        <CardDescription>
          Verify your identity to complete the login process
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-start gap-3 mb-6">
          <Smartphone className="h-5 w-5 mt-0.5 text-primary" />
          <div>
            <h4 className="font-medium">Verification Required</h4>
            <p className="text-sm text-muted-foreground mt-1">
              A verification code will be sent to {phoneNumber}
            </p>
            <p className="text-sm mt-3">
              For this demo, the code will be sent to your email instead of SMS.
            </p>
          </div>
        </div>

        {!isCodeSent ? (
          <Button 
            className="w-full" 
            onClick={handleSendCode}
            disabled={sendCodeMutation.isPending}
          >
            {sendCodeMutation.isPending ? 'Sending...' : 'Send Verification Code'}
          </Button>
        ) : (
          <form onSubmit={handleVerifyCode}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="verificationCode">Verification Code</Label>
                <Input
                  id="verificationCode"
                  placeholder="Enter 6-digit code"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  required
                />
              </div>
              <Button 
                type="submit" 
                className="w-full"
                disabled={verifyCodeMutation.isPending}
              >
                {verifyCodeMutation.isPending ? 'Verifying...' : 'Verify Code'}
              </Button>
            </div>
          </form>
        )}

        {(sendCodeMutation.isError || verifyCodeMutation.isError) && (
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {(sendCodeMutation.error as any)?.response?.data?.message || 
               (verifyCodeMutation.error as any)?.response?.data?.message || 
               'An error occurred. Please try again.'}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        {isCodeSent && (
          <Button variant="link" onClick={handleSendCode} disabled={sendCodeMutation.isPending}>
            Resend Code
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default MFAVerification;

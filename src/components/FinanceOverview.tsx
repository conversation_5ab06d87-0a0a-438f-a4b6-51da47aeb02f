import React, { useContext } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip,
  ResponsiveContainer, Legend
} from 'recharts';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/axios';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface FinanceOverviewProps {
  hoaId?: string | null;
  communityId?: string | null;
  communityIds?: string[]; // Support for multiple communities
}

const FinanceOverview: React.FC<FinanceOverviewProps> = ({ hoaId, communityId, communityIds }) => {
  const queryClient = useQueryClient();
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isCompanyAdmin = user?.role === 'company_admin';
  const isAdmin = user?.role === 'admin';

  // Fetch HOAs for company admin dropdown
  const { data: hoas = [] } = useQuery({
    queryKey: ['hoas'],
    queryFn: async () => {
      try {
        const response = await api.get('/api/hoa');
        return response.data.data || [];
      } catch (error) {
        console.error('Error fetching HOAs:', error);
        return [];
      }
    },
    enabled: isCompanyAdmin
  });

  // Chart Queries
  const { data: monthlyData = [], isLoading: loadingChart, isError: errorChart } = useQuery({
    queryKey: ['monthlyFinance', hoaId, communityId, communityIds],
    queryFn: async () => {
      try {
        // Build endpoint with filters
        let endpoint = '/api/finances/monthly';
        const params = [];

        // Handle multiple communities
        if (communityIds && communityIds.length > 0) {
          communityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('Fetching monthly finance for multiple communities:', communityIds);
        } else {
          if (hoaId) {
            params.push(`hoaId=${hoaId}`);
          }

          if (communityId) {
            params.push(`communityId=${communityId}`);
          }
        }

        if (params.length > 0) {
          endpoint += `?${params.join('&')}`;
        }

        console.log('Fetching monthly finance data from:', endpoint);
        const res = await api.get(endpoint);
        console.log('Monthly finance data:', res.data);
        return res.data || [];
      } catch (error) {
        console.error('Error fetching monthly finance:', error);
        return [];
      }
    }
  });

  const { data: expensesCategories = [], isLoading: loadingBreakdown, isError: errorBreakdown } = useQuery({
    queryKey: ['financeBreakdown', hoaId, communityId, communityIds],
    queryFn: async () => {
      try {
        // Build endpoint with filters
        let endpoint = '/api/finances/breakdown';
        const params = [];

        // Handle multiple communities
        if (communityIds && communityIds.length > 0) {
          communityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('Fetching finance breakdown for multiple communities:', communityIds);
        } else {
          if (hoaId) {
            params.push(`hoaId=${hoaId}`);
          }

          if (communityId) {
            params.push(`communityId=${communityId}`);
          }
        }

        if (params.length > 0) {
          endpoint += `?${params.join('&')}`;
        }

        console.log('Fetching finance breakdown from:', endpoint);
        const res = await api.get(endpoint);
        console.log('Finance breakdown data:', res.data);
        return res.data || [];
      } catch (error) {
        console.error('Error fetching finance breakdown:', error);
        return [];
      }
    }
  });

  // Ledger Query
  const { data: allEntries = [] } = useQuery({
    queryKey: ['allFinance', hoaId, communityId, communityIds],
    queryFn: async () => {
      try {
        // Build endpoint with filters
        let endpoint = '/api/finances';
        const params = [];

        // Handle multiple communities
        if (communityIds && communityIds.length > 0) {
          communityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('Fetching all finance entries for multiple communities:', communityIds);
        } else {
          if (hoaId) {
            params.push(`hoaId=${hoaId}`);
          }

          if (communityId) {
            params.push(`communityId=${communityId}`);
          }
        }

        if (params.length > 0) {
          endpoint += `?${params.join('&')}`;
        }

        console.log('Fetching all finance entries from:', endpoint);
        const res = await api.get(endpoint);
        console.log('All finance entries:', res.data);
        return res.data || [];
      } catch (error) {
        console.error('Error fetching all finances:', error);
        return [];
      }
    }
  });

  // Delete Mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      await api.delete(`/api/finances/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['allFinance'], exact: true });
      queryClient.invalidateQueries({ queryKey: ['monthlyFinance'], exact: true });
      queryClient.invalidateQueries({ queryKey: ['financeBreakdown'], exact: true });
    }
  });

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background p-3 border border-border rounded-md shadow-md">
          <p className="font-medium">{label}</p>
          <p className="text-sm text-hoa-primary">Income: ${payload[0].value}</p>
          <p className="text-sm text-hoa-destructive">Expenses: ${payload[1].value}</p>
          <p className="text-sm text-muted-foreground">Net: ${payload[0].value - payload[1].value}</p>
        </div>
      );
    }
    return null;
  };

  if (loadingChart || loadingBreakdown) return <div>Loading finances...</div>;

  if (errorChart || errorBreakdown) {
    return (
      <div className="space-y-4">
        <p className="text-red-500 font-semibold">Failed to load finance data.</p>
      </div>
    );
  }

  if (!monthlyData?.length && !expensesCategories?.length) {
    return (
      <div className="space-y-4">
        <p className="text-muted-foreground">No finance data found yet.</p>
      </div>
    );
  }

  return (
    <div>
      {/* Company Admin HOA Selector */}
      {isCompanyAdmin && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-blue-900">Financial Oversight</h3>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-blue-800 mb-2">
                Select HOA to Monitor
              </label>
              <Select value={hoaId || 'all'} onValueChange={(value) => {
                const url = new URL(window.location.href);
                if (value && value !== 'all') {
                  url.searchParams.set('hoaId', value);
                } else {
                  url.searchParams.delete('hoaId');
                }
                window.history.pushState({}, '', url.toString());
                window.location.reload(); // Refresh to update data
              }}>
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="All HOAs" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All HOAs</SelectItem>
                  {hoas.map((hoa: any) => (
                    <SelectItem key={hoa._id} value={hoa._id}>
                      {hoa.hoaCommunityName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {hoaId && (
              <div className="flex-1">
                <label className="block text-sm font-medium text-blue-800 mb-2">
                  Currently Viewing
                </label>
                <div className="p-2 bg-white border rounded text-sm">
                  {hoas.find((hoa: any) => hoa._id === hoaId)?.hoaCommunityName || 'Selected HOA'}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <Tabs defaultValue="chart">
        <TabsList className="mb-4 w-full">
          <TabsTrigger value="chart" className="flex-1">Income vs Expenses</TabsTrigger>
          <TabsTrigger value="breakdown" className="flex-1">Expense Breakdown</TabsTrigger>
        </TabsList>

        <TabsContent value="chart" className="h-[250px] sm:h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={monthlyData}
              margin={{
                top: 20,
                right: 10,
                left: 0,
                bottom: 5
              }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis
                dataKey="month"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => value.substring(0, 3)} // Show abbreviated month on mobile
              />
              <YAxis
                width={35}
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `$${value}`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend wrapperStyle={{ fontSize: '12px', marginTop: '10px' }} />
              <Bar dataKey="income" name="Income" fill="#0EA5E9" radius={[4, 4, 0, 0]} />
              <Bar dataKey="expenses" name="Expenses" fill="#EF4444" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </TabsContent>

        <TabsContent value="breakdown">
          <div className="space-y-4">
            {expensesCategories.map((category: any, index: number) => (
              <div key={index} className="space-y-1">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{category.category}</span>
                  <span className="text-sm text-muted-foreground">${category.amount}</span>
                </div>
                <Progress value={category.percentage} className="h-2" />
              </div>
            ))}

            <div className="mt-6 pt-4 border-t border-border">
              <div className="flex justify-between items-center">
                <span className="font-semibold">Total Expenses</span>
                <span className="font-semibold">
                  ${expensesCategories.reduce((total: number, curr: any) => total + curr.amount, 0)}
                </span>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* 📒 Written Ledger */}
      {isAdmin && <div className="mt-10 border-t pt-6">
        <h3 className="text-lg font-bold mb-4">Ledger</h3>
        <div className="space-y-3">
          {allEntries.map((entry: any) => (
            <div
              key={entry._id}
              className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 bg-gray-50 border p-3 rounded"
            >
              <div>
                <p className="font-semibold">{entry.type.toUpperCase()} - ${entry.amount}</p>
                <p className="text-sm text-muted-foreground">
                  {entry.category} — {new Date(entry.createdAt).toLocaleDateString()}
                </p>
                {entry.note && <p className="text-sm italic mt-1">{entry.note}</p>}
                {entry.createdBy && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Created by: {entry.createdBy.username || entry.createdBy.fullName}
                  </p>
                )}
              </div>
              <div className="flex gap-2 self-end sm:self-center">
                {entry.canModify && !isCompanyAdmin && (
                  <button
                    className="bg-red-500 text-white px-3 py-1 rounded text-sm"
                    onClick={() => deleteMutation.mutate(entry._id)}
                  >
                    Delete
                  </button>
                )}
                {/* Future: store/archive button here */}
              </div>
            </div>
          ))}
        </div>
      </div>}
    </div>
  );
};

export default FinanceOverview;

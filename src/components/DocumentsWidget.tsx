/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronDown, ChevronUp, FileText, Download } from 'lucide-react';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import api from '@/lib/axios';

interface Document {
  _id: string;
  title: string;
  description: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  category: string;
  uploadedAt: string;
  downloadUrl: string;
}

interface DocumentsWidgetProps {
  hoaId?: string | null;
  communityId?: string | null;
}

const DocumentsWidget: React.FC<DocumentsWidgetProps> = ({ hoaId, communityId }) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);

  // Fetch recent documents
  const { data: documents = [], isLoading } = useQuery({
    queryKey: ['recentDocuments', hoaId, communityId],
    queryFn: async () => {
      // Build endpoint with filters
      let endpoint = '/api/documents';
      const params = [];

      if (hoaId) {
        params.push(`hoaId=${hoaId}`);
      }

      if (communityId) {
        params.push(`communityId=${communityId}`);
      }

      if (params.length > 0) {
        endpoint += `?${params.join('&')}`;
      }

      console.log('Fetching documents from:', endpoint);
      const response = await api.get(endpoint);
      // Return only the 5 most recent documents
      return response.data.slice(0, 5);
    }
  });

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  // Handle document download - use direct navigation to avoid CORS issues
  const handleDownload = (doc: Document) => {
    try {
      console.log('Attempting to download document from widget:', doc.title);
      console.log('Download URL:', doc.downloadUrl);

      // Fix the download URL if it's using the wrong domain
      let downloadUrl = doc.downloadUrl;
      if (downloadUrl.includes('hoa-management-app.herokuapp.com')) {
        downloadUrl = downloadUrl.replace(
          'hoa-management-app.herokuapp.com',
          'hoa-management-app-dad2f9d126ae.herokuapp.com'
        );
        console.log('Fixed download URL:', downloadUrl);
      }

      // Use direct navigation to download - this avoids CORS issues
      // The server will redirect to the S3 signed URL and browser will handle the download
      window.open(downloadUrl, '_blank');

      toast({
        title: "Download Started",
        description: `${doc.title} download has been initiated`,
      });
    } catch (error) {
      console.error('Download error:', error);

      toast({
        title: "Download Failed",
        description: "Failed to initiate download. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>HOA Documents</CardTitle>
        <CardDescription>
          Access important community documents
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Collapsible open={isOpen} onOpenChange={setIsOpen} className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-semibold">Recent Documents</h4>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="w-9 p-0">
                {isOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
                <span className="sr-only">Toggle</span>
              </Button>
            </CollapsibleTrigger>
          </div>

          <CollapsibleContent className="space-y-2">
            {isLoading ? (
              // Loading skeletons
              Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-2 py-1">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-full" />
                </div>
              ))
            ) : documents.length === 0 ? (
              <p className="text-sm text-muted-foreground py-2">No documents available</p>
            ) : (
              documents.map((doc: Document) => (
                <div key={doc._id} className="flex items-center justify-between py-1 text-sm">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium truncate max-w-[200px]">{doc.title}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleDownload(doc)}
                  >
                    <Download className="h-4 w-4" />
                    <span className="sr-only">Download</span>
                  </Button>
                </div>
              ))
            )}
          </CollapsibleContent>
        </Collapsible>

        <div className="mt-4 pt-4 border-t">
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/documents')}
          >
            View All Documents
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default DocumentsWidget;

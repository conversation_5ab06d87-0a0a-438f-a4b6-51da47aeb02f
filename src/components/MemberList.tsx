import React, { useEffect, useRef } from 'react';
import { MessageSquare, Loader2, AlertCircle } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import MessageBubble from './MessageBubble';

interface Message {
  _id: string;
  sender: {
    _id: string;
    username: string;
    fullName: string;
    email: string;
    role: string;
  };
  content: string;
  createdAt: string;
  read: boolean;
  edited?: boolean;
  editedAt?: string;
  deleted?: boolean;
  deletedAt?: string;
}

interface MessageListProps {
  messages: Message[] | undefined;
  isLoading: boolean;
  isError: boolean;
  currentUserId: string;
  editingMessageId: string | null;
  editedContent: string;
  editInputRef: React.RefObject<HTMLTextAreaElement>;
  setEditedContent: (content: string) => void;
  saveEditedMessage: () => void;
  cancelEditingMessage: () => void;
  startEditingMessage: (message: Message) => void;
  handleDeleteMessage: (messageId: string) => void;
  getDeletedMessageIds: () => string[];
  isPending: boolean;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  isLoading,
  isError,
  currentUserId,
  editingMessageId,
  editedContent,
  editInputRef,
  setEditedContent,
  saveEditedMessage,
  cancelEditingMessage,
  startEditingMessage,
  handleDeleteMessage,
  getDeletedMessageIds,
  isPending
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setTimeout(() => {
      if (scrollAreaRef.current) {
        scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
      }
    }, 100);
  }, [messages, editingMessageId]);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <AlertCircle className="h-8 w-8 text-destructive mb-2" />
        <p className="text-center text-destructive font-medium">Failed to load messages</p>
        <p className="text-center text-sm text-muted-foreground mt-1">
          There was an error retrieving messages for this conversation
        </p>
      </div>
    );
  }

  if (!messages || messages.length === 0) {
    return (
      <div className="text-center py-8">
        <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground" />
        <h3 className="mt-4 text-lg font-medium">No messages yet</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          Send a message to start the conversation
        </p>
      </div>
    );
  }

  // Updated deduplication logic
  const processMessages = () => {
    const deletedIds = getDeletedMessageIds();
    const tempToRealMap = new Map();

    const realMessages = messages.filter(msg => !msg._id.startsWith('temp-'));
    const tempMessages = messages.filter(msg => msg._id.startsWith('temp-'));

    for (const temp of tempMessages) {
      const match = realMessages.find(real =>
        real.content === temp.content &&
        real.sender._id === temp.sender._id &&
        Math.abs(new Date(real.createdAt).getTime() - new Date(temp.createdAt).getTime()) < 60000
      );
      if (match) {
        tempToRealMap.set(temp._id, match._id);
      }
    }

    const uniqueMessages = new Map();
    for (const msg of messages) {
      if (tempToRealMap.has(msg._id)) continue;
      if (!deletedIds.includes(msg._id) && !msg.deleted) {
        uniqueMessages.set(msg._id, msg);
      }
    }

    return Array.from(uniqueMessages.values()).sort(
      (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
  };

  const processedMessages = processMessages();

  return (
    <ScrollArea className="flex-1 p-4" id="messages-container" ref={scrollAreaRef}>
      <div className="space-y-4">
        {processedMessages.map((message) => (
          <MessageBubble
            key={message._id}
            message={message}
            isCurrentUser={message.sender._id === currentUserId}
            isEditing={editingMessageId === message._id}
            editedContent={editedContent}
            editInputRef={editInputRef}
            setEditedContent={setEditedContent}
            saveEditedMessage={saveEditedMessage}
            cancelEditingMessage={cancelEditingMessage}
            startEditingMessage={startEditingMessage}
            handleDeleteMessage={handleDeleteMessage}
            isPending={isPending}
          />
        ))}
      </div>
    </ScrollArea>
  );
};

export default MessageList;

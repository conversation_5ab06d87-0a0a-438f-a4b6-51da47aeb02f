import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Building2, Copy, Save, AlertCircle, CreditCard } from 'lucide-react';
import { getCurrentUserHOA, updateHOA, HOA, HOAUpdateData } from '@/services/hoaService';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Link } from 'react-router-dom';
import api from '@/lib/axios';

const HOAOffice = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<HOAUpdateData>({
    hoaCommunityName: '',
    hoaStreetAddress: '',
    hoaCity: '',
    hoaState: '',
    hoaZipCode: '',
    hoaPaymentInfo: {
      paymentMethod: 'check',
      paymentInstructions: '',
      dueDate: 1,
      paymentAmount: 0
    }
  });

  // Get selectedCommunityId from localStorage
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });

  // Listen for community selection changes
  useEffect(() => {
    const handleCommunityChange = (event: CustomEvent) => {
      const { communityId } = event.detail;
      setSelectedCommunityId(communityId);
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, []);

  // Fetch HOA data with communityId parameter
  const { data: hoa, isLoading, isError, error } = useQuery({
    queryKey: ['hoa', selectedCommunityId],
    queryFn: async () => {
      // Get selectedCommunityId from localStorage (in case it's updated)
      const communityId = localStorage.getItem('selectedCommunityId');

      if (communityId) {
        console.log('HOAOffice: Fetching HOA data for community:', communityId);

        // Call the correct API endpoint with communityId parameter
        try {
          const response = await api.get(`/api/hoa/by-community/${communityId}`);
          return response.data.data || response.data;
        } catch (error) {
          console.error('Error fetching HOA data with community context:', error);
          throw error;
        }
      } else {
        // Fallback to default service if no community selected
        console.log('HOAOffice: Fetching default HOA data');
        return getCurrentUserHOA();
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Update HOA mutation with communityId parameter
  const updateHOAMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: HOAUpdateData }) => {
      // Get selectedCommunityId from localStorage
      const communityId = localStorage.getItem('selectedCommunityId');

      if (communityId) {
        console.log('HOAOffice: Updating HOA with community context:', communityId);

        // Use existing API endpoint with communityId parameter
        try {
          const response = await api.put(`/api/hoa/${id}?communityId=${communityId}`, data);
          return response.data.data || response.data;
        } catch (error) {
          console.error('Error updating HOA with community context:', error);
          throw error;
        }
      } else {
        // Fallback to default service if no community selected
        console.log('HOAOffice: Updating HOA without community context');
        return updateHOA(id, data);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['hoa', selectedCommunityId] });
      toast({
        title: 'HOA information updated',
        description: 'Your HOA information has been successfully updated.',
        variant: 'default',
      });
      setIsEditing(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Update failed',
        description: error.response?.data?.message || 'Failed to update HOA information. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Update form data when HOA data is loaded
  useEffect(() => {
    if (hoa) {
      setFormData({
        hoaCommunityName: hoa.hoaCommunityName,
        hoaStreetAddress: hoa.hoaStreetAddress,
        hoaCity: hoa.hoaCity,
        hoaState: hoa.hoaState,
        hoaZipCode: hoa.hoaZipCode,
        hoaPaymentInfo: {
          paymentMethod: hoa.hoaPaymentInfo.paymentMethod,
          paymentInstructions: hoa.hoaPaymentInfo.paymentInstructions,
          dueDate: hoa.hoaPaymentInfo.dueDate,
          paymentAmount: hoa.hoaPaymentInfo.paymentAmount
        }
      });
    }
  }, [hoa]);

  // Validate input based on field type
  const validateInput = (name: string, value: string): { isValid: boolean; value: any; error?: string } => {
    // For payment due date, ensure it's a number between 1-31
    if (name === 'dueDate') {
      const numValue = parseInt(value, 10);
      if (isNaN(numValue)) {
        return { isValid: false, value: numValue, error: 'Due date must be a number' };
      }
      if (numValue < 1 || numValue > 31) {
        return { isValid: false, value: numValue, error: 'Due date must be between 1 and 31' };
      }
      return { isValid: true, value: numValue };
    }

    // For ZIP code, ensure it's a valid format (5 digits or 5+4 format)
    if (name === 'hoaZipCode') {
      const zipRegex = /^\d{5}(-\d{4})?$/;
      if (!zipRegex.test(value) && value.trim() !== '') {
        return { isValid: false, value, error: 'ZIP code must be in 12345 or 12345-6789 format' };
      }
    }

    return { isValid: true, value };
  };

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('hoaPaymentInfo.')) {
      const paymentInfoField = name.split('.')[1];

      // Validate the input if needed
      const validation = validateInput(paymentInfoField, value);

      // Update validation errors
      if (!validation.isValid) {
        setValidationErrors(prev => ({
          ...prev,
          [paymentInfoField]: validation.error || 'Invalid input'
        }));
      } else {
        // Clear error if input is now valid
        setValidationErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[paymentInfoField];
          return newErrors;
        });
      }

      setFormData(prev => ({
        ...prev,
        hoaPaymentInfo: {
          ...prev.hoaPaymentInfo,
          [paymentInfoField]: validation.value
        }
      }));
    } else {
      // Validate the input if needed
      const validation = validateInput(name, value);

      // Update validation errors
      if (!validation.isValid) {
        setValidationErrors(prev => ({
          ...prev,
          [name]: validation.error || 'Invalid input'
        }));
      } else {
        // Clear error if input is now valid
        setValidationErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }

      setFormData(prev => ({
        ...prev,
        [name]: validation.value
      }));
    }
  };

  const handleSelectChange = (value: string, field: string) => {
    if (field === 'paymentMethod') {
      setFormData(prev => ({
        ...prev,
        hoaPaymentInfo: {
          ...prev.hoaPaymentInfo,
          paymentMethod: value
        }
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Check if there are any validation errors
    if (Object.keys(validationErrors).length > 0) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form before submitting.",
        variant: "destructive",
      });
      return;
    }

    // Perform additional validation for required fields
    const requiredFields = ['hoaCommunityName', 'hoaCity', 'hoaState', 'hoaZipCode'];
    const missingFields = requiredFields.filter(field => !formData[field]);

    if (missingFields.length > 0) {
      const fieldNames = missingFields.map(field =>
        field.replace('hoa', '')
      ).join(', ');

      toast({
        title: "Missing Information",
        description: `Please fill in the following required fields: ${fieldNames}`,
        variant: "destructive",
      });
      return;
    }

    // Add last updated timestamp
    const updatedData = {
      ...formData,
      lastUpdated: new Date().toISOString()
    };

    if (hoa) {
      updateHOAMutation.mutate({
        id: hoa._id,
        data: updatedData
      });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied to clipboard',
      description: 'The HOA community code has been copied to your clipboard.',
      variant: 'default',
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-3/4 mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {(error as any)?.message || 'Failed to load HOA information. Please try again later.'}
        </AlertDescription>
      </Alert>
    );
  }

  if (!hoa) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>No HOA Found</AlertTitle>
        <AlertDescription>
          Your account is not associated with any HOA. Please contact the system administrator.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          <CardTitle>HOA Office</CardTitle>
        </div>
        <CardDescription>
          Manage your HOA community information
          {hoa.lastUpdated && (
            <div className="text-xs text-muted-foreground mt-1">
              Last updated: {new Date(hoa.lastUpdated).toLocaleString()}
            </div>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="hoaCommunityName">HOA Community Name <span className="text-red-500">*</span></Label>
            <Input
              id="hoaCommunityName"
              name="hoaCommunityName"
              value={formData.hoaCommunityName}
              onChange={handleInputChange}
              disabled={!isEditing || updateHOAMutation.isPending}
              required
            />
            <p className="text-xs text-muted-foreground">Required field</p>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="hoaCommunityCode">HOA Community Code</Label>
            <div className="flex gap-2">
              <Input
                id="hoaCommunityCode"
                value={hoa.hoaCommunityCode}
                disabled={true}
                className="font-mono"
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => copyToClipboard(hoa.hoaCommunityCode)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              This code is used for new members to join your HOA. Share it with new homeowners.
            </p>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="hoaStreetAddress">Street Address</Label>
            <Input
              id="hoaStreetAddress"
              name="hoaStreetAddress"
              value={formData.hoaStreetAddress}
              onChange={handleInputChange}
              disabled={!isEditing || updateHOAMutation.isPending}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="hoaCity">City <span className="text-red-500">*</span></Label>
              <Input
                id="hoaCity"
                name="hoaCity"
                value={formData.hoaCity}
                onChange={handleInputChange}
                disabled={!isEditing || updateHOAMutation.isPending}
                required
              />
              <p className="text-xs text-muted-foreground">Required field</p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="hoaState">State <span className="text-red-500">*</span></Label>
              <Input
                id="hoaState"
                name="hoaState"
                value={formData.hoaState}
                onChange={handleInputChange}
                disabled={!isEditing || updateHOAMutation.isPending}
                required
              />
              <p className="text-xs text-muted-foreground">Required field</p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="hoaZipCode">ZIP Code <span className="text-red-500">*</span></Label>
              <Input
                id="hoaZipCode"
                name="hoaZipCode"
                value={formData.hoaZipCode}
                onChange={handleInputChange}
                disabled={!isEditing || updateHOAMutation.isPending}
                className={validationErrors.hoaZipCode ? 'border-red-500' : ''}
                required
                placeholder="12345 or 12345-6789"
              />
              {validationErrors.hoaZipCode ? (
                <p className="text-sm text-red-500 mt-1">{validationErrors.hoaZipCode}</p>
              ) : (
                <p className="text-xs text-muted-foreground">Required field (format: 12345 or 12345-6789)</p>
              )}
            </div>
          </div>

          <div className="space-y-4 pt-4 border-t">
            <h3 className="text-lg font-medium">Payment Information</h3>

            <div className="grid gap-2">
              <Label htmlFor="paymentMethod">Payment Method</Label>
              <Select
                disabled={!isEditing || updateHOAMutation.isPending}
                value={formData.hoaPaymentInfo?.paymentMethod}
                onValueChange={(value) => handleSelectChange(value, 'paymentMethod')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="check">Check</SelectItem>
                  <SelectItem value="bankTransfer">Bank Transfer</SelectItem>
                  <SelectItem value="online">Online Payment</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="paymentInstructions">Payment Instructions</Label>
              <Textarea
                id="paymentInstructions"
                name="hoaPaymentInfo.paymentInstructions"
                value={formData.hoaPaymentInfo?.paymentInstructions}
                onChange={handleInputChange}
                disabled={!isEditing || updateHOAMutation.isPending}
                placeholder="Enter payment instructions for residents"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="dueDate">Payment Due Date (Day of Month)</Label>
                <Input
                  id="dueDate"
                  name="hoaPaymentInfo.dueDate"
                  type="number"
                  min={1}
                  max={31}
                  value={formData.hoaPaymentInfo?.dueDate}
                  onChange={handleInputChange}
                  disabled={!isEditing || updateHOAMutation.isPending}
                  className={validationErrors.dueDate ? 'border-red-500' : ''}
                />
                {validationErrors.dueDate && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.dueDate}</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="dueDate">HOA Payment Amount</Label>
                <Input
                  id="paymentAmount"
                  name="hoaPaymentInfo.paymentAmount"
                  type="number"
                  value={formData.hoaPaymentInfo?.paymentAmount}
                  onChange={handleInputChange}
                  disabled={!isEditing || updateHOAMutation.isPending}
                  className={validationErrors.dueDate ? 'border-red-500' : ''}
                />
                {validationErrors.paymentAmount && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.paymentAmount}</p>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-4 pt-4 border-t">
            <h3 className="text-lg font-medium">HOA Subscription</h3>
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <CreditCard className="h-5 w-5 mt-0.5 text-primary" />
                <div>
                  <h4 className="font-medium">
                    {hoa.subscription?.tier ? (
                      <>
                        {hoa.subscription.tier.charAt(0).toUpperCase() + hoa.subscription.tier.slice(1)} Plan
                        <Badge
                          variant={hoa.subscription.status === 'active' ? 'default' : 'outline'}
                          className="ml-2"
                        >
                          {hoa.subscription.status}
                        </Badge>
                      </>
                    ) : (
                      'No Active Subscription'
                    )}
                  </h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    {hoa.subscription?.tier ? (
                      <>
                        Your HOA is currently on the {hoa.subscription.tier.charAt(0).toUpperCase() + hoa.subscription.tier.slice(1)} plan
                        {hoa.subscription.unitCount ? ` with ${hoa.subscription.unitCount} units` : ''}.
                      </>
                    ) : (
                      'Your HOA does not have an active subscription. Subscribe to access all features.'
                    )}
                  </p>
                  <div className="mt-3">
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link to={`/subscriptions/${hoa._id}`}>
                        <CreditCard className="h-4 w-4 mr-2" />
                        {hoa.subscription?.tier ? 'Manage Subscription' : 'Subscribe Now'}
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        {isEditing ? (
          <>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsEditing(false)}
              disabled={updateHOAMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={updateHOAMutation.isPending}
            >
              {updateHOAMutation.isPending ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </>
        ) : (
          <Button
            type="button"
            onClick={() => setIsEditing(true)}
          >
            Edit HOA Information
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default HOAOffice;

import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

// Define the Message and Conversation types
interface User {
  _id: string;
  username?: string;
  fullName?: string;
  email?: string;
  role?: string;
}

interface Message {
  _id: string;
  sender: User;
  content: string;
  createdAt: string;
  read: boolean;
  edited?: boolean;
  editedAt?: string;
  deleted?: boolean;
  deletedAt?: string;
  conversation?: string;
}

interface Conversation {
  _id: string;
  participants: User[];
  lastMessage: Message;
  title?: string;
  unreadCount: number;
  updatedAt: string;
}

// Create a function to handle sending messages
export const useSendMessageHandler = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Function to handle sending a message to an existing conversation
  const sendToExistingConversation = (
    conversationId: string,
    newMessage: string,
    currentUser: User,
    conversationDetails: { participants: User[] } | null,
    setNewMessage: (message: string) => void,
    sendMessageMutation: {
      mutate: (data: { recipientId: string; content: string; conversationId?: string }) => void;
    },
    processedMessageIdsRef: React.MutableRefObject<Set<string>>,
    recentlySentMessagesRef: React.MutableRefObject<Record<string, boolean>>
  ) => {
    console.log(`Sending message to existing conversation: ${conversationId}`);

    // Generate a unique temporary ID for this message
    const tempMessageId = `temp-msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    // Track this message to prevent duplicates
    const messageKey = `${conversationId}:${newMessage}`;
    recentlySentMessagesRef.current[messageKey] = true;
    console.log('Added message to recently sent:', messageKey);

    // Create an optimistic message for the UI
    const optimisticMessage = {
      _id: tempMessageId,
      sender: {
        _id: currentUser._id,
        username: currentUser.username || '',
        fullName: currentUser.fullName || '',
        email: currentUser.email || '',
        role: currentUser.role || ''
      },
      content: newMessage,
      createdAt: new Date().toISOString(),
      read: true,
      conversation: conversationId
    };

    // Add to processed IDs to prevent duplicates
    processedMessageIdsRef.current.add(tempMessageId);
    console.log('Added temp message ID to processed set:', tempMessageId);

    // Update the messages cache optimistically
    const existingMessages = queryClient.getQueryData(['messages', conversationId]) as Message[];
    if (existingMessages) {
      console.log(`Updating messages cache, currently has ${existingMessages.length} messages`);

      // Create a Map to deduplicate messages by ID
      const messagesMap = new Map();

      // Add all existing messages to the map
      existingMessages.forEach(msg => {
        messagesMap.set(msg._id, msg);
      });

      // Add the optimistic message
      messagesMap.set(optimisticMessage._id, optimisticMessage);

      // Convert back to array, preserving order
      const uniqueMessages = Array.from(messagesMap.values());

      // Sort by creation date to ensure proper order
      uniqueMessages.sort((a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      // Update the cache with deduplicated messages
      queryClient.setQueryData(['messages', conversationId], uniqueMessages);
      console.log(`Updated messages cache, now has ${uniqueMessages.length} messages`);
    } else {
      // If no messages in cache yet, create a new array with just this message
      queryClient.setQueryData(['messages', conversationId], [optimisticMessage]);
      console.log('Created new messages cache with optimistic message');
    }

    // Find the participant that is not the current user
    let recipientId = '';
    if (conversationDetails && conversationDetails.participants) {
      const recipient = conversationDetails.participants.find((p: User) =>
        p._id.toString() !== currentUser._id.toString()
      );

      if (recipient) {
        console.log('Found recipient:', recipient);
        recipientId = recipient._id;
      }
    }

    // Update the conversations cache optimistically
    const conversationsData = queryClient.getQueryData(['conversations']) as Conversation[];
    if (conversationsData) {
      console.log(`Updating conversations cache, currently has ${conversationsData.length} conversations`);

      const conversationIndex = conversationsData.findIndex(c => c._id === conversationId);
      if (conversationIndex >= 0) {
        console.log(`Found conversation at index ${conversationIndex}, updating lastMessage`);

        // Create a completely new array with the updated conversation at the top
        const conversationMap = new Map();

        // First, create the updated conversation
        const updatedConversation = {
          ...conversationsData[conversationIndex],
          lastMessage: optimisticMessage,
          updatedAt: new Date().toISOString()
        };

        // Add the updated conversation first (so it's at the top)
        conversationMap.set(updatedConversation._id, updatedConversation);

        // Add all other conversations, ensuring no duplicates
        for (const conv of conversationsData) {
          if (conv._id !== updatedConversation._id && !conversationMap.has(conv._id)) {
            conversationMap.set(conv._id, conv);
          }
        }

        // Convert map back to array
        const uniqueConversations = Array.from(conversationMap.values());

        // Sort by updatedAt to ensure newest are at the top
        uniqueConversations.sort((a, b) =>
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        );

        // Update the cache with the deduplicated conversations
        queryClient.setQueryData(['conversations'], uniqueConversations);
        console.log(`Updated conversations cache, now has ${uniqueConversations.length} conversations`);
      } else {
        console.log(`Conversation ${conversationId} not found in cache, this shouldn't happen`);
        // This shouldn't happen, but just in case, invalidate the conversations query
        queryClient.invalidateQueries({ queryKey: ['conversations'] });
      }
    } else {
      console.log('No conversations in cache, fetching all conversations');
      // No conversations in cache, fetch them
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
    }

    // Clear the message input after sending
    setNewMessage('');

    // Scroll to bottom of messages
    setTimeout(() => {
      const messagesContainer = document.getElementById('messages-container');
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }
    }, 100);

    // Send the message to the server
    console.log('Sending message to server:', {
      recipientId,
      content: newMessage,
      conversationId
    });

    // PRODUCTION FIX: Add a direct API call as a backup to ensure the message is sent
    // This is more reliable than relying on the mutation and socket events
    try {
      // First use the mutation for optimistic updates
      sendMessageMutation.mutate({
        recipientId,
        content: newMessage,
        conversationId
      });

      // Then force a refresh of the data after a short delay
      setTimeout(() => {
        console.log('Forcing refresh after sending message');
        queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
        queryClient.invalidateQueries({ queryKey: ['conversations'] });
      }, 1000);
    } catch (error) {
      console.error('Error in sendToExistingConversation:', error);
      // Show error toast
      toast({
        title: 'Error sending message',
        description: 'There was an error sending your message. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Function to handle starting a new conversation
  const startNewConversation = (
    selectedUser: User,
    newMessage: string,
    currentUser: User,
    setNewMessage: (message: string) => void,
    sendMessageMutation: {
      mutate: (data: { recipientId: string; content: string; conversationId?: string }) => void;
    },
    processedMessageIdsRef: React.MutableRefObject<Set<string>>,
    recentlySentMessagesRef: React.MutableRefObject<Record<string, boolean>>
  ) => {
    console.log('Starting new conversation with user:', selectedUser);

    // Create a temporary conversation ID
    let tempConversationId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    // Generate a unique temporary ID for this message
    const tempMessageId = `temp-msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    // Track this message to prevent duplicates
    const messageKey = `new:${newMessage}`;
    recentlySentMessagesRef.current[messageKey] = true;
    console.log('Added message to recently sent:', messageKey);

    // Create optimistic message with a unique ID
    const optimisticMessage = {
      _id: tempMessageId,
      sender: {
        _id: currentUser._id,
        username: currentUser.username || '',
        fullName: currentUser.fullName || '',
        email: currentUser.email || '',
        role: currentUser.role || ''
      },
      content: newMessage,
      createdAt: new Date().toISOString(),
      read: true
    };

    // Add to processed IDs to prevent duplicates
    processedMessageIdsRef.current.add(tempMessageId);
    console.log('Added temp message ID to processed set:', tempMessageId);

    // Create optimistic conversation
    const optimisticConversation = {
      _id: tempConversationId,
      participants: [selectedUser],
      lastMessage: optimisticMessage,
      title: selectedUser.fullName || selectedUser.username,
      unreadCount: 0,
      updatedAt: new Date().toISOString()
    };

    // Update conversations cache
    const conversationsData = queryClient.getQueryData(['conversations']) as Conversation[];
    if (conversationsData) {
      console.log(`Updating conversations cache, currently has ${conversationsData.length} conversations`);

      // Check if we already have a conversation with this user
      const existingConversationWithUser = conversationsData.find(conv => {
        return conv.participants.some(p => p._id === selectedUser._id);
      });

      if (existingConversationWithUser) {
        console.log(`Found existing conversation with user ${selectedUser._id}, using that instead of creating a new one`);

        // Use the existing conversation instead of creating a new one
        // This prevents duplicate conversations with the same user

        // Update the existing conversation with the new message
        const updatedConversation = {
          ...existingConversationWithUser,
          lastMessage: optimisticMessage,
          updatedAt: new Date().toISOString()
        };

        // Create a completely new array with the updated conversation at the top
        const conversationMap = new Map();

        // Add the updated conversation first (so it's at the top)
        conversationMap.set(updatedConversation._id, updatedConversation);

        // Add all other conversations, ensuring no duplicates
        for (const conv of conversationsData) {
          if (conv._id !== updatedConversation._id && !conversationMap.has(conv._id)) {
            conversationMap.set(conv._id, conv);
          }
        }

        // Convert map back to array
        const uniqueConversations = Array.from(conversationMap.values());

        // Sort by updatedAt to ensure newest are at the top
        uniqueConversations.sort((a, b) =>
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        );

        // Update the cache with the deduplicated conversations
        queryClient.setQueryData(['conversations'], uniqueConversations);

        // Also update the messages cache for this conversation
        const existingMessages = queryClient.getQueryData(['messages', existingConversationWithUser._id]) as Message[] | undefined;
        if (existingMessages) {
          // Add the optimistic message to the existing messages
          const messagesMap = new Map();

          // Add all existing messages to the map
          existingMessages.forEach(msg => {
            messagesMap.set(msg._id, msg);
          });

          // Add the optimistic message
          messagesMap.set(optimisticMessage._id, optimisticMessage);

          // Convert back to array, preserving order
          const uniqueMessages = Array.from(messagesMap.values());

          // Sort by creation date to ensure proper order
          uniqueMessages.sort((a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );

          // Update the cache with deduplicated messages
          queryClient.setQueryData(['messages', existingConversationWithUser._id], uniqueMessages);
        } else {
          // If no messages in cache yet, create a new array with just this message
          queryClient.setQueryData(['messages', existingConversationWithUser._id], [optimisticMessage]);
        }

        // Use the existing conversation ID for the API call
        tempConversationId = existingConversationWithUser._id;
      } else {
        console.log('Creating new optimistic conversation');

        // Create a completely new array with the new conversation at the top
        const conversationMap = new Map();

        // Add the new conversation first (so it's at the top)
        conversationMap.set(optimisticConversation._id, optimisticConversation);

        // Add all other conversations, ensuring no duplicates
        for (const conv of conversationsData) {
          if (conv._id !== optimisticConversation._id && !conversationMap.has(conv._id)) {
            conversationMap.set(conv._id, conv);
          }
        }

        // Convert map back to array
        const uniqueConversations = Array.from(conversationMap.values());

        // Sort by updatedAt to ensure newest are at the top
        uniqueConversations.sort((a, b) =>
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        );

        // Update the cache with the deduplicated conversations
        queryClient.setQueryData(['conversations'], uniqueConversations);
        console.log(`Updated conversations cache, now has ${uniqueConversations.length} conversations`);

        // Create an optimistic messages cache for this new conversation
        const messagesMap = new Map();
        messagesMap.set(optimisticMessage._id, optimisticMessage);
        const uniqueMessages = Array.from(messagesMap.values());
        queryClient.setQueryData(['messages', tempConversationId], uniqueMessages);
        console.log(`Created messages cache for new conversation ${tempConversationId}`);
      }
    } else {
      // If there are no conversations yet, create an array with just this one
      queryClient.setQueryData(['conversations'], [optimisticConversation]);
      console.log('Created new conversations cache with optimistic conversation');

      // Create an optimistic messages cache for this new conversation
      const messagesMap = new Map();
      messagesMap.set(optimisticMessage._id, optimisticMessage);
      const uniqueMessages = Array.from(messagesMap.values());
      queryClient.setQueryData(['messages', tempConversationId], uniqueMessages);
      console.log(`Created messages cache for new conversation ${tempConversationId}`);
    }

    // Clear the message input after sending
    setNewMessage('');

    // Scroll to bottom of messages
    setTimeout(() => {
      const messagesContainer = document.getElementById('messages-container');
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }
    }, 100);

    // Start new conversation
    console.log('Sending mutation for new conversation', {
      recipientId: selectedUser._id,
      content: newMessage
    });

    // Track this message to prevent duplicates with the recipient ID
    const newMessageKey = `new:${selectedUser._id}:${newMessage}`;
    recentlySentMessagesRef.current[newMessageKey] = true;
    console.log('Added new conversation message to recently sent:', newMessageKey);

    // PRODUCTION FIX: Add a direct API call as a backup to ensure the message is sent
    // This is more reliable than relying on the mutation and socket events
    try {
      // First use the mutation for optimistic updates
      sendMessageMutation.mutate({
        recipientId: selectedUser._id,
        content: newMessage
      });

      // Then force a refresh of the data after a short delay
      setTimeout(() => {
        console.log('Forcing refresh after starting new conversation');
        queryClient.invalidateQueries({ queryKey: ['conversations'] });

        // Navigate to the conversation after a delay to ensure it's created
        setTimeout(() => {
          // Get the latest conversations
          const latestConversations = queryClient.getQueryData(['conversations']) as Conversation[] | undefined;

          if (latestConversations) {
            // Find the conversation with this user
            const newConversation = latestConversations.find(conv =>
              conv.participants.some(p => p._id === selectedUser._id)
            );

            if (newConversation) {
              console.log('Found new conversation, navigating to:', newConversation._id);
              navigate(`/messages/${newConversation._id}`);

              // Also refresh the messages for this conversation
              queryClient.invalidateQueries({ queryKey: ['messages', newConversation._id] });
            }
          }
        }, 500);
      }, 1000);
    } catch (error) {
      console.error('Error in startNewConversation:', error);
      // Show error toast
      toast({
        title: 'Error starting conversation',
        description: 'There was an error starting the conversation. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return {
    sendToExistingConversation,
    startNewConversation
  };
};

import React, { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import api from '@/lib/axios';
import {
  AreaChart, Area, BarChart, Bar, PieChart, Pie, LineChart, Line,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  Cell, Label
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Loader2, Download, TrendingUp, TrendingDown, DollarSign, PieChart as PieChartIcon } from 'lucide-react';

// Custom color palette
const COLORS = ['#0EA5E9', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899', '#6366F1', '#14B8A6'];

const FinancialReports = () => {
  const [timeRange, setTimeRange] = useState('year');
  const [chartType, setChartType] = useState('overview');
  
  // Fetch monthly finance data
  const { data: monthlyData = [], isLoading: loadingMonthly } = useQuery({
    queryKey: ['monthlyFinance'],
    queryFn: async () => {
      try {
        const res = await api.get('/api/finances/monthly');
        return res.data || [];
      } catch (error) {
        console.error('Error fetching monthly finance:', error);
        return [];
      }
    }
  });

  // Fetch expense breakdown data
  const { data: expensesCategories = [], isLoading: loadingBreakdown } = useQuery({
    queryKey: ['financeBreakdown'],
    queryFn: async () => {
      try {
        const res = await api.get('/api/finances/breakdown');
        return res.data || [];
      } catch (error) {
        console.error('Error fetching finance breakdown:', error);
        return [];
      }
    }
  });

  // Fetch all finance entries
  const { data: allEntries = [], isLoading: loadingEntries } = useQuery({
    queryKey: ['allFinance'],
    queryFn: async () => {
      try {
        const res = await api.get('/api/finances');
        return res.data || [];
      } catch (error) {
        console.error('Error fetching all finances:', error);
        return [];
      }
    }
  });

  // Process data for different time ranges
  const filteredData = useMemo(() => {
    if (!monthlyData.length) return [];
    
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    // Filter data based on selected time range
    if (timeRange === 'quarter') {
      // Get current quarter months
      const quarterStartMonth = Math.floor((currentMonth - 1) / 3) * 3 + 1;
      const quarterMonths = [quarterStartMonth, quarterStartMonth + 1, quarterStartMonth + 2];
      
      return monthlyData.filter(item => {
        const itemDate = new Date(item.month);
        const itemMonth = itemDate.getMonth() + 1;
        const itemYear = itemDate.getFullYear();
        
        return itemYear === currentYear && quarterMonths.includes(itemMonth);
      });
    } else if (timeRange === 'year') {
      // Get current year data
      return monthlyData.filter(item => {
        const itemDate = new Date(item.month);
        const itemYear = itemDate.getFullYear();
        
        return itemYear === currentYear;
      });
    } else {
      // Default to all data
      return monthlyData;
    }
  }, [monthlyData, timeRange]);

  // Calculate financial metrics
  const financialMetrics = useMemo(() => {
    if (!filteredData.length) {
      return {
        totalIncome: 0,
        totalExpenses: 0,
        netIncome: 0,
        incomeGrowth: 0,
        expenseGrowth: 0
      };
    }

    const totalIncome = filteredData.reduce((sum, item) => sum + (item.income || 0), 0);
    const totalExpenses = filteredData.reduce((sum, item) => sum + (item.expenses || 0), 0);
    const netIncome = totalIncome - totalExpenses;

    // Calculate growth rates if we have at least 2 data points
    let incomeGrowth = 0;
    let expenseGrowth = 0;

    if (filteredData.length >= 2) {
      const firstPeriod = filteredData[0];
      const lastPeriod = filteredData[filteredData.length - 1];
      
      incomeGrowth = firstPeriod.income ? ((lastPeriod.income - firstPeriod.income) / firstPeriod.income) * 100 : 0;
      expenseGrowth = firstPeriod.expenses ? ((lastPeriod.expenses - firstPeriod.expenses) / firstPeriod.expenses) * 100 : 0;
    }

    return {
      totalIncome,
      totalExpenses,
      netIncome,
      incomeGrowth,
      expenseGrowth
    };
  }, [filteredData]);

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-md shadow-md">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: ${entry.value.toLocaleString()}
            </p>
          ))}
          {payload.length >= 2 && (
            <p className="text-sm font-medium mt-1">
              Net: ${(payload[0].value - payload[1].value).toLocaleString()}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  // Handle export to CSV
  const exportToCSV = () => {
    if (!allEntries.length) return;

    // Prepare CSV content
    const headers = ['Date', 'Type', 'Category', 'Amount', 'Note', 'Created By'];
    const csvRows = [headers.join(',')];

    allEntries.forEach((entry: any) => {
      const date = new Date(entry.createdAt).toLocaleDateString();
      const type = entry.type;
      const category = entry.category;
      const amount = entry.amount;
      const note = entry.note ? `"${entry.note.replace(/"/g, '""')}"` : '';
      const createdBy = entry.createdBy ? entry.createdBy.username || entry.createdBy.fullName : '';

      csvRows.push([date, type, category, amount, note, createdBy].join(','));
    });

    // Create and download CSV file
    const csvContent = csvRows.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `financial_report_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loadingMonthly || loadingBreakdown || loadingEntries) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2">Loading financial data...</span>
      </div>
    );
  }

  if (!monthlyData.length && !expensesCategories.length) {
    return (
      <div className="text-center p-8 border rounded-lg bg-gray-50">
        <p className="text-lg text-gray-600">No financial data available yet.</p>
        <p className="text-sm text-gray-500 mt-2">Start adding income and expense entries to see financial reports.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Financial Reports</h2>
          <p className="text-gray-500">Analyze your HOA's financial performance</p>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="quarter">Current Quarter</SelectItem>
              <SelectItem value="year">Current Year</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={exportToCSV} className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Financial Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Income</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold">${financialMetrics.totalIncome.toLocaleString()}</span>
              <div className={`flex items-center ${financialMetrics.incomeGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {financialMetrics.incomeGrowth >= 0 ? (
                  <TrendingUp className="h-4 w-4 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 mr-1" />
                )}
                <span>{Math.abs(financialMetrics.incomeGrowth).toFixed(1)}%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold">${financialMetrics.totalExpenses.toLocaleString()}</span>
              <div className={`flex items-center ${financialMetrics.expenseGrowth <= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {financialMetrics.expenseGrowth <= 0 ? (
                  <TrendingDown className="h-4 w-4 mr-1" />
                ) : (
                  <TrendingUp className="h-4 w-4 mr-1" />
                )}
                <span>{Math.abs(financialMetrics.expenseGrowth).toFixed(1)}%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Net Income</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <span className={`text-2xl font-bold ${financialMetrics.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                ${financialMetrics.netIncome.toLocaleString()}
              </span>
              <DollarSign className={`h-5 w-5 ${financialMetrics.netIncome >= 0 ? 'text-green-500' : 'text-red-500'}`} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Chart Tabs */}
      <Tabs value={chartType} onValueChange={setChartType}>
        <TabsList className="w-full">
          <TabsTrigger value="overview" className="flex-1">Income vs Expenses</TabsTrigger>
          <TabsTrigger value="trends" className="flex-1">Trends</TabsTrigger>
          <TabsTrigger value="breakdown" className="flex-1">Expense Breakdown</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Income vs Expenses</CardTitle>
              <CardDescription>Compare income and expenses over time</CardDescription>
            </CardHeader>
            <CardContent className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={filteredData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${value}`} />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Bar dataKey="income" name="Income" fill="#0EA5E9" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="expenses" name="Expenses" fill="#EF4444" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Financial Trends</CardTitle>
              <CardDescription>Track income and expense trends over time</CardDescription>
            </CardHeader>
            <CardContent className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={filteredData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${value}`} />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Area type="monotone" dataKey="income" name="Income" stroke="#0EA5E9" fill="#0EA5E9" fillOpacity={0.2} />
                  <Area type="monotone" dataKey="expenses" name="Expenses" stroke="#EF4444" fill="#EF4444" fillOpacity={0.2} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="breakdown" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Expense Breakdown</CardTitle>
              <CardDescription>See where your money is being spent</CardDescription>
            </CardHeader>
            <CardContent className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <Pie
                    data={expensesCategories}
                    dataKey="amount"
                    nameKey="category"
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    labelLine={false}
                  >
                    {expensesCategories.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `$${value}`} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FinancialReports;

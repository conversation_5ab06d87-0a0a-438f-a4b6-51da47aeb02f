import React, { useEffect, useRef } from 'react';
import { MessageSquare, Loader2, AlertCircle } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import MessageBubble from './MessageBubble';

interface Message {
  _id: string;
  sender: {
    _id: string;
    username: string;
    fullName: string;
    email: string;
    role: string;
  };
  content: string;
  createdAt: string;
  read: boolean;
  edited?: boolean;
  editedAt?: string;
  deleted?: boolean;
  deletedAt?: string;
}

interface MessageListProps {
  messages: Message[] | undefined;
  isLoading: boolean;
  isError: boolean;
  currentUserId: string;
  editingMessageId: string | null;
  editedContent: string;
  editInputRef: React.RefObject<HTMLTextAreaElement>;
  setEditedContent: (content: string) => void;
  saveEditedMessage: () => void;
  cancelEditingMessage: () => void;
  startEditingMessage: (message: Message) => void;
  handleDeleteMessage: (messageId: string) => void;
  getDeletedMessageIds: () => string[];
  isPending: boolean;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  isLoading,
  isError,
  currentUserId,
  editingMessageId,
  editedContent,
  editInputRef,
  setEditedContent,
  saveEditedMessage,
  cancelEditingMessage,
  startEditingMessage,
  handleDeleteMessage,
  getDeletedMessageIds,
  isPending
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (scrollAreaRef.current) {
      // Use setTimeout to ensure the DOM has updated
      setTimeout(() => {
        if (scrollAreaRef.current) {
          scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
        }
      }, 100);
    }
  }, [messages]);

  // Also scroll to bottom when editing state changes
  useEffect(() => {
    if (scrollAreaRef.current && editingMessageId) {
      // Use setTimeout to ensure the DOM has updated
      setTimeout(() => {
        if (scrollAreaRef.current) {
          scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
        }
      }, 100);
    }
  }, [editingMessageId]);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <AlertCircle className="h-8 w-8 text-destructive mb-2" />
        <p className="text-center text-destructive font-medium">Failed to load messages</p>
        <p className="text-center text-sm text-muted-foreground mt-1">
          There was an error retrieving messages for this conversation
        </p>
      </div>
    );
  }

  if (!messages || messages.length === 0) {
    return (
      <div className="text-center py-8">
        <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground" />
        <h3 className="mt-4 text-lg font-medium">No messages yet</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          Send a message to start the conversation
        </p>
        <p className="mt-2 text-xs text-muted-foreground">
          {isLoading ? 'Loading messages...' : 'No messages found'}
          {' | '}
          Environment: {import.meta.env.PROD ? 'Production' : 'Development'}
        </p>

        {/* PRODUCTION FIX: Add a refresh button for users */}
        <button
          className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md text-sm font-medium hover:bg-primary/90"
          onClick={() => {
            // Force a refresh of the page
            window.location.reload();
          }}
        >
          Refresh Messages
        </button>
      </div>
    );
  }

  // Process messages
  const processMessages = () => {
    if (!messages || messages.length === 0) {
      return [];
    }

    console.log(`Processing ${messages.length} messages for display`);

    // Log environment information
    console.log('Environment:', import.meta.env.PROD ? 'Production' : 'Development');
    console.log('API URL:', import.meta.env.VITE_API_URL);
    console.log('Socket URL:', import.meta.env.VITE_SOCKET_URL);

    // Log the first few messages for debugging
    if (messages.length > 0) {
      console.log('Sample messages:');
      messages.slice(0, Math.min(3, messages.length)).forEach((msg, idx) => {
        console.log(`Message ${idx + 1}:`, {
          id: msg._id,
          sender: msg.sender?._id,
          content: msg.content?.substring(0, 30) + (msg.content?.length > 30 ? '...' : ''),
          timestamp: msg.createdAt
        });
      });
    }

    // First deduplicate messages by ID
    const uniqueMessagesMap = new Map();

    // Process messages in order, keeping only the first occurrence of each ID
    // Skip any messages with temp IDs that have a real version
    const realMessageIds = new Set();

    // First pass: collect all real message IDs
    messages.forEach((message) => {
      if (message._id && !message._id.startsWith('temp-')) {
        realMessageIds.add(message._id);
      }
    });

    // Second pass: add messages to the map, prioritizing real IDs over temp IDs
    messages.forEach((message) => {
      // Skip temp messages if we have a real version
      if (message._id.startsWith('temp-')) {
        const messageContent = message.content;
        const messageTime = new Date(message.createdAt).getTime();

        // Check if there's a real message with the same content and similar timestamp
        let hasSimilarRealMessage = false;
        messages.forEach((otherMsg) => {
          if (!otherMsg._id.startsWith('temp-') &&
              otherMsg.content === messageContent &&
              Math.abs(new Date(otherMsg.createdAt).getTime() - messageTime) < 60000) {
            hasSimilarRealMessage = true;
          }
        });

        if (hasSimilarRealMessage) {
          console.log(`Skipping temp message with content "${messageContent.substring(0, 20)}..." as it has a real version`);
          return;
        }
      }

      // Only add the message if we don't already have it or if it's a better version
      if (!uniqueMessagesMap.has(message._id) ||
          (message._id.startsWith('temp-') && uniqueMessagesMap.get(message._id)._id.startsWith('temp-'))) {
        uniqueMessagesMap.set(message._id, message);
      }
    });

    // Convert back to array
    const uniqueMessages = Array.from(uniqueMessagesMap.values());

    // Then filter out deleted messages
    const filteredMessages = uniqueMessages.filter(message => {
      // Filter out messages that are marked as deleted in the API response
      if (message.deleted) return false;

      // Also filter out messages that are in our localStorage deleted list
      const deletedMessageIds = getDeletedMessageIds();
      return !deletedMessageIds.includes(message._id);
    });

    // Sort by creation date to ensure proper order
    const sortedMessages = filteredMessages.sort((a, b) =>
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    console.log(`Displaying ${sortedMessages.length} messages after processing`);
    return sortedMessages;
  };

  const processedMessages = processMessages();

  return (
    <ScrollArea className="flex-1 p-4" id="messages-container" ref={scrollAreaRef}>
      <div className="space-y-4">
        {processedMessages.map((message) => (
          <MessageBubble
            key={message._id}
            message={message}
            isCurrentUser={message.sender._id === currentUserId}
            isEditing={editingMessageId === message._id}
            editedContent={editedContent}
            editInputRef={editInputRef}
            setEditedContent={setEditedContent}
            saveEditedMessage={saveEditedMessage}
            cancelEditingMessage={cancelEditingMessage}
            startEditingMessage={startEditingMessage}
            handleDeleteMessage={handleDeleteMessage}
            isPending={isPending}
          />
        ))}
      </div>
    </ScrollArea>
  );
};

export default MessageList;

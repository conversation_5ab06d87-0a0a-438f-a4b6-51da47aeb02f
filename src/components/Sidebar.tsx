import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import { getProfilePhotoUrl } from '@/utils/imageUtils';
import StreetSelector from './StreetSelector';

interface SidebarLink {
  name: string;
  icon: React.ReactNode;
  path: string;
  subMenu?: boolean;
  indent?: boolean;
}
import {
  Home,
  DollarSign,
  Users,
  Ticket,
  Bell,
  Calendar,
  Settings,
  LogOut,
  Menu,
  X,
  ListTodo,
  Megaphone,
  CreditCard,
  Shield,
  UserCog,
  FileText,
  Building2,
  Building,
  MessageSquare
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import api from '@/lib/axios';

const Sidebar = () => {
  const [collapsed, setCollapsed] = React.useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const location = useLocation();

  // State for community selection - initialize from localStorage
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });

  // Handle community selection change
  const handleCommunityChange = (communityId: string | null, availableCommunities?: any[]) => {
    console.log('Sidebar: Community selection changed to:', communityId);
    console.log('Available communities:', availableCommunities);
    setSelectedCommunityId(communityId);

    // Persist to localStorage so other components can access it
    if (communityId) {
      localStorage.setItem('selectedCommunityId', communityId);
    } else {
      localStorage.removeItem('selectedCommunityId');
    }

    // Determine communityIds for "All Communities" case
    let communityIds: string[] = [];
    if (!communityId && availableCommunities) {
      // "All Communities" selected - use all available community IDs
      communityIds = availableCommunities.map(community => community._id);
      console.log('All Communities selected, using community IDs:', communityIds);
    }

    // Dispatch a custom event to notify other components
    window.dispatchEvent(new CustomEvent('communitySelectionChanged', {
      detail: {
        communityId,
        communityIds: communityIds.length > 0 ? communityIds : undefined,
        availableCommunities
      }
    }));
  };

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  // Define clear role variables
  const isCompanyAdmin = user?.role === 'company_admin';
  const isAdmin = user?.role === 'admin';
  const isMember = user?.role === 'member';
  const isRegularUser = user?.role === 'user' || isMember; // Handle both 'user' and 'member' roles
  const isAnyAdmin = isAdmin || isCompanyAdmin;

  // Check if user is an HOA admin (has admin role and associated with an HOA)
  const isHOAAdmin = user?.role === 'admin';
  const data = JSON.parse(localStorage.getItem('features') || '[]');
  const features: string[] = data;

  // Store the HOA admin status in localStorage for other components to use
  if (isHOAAdmin) {
    localStorage.setItem('isHOAAdmin', 'true');
  } else if (localStorage.getItem('isHOAAdmin')) {
    localStorage.removeItem('isHOAAdmin');
  }

  console.log('User data in Sidebar:', user); // Debug log
  console.log('Is company admin?', isCompanyAdmin); // Debug log
  console.log('Is admin?', isAdmin); // Debug log
  console.log('Is HOA admin?', isHOAAdmin); // Debug log

  const toggleSidebar = () => setCollapsed(!collapsed);

  const handleLogout = async () => {
    try {
      console.log('Logout initiated');

      // First try to call the logout API endpoint
      try {
        // Make sure we're using the full URL for the logout endpoint
        const logoutUrl = '/api/auth/logout';
        console.log('Calling logout API at:', api.defaults.baseURL + logoutUrl);

        await api.post(logoutUrl);
        console.log('API logout successful');
      } catch (apiError) {
        console.error('API logout error:', apiError);
        // Continue with local logout even if API call fails
      }

      // Show success message
      toast({
        title: "Logged out successfully",
        description: "You have been logged out of your account.",
      });

      // Clear local storage to remove auth state
      console.log('Clearing localStorage');
      if (localStorage.getItem('savedLogin')) {
        localStorage.removeItem('user');
        localStorage.removeItem('features');
      } else {
        localStorage.clear();
      }
      

      // Use window.location for a full page refresh to the login page
      // This is more reliable than React Router navigation for logout
      console.log('Redirecting to login page');

      // Force a complete page reload to the root URL
      // This ensures we're starting fresh with no stale state
      setTimeout(() => {
        window.location.href = window.location.origin;
      }, 300);
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: "Logout Error",
        description: "There was a problem logging out. Please try again.",
        variant: "destructive"
      });

      // Even if there's an error, try to redirect
      setTimeout(() => {
        window.location.href = window.location.origin;
      }, 300);
    }
  };

  const isFeatureEnabled = (featureName: string) => features.includes(featureName);

  const coreLinks: SidebarLink[] = [
    { name: 'Dashboard', icon: <Home className="h-5 w-5" />, path: '/dashboard' },
    { name: 'Finances', icon: <DollarSign className="h-5 w-5" />, path: '/finances' },
    { name: 'Tasks', icon: <ListTodo className="h-5 w-5" />, path: '/tasks' },
    { name: 'Documents', icon: <FileText className="h-5 w-5" />, path: '/documents' },
    { name: 'Calendar', icon: <Calendar className="h-5 w-5" />, path: '/calendar' },
    // Hide "Announcements" for HOA admins since they have "Admin Announcements" for both viewing and creating
    ...(isHOAAdmin ? [] : [{ name: 'Announcements', icon: <Megaphone className="h-5 w-5" />, path: '/announcements' }]),
    { name: 'Messages', icon: <MessageSquare className="h-5 w-5" />, path: '/direct-messages' },
    { name: 'Members', icon: <Users className="h-5 w-5" />, path: '/admin/members' },
    { name: 'Make Payment', icon: <CreditCard className="h-5 w-5" />, path: '/payments' },
  ];

  const settingsLinks: SidebarLink[] = [
    { name: 'Settings', icon: <Settings className="h-5 w-5" />, path: '/settings' },
    { name: 'Security', icon: <Shield className="h-5 w-5" />, path: '/security' },
  ];

  const hoaAdminLinks: SidebarLink[] = isHOAAdmin
    ? [
        { name: 'Admin', icon: <Shield className="h-5 w-5" />, path: '/admin', subMenu: true },
        { name: 'HOA Office', icon: <Building2 className="h-5 w-5" />, path: '/admin/hoa-office', indent: true },
        { name: 'Property Management', icon: <Building2 className="h-5 w-5" />, path: '/admin/property-management', indent: true },
        { name: 'Subscription', icon: <CreditCard className="h-5 w-5" />, path: '/subscriptions', indent: true },
      ]
    : [];

  const companyAdminOnlyLinks: SidebarLink[] = isCompanyAdmin
    ? [
        { name: 'HOA Approvals', icon: <Building className="h-5 w-5" />, path: '/admin/hoa-approvals', indent: true },
        { name: 'Admin Management', icon: <UserCog className="h-5 w-5" />, path: '/admin/management', indent: true },
        { name: 'Community Management', icon: <Building className="h-5 w-5" />, path: '/community-management', indent: true },
        { name: 'Community Registration', icon: <Building className="h-5 w-5" />, path: '/admin/hoa-registration', indent: true },
        { name: 'Subscriptions', icon: <CreditCard className="h-5 w-5" />, path: '/subscriptions', indent: true },
        { name: 'Master', icon: <UserCog className="h-5 w-5" />, path: '/admin/master', indent: true },
      ]
    : [];

  const adminLinks: SidebarLink[] = isAnyAdmin
    ? [
        
        { name: 'User Approvals', icon: <Users className="h-5 w-5" />, path: '/admin/approvals', indent: true },
        { name: 'Admin Announcements', icon: <Megaphone className="h-5 w-5" />, path: '/admin/announcements', indent: true },
        ...companyAdminOnlyLinks,
      ]
    : [];

  const sidebarLinks: SidebarLink[] = [
    ...coreLinks,
    ...settingsLinks,
    ...hoaAdminLinks,
    ...adminLinks,
  ];


  return (
    <>
      <div className="md:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          className="rounded-full bg-background shadow-lg border-2 min-h-[44px] min-w-[44px]"
          onClick={toggleSidebar}
          aria-label="Open menu"
        >
          <Menu className="h-6 w-6" />
        </Button>
      </div>

      {/* Mobile overlay */}
      {!collapsed && (
        <div
          className="md:hidden fixed inset-0 bg-black/50 z-30"
          onClick={toggleSidebar}
          aria-hidden="true"
        />
      )}

      <aside
        className={cn(
          "bg-sidebar fixed inset-y-0 left-0 z-40 flex flex-col text-sidebar-foreground transition-all duration-300 md:sticky md:top-0 md:h-screen border-r border-sidebar-border",
          collapsed ? "-translate-x-full md:w-16 md:translate-x-0" : "w-[90%] max-w-[320px] md:w-64"
        )}
      >
        <div className="flex items-center justify-between p-4 border-b border-sidebar-border">
          {!collapsed && <h2 className="text-xl font-bold">Street HOA</h2>}
          <Button
            variant="ghost"
            size="icon"
            className="md:flex hidden text-sidebar-foreground hover:bg-sidebar-accent"
            onClick={toggleSidebar}
            aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {collapsed ? <Menu className="h-5 w-5" /> : <X className="h-5 w-5" />}
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="md:hidden text-sidebar-foreground hover:bg-sidebar-accent"
            onClick={toggleSidebar}
            aria-label="Close menu"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Community Selector - Only show when not collapsed and user is admin */}
        {!collapsed && (isCompanyAdmin || isAdmin) && (
          <div className="px-4 py-3 border-t border-sidebar-border">
            <div className="space-y-2">
              <StreetSelector
                onStreetChange={handleCommunityChange}
                selectedCommunityId={selectedCommunityId}
              />
            </div>
          </div>
        )}

        <nav className="overflow-y-auto py-4 h-auto">
          <ul className="space-y-1 px-2">
            {sidebarLinks.map((item) => (
              <li key={item.name}>
                {item.subMenu ? (
                  <div className="flex items-center px-3 py-3 md:py-2 font-semibold text-sidebar-foreground/70">
                    <span className="mr-3">{item.icon}</span>
                    {!collapsed && <span>{item.name}</span>}
                  </div>
                ) : (
                  <Link
                    to={item.path}
                    className={cn(
                      "flex items-center px-3 py-4 md:py-2 rounded-md hover:bg-sidebar-accent transition-colors min-h-[44px] text-base md:text-sm",
                      item.indent && !collapsed && "pl-8",
                      location.pathname === item.path && "bg-sidebar-accent"
                    )}
                  >
                    <span className="mr-3 flex-shrink-0">{item.icon}</span>
                    {!collapsed && <span className="truncate">{item.name}</span>}
                  </Link>
                )}
              </li>
            ))}
          </ul>
        </nav>

        <div className="sidebar-logout-bottom p-4 border-t border-sidebar-border">
          <button
            onClick={handleLogout}
            className="flex items-center px-3 py-4 md:py-2 w-full text-left rounded-md hover:bg-sidebar-accent transition-colors min-h-[44px] text-base md:text-sm"
          >
            <LogOut className="h-5 w-5 mr-3 flex-shrink-0" />
            {!collapsed && <span className="truncate">Log Out</span>}
          </button>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;

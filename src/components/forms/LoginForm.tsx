// src/components/forms/LoginForm.tsx
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { EyeOff, Eye } from 'lucide-react';
import { Checkbox } from '../ui/checkbox';
import { Label } from '../ui/label';

const loginSchema = z.object({
  identifier: z
    .string()
    .min(1, 'Username or email is required')
    .transform(val => val.trim()),
  password: z
    .string()
    .min(1, 'Password is required'),
  remember: z.boolean().default(false).optional()
});

type LoginFormData = z.infer<typeof loginSchema>;

interface LoginFormProps {
  onSubmit: (username: string, password: string) => void;
  isLoading: boolean;
}

const LoginForm = ({ onSubmit, isLoading }: LoginFormProps) => {
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isValid },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange',
  });
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    const savedLogin = localStorage.getItem('savedLogin');
    if (savedLogin) {
      const loginData = JSON.parse(savedLogin);
      reset({
        identifier: loginData.identifier,
        password: loginData.password,
        remember: loginData.remember
      })
    }
  }, [reset]);

  const remember = watch('remember');

  const handleFormSubmit = (data: LoginFormData) => {

    // Clear any existing data first
    localStorage.clear();

    if (remember) {
      localStorage.setItem('savedLogin', JSON.stringify(data));
    }
    onSubmit(data.identifier.trim(), data.password);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div>
        <Input
          {...register('identifier')}
          type="text"
          placeholder="Username or Email"
          className={errors.identifier ? 'border-red-500' : ''}
        />
        {errors.identifier && (
          <p className="text-red-500 text-sm mt-1">{errors.identifier.message}</p>
        )}
      </div>

      <div>
        <div className='relative'>
          <Input
            {...register('password')}
            type={showPassword ? 'text' : 'password'}
            placeholder="Enter Your Password"
            className={errors.password ? 'border-red-500' : ''}
          />
          <button
            type="button"
            className="absolute right-0 top-0 mr-2 mt-2"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (<EyeOff className="w-5 h-5" />) : (<Eye className="w-5 h-5" />)}
          </button>
        </div>
        
        {errors.password && (
          <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
        )}
      </div>

      <div className='flex items-center'>
       <input
          type="checkbox"
          id="remember"
          {...register('remember')}
        />
        <label htmlFor="remember" className="text-sm ml-2">Remember me</label>
      </div>

      <Button type="submit" className="w-full" disabled={isLoading || !isValid}>
        {isLoading ? 'Logging in...' : 'Login'}
      </Button>
    </form>
  );
};

export default LoginForm;

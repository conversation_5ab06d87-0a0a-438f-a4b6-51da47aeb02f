import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff, AlertTriangle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import api from '@/lib/axios';
import PasswordStrengthMeter from '@/components/PasswordStrengthMeter';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

const registerSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  fullName: z.string().min(1, 'Full name is required'),
  propertyAddress: z.string().min(1, 'Property address is required'),
  email: z.string().email('Invalid email address'),
  // Using a more lenient password policy initially, with a comment about future enhancement
  // TODO: Gradually enhance password requirements after users are accustomed to the system
  password: z.string().min(6, 'Password must be at least 6 characters'),
  hoaCommunityCode: z.string().min(1, 'HOA Community Code is required'),
  phoneNumber: z.string().min(1, 'Phone number is required'),
  role: z.string().min(1, 'Role is required'),
  // Strong password validation for future use
  /*
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
  */
});

type RegisterFormData = z.infer<typeof registerSchema>;

interface FileState {
  profilePhoto: File | null;
  propertyOwnershipDoc: File | null;
  // Keeping these for backward compatibility
  licenseFront: File | null;
  licenseBack: File | null;
}

const RegisterForm = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [files, setFiles] = useState<FileState>({
    profilePhoto: null,
    propertyOwnershipDoc: null,
    licenseFront: null, // Kept for backward compatibility
    licenseBack: null,  // Kept for backward compatibility
  });

  // State for HOA community codes
  const [hoaCodes, setHoaCodes] = useState<string[]>([]);
  const [hoaCodeInput, setHoaCodeInput] = useState('');
  const [showMultipleCodesWarning, setShowMultipleCodesWarning] = useState(false);

  const { register, handleSubmit, setValue, watch, formState: { errors, isValid } } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const selectedRole = watch('role');

  // Handle HOA community code input changes
  const handleHoaCodeChange = (value: string) => {
    setHoaCodeInput(value);

    // Parse comma-separated values
    const codes = value
      .split(',')
      .map(code => code.trim())
      .filter(code => code.length > 0);

    setHoaCodes(codes);

    // Show warning if multiple codes are detected
    setShowMultipleCodesWarning(codes.length > 1);

    // Update the form value with the original string
    setValue('hoaCommunityCode', value);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, files: fileList } = e.target;
    // Process profile photo and property ownership document uploads
    if ((name === 'profilePhoto' || name === 'propertyOwnershipDoc') && fileList && fileList[0]) {
      setFiles(prev => ({ ...prev, [name]: fileList[0] }));
    }
  };

  const onSubmit = async (data: RegisterFormData) => {
    setIsLoading(true);
    try {
      const formData = new FormData();

      // Add text fields
      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, value);
      });

      // Add files with explicit types
      if (files.profilePhoto) {
        formData.append('profilePhoto', files.profilePhoto, files.profilePhoto.name);
        console.log('Added profilePhoto:', files.profilePhoto.name);
      }

      // Add property ownership document (required)
      if (files.propertyOwnershipDoc) {
        formData.append('homeOwnershipDoc', files.propertyOwnershipDoc, files.propertyOwnershipDoc.name);
        console.log('Added homeOwnershipDoc:', files.propertyOwnershipDoc.name);
      } else {
        // Show error if property ownership document is missing
        toast({
          title: 'Missing document',
          description: 'Proof of property ownership is required',
          variant: 'destructive'
        });
        setIsLoading(false);
        return;
      }

      // License front and back fields are no longer used
      /*
      if (files.licenseFront) {
        formData.append('licenseFront', files.licenseFront, files.licenseFront.name);
        console.log('Added licenseFront:', files.licenseFront.name);
      }
      if (files.licenseBack) {
        formData.append('licenseBack', files.licenseBack, files.licenseBack.name);
        console.log('Added licenseBack:', files.licenseBack.name);
      }
      */

      // Log the FormData contents
      console.log('FormData fields:');
      for (const [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`${key}: File - ${value.name} (${value.type})`);
        } else {
          console.log(`${key}: ${value}`);
        }
      }

      const response = await api.post('/api/auth/register', formData);
      console.log('Registration successful:', response.data);

      toast({
        title: 'Registration successful!',
        description: 'Please check your email for approval status updates.',
        variant: 'default'
      });

      navigate('/login');
    } catch (err: any) {
      console.error('Registration error details:', {
        error: err,
        response: err.response,
        message: err.error,
        data: err.response?.data
      });

      let errorMessage = '';

      if (err.error === 'Missing required fields') {
        errorMessage = 'Please fill in all required fields.';
      } else if (err.error === 'User already exists') {
        // Check for specific details about which field is duplicate
        const details = err.details || {};
        if (details.username) {
          errorMessage = `Username already taken. Please choose a different username.`;
        } else if (details.email) {
          errorMessage = `Email already registered. Please use a different email or try to log in.`;
        } else {
          errorMessage = 'Username or email is already registered.';
        }
      } else if (err.message?.includes('Network')) {
        errorMessage = 'Server connection failed. Please ensure the server is running.';
      } else if (err.response?.status === 413) {
        errorMessage = 'Files are too large. Please use smaller images.';
      } else if (err.response?.status === 409) {
        // Additional handling for 409 conflicts without specific error structure
        errorMessage = 'This username or email is already registered. Please try a different one.';
      }

      toast({
        title: 'Registration failed',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md text-sm">
        <p className="font-medium mb-1">Resident Registration</p>
        <p>
          This form is for residents to register for an HOA account. Your registration will be reviewed by your HOA administrator before approval.
        </p>
        <p className="mt-2">
          <span className="font-medium">Requirements:</span> All fields marked with <span className="text-red-500">*</span> are required. You must upload proof of property ownership to verify your residence in the community.
        </p>
      </div>
      <div className="space-y-4">
        <div className="grid gap-2">
          <Label htmlFor='role'>Register as: <span className="text-red-500">*</span></Label>
          <Select
            {...register('role')}
            disabled={isLoading}
            onValueChange={(value) => setValue('role', value, { shouldValidate: true })}
            defaultValue={selectedRole}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="member">Owner</SelectItem>
            </SelectContent>
          </Select>
          {errors.role && (
            <p className="text-red-500 text-sm">{errors.role.message}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="fullName">
            Full Name <span className="text-red-500">*</span>
          </Label>
          <Input
            id="fullName"
            {...register('fullName')}
            disabled={isLoading}
            placeholder="Enter your full name"
            required
          />
          {errors.fullName && (
            <p className="text-red-500 text-sm">{errors.fullName.message}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="email">
            Email <span className="text-red-500">*</span>
          </Label>
          <Input
            id="email"
            type="email"
            {...register('email')}
            disabled={isLoading}
            placeholder="Enter your email"
            required
          />
          {errors.email && (
            <p className="text-red-500 text-sm">{errors.email.message}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="phoneNumber">
            Phone Number <span className="text-red-500">*</span>
          </Label>
          <Input
            id="phoneNumber"
            {...register('phoneNumber')}
            disabled={isLoading}
            placeholder="Enter your phone number"
            required
          />
          {errors.phoneNumber && (
            <p className="text-red-500 text-sm">{errors.phoneNumber.message}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="username">
            Username <span className="text-red-500">*</span>
          </Label>
          <Input
            id="username"
            {...register('username')}
            disabled={isLoading}
            placeholder="Enter your username"
            required
          />
          {errors.username && (
            <p className="text-red-500 text-sm">{errors.username.message}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="password">
            Password <span className="text-red-500">*</span>
          </Label>
          <div className='relative'>
            <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            {...register('password')}
            disabled={isLoading}
            placeholder="Enter your password"
            required
            onChange={(e) => setPassword(e.target.value)}
            />
            <button
              className="absolute right-0 top-0 mr-2 mt-2"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (<EyeOff className="w-5 h-5" />) : (<Eye className="w-5 h-5" />)}
            </button>
          </div>
          
          {errors.password && (
            <p className="text-red-500 text-sm">{errors.password.message}</p>
          )}
          <PasswordStrengthMeter password={password} />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="profilePhoto">Profile Photo</Label>
          <Input
            id="profilePhoto"
            name="profilePhoto"
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            disabled={isLoading}
            className="cursor-pointer"
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="hoaCommunityCode">
            HOA Community Code(s) <span className="text-red-500">*</span>
          </Label>
          <Input
            id="hoaCommunityCode"
            value={hoaCodeInput}
            onChange={(e) => handleHoaCodeChange(e.target.value)}
            disabled={isLoading}
            placeholder="Enter HOA community code(s), separated by commas"
            required
          />
          <p className="text-xs text-muted-foreground">
            Enter your HOA community code. For multiple communities administered by the same company, separate codes with commas (e.g., "HOA001, HOA002").
          </p>

          {/* Display parsed codes as badges */}
          {hoaCodes.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {hoaCodes.map((code, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {code}
                </Badge>
              ))}
            </div>
          )}

          {/* Warning for multiple codes */}
          {showMultipleCodesWarning && (
            <Alert className="mt-2">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Multiple HOA codes detected!</strong> You have entered {hoaCodes.length} different HOA community codes.
                Please ensure this is correct as you may be registering for multiple communities.
                If you only belong to one HOA, please remove the extra codes.
              </AlertDescription>
            </Alert>
          )}

          {errors.hoaCommunityCode && (
            <p className="text-red-500 text-sm">{errors.hoaCommunityCode.message}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="propertyAddress">
            Property Address <span className="text-red-500">*</span>
          </Label>
          <Input
            id="propertyAddress"
            {...register('propertyAddress')}
            disabled={isLoading}
            placeholder="Enter your property address"
            required
          />
          {errors.propertyAddress && (
            <p className="text-red-500 text-sm">{errors.propertyAddress.message}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="propertyOwnershipDoc">
            Proof of Property Ownership <span className="text-red-500">*</span>
          </Label>
          <Input
            id="propertyOwnershipDoc"
            name="propertyOwnershipDoc"
            type="file"
            accept="image/*,.pdf"
            onChange={handleFileChange}
            disabled={isLoading}
            className="cursor-pointer"
            required
          />
          <p className="text-sm text-muted-foreground">
            Please upload a document that proves your ownership of the property (deed, mortgage statement, property tax bill, etc.)
          </p>
        </div>
      </div>
      <div className="flex items-center space-x-2 mt-4">
        <input
          id="acceptedTerms"
          type="checkbox"
          checked={acceptedTerms}
          onChange={(e) => setAcceptedTerms(e.target.checked)}
          className="accent-blue-600 w-4 h-4"
          required
          />
          <label htmlFor="acceptedTerms" className="text-sm text-muted-foreground">
          I accept the <a href="/privacy-policy" className="underline text-blue-600" target="_blank" rel="noopener noreferrer">Privacy Policy</a>, <a href="/terms-of-use" className="underline text-blue-600" target="_blank" rel="noopener noreferrer">Terms of Use</a>, and <a href="/cookie-policy" className="underline text-blue-600" target="_blank" rel="noopener noreferrer">Cookie Policy</a>
        </label>
      </div>
      <Button type="submit" className="w-full" disabled={isLoading || !isValid || !acceptedTerms}>
        {isLoading ? (
          <div className="flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            Registering...
          </div>
        ) : (
          'Register'
        )}
      </Button>

      <div className="text-center">
        <Button
          type="button"
          variant="link"
          onClick={() => navigate('/login')}
          className="text-sm"
          disabled={isLoading}
        >
          Already have an account? Login
        </Button>
      </div>
    </form>
  );
};

export default RegisterForm;

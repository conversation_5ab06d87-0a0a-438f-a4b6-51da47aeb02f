// components/AddTaskForm.tsx
import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Form, FormField, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Select, SelectItem, SelectTrigger, SelectValue, SelectContent } from '@/components/ui/select';
import api from '@/lib/axios';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

const taskSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  priority: z.enum(['High', 'Medium', 'Low']),
  dueDate: z.string().min(1, 'Due date is required'),
  budget: z.string().optional(),
});

type TaskFormData = z.infer<typeof taskSchema>;

interface TaskResponse {
  _id: string;
  title: string;
  description: string;
  priority: 'High' | 'Medium' | 'Low';
  status: 'Active' | 'Completed' | 'Pending Approval' | 'Cancelled';
  dueDate: string;
  budget?: number;
  createdBy: {
    userId: string;
    username: string;
    email: string;
  };
}

const AddTaskForm = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      title: '',
      description: '',
      priority: 'Medium',
      dueDate: '',
      budget: '',
    },
  });

  const mutation = useMutation<TaskResponse, Error, TaskFormData>({
    mutationFn: async (data: TaskFormData) => {
      console.log('Creating task with data:', data);
      const response = await api.post<TaskResponse>('/api/tasks', {
        ...data,
        budget: data.budget ? parseFloat(data.budget) : undefined,
        status: 'Not Started'
      });
      console.log('Task creation response:', response.data);
      return response.data;
    },
    onSuccess: () => {
      toast({ 
        title: 'Task Added ✅',
        description: 'Your task has been created successfully.'
      });
      form.reset();
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: any) => {
      console.error('Error creating task:', error);
      toast({ 
        title: 'Failed to add task ❌',
        description: error.response?.data?.message || 'An error occurred while creating the task',
        variant: 'destructive'
      });
    },
  });

  const onSubmit = (data: TaskFormData) => {
    mutation.mutate(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          name="title"
          control={form.control}
          render={({ field }) => (
            <>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Task title" />
              </FormControl>
              <FormMessage />
            </>
          )}
        />

        <FormField
          name="description"
          control={form.control}
          render={({ field }) => (
            <>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea {...field} placeholder="Describe the task" />
              </FormControl>
              <FormMessage />
            </>
          )}
        />

        <FormField
          name="priority"
          control={form.control}
          render={({ field }) => (
            <>
              <FormLabel>Priority</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="Low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </>
          )}
        />

        <FormField
          name="dueDate"
          control={form.control}
          render={({ field }) => (
            <>
              <FormLabel>Due Date</FormLabel>
              <FormControl>
                <Input type="date" {...field} />
              </FormControl>
              <FormMessage />
            </>
          )}
        />

        <FormField
          name="budget"
          control={form.control}
          render={({ field }) => (
            <>
              <FormLabel>Budget (Optional)</FormLabel>
              <FormControl>
                <Input type="number" {...field} placeholder="Enter budget amount" />
              </FormControl>
              <FormMessage />
            </>
          )}
        />

        <Button 
          type="submit" 
          className="w-full"
          disabled={mutation.isPending}
        >
          {mutation.isPending ? "Creating Task..." : "Create Task"}
        </Button>
      </form>
    </Form>
  );
};

export default AddTaskForm;

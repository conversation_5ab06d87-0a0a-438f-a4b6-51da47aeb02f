import React, { useState } from 'react';
import api from '@/lib/axios';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';

interface FinanceFormProps {
  hoaId?: string | null;
  communityId?: string | null;
}

const FinanceForm: React.FC<FinanceFormProps> = ({ hoaId, communityId }) => {
  const queryClient = useQueryClient();
  const [form, setForm] = useState({
    type: 'income',
    category: '',
    amount: '',
    note: ''
  });

  const [error, setError] = useState('');

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const { mutate, isPending } = useMutation<void, Error>({
    mutationFn: async () => {
      await api.post('/api/finances', {
        ...form,
        amount: parseFloat(form.amount),
        hoaId: hoaId || undefined,
        communityId: communityId || undefined
      });
    },
    onSuccess: () => {
      setForm({
        type: 'income',
        category: '',
        amount: '',
        note: ''
      });

      // Invalidate queries with the correct parameters
      queryClient.invalidateQueries({ queryKey: ['allFinance', hoaId, communityId] });
      queryClient.invalidateQueries({ queryKey: ['monthlyFinance', hoaId, communityId] });
      queryClient.invalidateQueries({ queryKey: ['financeBreakdown', hoaId, communityId] });

      // Also invalidate dashboard queries
      queryClient.invalidateQueries({ queryKey: ['dashboardFinance'] });
    },
    onError: (error: any) => {
      console.error('Finance entry error:', error);

      // Handle different error scenarios
      if (error.response?.status === 401) {
        setError('You must be logged in to add finance entries');
        toast({
          title: 'Authentication Error',
          description: 'You must be logged in to add finance entries',
          variant: 'destructive'
        });
      } else {
        setError('Failed to save entry. Please try again.');
        toast({
          title: 'Error',
          description: 'Failed to save finance entry',
          variant: 'destructive'
        });
      }
    }
  });


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError(''); // Clear any previous errors
    mutate();
  };


  return (
    <div className="w-full max-w-md mx-auto p-4 sm:p-6 bg-white border rounded shadow">
      <h2 className="text-xl font-bold mb-4">Add Finance Entry</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block">{error}</span>
          </div>
        )}

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block mb-1 text-sm font-medium">Type</label>
            <select
              name="type"
              value={form.type}
              onChange={handleChange}
              className="w-full border px-3 py-2 rounded text-base"
            >
              <option value="income">Income</option>
              <option value="expense">Expense</option>
            </select>
          </div>

          <div>
            <label className="block mb-1 text-sm font-medium">Amount ($)</label>
            <input
              type="number"
              name="amount"
              value={form.amount}
              onChange={handleChange}
              required
              inputMode="decimal"
              className="w-full border px-3 py-2 rounded text-base"
              placeholder="0.00"
            />
          </div>
        </div>

        <div>
          <label className="block mb-1 text-sm font-medium">Category</label>
          <input
            type="text"
            name="category"
            value={form.category}
            onChange={handleChange}
            required
            className="w-full border px-3 py-2 rounded text-base"
            placeholder="e.g., Dues, Maintenance, Utilities"
          />
        </div>

        <div>
          <label className="block mb-1 text-sm font-medium">Note</label>
          <textarea
            name="note"
            value={form.note}
            onChange={handleChange}
            rows={3}
            className="w-full border px-3 py-2 rounded text-base"
            placeholder="Add details about this transaction"
          />
        </div>

        <button
          type="submit"
          className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded font-medium text-base"
          disabled={isPending}
        >
          {isPending ? 'Saving...' : 'Save Entry'}
        </button>
      </form>
    </div>
  );
};

export default FinanceForm;

import React, { useState, useEffect } from 'react';
import {
  Users,
  Send,
  Upload,
  X,
  CheckSquare,
  Square,
  AlertCircle,
  FileText,
  Image,
  Paperclip
} from 'lucide-react';
import api from '@/lib/axios';

interface Street {
  id: string;
  name: string;
  code: string;
  description?: string;
}

interface Resident {
  id: string;
  email: string;
  username: string;
  fullName: string;
  streetAddress: string;
}

interface HOA {
  id: string;
  name: string;
  code: string;
  location: string;
}

interface TargetingOptions {
  allResidents: {
    id: string;
    label: string;
    count: number;
  };
  streets: Array<{
    id: string;
    name: string;
    code: string;
    residentCount: number;
  }>;
}

interface AttachmentFile {
  file: File;
  preview?: string;
}

interface EnhancedAnnouncementFormProps {
  userRole: 'admin' | 'company_admin';
  onSuccess?: () => void;
  onCancel?: () => void;
}

const EnhancedAnnouncementForm: React.FC<EnhancedAnnouncementFormProps> = ({
  userRole,
  onSuccess,
  onCancel
}) => {
  // Form state
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [sendEmail, setSendEmail] = useState(true);
  const [attachments, setAttachments] = useState<AttachmentFile[]>([]);

  // HOA Admin state
  const [streets, setStreets] = useState<Street[]>([]);
  const [selectedStreet, setSelectedStreet] = useState<string>('');
  const [residents, setResidents] = useState<Resident[]>([]);
  const [selectedResidents, setSelectedResidents] = useState<string[]>([]);
  const [selectAllResidents, setSelectAllResidents] = useState(false);

  // Company Admin state
  const [hoas, setHOAs] = useState<HOA[]>([]);
  const [selectedHOA, setSelectedHOA] = useState<string>('');
  const [targetingOptions, setTargetingOptions] = useState<TargetingOptions | null>(null);
  const [targetType, setTargetType] = useState<'all' | 'street'>('all');
  const [selectedTargetStreet, setSelectedTargetStreet] = useState<string>('');

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  // Load initial data based on user role and check for pre-selected street
  useEffect(() => {
    // Check if there's a pre-selected community from the sidebar
    const selectedCommunityId = localStorage.getItem('selectedCommunityId');

    if (userRole === 'admin') {
      loadStreetsForAdmin();

      // If there's a pre-selected community, auto-select it and load residents
      if (selectedCommunityId && selectedCommunityId !== 'all') {
        console.log('Auto-selecting street from sidebar:', selectedCommunityId);
        setSelectedStreet(selectedCommunityId);
        loadResidentsForStreet(selectedCommunityId);
      }
    } else if (userRole === 'company_admin') {
      loadHOAsForCompanyAdmin();
    }
  }, [userRole]);

  // Load streets for HOA admin
  const loadStreetsForAdmin = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/announcements/streets');
      setStreets(response.data.streets || []);
    } catch (error) {
      console.error('Error loading streets:', error);
      setError('Failed to load streets');
    } finally {
      setLoading(false);
    }
  };

  // Load residents for selected street
  const loadResidentsForStreet = async (streetId: string) => {
    try {
      setLoading(true);
      const response = await api.get(`/api/announcements/streets/${streetId}/residents`);
      setResidents(response.data.residents || []);
      setSelectedResidents([]);
      setSelectAllResidents(false);
    } catch (error) {
      console.error('Error loading residents:', error);
      setError('Failed to load residents');
    } finally {
      setLoading(false);
    }
  };

  // Load HOAs for company admin
  const loadHOAsForCompanyAdmin = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/announcements/hoas');
      setHOAs(response.data.hoas || []);
    } catch (error) {
      console.error('Error loading HOAs:', error);
      setError('Failed to load HOAs');
    } finally {
      setLoading(false);
    }
  };

  // Load targeting options for selected HOA
  const loadTargetingOptions = async (hoaId: string) => {
    try {
      setLoading(true);
      const response = await api.get(`/api/announcements/hoas/${hoaId}/targeting`);
      setTargetingOptions(response.data.targetingOptions);
      setTargetType('all');
      setSelectedTargetStreet('');
    } catch (error) {
      console.error('Error loading targeting options:', error);
      setError('Failed to load targeting options');
    } finally {
      setLoading(false);
    }
  };

  // Handle street selection for HOA admin
  const handleStreetSelection = (streetId: string) => {
    setSelectedStreet(streetId);
    if (streetId) {
      loadResidentsForStreet(streetId);
    } else {
      setResidents([]);
      setSelectedResidents([]);
    }
  };

  // Handle HOA selection for company admin
  const handleHOASelection = (hoaId: string) => {
    setSelectedHOA(hoaId);
    if (hoaId) {
      loadTargetingOptions(hoaId);
    } else {
      setTargetingOptions(null);
    }
  };

  // Handle resident selection
  const handleResidentToggle = (residentId: string) => {
    setSelectedResidents(prev => {
      const newSelection = prev.includes(residentId)
        ? prev.filter(id => id !== residentId)
        : [...prev, residentId];
      
      setSelectAllResidents(newSelection.length === residents.length);
      return newSelection;
    });
  };

  // Handle select all residents
  const handleSelectAllResidents = () => {
    if (selectAllResidents) {
      setSelectedResidents([]);
      setSelectAllResidents(false);
    } else {
      setSelectedResidents(residents.map(r => r.id));
      setSelectAllResidents(true);
    }
  };

  // Handle file attachment
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    files.forEach(file => {
      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        setError(`File ${file.name} is too large. Maximum size is 10MB.`);
        return;
      }

      // Create preview for images
      let preview: string | undefined;
      if (file.type.startsWith('image/')) {
        preview = URL.createObjectURL(file);
      }

      setAttachments(prev => [...prev, { file, preview }]);
    });

    // Clear the input
    event.target.value = '';
  };

  // Remove attachment
  const removeAttachment = (index: number) => {
    setAttachments(prev => {
      const newAttachments = [...prev];
      const removed = newAttachments.splice(index, 1)[0];
      
      // Clean up preview URL
      if (removed.preview) {
        URL.revokeObjectURL(removed.preview);
      }
      
      return newAttachments;
    });
  };

  // Get file icon
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <Image className="h-4 w-4" />;
    if (file.type.includes('pdf')) return <FileText className="h-4 w-4" />;
    return <Paperclip className="h-4 w-4" />;
  };

  // Validate form
  const validateForm = () => {
    if (!subject.trim()) {
      setError('Subject is required');
      return false;
    }
    if (!message.trim()) {
      setError('Message is required');
      return false;
    }

    if (userRole === 'admin') {
      if (!selectedStreet) {
        setError('Please select a street');
        return false;
      }
      if (selectedResidents.length === 0) {
        setError('Please select at least one recipient');
        return false;
      }
    } else if (userRole === 'company_admin') {
      if (!selectedHOA) {
        setError('Please select an HOA');
        return false;
      }
      if (targetType === 'street' && !selectedTargetStreet) {
        setError('Please select a street for targeting');
        return false;
      }
    }

    return true;
  };

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setLoading(true);
      setError('');

      // Prepare form data
      const formData = new FormData();
      formData.append('subject', subject);
      formData.append('message', message);
      formData.append('sendEmail', sendEmail.toString());

      // Add attachments
      attachments.forEach(attachment => {
        formData.append('attachments', attachment.file);
      });

      let endpoint = '';
      
      if (userRole === 'admin') {
        // HOA admin announcement
        endpoint = '/api/announcements/send/street';
        formData.append('streetId', selectedStreet);
        formData.append('recipients', JSON.stringify(selectedResidents));
      } else if (userRole === 'company_admin') {
        // Company admin announcement
        endpoint = '/api/announcements/send/hoa';
        formData.append('hoaId', selectedHOA);
        formData.append('targetType', targetType);
        if (targetType === 'street' && selectedTargetStreet) {
          formData.append('targetStreetId', selectedTargetStreet);
        }
      }

      const response = await api.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setSuccess(`Announcement sent successfully to ${response.data.recipientCount} recipients!`);
      
      // Reset form
      setSubject('');
      setMessage('');
      setAttachments([]);
      setSelectedResidents([]);
      setSelectAllResidents(false);
      
      if (onSuccess) {
        setTimeout(() => onSuccess(), 2000);
      }

    } catch (error: any) {
      console.error('Error sending announcement:', error);
      setError(error.response?.data?.message || 'Failed to send announcement');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          {userRole === 'admin' ? 'Send Street Announcement' : 'Send HOA Announcement'}
        </h2>
        {onCancel && (
          <button
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-6 w-6" />
          </button>
        )}
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
          <span className="text-green-700">{success}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Targeting Section */}
        {userRole === 'admin' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Select Recipients</h3>
            <p className="text-sm text-gray-600">
              💡 Tip: Select a street in the sidebar first to automatically pre-populate your announcement recipients.
            </p>
            
            {/* Street Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Street
                {selectedStreet && localStorage.getItem('selectedCommunityId') === selectedStreet && (
                  <span className="ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                    Pre-selected from sidebar
                  </span>
                )}
              </label>
              <select
                value={selectedStreet}
                onChange={(e) => handleStreetSelection(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Choose a street...</option>
                {streets.map(street => (
                  <option key={street.id} value={street.id}>
                    {street.name} ({street.code})
                  </option>
                ))}
              </select>
            </div>

            {/* Resident Selection */}
            {residents.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Recipients ({residents.length} residents)
                </label>
                
                {/* Select All Option */}
                <div className="mb-3 p-3 bg-gray-50 rounded-md">
                  <button
                    type="button"
                    onClick={handleSelectAllResidents}
                    className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
                  >
                    {selectAllResidents ? (
                      <CheckSquare className="h-5 w-5" />
                    ) : (
                      <Square className="h-5 w-5" />
                    )}
                    <span>Select All Residents</span>
                  </button>
                </div>

                {/* Individual Residents */}
                <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-md p-3 space-y-2">
                  {residents.map(resident => (
                    <div key={resident.id} className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => handleResidentToggle(resident.id)}
                        className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
                      >
                        {selectedResidents.includes(resident.id) ? (
                          <CheckSquare className="h-4 w-4 text-blue-600" />
                        ) : (
                          <Square className="h-4 w-4" />
                        )}
                        <span className="text-sm">
                          {resident.fullName} ({resident.email})
                          {resident.streetAddress && (
                            <span className="text-gray-500"> - {resident.streetAddress}</span>
                          )}
                        </span>
                      </button>
                    </div>
                  ))}
                </div>
                
                <p className="text-sm text-gray-600 mt-2">
                  {selectedResidents.length} of {residents.length} residents selected
                </p>
              </div>
            )}
          </div>
        )}

        {userRole === 'company_admin' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Select Target HOA</h3>
            
            {/* HOA Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select HOA
              </label>
              <select
                value={selectedHOA}
                onChange={(e) => handleHOASelection(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Choose an HOA...</option>
                {hoas.map(hoa => (
                  <option key={hoa.id} value={hoa.id}>
                    {hoa.name} - {hoa.location}
                  </option>
                ))}
              </select>
            </div>

            {/* Targeting Options */}
            {targetingOptions && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Targeting Options
                </label>
                
                <div className="space-y-3">
                  {/* All Residents Option */}
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="target-all"
                      name="targetType"
                      value="all"
                      checked={targetType === 'all'}
                      onChange={(e) => setTargetType(e.target.value as 'all' | 'street')}
                      className="text-blue-600"
                    />
                    <label htmlFor="target-all" className="text-sm text-gray-700">
                      {targetingOptions.allResidents.label} ({targetingOptions.allResidents.count} residents)
                    </label>
                  </div>

                  {/* Specific Street Option */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="target-street"
                        name="targetType"
                        value="street"
                        checked={targetType === 'street'}
                        onChange={(e) => setTargetType(e.target.value as 'all' | 'street')}
                        className="text-blue-600"
                      />
                      <label htmlFor="target-street" className="text-sm text-gray-700">
                        Specific Street
                      </label>
                    </div>

                    {targetType === 'street' && (
                      <div className="ml-6">
                        <select
                          value={selectedTargetStreet}
                          onChange={(e) => setSelectedTargetStreet(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required={targetType === 'street'}
                        >
                          <option value="">Choose a street...</option>
                          {targetingOptions.streets.map(street => (
                            <option key={street.id} value={street.id}>
                              {street.name} ({street.residentCount} residents)
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Message Composition */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Compose Message</h3>
          
          {/* Subject */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subject *
            </label>
            <input
              type="text"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter announcement subject..."
              required
            />
          </div>

          {/* Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Message *
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your announcement message..."
              required
            />
          </div>

          {/* File Attachments */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Attachments (Optional)
            </label>
            
            <div className="border-2 border-dashed border-gray-300 rounded-md p-4">
              <input
                type="file"
                id="file-upload"
                multiple
                onChange={handleFileUpload}
                className="hidden"
                accept="image/*,.pdf,.doc,.docx,.txt,.xls,.xlsx"
              />
              
              <label
                htmlFor="file-upload"
                className="flex flex-col items-center justify-center cursor-pointer"
              >
                <Upload className="h-8 w-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-600">
                  Click to upload files or drag and drop
                </span>
                <span className="text-xs text-gray-500 mt-1">
                  Images, PDFs, Documents (Max 10MB each, 5 files total)
                </span>
              </label>
            </div>

            {/* Attachment List */}
            {attachments.length > 0 && (
              <div className="mt-3 space-y-2">
                {attachments.map((attachment, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                    <div className="flex items-center space-x-2">
                      {getFileIcon(attachment.file)}
                      <span className="text-sm text-gray-700">{attachment.file.name}</span>
                      <span className="text-xs text-gray-500">
                        ({Math.round(attachment.file.size / 1024)} KB)
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeAttachment(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Email Option */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="send-email"
              checked={sendEmail}
              onChange={(e) => setSendEmail(e.target.checked)}
              className="text-blue-600"
            />
            <label htmlFor="send-email" className="text-sm text-gray-700">
              Send email notifications to recipients
            </label>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={loading}
            className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Sending...</span>
              </>
            ) : (
              <>
                <Send className="h-4 w-4" />
                <span>Send Announcement</span>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EnhancedAnnouncementForm;

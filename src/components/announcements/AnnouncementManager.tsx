import React, { useState } from 'react';
import { Megaphone, Plus, List, ArrowLeft } from 'lucide-react';
import EnhancedAnnouncementForm from './EnhancedAnnouncementForm';

interface AnnouncementManagerProps {
  userRole: 'admin' | 'company_admin' | 'user' | 'member';
}

const AnnouncementManager: React.FC<AnnouncementManagerProps> = ({ userRole }) => {
  const [currentView, setCurrentView] = useState<'list' | 'create'>('list');

  // Regular users should not see announcements
  if (userRole === 'user' || userRole === 'member') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <Megaphone className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Announcements</h2>
          <p className="text-gray-600">
            You don't have permission to access announcements.
            Please contact your HOA administrator for assistance.
          </p>
        </div>
      </div>
    );
  }

  const handleCreateSuccess = () => {
    setCurrentView('list');
  };

  const handleCancel = () => {
    setCurrentView('list');
  };

  if (currentView === 'create') {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <div className="mb-6">
            <button
              onClick={() => setCurrentView('list')}
              className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Announcements</span>
            </button>
          </div>

          <EnhancedAnnouncementForm
            userRole={userRole as 'admin' | 'company_admin'}
            onSuccess={handleCreateSuccess}
            onCancel={handleCancel}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Megaphone className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Announcements</h1>
                <p className="text-gray-600">
                  {userRole === 'admin'
                    ? 'Send announcements to residents on your streets'
                    : 'Send announcements to HOA communities'
                  }
                </p>
              </div>
            </div>

            <button
              onClick={() => setCurrentView('create')}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>New Announcement</span>
            </button>
          </div>
        </div>

        {/* Announcement Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {userRole === 'admin' && (
            <>
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <List className="h-5 w-5 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Street Targeting</h3>
                </div>
                <p className="text-gray-600 text-sm">
                  Select specific streets you manage and choose individual residents or all residents on that street.
                </p>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Megaphone className="h-5 w-5 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Rich Messaging</h3>
                </div>
                <p className="text-gray-600 text-sm">
                  Compose detailed announcements with file attachments including images and documents.
                </p>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Plus className="h-5 w-5 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Email & In-App</h3>
                </div>
                <p className="text-gray-600 text-sm">
                  Send announcements via email and in-app notifications to ensure all residents are informed.
                </p>
              </div>
            </>
          )}

          {userRole === 'company_admin' && (
            <>
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <List className="h-5 w-5 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">HOA-Level Targeting</h3>
                </div>
                <p className="text-gray-600 text-sm">
                  Send announcements to entire HOA communities or target specific streets within an HOA.
                </p>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Megaphone className="h-5 w-5 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">System Announcements</h3>
                </div>
                <p className="text-gray-600 text-sm">
                  Send platform-wide announcements, maintenance notices, and important system updates.
                </p>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Plus className="h-5 w-5 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Multi-HOA Reach</h3>
                </div>
                <p className="text-gray-600 text-sm">
                  Manage communications across multiple HOA communities from a single interface.
                </p>
              </div>
            </>
          )}
        </div>

        {/* Recent Announcements Placeholder */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Recent Announcements</h2>
            <button className="text-blue-600 hover:text-blue-800 text-sm">
              View All
            </button>
          </div>

          <div className="text-center py-12">
            <Megaphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">
              No announcements sent yet. Click "New Announcement" to get started.
            </p>
          </div>
        </div>

        {/* Quick Tips */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="font-semibold text-blue-900 mb-3">💡 Quick Tips</h3>
          <ul className="space-y-2 text-blue-800 text-sm">
            {userRole === 'admin' ? (
              <>
                <li>• Use clear, descriptive subjects to help residents understand the announcement importance</li>
                <li>• Attach relevant documents or images to provide additional context</li>
                <li>• Consider the timing of your announcements for maximum visibility</li>
                <li>• Use the "Select All" option for street-wide announcements</li>
              </>
            ) : (
              <>
                <li>• Target specific HOAs for relevant community announcements</li>
                <li>• Use "All Residents" for platform-wide important updates</li>
                <li>• Include clear action items and deadlines in your messages</li>
                <li>• Attach policy documents or important notices as needed</li>
              </>
            )}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementManager;

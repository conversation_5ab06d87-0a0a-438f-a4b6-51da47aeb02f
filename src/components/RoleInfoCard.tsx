import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, User, Clock, Building, UserCog, AlertTriangle } from 'lucide-react';
import RoleBadge from './RoleBadge';

type RoleType = 'admin' | 'resident' | 'pending' | 'property_manager' | 'company_admin' | 'denied';

interface RoleInfoCardProps {
  role: RoleType;
  hoaName?: string;
  selectedStreet?: string;
}

const RoleInfoCard: React.FC<RoleInfoCardProps> = ({ role, hoaName, selectedStreet }) => {
  const getRoleIcon = () => {
    switch (role) {
      case 'admin':
        return <Shield className="h-8 w-8 text-primary" />;
      case 'company_admin':
        return <Building className="h-8 w-8 text-destructive" />;
      case 'property_manager':
        return <UserCog className="h-8 w-8 text-secondary" />;
      case 'resident':
        return <User className="h-8 w-8 text-muted-foreground" />;
      case 'pending':
        return <Clock className="h-8 w-8 text-secondary" />;
      case 'denied':
        return <AlertTriangle className="h-8 w-8 text-destructive" />;
      default:
        return <User className="h-8 w-8 text-muted-foreground" />;
    }
  };

  const getRoleDescription = () => {
    switch (role) {
      case 'admin':
        return `You are an administrator for ${hoaName || 'your HOA community'}. You can manage community settings, approve new members, and handle community tasks.`;
      case 'company_admin':
        return selectedStreet
          ? `You are viewing data for the selected community. Use the HOA Watchdog dropdown to monitor financial contributions across all HOAs.`
          : 'You are a company administrator with full platform access. Use the HOA Watchdog dropdown to monitor financial contributions across all HOAs.';
      case 'property_manager':
        return `You are a property manager for ${hoaName || 'your HOA community'}. You can manage properties, handle maintenance requests, and assist residents.`;
      case 'resident':
        return `You are a resident of ${hoaName || 'your HOA community'}. You can view community information, submit requests, and participate in community activities.`;
      case 'pending':
        return 'Your account is pending approval. Once approved, you will have access to your community features.';
      case 'denied':
        return 'Your account access has been denied. Please contact your HOA administrator for assistance.';
      default:
        return `Welcome to ${hoaName || 'your'} HOA community portal.`;
    }
  };

  const getRoleTitle = () => {
    switch (role) {
      case 'admin':
        return 'HOA Administrator';
      case 'company_admin':
        return 'Company Administrator';
      case 'property_manager':
        return 'Property Manager';
      case 'resident':
        return 'Community Resident';
      case 'pending':
        return 'Pending Approval';
      case 'denied':
        return 'Access Denied';
      default:
        return 'Community Member';
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">{getRoleTitle()}</CardTitle>
          <RoleBadge role={role} />
        </div>
        <CardDescription>
          {selectedStreet
            ? `Managing: ${selectedStreet}`
            : (hoaName && role !== 'company_admin'
                ? `${hoaName} Community`
                : 'Street Harmony HOA Management')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-start space-x-4">
          <div className="mt-0.5">{getRoleIcon()}</div>
          <div className="space-y-2 flex-1">
            <p className="text-sm text-muted-foreground">{getRoleDescription()}</p>

            {role === 'company_admin' && (
              <div className="mt-2 p-2 bg-muted rounded-md">
                {selectedStreet ? (
                  <>
                    <p className="text-xs font-medium">You are viewing data specific to the selected community.</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      All dashboard statistics, tasks, and financial data shown are filtered for this community only.
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-xs font-medium">Platform Oversight Dashboard</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Use the HOA Watchdog dropdown to monitor specific HOAs and their financial contributions.
                    </p>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RoleInfoCard;

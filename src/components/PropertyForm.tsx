import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { PropertyCreateData, PropertyUpdateData, getAllMembers } from '@/services/propertyService';
import { getCommunitiesByHoa, Community } from '@/services/communityService';
import { X, Plus } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';

// Define the form schema with Zod
const propertyFormSchema = z.object({
  address: z.string().min(1, 'Address is required'),
  type: z.string().min(1, 'Property type is required'),
  status: z.enum(['occupied', 'vacant', 'maintenance']).optional(),
  yearBuilt: z.string().optional(),
  squareFeet: z.coerce.number().positive().optional(),
  bedrooms: z.coerce.number().int().min(0).optional(),
  bathrooms: z.coerce.number().min(0).optional(),
  amenities: z.array(z.string()).optional(),
  nextInspection: z.string().optional(),
  communityId: z.string().optional(),
  resident: z.object({
    fullName: z.string().min(1, 'Resident name is required'),
    _id: z.string()
  })
});

type PropertyFormValues = z.infer<typeof propertyFormSchema>;

interface PropertyFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: PropertyCreateData | PropertyUpdateData) => void;
  initialData?: Partial<PropertyCreateData>;
  isLoading?: boolean;
}

const PropertyForm: React.FC<PropertyFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  isLoading = false
}) => {
  const [amenity, setAmenity] = useState('');
  const [amenities, setAmenities] = useState<string[]>(initialData?.amenities || []);

  // Get the user's HOA ID from localStorage
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const userHoaId = user?.hoaId || '';

  // Fetch communities for the user's HOA
  const { data: communities = [], isLoading: isLoadingCommunities } = useQuery({
    queryKey: ['communities', userHoaId],
    queryFn: () => getCommunitiesByHoa(userHoaId),
    enabled: !!userHoaId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Fetch all members for the HOA
  const { data: memberLists = [], isLoading: isLoadingMember } = useQuery({
     queryKey: ['memberLists', userHoaId],
      queryFn: () => getAllMembers(),
      enabled: !!userHoaId,
  })

  // Initialize the form with react-hook-form
  const form = useForm<PropertyFormValues>({
    resolver: zodResolver(propertyFormSchema),
    defaultValues: {
      address: initialData?.address || '',
      type: initialData?.type || 'Single Family',
      status: initialData?.status || 'vacant',
      yearBuilt: initialData?.yearBuilt || '',
      squareFeet: initialData?.squareFeet || undefined,
      bedrooms: initialData?.bedrooms || undefined,
      bathrooms: initialData?.bathrooms || undefined,
      amenities: initialData?.amenities || [],
      nextInspection: initialData?.nextInspection ? new Date(initialData.nextInspection).toISOString().split('T')[0] : '',
      communityId: initialData?.communityId || 'none',
      resident: initialData?.resident && initialData.resident._id ? initialData.resident : initialData?.resident || {
        fullName: '',
        _id: ''
      },
    }
  });

  // Update form when initialData changes
  useEffect(() => {
    try {
      console.log("PropertyForm initialData:", initialData);

      if (initialData) {
        // Create a safe copy of the data with default values for all fields
        const safeData = {
          address: initialData.address || '',
          type: initialData.type || 'Single Family',
          status: initialData.status || 'vacant',
          yearBuilt: initialData.yearBuilt || '',
          squareFeet: initialData.squareFeet || undefined,
          bedrooms: initialData.bedrooms || undefined,
          bathrooms: initialData.bathrooms || undefined,
          amenities: initialData.amenities || [],
          nextInspection: '',
          communityId: initialData.communityId || 'none',
          resident: initialData?.resident && initialData.resident._id ? initialData.resident : initialData?.resident || {
            fullName: '',
            _id: ''
          },
        };

        // Safely handle date conversion
        if (initialData.nextInspection) {
          try {
            safeData.nextInspection = new Date(initialData.nextInspection).toISOString().split('T')[0];
          } catch (dateError) {
            console.error("Error parsing nextInspection date:", dateError);
            // Keep the default empty string
          }
        }

        console.log("Resetting form with safe data:", safeData);
        form.reset(safeData);
        setAmenities(initialData.amenities || []);
      }
    } catch (error) {
      console.error("Error updating form with initialData:", error);
      // Reset to empty form as fallback
      form.reset({
        address: '',
        type: 'Single Family',
        status: 'vacant',
        yearBuilt: '',
        squareFeet: undefined,
        bedrooms: undefined,
        bathrooms: undefined,
        amenities: [],
        nextInspection: '',
        communityId: 'none',
        resident: {
          fullName: '',
          _id: ''
        }
      });
      setAmenities([]);
    }
  }, [initialData, form]);

  // Handle form submission
  const handleSubmit = (values: PropertyFormValues) => {
    // Include amenities in the form data
    const formData = {
      ...values,
      amenities,
      nextInspection: values.nextInspection ? new Date(values.nextInspection).toISOString() : undefined,
      // Convert 'none' to empty string or null for the backend
      communityId: values.communityId === 'none' ? null : values.communityId,
      resident: values.resident
    };
    console.log("Submitting property form with data:", formData);
    onSubmit(formData);
  };

  // Handle adding an amenity
  const handleAddAmenity = () => {
    if (amenity.trim() && !amenities.includes(amenity.trim())) {
      const newAmenities = [...amenities, amenity.trim()];
      setAmenities(newAmenities);
      form.setValue('amenities', newAmenities);
      setAmenity('');
    }
  };

  // Handle removing an amenity
  const handleRemoveAmenity = (index: number) => {
    const newAmenities = amenities.filter((_, i) => i !== index);
    setAmenities(newAmenities);
    form.setValue('amenities', newAmenities);
  };

  // Wrap the entire component in a try-catch to prevent white screens
  try {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{initialData?._id ? 'Edit Property' : 'Add New Property'}</DialogTitle>
          </DialogHeader>

          <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input placeholder="123 Main St" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              
              <FormField
                control={form.control}
                name="resident"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Owned By <span className="text-red-500">*</span></FormLabel>
                    <Select
                      onValueChange={(value) => {
                        const selectedMember = memberLists.find((member) => member._id === value);
                        if (selectedMember) {
                          field.onChange({_id: selectedMember._id, fullName: selectedMember.fullName  });
                        }
                      }}
                      value={field.value?._id || ''}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select Owner" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {memberLists.map((member) => {
                          return (
                            <SelectItem key={member._id} value={member._id}>
                              {member.fullName}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Property Type <span className="text-red-500">*</span></FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select property type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Single Family">Single Family</SelectItem>
                        <SelectItem value="Townhouse">Townhouse</SelectItem>
                        <SelectItem value="Condo">Condo</SelectItem>
                        <SelectItem value="Apartment">Apartment</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="vacant">Vacant</SelectItem>
                        <SelectItem value="occupied">Occupied</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="yearBuilt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Year Built</FormLabel>
                    <FormControl>
                      <Input placeholder="2010" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="squareFeet"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Square Feet</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="2000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="bedrooms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bedrooms</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="3" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bathrooms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bathrooms</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="2" step="0.5" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="nextInspection"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Next Inspection Date</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="communityId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Community/Street</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isLoadingCommunities}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a community" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {communities.map((community: Community) => (
                        <SelectItem key={community._id} value={community._id}>
                          {community.name} ({community.streetAddress || 'No address'})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div>
              <Label>Amenities</Label>
              <div className="flex mt-1 mb-2">
                <Input
                  value={amenity}
                  onChange={(e) => setAmenity(e.target.value)}
                  placeholder="Add amenity (e.g., Garage, Pool)"
                  className="flex-1"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddAmenity();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  className="ml-2"
                  onClick={handleAddAmenity}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2 mt-2">
                {amenities.map((item, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {item}
                    <button
                      type="button"
                      onClick={() => handleRemoveAmenity(index)}
                      className="ml-1 rounded-full hover:bg-gray-200 p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>

            <DialogFooter className="mt-6">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : initialData?._id ? 'Update Property' : 'Add Property'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
    );
  } catch (error) {
    console.error("Error rendering PropertyForm:", error);
    // Provide a fallback UI
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
          </DialogHeader>
          <div className="p-4 border border-red-200 rounded-md bg-red-50">
            <p className="text-red-800 mb-2">There was an error loading the property form.</p>
            <p className="text-sm text-red-600">Please try again or contact support if the problem persists.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={onClose}>Close</Button>
            <Button onClick={() => window.location.reload()}>Refresh Page</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }
};

export default PropertyForm;

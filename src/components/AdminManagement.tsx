import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertCircle, Edit, Trash2, UserPlus, Shield } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { getAllAdmins, createAdmin, updateAdmin, deleteAdmin, AdminUser, CreateAdminData, UpdateAdminData } from '@/services/adminService';
import { getAllHOAs, HOA } from '@/services/hoaService';

const AdminManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedAdmin, setSelectedAdmin] = useState<AdminUser | null>(null);
  const [formData, setFormData] = useState<CreateAdminData>({
    username: '',
    email: '',
    password: '',
    fullName: '',
    propertyAddress: '',
    hoaId: '',
    phoneNumber: ''
  });

  // Fetch admin users
  const { data: admins, isLoading: isLoadingAdmins, isError: isErrorAdmins, error: adminsError } = useQuery({
    queryKey: ['admins'],
    queryFn: async () => {
      try {
        console.log("Fetching admin users...");
        const result = await getAllAdmins();
        console.log("Admin users fetched:", result);
        return result;
      } catch (error) {
        console.error("Error fetching admin users:", error);
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: false
  });

  // Fetch HOAs for dropdown
  const { data: hoas, isLoading: isLoadingHOAs, isError: isErrorHOAs, error: hoasError } = useQuery({
    queryKey: ['hoas'],
    queryFn: async () => {
      try {
        console.log("Fetching HOAs...");
        const result = await getAllHOAs();
        console.log("HOAs fetched:", result);
        return result;
      } catch (error) {
        console.error("Error fetching HOAs:", error);
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: false
  });

  // Create admin mutation
  const createAdminMutation = useMutation({
    mutationFn: createAdmin,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admins'] });
      toast({
        title: 'Admin created',
        description: 'The admin user has been successfully created.',
        variant: 'default',
      });
      setIsCreateDialogOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: 'Creation failed',
        description: error.response?.data?.message || 'Failed to create admin user. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Update admin mutation
  const updateAdminMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAdminData }) => updateAdmin(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admins'] });
      toast({
        title: 'Admin updated',
        description: 'The admin user has been successfully updated.',
        variant: 'default',
      });
      setIsEditDialogOpen(false);
      setSelectedAdmin(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Update failed',
        description: error.response?.data?.message || 'Failed to update admin user. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Delete admin mutation
  const deleteAdminMutation = useMutation({
    mutationFn: deleteAdmin,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admins'] });
      toast({
        title: 'Admin deleted',
        description: 'The admin user has been successfully deleted.',
        variant: 'default',
      });
      setIsDeleteDialogOpen(false);
      setSelectedAdmin(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Deletion failed',
        description: error.response?.data?.message || 'Failed to delete admin user. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      hoaId: value === "none" ? "" : value
    }));
  };

  const handleCreateSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createAdminMutation.mutate(formData);
  };

  const handleEditSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedAdmin) {
      const updateData: UpdateAdminData = {
        username: formData.username,
        email: formData.email,
        fullName: formData.fullName,
        propertyAddress: formData.propertyAddress,
        hoaId: formData.hoaId,
        phoneNumber: formData.phoneNumber
      };

      // Only include password if it's not empty
      if (formData.password) {
        updateData.password = formData.password;
      }

      updateAdminMutation.mutate({ id: selectedAdmin._id, data: updateData });
    }
  };

  const handleDeleteConfirm = () => {
    if (selectedAdmin) {
      deleteAdminMutation.mutate(selectedAdmin._id);
    }
  };

  const openEditDialog = (admin: AdminUser) => {
    try {
      console.log("Opening edit dialog for admin:", admin);
      setSelectedAdmin(admin);
      setFormData({
        username: admin.username || '',
        email: admin.email || '',
        password: '', // Don't populate password field for security
        fullName: admin.fullName || '',
        propertyAddress: admin.propertyAddress || '',
        hoaId: admin.hoaId || '',
        phoneNumber: admin.phoneNumber || ''
      });
      setIsEditDialogOpen(true);
    } catch (error) {
      console.error("Error opening edit dialog:", error);
      toast({
        title: 'Error',
        description: 'Failed to open edit dialog. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const openDeleteDialog = (admin: AdminUser) => {
    try {
      console.log("Opening delete dialog for admin:", admin);
      setSelectedAdmin(admin);
      setIsDeleteDialogOpen(true);
    } catch (error) {
      console.error("Error opening delete dialog:", error);
      toast({
        title: 'Error',
        description: 'Failed to open delete dialog. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const resetForm = () => {
    setFormData({
      username: '',
      email: '',
      password: '',
      fullName: '',
      propertyAddress: '',
      hoaId: '',
      phoneNumber: ''
    });
  };

  if (isLoadingAdmins) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-3/4 mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (isErrorAdmins || isErrorHOAs) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {isErrorAdmins
            ? ((adminsError as any)?.message || 'Failed to load admin users. Please try again later.')
            : ((hoasError as any)?.message || 'Failed to load HOA data. Please try again later.')}
        </AlertDescription>
        <Button
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Reload Page
        </Button>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            <CardTitle>Admin Management</CardTitle>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <UserPlus className="h-4 w-4 mr-2" />
            Add Admin
          </Button>
        </div>
        <CardDescription>
          Manage admin users for your HOA communities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Username</TableHead>
              <TableHead>Full Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>HOA Community</TableHead>
              <TableHead>Street/Community</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {admins && Array.isArray(admins) && admins.length > 0 ? (
              admins.map((admin) => {
                if (!admin || !admin._id) {
                  return null; // Skip invalid admin entries
                }
                return (
                  <TableRow key={admin._id}>
                    <TableCell className="font-medium">{admin.username || 'N/A'}</TableCell>
                    <TableCell>{admin.fullName || 'N/A'}</TableCell>
                    <TableCell>{admin.email || 'N/A'}</TableCell>
                    <TableCell>
                      {admin.hoaId
                        ? ((admin as any).hoaId?.hoaCommunityName || admin.hoaCommunityCode || 'Unknown HOA')
                        : 'None'}
                    </TableCell>
                    <TableCell>
                      {(() => {
                        // Debug logging for admin community data
                        console.log('Admin community data:', {
                          adminId: admin._id,
                          adminEmail: admin.email,
                          communityId: admin.communityId,
                          communityType: typeof admin.communityId,
                          propertyAddress: admin.propertyAddress
                        });

                        if (!admin.communityId) {
                          return <span className="text-muted-foreground text-sm">
                            {admin.propertyAddress || 'No street assigned'}
                          </span>;
                        }

                        // Handle populated community object or array
                        if (typeof admin.communityId === 'object' && admin.communityId !== null) {
                          // Check if it's an array
                          if (Array.isArray(admin.communityId)) {
                            if (admin.communityId.length === 0) {
                              return <span className="text-muted-foreground text-sm">No communities assigned</span>;
                            }

                            // Show first community if multiple, or list all if few
                            if (admin.communityId.length === 1) {
                              const community = admin.communityId[0];
                              return (
                                <div className="text-sm">
                                  <div className="font-medium">{community.name || 'Unknown Street'}</div>
                                  {community.communityCode && (
                                    <div className="text-muted-foreground text-xs">Code: {community.communityCode}</div>
                                  )}
                                </div>
                              );
                            } else {
                              // Multiple communities - show count and first one
                              const firstCommunity = admin.communityId[0];
                              return (
                                <div className="text-sm">
                                  <div className="font-medium">{firstCommunity.name || 'Unknown Street'}</div>
                                  <div className="text-muted-foreground text-xs">
                                    +{admin.communityId.length - 1} more communities
                                  </div>
                                </div>
                              );
                            }
                          } else {
                            // Single community object
                            const community = admin.communityId as any;
                            return (
                              <div className="text-sm">
                                <div className="font-medium">{community.name || 'Unknown Street'}</div>
                                {community.communityCode && (
                                  <div className="text-muted-foreground text-xs">Code: {community.communityCode}</div>
                                )}
                              </div>
                            );
                          }
                        }

                        // Handle string ID (not populated)
                        if (typeof admin.communityId === 'string') {
                          return <span className="text-muted-foreground text-sm">Community ID: {admin.communityId}</span>;
                        }

                        return <span className="text-muted-foreground text-sm">No street data</span>;
                      })()}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="icon" onClick={() => openEditDialog(admin)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon" className="text-destructive" onClick={() => openDeleteDialog(admin)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  No admin users found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>

      {/* Create Admin Dialog */}
      {isCreateDialogOpen && (
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Create Admin User</DialogTitle>
              <DialogDescription>
                Add a new admin user to manage an HOA community
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreateSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="username">Username</Label>
                    <Input
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="propertyAddress">Property Address</Label>
                  <Input
                    id="propertyAddress"
                    name="propertyAddress"
                    value={formData.propertyAddress}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="hoaId">HOA Community</Label>
                  <Select value={formData.hoaId} onValueChange={handleSelectChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select HOA community" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {hoas?.map((hoa: HOA) => (
                        <SelectItem key={hoa._id} value={hoa._id}>
                          {hoa.hoaCommunityName} ({hoa.hoaCommunityCode})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={createAdminMutation.isPending}>
                  {createAdminMutation.isPending ? 'Creating...' : 'Create Admin'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Admin Dialog */}
      {isEditDialogOpen && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Edit Admin User</DialogTitle>
              <DialogDescription>
                Update admin user information
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleEditSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="edit-username">Username</Label>
                    <Input
                      id="edit-username"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="edit-email">Email</Label>
                    <Input
                      id="edit-email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-password">Password (leave blank to keep unchanged)</Label>
                  <Input
                    id="edit-password"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-fullName">Full Name</Label>
                  <Input
                    id="edit-fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-propertyAddress">Property Address</Label>
                  <Input
                    id="edit-propertyAddress"
                    name="propertyAddress"
                    value={formData.propertyAddress}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-phoneNumber">Phone Number</Label>
                  <Input
                    id="edit-phoneNumber"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-hoaId">HOA Community</Label>
                  <Select value={formData.hoaId} onValueChange={handleSelectChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select HOA community" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {hoas?.map((hoa: HOA) => (
                        <SelectItem key={hoa._id} value={hoa._id}>
                          {hoa.hoaCommunityName} ({hoa.hoaCommunityCode})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={updateAdminMutation.isPending}>
                  {updateAdminMutation.isPending ? 'Updating...' : 'Update Admin'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Admin Dialog */}
      {isDeleteDialogOpen && (
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle className="text-destructive">Delete Admin User</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this admin user? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              {selectedAdmin && (
                <div className="space-y-2">
                  <p><strong>Username:</strong> {selectedAdmin.username}</p>
                  <p><strong>Email:</strong> {selectedAdmin.email}</p>
                  <p><strong>Full Name:</strong> {selectedAdmin.fullName}</p>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                type="button"
                variant="destructive"
                onClick={handleDeleteConfirm}
                disabled={deleteAdminMutation.isPending}
              >
                {deleteAdminMutation.isPending ? 'Deleting...' : 'Delete Admin'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
};

export default AdminManagement;

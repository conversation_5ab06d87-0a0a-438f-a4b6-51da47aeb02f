import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Building2, Home, Users, Wrench, Calendar, ArrowRight, Plus, Search, Image, RefreshCw, AlertTriangle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import PropertyDetailsModal from './PropertyDetailsModal';
import PropertyForm from './PropertyForm';
import MaintenanceRequestForm from './MaintenanceRequestForm';
import { getProfilePhotoUrl } from '@/utils/imageUtils';
import { ErrorBoundary } from 'react-error-boundary';
import api from '@/lib/axios';
import {
  getProperties,
  getProperty,
  createProperty,
  updateProperty,
  deleteProperty,
  Property,
  PropertyCreateData,
  PropertyUpdateData
} from '@/services/propertyService';
import {
  getMaintenanceRequests,
  getMaintenanceRequest,
  createMaintenanceRequest,
  updateMaintenanceRequest,
  deleteMaintenanceRequest,
  MaintenanceRequest,
  MaintenanceRequestCreateData,
  MaintenanceRequestUpdateData
} from '@/services/maintenanceService';

// Interface for the local component state
interface PropertyWithDetails extends Property {
  // Additional UI-specific properties
  isSelected?: boolean;
}

interface MaintenanceRequestWithDetails extends MaintenanceRequest {
  // Additional UI-specific properties
  isSelected?: boolean;
}

const PropertyManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [selectedMaintenanceRequest, setSelectedMaintenanceRequest] = useState<MaintenanceRequest | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPropertyFormOpen, setIsPropertyFormOpen] = useState(false);
  const [isMaintenanceFormOpen, setIsMaintenanceFormOpen] = useState(false);
  const [editingProperty, setEditingProperty] = useState<Property | null>(null);
  const [editingRequest, setEditingRequest] = useState<MaintenanceRequest | null>(null);
  const [propertyToDelete, setPropertyToDelete] = useState<Property | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Multiple community support (same as other components)
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });
  const [selectedCommunityIds, setSelectedCommunityIds] = useState<string[]>(() => {
    const stored = localStorage.getItem('selectedCommunityIds');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);
  const [isMultipleCommunities, setIsMultipleCommunities] = useState<boolean>(false);
  const [availableCommunitiesInDropdown, setAvailableCommunitiesInDropdown] = useState<string[]>([]);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const hoaId = user.hoaId;
  const userCommunityId = user?.communityId;

  // Simplified role detection
  const isCompanyAdmin = user.role === 'company_admin';
  const isAdmin = user.role === 'admin';
  const isMember = user.role === 'member';

  // Any admin can manage properties
  const canManageProperties = isCompanyAdmin || isAdmin;

  // Listen for community selection changes from Sidebar
  React.useEffect(() => {
    // Initialize from localStorage
    const storedCommunityId = localStorage.getItem('selectedCommunityId');
    const storedCommunityIds = localStorage.getItem('selectedCommunityIds');

    if (storedCommunityIds) {
      const ids = JSON.parse(storedCommunityIds);
      setSelectedCommunityIds(ids);
      setIsMultipleCommunities(true);
      setSelectedCommunityName(`${ids.length} Communities Selected`);
    } else if (storedCommunityId) {
      setSelectedCommunityId(storedCommunityId);
      setIsMultipleCommunities(false);
    }

    const handleCommunityChange = async (event: CustomEvent) => {
      const { communityId, communityIds, availableCommunities } = event.detail;
      console.log('PropertyManagement: Received community selection change:', { communityId, communityIds, availableCommunities });

      // Store available communities from dropdown for "All Communities" use
      if (availableCommunities && Array.isArray(availableCommunities)) {
        const communityIdsList = availableCommunities.map((community: any) => community._id || community.id);
        setAvailableCommunitiesInDropdown(communityIdsList);
      }

      // Handle both single and multiple community selection
      if (communityIds && Array.isArray(communityIds)) {
        // Multiple communities selected
        setSelectedCommunityIds(communityIds);
        setSelectedCommunityId(null);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`${communityIds.length} Communities Selected`);

        console.log('PropertyManagement: Multiple communities selected:', communityIds);
      } else if (communityId && communityId !== 'all') {
        // Single community selected
        setSelectedCommunityId(communityId);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);

        // Fetch community details for display name
        try {
          const response = await api.get(`/api/communities/${communityId}`);
          const community = response.data;
          if (community) {
            setSelectedCommunityName(`${community.name} (${community.streetAddress || 'No address'})`);
          }
        } catch (error) {
          console.error('Error fetching community details:', error);
          setSelectedCommunityName('Selected Community');
        }
      } else {
        // "All Communities" selection
        setSelectedCommunityId(null);
        const communitiesToUse = communityIds && communityIds.length > 0 ? communityIds : availableCommunitiesInDropdown;
        setSelectedCommunityIds(communitiesToUse);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`All Communities (${communitiesToUse.length})`);
      }
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, [availableCommunitiesInDropdown]);

  // Fetch properties with community filtering
  const {
    data: properties = [],
    isLoading: isLoadingProperties,
    error: propertiesError
  } = useQuery({
    queryKey: ['properties', selectedCommunityId, selectedCommunityIds],
    queryFn: async () => {
      // Build endpoint with community filtering
      let endpoint = '/api/properties';
      const params = [];

      // Role-based filtering using communityId (same as other components)
      if (isCompanyAdmin) {
        // Company admin can see all, but apply community filters if specified
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities")
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('PropertyManagement: Company admin fetching properties for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          // Single community selected
          params.push(`communityId=${selectedCommunityId}`);
          console.log('PropertyManagement: Company admin fetching properties for single community:', selectedCommunityId);
        }
        // If no filters, company admin sees all properties (this is intentional)
      } else {
        // Admin and Members see properties from communities they have access to
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities" = all accessible communities)
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('PropertyManagement: User fetching properties for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          params.push(`communityId=${selectedCommunityId}`);
          console.log('PropertyManagement: User fetching properties for single community:', selectedCommunityId);
        } else if (userCommunityId) {
          // Default to their community if no specific selection
          params.push(`communityId=${userCommunityId}`);
          console.log('PropertyManagement: User fetching properties for their default community:', userCommunityId);
        }
      }

      // IMPORTANT: Always ensure we have some filtering for non-company-admin users
      if (!isCompanyAdmin && params.length === 0) {
        console.warn('PropertyManagement: No filtering applied for non-company-admin user, applying default community filter');
        // Apply default user-based filtering with communityId only
        if (userCommunityId) {
          params.push(`communityId=${userCommunityId}`);
          console.log('PropertyManagement: Applied default community filter for security:', userCommunityId);
        }
      }

      if (params.length > 0) {
        endpoint += `?${params.join('&')}`;
      }

      console.log('PropertyManagement: Fetching properties from endpoint:', endpoint);

      // Use existing getProperties service but with custom endpoint if needed
      if (params.length > 0) {
        // Custom fetch with community filtering using api instance
        const response = await api.get(endpoint);
        return response.data.data || response.data;
      } else {
        // Use existing service for fallback
        return getProperties();
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Fetch maintenance requests with community filtering
  const {
    data: maintenanceRequests = [],
    isLoading: isLoadingMaintenance,
    error: maintenanceError
  } = useQuery({
    queryKey: ['maintenanceRequests', selectedCommunityId, selectedCommunityIds],
    queryFn: async () => {
      // Build endpoint with community filtering (same logic as properties)
      let endpoint = '/api/maintenance-requests';
      const params = [];

      // Role-based filtering using communityId
      if (isCompanyAdmin) {
        // Company admin can see all, but apply community filters if specified
        if (selectedCommunityIds.length > 0) {
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('PropertyManagement: Company admin fetching maintenance requests for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          params.push(`communityId=${selectedCommunityId}`);
          console.log('PropertyManagement: Company admin fetching maintenance requests for single community:', selectedCommunityId);
        }
      } else {
        // Admin and Members see maintenance requests from communities they have access to
        if (selectedCommunityIds.length > 0) {
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('PropertyManagement: User fetching maintenance requests for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          params.push(`communityId=${selectedCommunityId}`);
          console.log('PropertyManagement: User fetching maintenance requests for single community:', selectedCommunityId);
        } else if (userCommunityId) {
          params.push(`communityId=${userCommunityId}`);
          console.log('PropertyManagement: User fetching maintenance requests for their default community:', userCommunityId);
        }
      }

      // Security fallback for non-company-admin users
      if (!isCompanyAdmin && params.length === 0) {
        console.warn('PropertyManagement: No filtering applied for maintenance requests, applying default community filter');
        if (userCommunityId) {
          params.push(`communityId=${userCommunityId}`);
          console.log('PropertyManagement: Applied default community filter for maintenance requests:', userCommunityId);
        }
      }

      if (params.length > 0) {
        endpoint += `?${params.join('&')}`;
      }

      console.log('PropertyManagement: Fetching maintenance requests from endpoint:', endpoint);

      // Use existing getMaintenanceRequests service but with custom endpoint if needed
      if (params.length > 0) {
        // Custom fetch with community filtering using api instance
        const response = await api.get(endpoint);
        return response.data.data || response.data;
      } else {
        // Use existing service for fallback
        return getMaintenanceRequests();
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Create property mutation
  const createPropertyMutation = useMutation({
    mutationFn: createProperty,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      toast({
        title: 'Success',
        description: 'Property created successfully',
      });
      setIsPropertyFormOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create property',
        variant: 'destructive',
      });
    },
  });

  // Update property mutation
  const updatePropertyMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: PropertyUpdateData }) =>
      updateProperty(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      toast({
        title: 'Success',
        description: 'Property updated successfully',
      });
      setIsPropertyFormOpen(false);
      setEditingProperty(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update property',
        variant: 'destructive',
      });
    },
  });

  // Delete property mutation
  const deletePropertyMutation = useMutation({
    mutationFn: ({ id, forceDelete }: { id: string; forceDelete?: boolean }) =>
      deleteProperty(id, forceDelete),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      toast({
        title: 'Success',
        description: 'Property deleted successfully',
      });
      setIsDeleteDialogOpen(false);
      setPropertyToDelete(null);
    },
    onError: (error: any) => {
      console.error('Property deletion error:', error);

      // Get a user-friendly error message
      let errorMessage = 'Failed to delete property';
      let errorTitle = 'Error';

      if (error instanceof Error) {
        errorMessage = error.message;

        // Check if this is a maintenance history error
        if (errorMessage.includes('maintenance history')) {
          errorTitle = 'Cannot Delete Property';
          // Update the error message to be more specific about property deletion
          errorMessage = errorMessage.replace('maintenance history', 'maintenance records');
        }
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;

        // Check if this is a maintenance history error from the API
        if (errorMessage.includes('maintenance history')) {
          errorTitle = 'Cannot Delete Property';
          // Update the error message to be more specific about property deletion
          errorMessage = errorMessage.replace('maintenance history', 'maintenance records');
        }
      }

      toast({
        title: errorTitle,
        description: errorMessage,
        variant: 'destructive',
      });

      // Close the dialog on error so user can try again with a fresh start
      setIsDeleteDialogOpen(false);
    },
  });

  // Create maintenance request mutation
  const createMaintenanceRequestMutation = useMutation({
    mutationFn: createMaintenanceRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['maintenanceRequests'] });
      toast({
        title: 'Success',
        description: 'Maintenance request created successfully',
      });
      setIsMaintenanceFormOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create maintenance request',
        variant: 'destructive',
      });
    },
  });

  // Update maintenance request mutation
  const updateMaintenanceRequestMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: MaintenanceRequestUpdateData }) =>
      updateMaintenanceRequest(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['maintenanceRequests'] });
      toast({
        title: 'Success',
        description: 'Maintenance request updated successfully',
      });
      setIsMaintenanceFormOpen(false);
      setEditingRequest(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update maintenance request',
        variant: 'destructive',
      });
    },
  });

  // Handle opening the property details modal
  const handleViewPropertyDetails = (property: Property) => {
    try {
      console.log("Opening property details for:", property);
      // Make a deep copy to avoid reference issues
      const propertyCopy = JSON.parse(JSON.stringify(property));
      setSelectedProperty(propertyCopy);
      setSelectedMaintenanceRequest(null);
      setIsModalOpen(true);
    } catch (error) {
      console.error("Error opening property details:", error);
      toast({
        title: "Error",
        description: "Could not open property details. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle opening the maintenance request details modal
  const handleViewMaintenanceDetails = (request: MaintenanceRequest) => {
    try {
      console.log("Opening maintenance details for:", request);
      // Make a deep copy to avoid reference issues
      const requestCopy = JSON.parse(JSON.stringify(request));
      setSelectedMaintenanceRequest(requestCopy);
      setSelectedProperty(null);
      setIsModalOpen(true);
    } catch (error) {
      console.error("Error opening maintenance details:", error);
      toast({
        title: "Error",
        description: "Could not open maintenance details. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle closing the modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedProperty(null);
    setSelectedMaintenanceRequest(null);
  };

  // Handle opening the property form
  const handleAddProperty = () => {
    setEditingProperty(null);
    setIsPropertyFormOpen(true);
  };

  // Handle editing a property
  const handleEditProperty = (property: Property) => {
    try {
      console.log("Opening property edit form for:", property);

      // Create a safe copy of the property with all required fields
      const safeProperty = {
        _id: property._id,
        address: property.address || '',
        type: property.type || 'Single Family',
        status: property.status || 'vacant',
        yearBuilt: property.yearBuilt || '',
        squareFeet: property.squareFeet,
        bedrooms: property.bedrooms,
        bathrooms: property.bathrooms,
        amenities: property.amenities || [],
        nextInspection: property.nextInspection || '',
        communityId: property.communityId || 'none',
        hoaId: property.hoaId || '',
        resident: property.resident
      };

      console.log("Safe property object for editing:", safeProperty);
      setEditingProperty(safeProperty);
      setIsPropertyFormOpen(true);
    } catch (error) {
      console.error("Error preparing property for editing:", error);
      toast({
        title: "Error",
        description: "Could not open property edit form. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle deleting a property
  const handleDeleteProperty = (property: Property) => {
    setPropertyToDelete(property);
    setIsDeleteDialogOpen(true);
  };

  // Handle confirming property deletion
  const handleConfirmDeleteProperty = (forceDelete: boolean = false) => {
    if (propertyToDelete) {
      console.log(`Deleting property ${propertyToDelete._id} with forceDelete=${forceDelete}`);
      deletePropertyMutation.mutate({
        id: propertyToDelete._id,
        forceDelete
      });
    }
  };

  // Handle archiving a property instead of deleting it
  const handleArchiveProperty = () => {
    if (propertyToDelete) {
      updatePropertyMutation.mutate({
        id: propertyToDelete._id,
        data: { status: 'archived' }
      });
      setIsDeleteDialogOpen(false);
    }
  };

  // Handle property form submission
  const handlePropertyFormSubmit = (data: PropertyCreateData | PropertyUpdateData) => {
    // Get the user's HOA ID if available
    const userData = JSON.parse(localStorage.getItem('user') || '{}');

    // Determine the HOA ID based on user role and available data
    let userHoaId = userData.hoaId;

    // For admins, ensure we have an HOA ID
    if (isAdmin && !userHoaId) {
      // Try to get HOA ID from other sources
      userHoaId = userData.hoaId || userData.hoaCommunityId;

      // If still no HOA ID, check if we have an HOA in the system
      if (!userHoaId) {
        toast({
          title: "HOA ID Required",
          description: "Please contact support to associate your account with an HOA.",
          variant: "destructive"
        });
        return;
      }
    }

    // Add HOA ID to the data
    const formData = {
      ...data,
      hoaId: userHoaId
    };

    // For company admins without an HOA ID, we'll let the backend handle it
    // The backend will use the first available HOA or create a default one

    if (editingProperty) {
      updatePropertyMutation.mutate({
        id: editingProperty._id,
        data: formData
      });
    } else {
      createPropertyMutation.mutate(formData as PropertyCreateData);
    }

    console.log('Submitting property data:', formData);
  };

  // Handle opening the maintenance request form
  const handleAddMaintenanceRequest = () => {
    setEditingRequest(null);
    setIsMaintenanceFormOpen(true);
  };

  // Handle editing a maintenance request
  const handleEditMaintenanceRequest = (request: MaintenanceRequest) => {
    setEditingRequest(request);
    setIsMaintenanceFormOpen(true);
  };

  // Handle maintenance request form submission
  const handleMaintenanceFormSubmit = (data: MaintenanceRequestCreateData | MaintenanceRequestUpdateData) => {
    // Get the user's HOA ID if available
    const userData = JSON.parse(localStorage.getItem('user') || '{}');

    // Determine the HOA ID based on user role and available data
    let userHoaId = userData.hoaId;

    // For admins, ensure we have an HOA ID
    if (isAdmin && !userHoaId) {
      // Try to get HOA ID from other sources
      userHoaId = userData.hoaId || userData.hoaCommunityId;

      // If still no HOA ID, check if we have an HOA in the system
      if (!userHoaId) {
        toast({
          title: "HOA ID Required",
          description: "Please contact support to associate your account with an HOA.",
          variant: "destructive"
        });
        return;
      }
    }

    // Add HOA ID and resident ID to the data
    const formData = {
      ...data,
      hoaId: userHoaId,
      resident: userData._id || undefined
    };

    // For company admins without an HOA ID, we'll let the backend handle it
    // The backend will use the first available HOA or create a default one

    if (editingRequest) {
      updateMaintenanceRequestMutation.mutate({
        id: editingRequest._id,
        data: formData
      });
    } else {
      createMaintenanceRequestMutation.mutate(formData as MaintenanceRequestCreateData);
    }

    console.log('Submitting maintenance request data:', formData);
  };

  // Filter properties based on search term
  const filteredProperties = properties.filter(property =>
    property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
    property.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (property.resident?.fullName && property.resident._id.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Filter maintenance requests based on search term
  const filteredRequests = maintenanceRequests.filter(request =>
    request.property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.resident.fullName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'occupied':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Occupied</Badge>;
      case 'vacant':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Vacant</Badge>;
      case 'maintenance':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Maintenance</Badge>;
      case 'archived':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Archived</Badge>;
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>;
      case 'in_progress':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">In Progress</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Completed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">High</Badge>;
      case 'medium':
        return <Badge variant="secondary">Medium</Badge>;
      case 'low':
        return <Badge variant="outline">Low</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with community selection status */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">Property Management</h2>
          <p className="text-muted-foreground">Manage properties and maintenance requests</p>
          {/* Show community selection status */}
          {selectedCommunityName && (
            <p className="text-sm text-muted-foreground mt-1">
              Viewing: {selectedCommunityName}
            </p>
          )}
          {isMultipleCommunities && selectedCommunityIds.length > 0 && (
            <p className="text-xs text-blue-600 mt-1">
              {selectedCommunityIds.length === availableCommunitiesInDropdown.length
                ? `Showing properties from all ${selectedCommunityIds.length} accessible communities`
                : `Properties from ${selectedCommunityIds.length} selected communities`
              }
            </p>
          )}
        </div>
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <Tabs defaultValue="properties">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="properties">Properties</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance Requests</TabsTrigger>
        </TabsList>

        <TabsContent value="properties" className="space-y-4 mt-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-medium">Property List</h3>
              <p className="text-sm text-muted-foreground">
                {filteredProperties.length} properties found
              </p>
            </div>
            {canManageProperties && (
              <Button onClick={handleAddProperty}>
                <Plus className="h-4 w-4 mr-2" />
                Add Property
              </Button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {isLoadingProperties ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, index) => (
                <Card key={`skeleton-${index}`} className="animate-pulse">
                  <CardHeader className="pb-2">
                    <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-full"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <div className="h-8 bg-gray-200 rounded w-full"></div>
                  </CardFooter>
                </Card>
              ))
            ) : filteredProperties.length === 0 ? (
              <div className="col-span-3 text-center py-8">
                <p className="text-muted-foreground">
                  {canManageProperties
                    ? "No properties found. Add a property to get started."
                    : "No properties found for your community."}
                </p>
              </div>
            ) : (
              filteredProperties.map(property => (
                <Card key={property._id} className="overflow-hidden">
                  {/* Property Photo Preview */}
                  <div className="relative w-full h-40 bg-gray-100">
                    {property.documents && property.documents.some(doc =>
                      doc.fileType?.startsWith('image/') ||
                      doc.name?.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/i)
                    ) ? (
                      <img
                        src={getProfilePhotoUrl(property.documents.find(doc =>
                          doc.fileType?.startsWith('image/') ||
                          doc.name?.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/i)
                        )?.fileUrl || '') || ''}
                        alt={property.address}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = 'https://placehold.co/400x200?text=No+Image';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gray-100">
                        <Home className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                    <div className="absolute top-2 right-2">
                      {getStatusBadge(property.status)}
                    </div>
                  </div>

                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-base">{property.address}</CardTitle>
                        <CardDescription>{property.type}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="space-y-2">
                      {property.resident && (
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            Resident: {property.resident.fullName}
                          </span>
                        </div>
                      )}
                      {property.nextInspection && (
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">Next Inspection: {new Date(property.nextInspection).toLocaleDateString()}</span>
                        </div>
                      )}
                      {/* Photo count */}
                      {property.documents && property.documents.filter(doc =>
                        doc.fileType?.startsWith('image/') ||
                        doc.name?.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/i)
                      ).length > 0 && (
                        <div className="flex items-center gap-2">
                          <Image className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {property.documents.filter(doc =>
                              doc.fileType?.startsWith('image/') ||
                              doc.name?.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/i)
                            ).length} photos
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewPropertyDetails(property)}
                    >
                      View Details
                    </Button>
                    {canManageProperties && (
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditProperty(property)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-destructive hover:text-destructive hover:bg-destructive/10"
                          onClick={() => handleDeleteProperty(property)}
                        >
                          Delete
                        </Button>
                      </div>
                    )}
                  </CardFooter>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-4 mt-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-medium">Maintenance Requests</h3>
              <p className="text-sm text-muted-foreground">
                {filteredRequests.length} requests found
              </p>
            </div>
            <Button onClick={handleAddMaintenanceRequest}>
              <Plus className="h-4 w-4 mr-2" />
              New Request
            </Button>
          </div>

          <div className="space-y-4">
            {isLoadingMaintenance ? (
              // Loading skeleton
              Array.from({ length: 2 }).map((_, index) => (
                <Card key={`skeleton-${index}`} className="animate-pulse">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div className="h-5 bg-gray-200 rounded w-1/2"></div>
                      <div className="flex gap-2">
                        <div className="h-5 bg-gray-200 rounded w-16"></div>
                        <div className="h-5 bg-gray-200 rounded w-16"></div>
                      </div>
                    </div>
                    <div className="h-4 bg-gray-200 rounded w-1/3 mt-2"></div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <div className="h-8 bg-gray-200 rounded w-24"></div>
                    <div className="h-8 bg-gray-200 rounded w-24"></div>
                  </CardFooter>
                </Card>
              ))
            ) : filteredRequests.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No maintenance requests found. Create a new request to get started.</p>
              </div>
            ) : (
              filteredRequests.map(request => (
                <Card key={request._id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-base">{request.property.address}</CardTitle>
                      <div className="flex gap-2">
                        {getPriorityBadge(request.priority)}
                        {getStatusBadge(request.status)}
                      </div>
                    </div>
                    <CardDescription>
                      Submitted on {new Date(request.dateSubmitted).toLocaleDateString()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <p className="text-sm">{request.description}</p>
                    <div className="mt-2 flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Reported by: {request.resident.fullName}
                      </span>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewMaintenanceDetails(request)}
                    >
                      View Details
                    </Button>
                    <div className="flex gap-2">
                      {canManageProperties && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditMaintenanceRequest(request)}
                        >
                          Edit
                        </Button>
                      )}
                      {canManageProperties && request.status !== 'completed' && (
                        <Button
                          size="sm"
                          onClick={() => updateMaintenanceRequestMutation.mutate({
                            id: request._id,
                            data: {
                              status: request.status === 'pending' ? 'in_progress' : 'completed'
                            }
                          })}
                        >
                          {request.status === 'pending' ? 'Start Work' : 'Mark Complete'}
                        </Button>
                      )}
                    </div>
                  </CardFooter>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Property Details Modal */}
      <PropertyDetailsModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        property={selectedProperty}
        maintenanceRequest={selectedMaintenanceRequest}
      />

      {/* Property Form */}
      <ErrorBoundary
        FallbackComponent={({ error, resetErrorBoundary }) => (
          <Dialog open={isPropertyFormOpen} onOpenChange={() => {
            setIsPropertyFormOpen(false);
            resetErrorBoundary();
          }}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Error</DialogTitle>
              </DialogHeader>
              <div className="p-4 border border-red-200 rounded-md bg-red-50">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  <p className="text-red-800 font-medium">Failed to load property form</p>
                </div>
                <p className="text-sm text-red-600 mb-4">{error.message}</p>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsPropertyFormOpen(false);
                  resetErrorBoundary();
                }}>
                  Close
                </Button>
                <Button onClick={() => {
                  resetErrorBoundary();
                  // Try again with a clean state
                  setEditingProperty(null);
                  setTimeout(() => setIsPropertyFormOpen(true), 100);
                }}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
        onReset={() => {
          console.log("PropertyForm error boundary reset");
        }}
      >
        <PropertyForm
          isOpen={isPropertyFormOpen}
          onClose={() => setIsPropertyFormOpen(false)}
          onSubmit={handlePropertyFormSubmit}
          initialData={editingProperty || undefined}
          isLoading={createPropertyMutation.isPending || updatePropertyMutation.isPending}
        />
      </ErrorBoundary>

      {/* Maintenance Request Form */}
      <MaintenanceRequestForm
        isOpen={isMaintenanceFormOpen}
        onClose={() => setIsMaintenanceFormOpen(false)}
        onSubmit={handleMaintenanceFormSubmit}
        initialData={editingRequest || undefined}
        isLoading={createMaintenanceRequestMutation.isPending || updateMaintenanceRequestMutation.isPending}
        isEditing={!!editingRequest}
      />

      {/* Delete Property Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Property</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this property? This action cannot be undone.
              <br /><br />
              Properties with maintenance records cannot be deleted and must be archived instead.
              This helps maintain a complete history of all property-related activities.
              {isCompanyAdmin && (
                <>
                  <br /><br />
                  <span className="text-red-600 font-medium">Company Admin Option:</span> You can use Force Delete to remove this property and all its records permanently.
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {propertyToDelete && (
              <div className="p-3 border rounded-md bg-muted">
                <p className="font-medium">{propertyToDelete.address}</p>
                <p className="text-sm text-muted-foreground mt-1">{propertyToDelete.type}</p>
                {propertyToDelete.status && (
                  <div className="mt-2">
                    <span className="text-sm font-medium">Status: </span>
                    {getStatusBadge(propertyToDelete.status)}
                  </div>
                )}
              </div>
            )}
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={deletePropertyMutation.isPending}
              className="sm:order-1"
            >
              Cancel
            </Button>
            <Button
              variant="secondary"
              onClick={handleArchiveProperty}
              disabled={deletePropertyMutation.isPending}
              className="sm:order-2"
            >
              Archive Instead
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleConfirmDeleteProperty(false)}
              disabled={deletePropertyMutation.isPending}
              className="sm:order-3"
            >
              {deletePropertyMutation.isPending ? 'Deleting...' : 'Delete Property'}
            </Button>

            {/* Force Delete button - only visible to company admins */}
            {isCompanyAdmin && (
              <Button
                variant="destructive"
                onClick={() => handleConfirmDeleteProperty(true)}
                disabled={deletePropertyMutation.isPending}
                className="sm:order-4 bg-red-800 hover:bg-red-900"
                title="Force delete will remove the property even if it has maintenance records"
              >
                {deletePropertyMutation.isPending ? 'Deleting...' : 'Force Delete'}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PropertyManagement;

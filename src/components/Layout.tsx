import React from 'react';
import Sidebar from './Sidebar';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  return (
    <div className="flex min-h-screen bg-background">
      {/* Sidebar container - fixed width, no flex grow */}
      <div className="shrink-0">
        <Sidebar />
      </div>
      {/* Main content area - takes remaining space and scrolls */}
      <main className="flex-1 overflow-y-auto safe-area-inset">
        <div className="mobile-container responsive-spacing-sm w-full max-w-[1920px] mx-auto">
          <div className="animate-in pt-16 sm:pt-12 md:pt-4 lg:pt-0">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
};

export default Layout;

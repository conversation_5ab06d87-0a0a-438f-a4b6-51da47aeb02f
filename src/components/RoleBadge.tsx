import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  User, 
  Clock, 
  Building, 
  UserCog,
  AlertTriangle
} from 'lucide-react';

type RoleType = 'admin' | 'resident' | 'pending' | 'property_manager' | 'company_admin' | 'denied';

interface RoleBadgeProps {
  role: RoleType;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showLabel?: boolean;
}

const RoleBadge: React.FC<RoleBadgeProps> = ({ 
  role, 
  size = 'md', 
  showIcon = true,
  showLabel = true
}) => {
  const getVariant = () => {
    switch (role) {
      case 'admin':
        return 'default';
      case 'company_admin':
        return 'destructive';
      case 'property_manager':
        return 'secondary';
      case 'resident':
        return 'outline';
      case 'pending':
        return 'secondary';
      case 'denied':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getIcon = () => {
    switch (role) {
      case 'admin':
        return <Shield className="h-3 w-3 mr-1" />;
      case 'company_admin':
        return <Building className="h-3 w-3 mr-1" />;
      case 'property_manager':
        return <UserCog className="h-3 w-3 mr-1" />;
      case 'resident':
        return <User className="h-3 w-3 mr-1" />;
      case 'pending':
        return <Clock className="h-3 w-3 mr-1" />;
      case 'denied':
        return <AlertTriangle className="h-3 w-3 mr-1" />;
      default:
        return <User className="h-3 w-3 mr-1" />;
    }
  };

  const getLabel = () => {
    switch (role) {
      case 'admin':
        return 'HOA Admin';
      case 'company_admin':
        return 'Company Admin';
      case 'property_manager':
        return 'Property Manager';
      case 'resident':
        return 'Resident';
      case 'pending':
        return 'Pending Approval';
      case 'denied':
        return 'Access Denied';
      default:
        return 'User';
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'text-xs py-0 px-1.5';
      case 'lg':
        return 'text-sm py-1 px-3';
      default:
        return 'text-xs py-0.5 px-2';
    }
  };

  return (
    <Badge variant={getVariant()} className={`${getSizeClass()} flex items-center`}>
      {showIcon && getIcon()}
      {showLabel && <span>{getLabel()}</span>}
    </Badge>
  );
};

export default RoleBadge;

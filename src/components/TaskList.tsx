import React, { useState } from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import api from '@/lib/axios';
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  ThumbsUp,
  ThumbsDown,
  Calendar,
  MessageSquare,
  Trash,
  ChevronDown,
  ChevronUp,
  Archive,
  Send,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { Card } from '@/components/ui/card';
import { format } from 'date-fns';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Input } from "@/components/ui/input";
import { AxiosError } from 'axios';
import { useNavigate } from 'react-router-dom';

interface Task {
  _id: string;
  title: string;
  description: string;
  priority: 'High' | 'Medium' | 'Low';
  status: 'Not Started' | 'In Progress' | 'Completed' | 'Closed';
  dueDate?: string;
  budget?: number;
  createdBy: {
    userId: {
      _id: string;
      username: string;
      email: string;
    };
    timestamp: string;
  };
  votes: {
    up: number;
    down: number;
    voters: Array<{
      userId: string | { _id: string; username?: string; email?: string; };
      vote: 'up' | 'down';
    }>;
  };
  comments: Array<{
    _id: string;
    user: string;
    username: string;
    message: string;
    date: string;
  }>;
  progress: number;
  permissions: {
    canEdit: string[];
    canDelete: string[];
  };
  isArchived: boolean;
  closedAt?: string;
  closedBy?: {
    userId: {
      _id: string;
      username: string;
      email: string;
    };
    timestamp: string;
  };
}

interface TasksResponse {
  tasks: Task[];
  closed: Task[];
  activeTasks: number;
}

interface ErrorResponse {
  message: string;
  error?: string;
  details?: string;
}

interface TaskListProps {
  limit?: number;
  communityId?: string | null;
  communityIds?: string[]; // Support for multiple communities
}

const TaskList = ({ limit, communityId, communityIds }: TaskListProps) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const [isClosedTasksOpen, setIsClosedTasksOpen] = useState(false);
  const [commentText, setCommentText] = useState<{ [key: string]: string }>({});
  const [expandedComments, setExpandedComments] = useState<{ [key: string]: boolean }>({});
  const navigate = useNavigate();

  // Get user role and access info
  const isCompanyAdmin = user?.role === 'company_admin';
  const isAdmin = user?.role === 'admin';
  const isMember = user?.role === 'member';
  const userCommunityId = user?.communityId;

  const { data: tasksData, isLoading, isError } = useQuery<TasksResponse>({
    queryKey: ['tasks', communityId, communityIds, user?.role, userCommunityId],
    queryFn: async () => {
      console.log('Fetching tasks with community filtering...', {
        communityId,
        communityIds,
        userRole: user?.role,
        userCommunityId
      });

      try {
        // Build endpoint with filters - Only use communityId for filtering
        let endpoint = '/api/tasks';
        const params = [];

        // Role-based filtering using only communityId
        if (isCompanyAdmin) {
          // Company admin can see all, but apply community filters if specified
          if (communityIds && communityIds.length > 0) {
            // Multiple communities selected
            communityIds.forEach(id => params.push(`communityId=${id}`));
            console.log('Company admin: Fetching tasks for multiple communities:', communityIds);
          } else if (communityId) {
            // Single community selected
            params.push(`communityId=${communityId}`);
            console.log('Company admin: Fetching tasks for single community:', communityId);
          }
          // If no filters, company admin sees all tasks (this is intentional)
        } else if (isAdmin) {
          // Admin can see tasks from communities they have access to
          if (communityIds && communityIds.length > 0) {
            // Filter multiple communities
            communityIds.forEach(id => params.push(`communityId=${id}`));
            console.log('Admin: Fetching tasks for multiple communities:', communityIds);
          } else if (communityId) {
            params.push(`communityId=${communityId}`);
            console.log('Admin: Fetching tasks for single community:', communityId);
          } else if (userCommunityId) {
            // Default to their community if no specific selection
            params.push(`communityId=${userCommunityId}`);
            console.log('Admin: Fetching tasks for their default community:', userCommunityId);
          }
        } else if (isMember) {
          // Members can only see tasks from their community(ies)
          if (communityIds && communityIds.length > 0) {
            // Filter to only communities they have access to
            communityIds.forEach(id => params.push(`communityId=${id}`));
            console.log('Member: Fetching tasks for their multiple communities:', communityIds);
          } else if (communityId) {
            params.push(`communityId=${communityId}`);
            console.log('Member: Fetching tasks for their community:', communityId);
          } else if (userCommunityId) {
            // Default to their community if no specific selection
            params.push(`communityId=${userCommunityId}`);
            console.log('Member: Fetching tasks for their default community:', userCommunityId);
          }
        }

        // IMPORTANT: Always ensure we have some filtering for non-company-admin users
        if (!isCompanyAdmin && params.length === 0) {
          console.warn('No filtering applied for non-company-admin user, applying default community filter');
          // Apply default user-based filtering with communityId only
          if (userCommunityId) {
            params.push(`communityId=${userCommunityId}`);
            console.log('Applied default community filter for security:', userCommunityId);
          }
        }

        if (params.length > 0) {
          endpoint += `?${params.join('&')}`;
        }

        console.log('Final tasks endpoint with community filtering only:', endpoint);
        const response = await api.get(endpoint);
        console.log('Tasks response:', response.data);

        if (!response.data || !response.data.tasks) {
          console.error('Invalid tasks data structure:', response.data);
          return { tasks: [], closed: [], activeTasks: 0 };
        }

        // Client-side filtering as additional security layer
        let filteredTasks = response.data.tasks;
        let filteredClosed = response.data.closed;

        // Apply client-side filtering based on user access
        if (!isCompanyAdmin) {
          const filterTasksByAccess = (tasks: any[]) => {
            return tasks.filter((task: any) => {
              // For admins: trust backend filtering since we're only using communityId
              if (isAdmin) {
                return true; // Backend should handle proper filtering
              }
              // For members: only show tasks from their community
              if (isMember && userCommunityId) {
                return task.communityId === userCommunityId;
              }
              // If we can't determine access, exclude the task for security
              return false;
            });
          };

          const originalTaskCount = filteredTasks.length;
          filteredTasks = filterTasksByAccess(filteredTasks);
          filteredClosed = filterTasksByAccess(filteredClosed);

          if (originalTaskCount !== filteredTasks.length) {
            console.warn(`Client-side filtering removed ${originalTaskCount - filteredTasks.length} unauthorized tasks`);
          }
        }

        // If limit is provided, slice the filtered arrays
        const limitedTasks = limit ? filteredTasks.slice(0, limit) : filteredTasks;
        const limitedClosed = limit ? filteredClosed.slice(0, limit) : filteredClosed;

        console.log('Final filtered tasks:', {
          totalTasks: limitedTasks.length,
          totalClosed: limitedClosed.length,
          userRole: user?.role,
          appliedFiltering: !isCompanyAdmin
        });

        return {
          tasks: limitedTasks,
          closed: limitedClosed,
          activeTasks: limitedTasks.length
        };
      } catch (error) {
        console.error('Error fetching tasks:', error);
        return { tasks: [], closed: [], activeTasks: 0 };
      }
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async (taskId: string) => {
      await api.delete(`/api/tasks/${taskId}`);
    },
    onMutate: async (taskId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['tasks', communityId, communityIds] });

      // Snapshot the previous value
      const previousTasks = queryClient.getQueryData<TasksResponse>(['tasks', communityId, communityIds]);

      // Optimistically update to the new value
      queryClient.setQueryData<TasksResponse>(['tasks'], (old) => {
        if (!old) return old;
        return {
          ...old,
          tasks: old.tasks.filter(t => t._id !== taskId),
          closed: old.closed.filter(t => t._id !== taskId)
        };
      });

      return { previousTasks };
    },
    onError: (error: any, taskId, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousTasks) {
        queryClient.setQueryData(['tasks'], context.previousTasks);
      }
      console.error('Delete failed:', error);
      toast({
        title: 'Failed to delete task',
        description: error.response?.data?.message || 'You do not have permission to delete this task',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'Task deleted successfully',
        description: 'The task has been removed from the list.',
      });
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({ queryKey: ['tasks', communityId, communityIds] });
    },
  });

  const closeTask = useMutation({
    mutationFn: async (taskId: string) => {
      console.log('Attempting to close task:', taskId);
      try {
        const response = await api.post(`/api/tasks/${taskId}/close`);
        console.log('Close task response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Close task error:', error.response?.data || error);
        throw error;
      }
    },
    onMutate: async (taskId) => {
      await queryClient.cancelQueries({ queryKey: ['tasks'] });
      const previousTasks = queryClient.getQueryData<TasksResponse>(['tasks']);

      queryClient.setQueryData<TasksResponse>(['tasks'], (old) => {
        if (!old) return old;

        const updatedTask = old.tasks.find(t => t._id === taskId);
        if (!updatedTask) return old;

        const closedTask = {
          ...updatedTask,
          status: 'Closed' as const,
          isArchived: true,
          closedAt: new Date().toISOString(),
          closedBy: {
            userId: {
              _id: user._id,
              username: user.username,
              email: user.email
            },
            timestamp: new Date().toISOString()
          }
        };

        return {
          ...old,
          tasks: old.tasks.filter(t => t._id !== taskId),
          closed: [...old.closed, closedTask],
          activeTasks: old.activeTasks - 1
        };
      });

      return { previousTasks };
    },
    onError: (error: any, variables, context) => {
      console.error('Close task error:', error);
      if (context?.previousTasks) {
        queryClient.setQueryData(['tasks'], context.previousTasks);
      }
      toast({
        title: 'Failed to close task',
        description: error.response?.data?.message || 'An error occurred while closing the task.',
        variant: 'destructive',
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      toast({
        title: 'Task closed successfully',
        description: 'The task has been closed and archived.',
      });
    },
  });

  const voteMutation = useMutation({
    mutationFn: async ({ taskId, direction }: { taskId: string; direction: 'up' | 'down' }) => {
      console.log('Voting on task:', { taskId, direction });
      const response = await api.patch(`/api/tasks/${taskId}/vote`, { direction });
      return response.data;
    },
    onMutate: async ({ taskId, direction }) => {
      await queryClient.cancelQueries({ queryKey: ['tasks'] });
      const previousTasks = queryClient.getQueryData<TasksResponse>(['tasks']);

      queryClient.setQueryData<TasksResponse>(['tasks'], (old) => {
        if (!old) return old;

        const updateVotes = (task: Task) => {
          if (task._id !== taskId) return task;

          const newTask = { ...task };
          if (!newTask.votes) {
            newTask.votes = { up: 0, down: 0, voters: [] };
          }

          const voterIndex = newTask.votes.voters.findIndex(
            voter => {
              const voterId = typeof voter.userId === 'string' ? voter.userId : voter.userId._id;
              return voterId === user._id;
            }
          );

          const currentVote = voterIndex !== -1 ? newTask.votes.voters[voterIndex].vote : null;

          // Remove current vote if it exists
          if (voterIndex !== -1) {
            newTask.votes[currentVote]--;
            newTask.votes.voters.splice(voterIndex, 1);
          }

          // Add new vote if clicking a different direction or no previous vote
          if (currentVote !== direction) {
            newTask.votes[direction]++;
            newTask.votes.voters.push({
              userId: user._id,
              vote: direction
            });
          }

          // Ensure vote counts don't go below 0
          newTask.votes.up = Math.max(0, newTask.votes.up);
          newTask.votes.down = Math.max(0, newTask.votes.down);

          return newTask;
        };

        return {
          ...old,
          tasks: old.tasks.map(updateVotes),
          closed: old.closed.map(updateVotes)
        };
      });

      return { previousTasks };
    },
    onError: (err, variables, context) => {
      if (context?.previousTasks) {
        queryClient.setQueryData(['tasks'], context.previousTasks);
      }
      toast({
        title: 'Failed to vote',
        description: 'An error occurred while voting on the task. Please try again.',
        variant: 'destructive',
      });
    },
    onSuccess: (data, variables) => {
      queryClient.setQueryData<TasksResponse>(['tasks'], (old) => {
        if (!old) return old;

        const updateTask = (task: Task) => {
          if (task._id === variables.taskId) {
            return data;
          }
          return task;
        };

        return {
          ...old,
          tasks: old.tasks.map(updateTask),
          closed: old.closed.map(updateTask)
        };
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });

  const commentMutation = useMutation({
    mutationFn: async ({ taskId, message }: { taskId: string; message: string }) => {
      console.log('Sending comment request:', {
        taskId,
        message,
        user: user._id,
        headers: {
          'Authorization': `Bearer ${user.token}`
        }
      });

      try {
        const response = await api.post(`/api/tasks/${taskId}/comments`, {
          message: message.trim()
        });
        console.log('Comment response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Comment request error:', error.response?.data || error.message);
        throw error;
      }
    },
    onMutate: async ({ taskId, message }) => {
      await queryClient.cancelQueries({ queryKey: ['tasks'] });
      const previousTasks = queryClient.getQueryData<TasksResponse>(['tasks']);

      const tempId = `temp-${Date.now()}`;

      queryClient.setQueryData<TasksResponse>(['tasks'], (old) => {
        if (!old) return old;

        const updateTask = (task: Task) => {
          if (task._id !== taskId) return task;

          return {
            ...task,
            comments: [
              ...(task.comments || []),
              {
                _id: tempId,
                user: user._id,
                username: user.username,
                message: message.trim(),
                date: new Date().toISOString()
              }
            ]
          };
        };

        return {
          ...old,
          tasks: old.tasks.map(updateTask),
          closed: old.closed.map(updateTask)
        };
      });

      return { previousTasks };
    },
    onError: (err: AxiosError<ErrorResponse>, variables, context) => {
      console.error('Comment mutation error:', {
        error: err,
        response: err.response?.data,
        status: err.response?.status
      });

      if (context?.previousTasks) {
        queryClient.setQueryData(['tasks'], context.previousTasks);
      }

      let errorMessage = 'An error occurred while posting your comment.';
      if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.response?.status === 401) {
        errorMessage = 'Please log in again to post comments.';
      } else if (err.response?.status === 404) {
        errorMessage = 'The task was not found.';
      }

      toast({
        title: 'Failed to add comment',
        description: errorMessage,
        variant: 'destructive',
      });
    },
    onSuccess: (data, variables) => {
      console.log('Comment added successfully:', data);

      queryClient.setQueryData<TasksResponse>(['tasks'], (old) => {
        if (!old) return old;

        const updateTask = (task: Task) => {
          if (task._id === variables.taskId) {
            return data;
          }
          return task;
        };

        return {
          ...old,
          tasks: old.tasks.map(updateTask),
          closed: old.closed.map(updateTask)
        };
      });

      toast({
        title: 'Comment added',
        description: 'Your comment has been posted.',
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });

  const deleteCommentMutation = useMutation({
    mutationFn: async ({ taskId, commentId }: { taskId: string; commentId: string }) => {
      console.log('Deleting comment:', { taskId, commentId });
      const response = await api.delete(`/api/tasks/${taskId}/comments/${commentId}`);
      return response.data;
    },
    onMutate: async ({ taskId, commentId }) => {
      await queryClient.cancelQueries({ queryKey: ['tasks'] });
      const previousTasks = queryClient.getQueryData<TasksResponse>(['tasks']);

      queryClient.setQueryData<TasksResponse>(['tasks'], (old) => {
        if (!old) return old;

        const updateTask = (task: Task) => {
          if (task._id !== taskId) return task;
          return {
            ...task,
            comments: task.comments.filter(comment => comment._id !== commentId)
          };
        };

        return {
          ...old,
          tasks: old.tasks.map(updateTask),
          closed: old.closed.map(updateTask)
        };
      });

      return { previousTasks };
    },
    onError: (err: AxiosError<ErrorResponse>, variables, context) => {
      console.error('Delete comment error:', err);
      if (context?.previousTasks) {
        queryClient.setQueryData(['tasks'], context.previousTasks);
      }
      toast({
        title: 'Failed to delete comment',
        description: err.response?.data?.message || 'An error occurred while deleting the comment.',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'Comment deleted',
        description: 'The comment has been removed.',
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });

  const handleVote = (taskId: string, direction: 'up' | 'down') => {
    if (!user._id) {
      toast({
        title: 'Login required',
        description: 'Please log in to vote on tasks.',
        variant: 'destructive',
      });
      return;
    }
    voteMutation.mutate({ taskId, direction });
  };

  const handleDelete = async (taskId: string) => {
    try {
      // For admin users, bypass permission check
      if (user.role === 'admin') {
        await deleteMutation.mutateAsync(taskId);
        return;
      }

      // For non-admin users, check permissions
      const task = tasksData?.tasks.find(t => t._id === taskId) ||
                  tasksData?.closed.find(t => t._id === taskId);

      if (task?.permissions?.canDelete?.includes(user._id) ||
          task?.createdBy?.userId._id === user._id) {
        await deleteMutation.mutateAsync(taskId);
      } else {
        toast({
          title: 'Permission denied',
          description: 'You do not have permission to delete this task',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error deleting task:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete task. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleClose = (taskId: string) => {
    if (!user._id) {
      toast({
        title: 'Login required',
        description: 'Please log in to close tasks.',
        variant: 'destructive',
      });
      return;
    }

    if (user.role !== 'admin') {
      toast({
        title: 'Permission denied',
        description: 'Only administrators can close tasks.',
        variant: 'destructive',
      });
      return;
    }

    closeTask.mutate(taskId);
  };

  const handleComment = (taskId: string) => {
    if (!user._id) {
      toast({
        title: 'Login required',
        description: 'Please log in to comment on tasks.',
        variant: 'destructive',
      });
      return;
    }

    const message = commentText[taskId]?.trim();
    if (!message) return;

    commentMutation.mutate({ taskId, message });
    setCommentText({ ...commentText, [taskId]: '' });
  };

  const toggleComments = (taskId: string) => {
    setExpandedComments(prev => ({
      ...prev,
      [taskId]: !prev[taskId]
    }));
  };

  const canDeleteTask = (task: Task) => {
    return (
      user.role === 'admin' ||
      task.createdBy?.userId._id === user._id ||
      task.permissions?.canDelete?.includes(user._id)
    );
  };

  const canCloseTask = (task: Task) => {
    if (!user) return false;
    // Only admins can close tasks
    return user.role === 'admin';
  };

  const getUserVote = (task: Task) => {
    if (!user._id || !task.votes?.voters) return null;

    const userVote = task.votes.voters.find(
      vote => {
        const voteUserId = typeof vote.userId === 'string' ? vote.userId : vote.userId._id;
        return voteUserId === user._id;
      }
    );
    return userVote?.vote || null;
  };

  const handleDeleteComment = (taskId: string, commentId: string) => {
    if (!user._id) {
      toast({
        title: 'Login required',
        description: 'Please log in to delete comments.',
        variant: 'destructive',
      });
      return;
    }
    deleteCommentMutation.mutate({ taskId, commentId });
  };

  const canDeleteComment = (comment: Task['comments'][0]) => {
    return user._id === comment.user || user.role === 'admin';
  };

  const getStatusColor = (status: string) => {
    if (!status) return 'bg-gray-100 text-gray-800';
    switch (status.toLowerCase()) {
      case 'not started':
        return 'bg-gray-100 text-gray-800';
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    if (!priority) return 'bg-gray-100 text-gray-800';
    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderComments = (task: Task) => {
    const isExpanded = expandedComments[task._id];
    const comments = task.comments || [];
    const displayedComments = isExpanded ? comments : comments.slice(0, 2);

    return (
      <div className="mt-4 space-y-3">
        {displayedComments.map((comment) => (
          <div key={comment._id} className="bg-gray-50 rounded-lg p-3 relative group">
            <div className="flex items-center gap-2 mb-1">
              <Avatar className="h-6 w-6">
                <AvatarFallback>
                  {comment.username?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium">{comment.username}</span>
              <span className="text-xs text-gray-500">
                {format(new Date(comment.date), 'PPp')}
              </span>
              {canDeleteComment(comment) && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 absolute right-2 top-2 h-6 w-6 p-0"
                  onClick={() => handleDeleteComment(task._id, comment._id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <p className="text-sm text-gray-600">{comment.message}</p>
          </div>
        ))}

        {comments.length > 2 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleComments(task._id)}
            className="text-sm"
          >
            {isExpanded ? 'Show less' : `Show ${comments.length - 2} more comments`}
          </Button>
        )}

        <div className="flex gap-2 mt-2">
          <Input
            placeholder="Add a comment..."
            value={commentText[task._id] || ''}
            onChange={(e) => setCommentText({
              ...commentText,
              [task._id]: e.target.value
            })}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleComment(task._id);
              }
            }}
            className="flex-1"
          />
          <Button
            size="sm"
            onClick={() => handleComment(task._id)}
            disabled={!commentText[task._id]?.trim()}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };

  const renderDeleteButton = (task: Task) => {
    // Always show delete button for admins
    if (user.role === 'admin') {
      return (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => handleDelete(task._id)}
          className="h-8 w-8 p-0"
        >
          <Trash className="h-4 w-4" />
        </Button>
      );
    }

    // Show delete button for task creator or users with delete permission
    if (task.permissions?.canDelete?.includes(user._id) ||
        task.createdBy?.userId._id === user._id) {
      return (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => handleDelete(task._id)}
          className="h-8 w-8 p-0"
        >
          <Trash className="h-4 w-4" />
        </Button>
      );
    }

    return null;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (isError || !tasksData) {
    return (
      <div className="text-center p-4">
        <p className="text-red-500">Failed to load tasks. Please try again later.</p>
      </div>
    );
  }

  if (!tasksData.tasks.length && !tasksData.closed.length) {
    return (
      <div className="text-center p-4">
        <p className="text-muted-foreground">No tasks found.</p>
        <Button className="mt-2" onClick={() => navigate('/tasks/new')}>
          Create Task
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="space-y-4">
        {tasksData.tasks.map((task) => (
          <Card key={task._id} className="p-4">
            <div className="flex flex-col space-y-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold">{task.title}</h3>
                  <p className="text-gray-600">{task.description}</p>
                  <div className="mt-2 space-y-1">
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="secondary" className={cn(getPriorityColor(task.priority))}>
                        {task.priority}
                      </Badge>
                      <Badge variant="secondary" className={cn(getStatusColor(task.status))}>
                        {task.status}
                      </Badge>
                      {task.dueDate && (
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" /> {format(new Date(task.dueDate), 'PPP')}
                        </Badge>
                      )}
                      {task.budget && (
                        <Badge variant="outline" className="flex items-center gap-1">
                          ${task.budget}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  {task.status !== 'Closed' && canCloseTask(task) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleClose(task._id)}
                      className="ml-2"
                      disabled={isLoading}
                    >
                      <Archive className="h-4 w-4 mr-1" />
                      Close Task
                    </Button>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Button
                    variant={getUserVote(task) === 'up' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleVote(task._id, 'up')}
                    className={cn(
                      "transition-colors duration-200",
                      getUserVote(task) === 'up' && 'bg-green-100 hover:bg-green-200 text-green-700'
                    )}
                  >
                    <ThumbsUp className={cn(
                      "h-4 w-4",
                      getUserVote(task) === 'up' ? 'text-green-600' : 'text-gray-500'
                    )} />
                    <span className="ml-1">{task.votes?.up || 0}</span>
                  </Button>

                  <Button
                    variant={getUserVote(task) === 'down' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleVote(task._id, 'down')}
                    className={cn(
                      "transition-colors duration-200",
                      getUserVote(task) === 'down' && 'bg-red-100 hover:bg-red-200 text-red-700'
                    )}
                  >
                    <ThumbsDown className={cn(
                      "h-4 w-4",
                      getUserVote(task) === 'down' ? 'text-red-600' : 'text-gray-500'
                    )} />
                    <span className="ml-1">{task.votes?.down || 0}</span>
                  </Button>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleComments(task._id)}
                >
                  <MessageSquare className="h-4 w-4 mr-1" />
                  <span>{task.comments?.length || 0}</span>
                </Button>

                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarFallback>
                      {task.createdBy.userId.username?.charAt(0).toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm text-muted-foreground">
                    Created by {task.createdBy.userId.username}
                  </span>
                </div>
              </div>

              {task.progress > 0 && (
                <div>
                  <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                    <span>Progress</span>
                    <span>{task.progress}%</span>
                  </div>
                  <Progress value={task.progress} className="h-1" />
                </div>
              )}
            </div>

            {renderComments(task)}
            {renderDeleteButton(task)}
          </Card>
        ))}
      </div>

      <Collapsible
        open={isClosedTasksOpen}
        onOpenChange={setIsClosedTasksOpen}
        className="w-full space-y-2"
      >
        <div className="flex items-center justify-between py-2">
          <h2 className="text-lg font-semibold">Closed Tasks</h2>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm">
              {isClosedTasksOpen ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="space-y-2">
          {tasksData.closed.map((task) => (
            <Card key={task._id} className="p-4 bg-gray-50">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold">{task.title}</h3>
                  <p className="text-gray-600">{task.description}</p>
                  <div className="mt-2 text-sm text-gray-500">
                    <p>Closed on: {format(new Date(task.closedAt!), 'PPP')}</p>
                    <p>Closed by: {task.closedBy?.userId.username}</p>
                    <p>Priority: {task.priority}</p>
                    {task.budget && <p>Budget: ${task.budget}</p>}
                    <div className="mt-2">
                      <span className="mr-4">
                        <ThumbsUp className={cn(
                          "h-4 w-4 inline mr-1",
                          getUserVote(task) === 'up' && 'text-green-600'
                        )} />
                        {task.votes?.up || 0}
                      </span>
                      <span className="mr-4">
                        <ThumbsDown className={cn(
                          "h-4 w-4 inline mr-1",
                          getUserVote(task) === 'down' && 'text-red-600'
                        )} />
                        {task.votes?.down || 0}
                      </span>
                      <span>
                        <MessageSquare className="h-4 w-4 inline mr-1" />
                        {task.comments?.length || 0}
                      </span>
                    </div>
                  </div>
                </div>
                {renderDeleteButton(task)}
              </div>
            </Card>
          ))}
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

export default TaskList;

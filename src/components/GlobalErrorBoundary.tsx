import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * ErrorBoundaryFallback component to display when an error occurs
 */
const ErrorBoundaryFallback = ({ 
  error, 
  errorInfo, 
  resetErrorBoundary 
}: { 
  error: Error | null; 
  errorInfo: ErrorInfo | null;
  resetErrorBoundary: () => void;
}) => {
  // We can't use useNavigate directly in a class component,
  // so we create a functional component for the fallback UI
  const navigate = useNavigate();

  const goHome = () => {
    navigate('/dashboard');
    resetErrorBoundary();
  };

  return (
    <div className="flex items-center justify-center min-h-screen p-4 bg-gray-50">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="bg-red-50 border-b border-red-100">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-6 w-6 text-red-500" />
            <CardTitle className="text-red-700">Something went wrong</CardTitle>
          </div>
          <CardDescription>
            The application encountered an unexpected error
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="p-3 bg-gray-100 rounded-md overflow-auto max-h-[200px]">
              <p className="font-medium text-red-600">{error?.message || 'Unknown error'}</p>
              {process.env.NODE_ENV === 'development' && (
                <pre className="text-xs mt-2 text-gray-700 whitespace-pre-wrap">
                  {errorInfo?.componentStack || 'No component stack available'}
                </pre>
              )}
            </div>
            <p className="text-sm text-gray-600">
              Try refreshing the page or returning to the dashboard. If the problem persists, please contact support.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between gap-4 border-t pt-4">
          <Button variant="outline" onClick={resetErrorBoundary} className="flex-1">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Page
          </Button>
          <Button onClick={goHome} className="flex-1">
            <Home className="h-4 w-4 mr-2" />
            Go to Dashboard
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

/**
 * Global error boundary component to catch unhandled errors
 * and prevent white screens
 */
class GlobalErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to the console
    console.error('Uncaught error in component:', error, errorInfo);
    
    // Update state with error info
    this.setState({
      errorInfo
    });

    // You could also log the error to an error reporting service here
    // Example: logErrorToService(error, errorInfo);
  }

  resetErrorBoundary = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        <ErrorBoundaryFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          resetErrorBoundary={this.resetErrorBoundary}
        />
      );
    }

    return this.props.children;
  }
}

export default GlobalErrorBoundary;

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Plus, X, Image, Upload, Camera, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import api from '@/lib/axios';
import { getProfilePhotoUrl } from '@/utils/imageUtils';
import { Property } from '@/services/propertyService';

interface PropertyPhotoGalleryProps {
  property: Property;
  isAdmin: boolean;
}

// Define a photo object structure
interface PropertyPhoto {
  _id: string;
  name: string;
  description?: string;
  fileUrl: string;
  uploadDate: string;
  category: string;
}

const PropertyPhotoGallery: React.FC<PropertyPhotoGalleryProps> = ({ property, isAdmin }) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedPhoto, setSelectedPhoto] = useState<PropertyPhoto | null>(null);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [photoName, setPhotoName] = useState('');
  const [photoDescription, setPhotoDescription] = useState('');
  const [photoCategory, setPhotoCategory] = useState('exterior');
  const [activeTab, setActiveTab] = useState('all');

  // Safety check for property
  if (!property) {
    console.error("PropertyPhotoGallery: property is undefined or null");
    return (
      <div className="p-4 border border-red-300 bg-red-50 rounded-md">
        <p className="text-red-500">Error: Property data is missing</p>
      </div>
    );
  }

  // Filter photos from property documents
  const propertyPhotos = property.documents?.filter(doc =>
    doc?.fileType?.startsWith('image/') ||
    doc?.name?.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/i)
  ) || [];

  // Group photos by category
  const photosByCategory = propertyPhotos.reduce((acc, photo) => {
    // Extract category from name if not explicitly set
    const category = photo.name?.includes('_')
      ? photo.name.split('_')[0].toLowerCase()
      : 'other';

    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push({
      ...photo,
      category
    });
    return acc;
  }, {} as Record<string, PropertyPhoto[]>);

  // Get all categories
  const categories = Object.keys(photosByCategory);

  // Get photos for current tab
  const currentPhotos = activeTab === 'all'
    ? propertyPhotos.map(photo => ({
        ...photo,
        category: photo.name?.includes('_')
          ? photo.name.split('_')[0].toLowerCase()
          : 'other'
      }))
    : photosByCategory[activeTab] || [];

  // Upload photo mutation
  const uploadPhotoMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await api.post('/api/documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    },
    onSuccess: async (data) => {
      // Now add the document to the property
      const documentData = {
        name: `${photoCategory}_${photoName || photoFile?.name || 'Photo'}`,
        fileType: photoFile?.type || 'image/jpeg',
        fileUrl: data.fileUrl,
        description: photoDescription
      };

      await api.post(`/api/properties/${property._id}/documents`, documentData);

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['property', property._id] });

      toast({
        title: 'Success',
        description: 'Photo uploaded successfully',
      });

      // Reset form
      setPhotoFile(null);
      setPhotoName('');
      setPhotoDescription('');
      setIsUploadDialogOpen(false);
      setUploadingPhoto(false);
    },
    onError: (error: any) => {
      console.error('Error uploading photo:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to upload photo',
        variant: 'destructive',
      });
      setUploadingPhoto(false);
    }
  });

  // Delete photo mutation
  const deletePhotoMutation = useMutation({
    mutationFn: async (documentId: string) => {
      return api.delete(`/api/properties/${property._id}/documents/${documentId}`);
    },
    onSuccess: () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['property', property._id] });

      toast({
        title: 'Success',
        description: 'Photo deleted successfully',
      });

      setSelectedPhoto(null);
      setIsViewDialogOpen(false);
    },
    onError: (error: any) => {
      console.error('Error deleting photo:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to delete photo',
        variant: 'destructive',
      });
    }
  });

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setPhotoFile(file);

      // Auto-fill name from filename (without extension)
      const fileName = file.name.replace(/\.[^/.]+$/, "");
      if (!photoName) {
        setPhotoName(fileName);
      }
    }
  };

  // Handle photo upload
  const handleUploadPhoto = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!photoFile) {
      toast({
        title: 'Error',
        description: 'Please select a photo to upload',
        variant: 'destructive',
      });
      return;
    }

    setUploadingPhoto(true);

    const formData = new FormData();
    formData.append('document', photoFile);
    formData.append('category', 'property');

    uploadPhotoMutation.mutate(formData);
  };

  // Handle photo deletion
  const handleDeletePhoto = () => {
    if (!selectedPhoto) return;

    if (confirm('Are you sure you want to delete this photo?')) {
      deletePhotoMutation.mutate(selectedPhoto._id);
    }
  };

  // View a photo
  const handleViewPhoto = (photo: PropertyPhoto) => {
    setSelectedPhoto(photo);
    setIsViewDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Property Photos</h3>
        {isAdmin && (
          <Button
            onClick={() => setIsUploadDialogOpen(true)}
            size="sm"
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            Add Photos
          </Button>
        )}
      </div>

      {propertyPhotos.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg bg-gray-50">
          <Image className="h-12 w-12 text-gray-400 mb-2" />
          <p className="text-gray-500">No photos available for this property</p>
          {isAdmin && (
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => setIsUploadDialogOpen(true)}
            >
              Upload First Photo
            </Button>
          )}
        </div>
      ) : (
        <>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Photos</TabsTrigger>
              {categories.map(category => (
                <TabsTrigger key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value={activeTab} className="mt-0">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {currentPhotos.map((photo) => (
                  <Card
                    key={photo._id}
                    className="overflow-hidden cursor-pointer group relative"
                    onClick={() => handleViewPhoto(photo as PropertyPhoto)}
                  >
                    <div className="aspect-square relative">
                      <img
                        src={getProfilePhotoUrl(photo.fileUrl) || ''}
                        alt={photo.name}
                        className="object-cover w-full h-full"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = 'https://placehold.co/400x400?text=Error';
                        }}
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200"></div>

                      {isAdmin && (
                        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild onClick={e => e.stopPropagation()}>
                              <Button variant="ghost" size="icon" className="h-8 w-8 bg-white/80 hover:bg-white">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                setSelectedPhoto(photo as PropertyPhoto);
                                setIsViewDialogOpen(true);
                              }}>
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedPhoto(photo as PropertyPhoto);
                                  handleDeletePhoto();
                                }}
                              >
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      )}
                    </div>
                    <CardContent className="p-2">
                      <p className="text-xs truncate">{photo.name.split('_').slice(1).join('_')}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </>
      )}

      {/* Photo Upload Dialog */}
      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload Property Photo</DialogTitle>
            <DialogDescription>
              Add a new photo to this property. Photos will be organized by category.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleUploadPhoto} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="photo-file">Photo</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="photo-file"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="flex-1"
                />
                {photoFile && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => setPhotoFile(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {photoFile && (
                <div className="mt-2 border rounded-md p-2 flex items-center gap-2">
                  <div className="h-16 w-16 overflow-hidden rounded-md">
                    <img
                      src={URL.createObjectURL(photoFile)}
                      alt="Preview"
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div className="text-sm">
                    <p className="font-medium truncate">{photoFile.name}</p>
                    <p className="text-gray-500">{(photoFile.size / 1024).toFixed(1)} KB</p>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="photo-category">Category</Label>
              <select
                id="photo-category"
                value={photoCategory}
                onChange={(e) => setPhotoCategory(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="exterior">Exterior</option>
                <option value="interior">Interior</option>
                <option value="kitchen">Kitchen</option>
                <option value="bathroom">Bathroom</option>
                <option value="bedroom">Bedroom</option>
                <option value="livingroom">Living Room</option>
                <option value="backyard">Backyard</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="photo-name">Name (Optional)</Label>
              <Input
                id="photo-name"
                value={photoName}
                onChange={(e) => setPhotoName(e.target.value)}
                placeholder="Front view"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="photo-description">Description (Optional)</Label>
              <Input
                id="photo-description"
                value={photoDescription}
                onChange={(e) => setPhotoDescription(e.target.value)}
                placeholder="Photo taken during spring 2023"
              />
            </div>

            <div className="flex justify-end gap-2 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsUploadDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!photoFile || uploadingPhoto}
              >
                {uploadingPhoto && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Upload Photo
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Photo View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>{selectedPhoto?.name?.split('_').slice(1).join('_') || 'Property Photo'}</DialogTitle>
            {selectedPhoto?.description && (
              <DialogDescription>
                {selectedPhoto.description}
              </DialogDescription>
            )}
          </DialogHeader>

          <div className="flex flex-col items-center">
            <div className="max-h-[500px] overflow-hidden rounded-md">
              <img
                src={getProfilePhotoUrl(selectedPhoto?.fileUrl || '') || ''}
                alt={selectedPhoto?.name || 'Property photo'}
                className="max-w-full max-h-[500px] object-contain"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://placehold.co/600x400?text=Error+Loading+Image';
                }}
              />
            </div>

            <div className="w-full mt-4 flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {selectedPhoto?.uploadDate && (
                  <p>Uploaded: {new Date(selectedPhoto.uploadDate).toLocaleDateString()}</p>
                )}
                <p>Category: {selectedPhoto?.category?.charAt(0).toUpperCase() + selectedPhoto?.category?.slice(1) || 'Unknown'}</p>
              </div>

              {isAdmin && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleDeletePhoto}
                  disabled={deletePhotoMutation.isPending}
                >
                  {deletePhotoMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Delete Photo
                </Button>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PropertyPhotoGallery;

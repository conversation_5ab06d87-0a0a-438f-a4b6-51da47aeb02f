import React, { useMemo } from 'react';
import { Progress } from '@/components/ui/progress';

interface PasswordStrengthMeterProps {
  password: string;
}

const PasswordStrengthMeter: React.FC<PasswordStrengthMeterProps> = ({ password }) => {
  // Calculate password strength
  const { strength, message, color } = useMemo(() => {
    if (!password) {
      return { strength: 0, message: '', color: 'bg-gray-200' };
    }

    // Check password length
    const lengthScore = Math.min(password.length / 12, 1) * 25;

    // Check for numbers
    const hasNumbers = /\d/.test(password);
    const numberScore = hasNumbers ? 25 : 0;

    // Check for special characters
    const hasSpecialChars = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password);
    const specialCharScore = hasSpecialChars ? 25 : 0;

    // Check for mixed case
    const hasMixedCase = /[a-z]/.test(password) && /[A-Z]/.test(password);
    const mixedCaseScore = hasMixedCase ? 25 : 0;

    // Calculate total score
    const totalScore = lengthScore + numberScore + specialCharScore + mixedCaseScore;
    
    // Determine strength level
    let strength = 0;
    let message = '';
    let color = 'bg-gray-200';

    if (totalScore < 25) {
      strength = 25;
      message = 'Very Weak';
      color = 'bg-red-500';
    } else if (totalScore < 50) {
      strength = 50;
      message = 'Weak';
      color = 'bg-orange-500';
    } else if (totalScore < 75) {
      strength = 75;
      message = 'Good';
      color = 'bg-yellow-500';
    } else {
      strength = 100;
      message = 'Strong';
      color = 'bg-green-500';
    }

    return { strength, message, color };
  }, [password]);

  if (!password) {
    return null;
  }

  return (
    <div className="mt-1 space-y-1">
      <div className="flex justify-between items-center text-xs">
        <span>Password Strength</span>
        <span className={`font-medium ${
          color === 'bg-red-500' ? 'text-red-500' : 
          color === 'bg-orange-500' ? 'text-orange-500' : 
          color === 'bg-yellow-500' ? 'text-yellow-500' : 
          'text-green-500'
        }`}>
          {message}
        </span>
      </div>
      <Progress value={strength} className={`h-1 ${color}`} />
      
      {strength <= 50 && (
        <div className="text-xs text-gray-500 mt-1">
          <p>Tips for a stronger password:</p>
          <ul className="list-disc list-inside ml-1 mt-1 space-y-0.5">
            {password.length < 8 && (
              <li>Use at least 8 characters</li>
            )}
            {!/\d/.test(password) && (
              <li>Include numbers</li>
            )}
            {!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password) && (
              <li>Include special characters</li>
            )}
            {!(/[a-z]/.test(password) && /[A-Z]/.test(password)) && (
              <li>Mix uppercase and lowercase letters</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default PasswordStrengthMeter;

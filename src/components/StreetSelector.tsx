/**
 * StreetSelector Component
 *
 * This component renders a dropdown selector for communities/streets.
 * It shows communities that belong to HOAs that are:
 * 1. Approved (verificationStatus === 'approved')
 * 2. Have an active subscription (subscription.status === 'active' or 'trialing')
 *
 * This ensures that only communities that are currently being managed are shown in the dropdown.
 */
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useQuery } from '@tanstack/react-query';
import api from '@/lib/axios';
import { Label } from '@/components/ui/label';

import { Community } from '@/services/communityService';

interface HOA {
  _id: string;
  hoaCommunityName: string;
  hoaCommunityCode: string;
  hoaStreetAddress: string;
  verificationStatus?: string;
  subscription?: {
    status?: string;
  };
}

interface StreetSelectorProps {
  onStreetChange: (communityId: string | null, availableCommunities?: Community[]) => void;
  selectedCommunityId?: string | null;
}

const StreetSelector: React.FC<StreetSelectorProps> = ({ onStreetChange, selectedCommunityId }) => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isAdmin = user?.role === 'admin' || user?.role === 'company_admin';
  const userHoaId = user?.hoaId || '';

  // First fetch all HOAs that are approved and have active subscriptions
  const { data: hoas, isLoading: isLoadingHoas } = useQuery({
    queryKey: ['managed-hoas'],
    queryFn: async () => {
      try {
        const response = await api.get('/api/hoa');
        // Filter HOAs to only include those that are approved and have an active subscription
        const allHoas = response.data.data || [];

        console.log('All HOAs:', allHoas.map((hoa: HOA) => ({
          id: hoa._id,
          name: hoa.hoaCommunityName,
          verificationStatus: hoa.verificationStatus,
          subscriptionStatus: hoa.subscription?.status
        })));

        const managedHoas = allHoas.filter((hoa: HOA) => {
          // Check if the HOA is approved and has an active subscription
          const isApproved = hoa.verificationStatus === 'approved';
          const hasActiveSubscription = hoa.subscription?.status === 'active' ||
                                       hoa.subscription?.status === 'trialing';

          return isApproved && hasActiveSubscription;
        });

        console.log('Managed HOAs:', managedHoas.length);
        return managedHoas;
      } catch (error) {
        console.error('Error fetching HOAs:', error);
        return [];
      }
    },
    enabled: isAdmin // Only fetch if user is admin or company_admin
  });

  // Then fetch communities for those HOAs
  const { data: communities = [], isLoading: isLoadingCommunities } = useQuery({
    queryKey: ['communities', hoas, user?.role, userHoaId],
    queryFn: async () => {
      console.log('Fetching communities...');
      console.log('User:', user);
      console.log('HOAs:', hoas);
      console.log('User HOA ID:', userHoaId);
      console.log('Is Admin:', isAdmin);

      try {
        // For company admins, fetch all communities
        if (user?.role === 'company_admin') {
          console.log('Fetching all communities for company admin');
          const response = await api.get('/api/communities');
          console.log('Company admin communities response:', response.data);
          return response.data || [];
        }

        // For admins (HOA admins), fetch communities for their HOA
        if (user?.role === 'admin' && userHoaId) {
          console.log('Fetching communities for admin, HOA ID:', userHoaId);
          try {
            const response = await api.get(`/api/communities/hoa/${userHoaId}`);
            console.log('Admin communities response:', response.data);
            return response.data || [];
          } catch (error) {
            console.error('403 Forbidden on /api/communities/hoa/:hoaId, trying alternative approach:', error);
            // Fallback: Try to get all communities and filter by user's HOA
            try {
              const allCommunitiesResponse = await api.get('/api/communities');
              const allCommunities = allCommunitiesResponse.data || [];
              const filteredCommunities = allCommunities.filter((community: Community) =>
                community.hoaId === userHoaId
              );
              console.log('Filtered communities for admin:', filteredCommunities);
              return filteredCommunities;
            } catch (fallbackError) {
              console.error('Fallback also failed:', fallbackError);
              return [];
            }
          }
        }

        // For members, try to get their community
        if (user?.role === 'member' && user?.communityId) {
          console.log('Fetching community for member, community ID:', user.communityId);
          const response = await api.get(`/api/communities/${user.communityId}`);
          console.log('Member community response:', response.data);
          const community = response.data;
          return community ? [community] : [];
        }

        // If we have HOAs but no specific role match, try to get communities from HOAs
        if (hoas && hoas.length > 0) {
          console.log('Fetching communities from available HOAs');
          const allCommunities = [];

          // Try to get all communities first and filter by HOA IDs
          try {
            console.log('Trying to get all communities and filter by HOA IDs');
            const allCommunitiesResponse = await api.get('/api/communities');
            const allCommunitiesData = allCommunitiesResponse.data || [];

            // Filter communities that belong to user's HOAs
            const hoaIds = hoas.map((hoa: HOA) => hoa._id);
            const filteredCommunities = allCommunitiesData.filter((community: Community) =>
              hoaIds.includes(community.hoaId)
            );
            console.log('Communities filtered by HOA IDs:', filteredCommunities);
            return filteredCommunities;
          } catch (error) {
            console.error('Failed to get all communities, trying individual HOA endpoints:', error);

            // Fallback: Try individual HOA endpoints (may fail with 403)
            for (const hoa of hoas) {
              try {
                const response = await api.get(`/api/communities/hoa/${hoa._id}`);
                const hoaCommunities = response.data || [];
                allCommunities.push(...hoaCommunities);
              } catch (hoaError) {
                console.error(`403 Forbidden for HOA ${hoa._id}, skipping:`, hoaError);
                // Continue with other HOAs instead of failing completely
              }
            }
            console.log('Communities from individual HOA calls:', allCommunities);
            return allCommunities;
          }
        }

        console.log('No matching conditions for fetching communities');
        return [];
      } catch (error) {
        console.error('Error fetching communities:', error);
        return [];
      }
    },
    enabled: !!user, // Enable for any authenticated user
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  console.log('Final communities data:', communities);
  console.log('Loading communities:', isLoadingCommunities);





  // Handle community selection
  const handleCommunityChange = (value: string) => {
    console.log('Community selection changed to:', value);
    console.log('Available communities:', communities);

    if (value === 'all') {
      onStreetChange(null, communities); // Pass available communities for "All Communities"
    } else {
      onStreetChange(value, communities); // Pass available communities for context
    }
  };

  // Check if user has multiple communities (based on communities data)
  const hasMultipleCommunities = communities.length > 1;

  // Debug logging for render conditions
  console.log('Render conditions:');
  console.log('- isLoadingCommunities:', isLoadingCommunities);
  console.log('- communities:', communities);
  console.log('- communities.length:', communities?.length);
  console.log('- user:', user);
  console.log('- isAdmin:', isAdmin);

  return (
    <div className="flex flex-col space-y-1.5">
      <div className="flex items-center justify-between">
        <Label htmlFor="street-select">Select Community</Label>
      </div>
      {isLoadingHoas || isLoadingCommunities ? (
        <div className="text-sm text-muted-foreground py-2">Loading communities...</div>
      ) : communities && communities.length > 0 ? (
        <Select
          value={selectedCommunityId || 'all'}
          onValueChange={handleCommunityChange}
          disabled={user?.role === 'member' && communities.length <= 1}
        >
          <SelectTrigger id="street-select" className="w-full text-black">
            <SelectValue placeholder="Select a community" className="text-black" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Communities</SelectItem>
            {communities.map((community: Community) => (
              <SelectItem key={community._id} value={community._id}>
                {community.name} ({community.streetAddress || 'No address'})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : (
        <div className="text-sm text-muted-foreground py-2 border rounded-md p-3">
          {isLoadingCommunities ? (
            "Loading communities..."
          ) : (
            <>
              No communities available. This may be due to:
              <ul className="mt-1 ml-4 text-xs list-disc">
                <li>No communities assigned to your account</li>
                <li>Permission restrictions on community access</li>
                <li>HOA subscription or approval status</li>
              </ul>
              <div className="mt-2 text-xs text-blue-600">
                Please contact your HOA administrator if you believe this is an error.
              </div>
            </>
          )}
        </div>
      )}


    </div>
  );
};

export default StreetSelector;

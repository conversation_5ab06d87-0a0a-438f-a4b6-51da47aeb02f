import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Building2, Plus, Search, Edit, Trash2 } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  getCommunities, 
  getCommunitiesByHoa, 
  createCommunity, 
  updateCommunity, 
  deleteCommunity,
  Community,
  CommunityCreateData,
  CommunityUpdateData
} from '@/services/communityService';
import { getAllHOAs, HOA } from '@/services/hoaService';

// Define the form schema with Zod
const communityFormSchema = z.object({
  name: z.string().min(1, 'Community name is required'),
  description: z.string().optional(),
  streetAddress: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  hoaId: z.string().min(1, 'HOA is required'),
  type: z.string().optional(),
  unitCount: z.coerce.number().int().min(0).optional(),
  visibility: z.enum(['public', 'private', 'hoa_only']).optional(),
});

type CommunityFormValues = z.infer<typeof communityFormSchema>;

const CommunityManagement: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingCommunity, setEditingCommunity] = useState<Community | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [communityToDelete, setCommunityToDelete] = useState<Community | null>(null);

  // Get user info
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isCompanyAdmin = user?.role === 'company_admin';
  const userHoaId = user?.hoaId || '';

  // Fetch communities
  const {
    data: communities = [],
    isLoading: isLoadingCommunities,
    error: communitiesError
  } = useQuery({
    queryKey: ['communities'],
    queryFn: async () => {
      if (isCompanyAdmin) {
        return getCommunities();
      } else if (userHoaId) {
        return getCommunitiesByHoa(userHoaId);
      }
      return [];
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Fetch HOAs for company admins
  const {
    data: hoas = [],
    isLoading: isLoadingHoas
  } = useQuery({
    queryKey: ['hoas'],
    queryFn: getAllHOAs,
    enabled: isCompanyAdmin,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Create community mutation
  const createCommunityMutation = useMutation({
    mutationFn: createCommunity,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['communities'] });
      toast({
        title: 'Success',
        description: 'Community created successfully',
      });
      setIsFormOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create community',
        variant: 'destructive',
      });
    },
  });

  // Update community mutation
  const updateCommunityMutation = useMutation({
    mutationFn: (data: { id: string; data: CommunityUpdateData }) => 
      updateCommunity(data.id, data.data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['communities'] });
      toast({
        title: 'Success',
        description: 'Community updated successfully',
      });
      setIsFormOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update community',
        variant: 'destructive',
      });
    },
  });

  // Delete community mutation
  const deleteCommunityMutation = useMutation({
    mutationFn: deleteCommunity,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['communities'] });
      toast({
        title: 'Success',
        description: 'Community deleted successfully',
      });
      setIsDeleteDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to delete community',
        variant: 'destructive',
      });
    },
  });

  // Initialize form
  const form = useForm<CommunityFormValues>({
    resolver: zodResolver(communityFormSchema),
    defaultValues: {
      name: '',
      description: '',
      streetAddress: '',
      city: '',
      state: '',
      zipCode: '',
      hoaId: userHoaId || '',
      type: 'single-family',
      unitCount: 0,
      visibility: 'public',
    }
  });

  // Handle form submission
  const onSubmit = (values: CommunityFormValues) => {
    if (editingCommunity) {
      updateCommunityMutation.mutate({
        id: editingCommunity._id,
        data: values
      });
    } else {
      createCommunityMutation.mutate(values);
    }
  };

  // Open form for editing
  const handleEdit = (community: Community) => {
    setEditingCommunity(community);
    form.reset({
      name: community.name,
      description: community.description || '',
      streetAddress: community.streetAddress || '',
      city: community.city || '',
      state: community.state || '',
      zipCode: community.zipCode || '',
      hoaId: community.hoaId,
      type: community.type || 'single-family',
      unitCount: community.unitCount || 0,
      visibility: community.visibility || 'public',
    });
    setIsFormOpen(true);
  };

  // Open form for creating
  const handleCreate = () => {
    setEditingCommunity(null);
    form.reset({
      name: '',
      description: '',
      streetAddress: '',
      city: '',
      state: '',
      zipCode: '',
      hoaId: userHoaId || '',
      type: 'single-family',
      unitCount: 0,
      visibility: 'public',
    });
    setIsFormOpen(true);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (community: Community) => {
    setCommunityToDelete(community);
    setIsDeleteDialogOpen(true);
  };

  // Confirm deletion
  const confirmDelete = () => {
    if (communityToDelete) {
      deleteCommunityMutation.mutate(communityToDelete._id);
    }
  };

  // Filter communities based on search term
  const filteredCommunities = communities.filter((community: Community) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      community.name.toLowerCase().includes(searchLower) ||
      (community.streetAddress && community.streetAddress.toLowerCase().includes(searchLower)) ||
      (community.description && community.description.toLowerCase().includes(searchLower))
    );
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">Community Management</h2>
          <p className="text-muted-foreground">Manage communities and streets</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button onClick={handleCreate}>
            <Plus className="h-4 w-4 mr-2" />
            Add Community
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="p-4 md:p-6">
          <CardTitle>Communities</CardTitle>
          <CardDescription>Manage communities and streets for your HOA</CardDescription>
        </CardHeader>
        <CardContent className="p-4 md:p-6">
          {isLoadingCommunities ? (
            <div className="text-center py-4">Loading communities...</div>
          ) : communitiesError ? (
            <div className="text-center py-4 text-red-500">Error loading communities</div>
          ) : filteredCommunities.length === 0 ? (
            <div className="text-center py-4">No communities found</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Address</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Units</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCommunities.map((community: Community) => (
                  <TableRow key={community._id}>
                    <TableCell className="font-medium">{community.name}</TableCell>
                    <TableCell>{community.streetAddress || 'N/A'}</TableCell>
                    <TableCell>{community.type || 'N/A'}</TableCell>
                    <TableCell>{community.unitCount || 0}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="icon" onClick={() => handleEdit(community)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="icon" 
                          className="text-destructive" 
                          onClick={() => handleDeleteClick(community)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Community Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{editingCommunity ? 'Edit Community' : 'Add New Community'}</DialogTitle>
            <DialogDescription>
              {editingCommunity 
                ? 'Update the community details below' 
                : 'Fill in the details to create a new community'}
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Community Name <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input placeholder="Oak Hills" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="A brief description of the community" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="streetAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Street Address</FormLabel>
                      <FormControl>
                        <Input placeholder="123 Oak Street" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input placeholder="Anytown" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State</FormLabel>
                      <FormControl>
                        <Input placeholder="CA" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="zipCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ZIP Code</FormLabel>
                      <FormControl>
                        <Input placeholder="90210" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              {isCompanyAdmin && (
                <FormField
                  control={form.control}
                  name="hoaId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>HOA <span className="text-red-500">*</span></FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                        disabled={isLoadingHoas}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an HOA" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {hoas.map((hoa: HOA) => (
                            <SelectItem key={hoa._id} value={hoa._id}>
                              {hoa.hoaCommunityName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Community Type</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="single-family">Single Family</SelectItem>
                          <SelectItem value="townhouse">Townhouse</SelectItem>
                          <SelectItem value="condo">Condo</SelectItem>
                          <SelectItem value="apartment">Apartment</SelectItem>
                          <SelectItem value="mixed">Mixed</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="unitCount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number of Units</FormLabel>
                      <FormControl>
                        <Input type="number" min="0" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="visibility"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Visibility</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select visibility" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="public">Public (Visible to all HOA members)</SelectItem>
                        <SelectItem value="private">Private (Visible only to community members)</SelectItem>
                        <SelectItem value="hoa_only">HOA Only (Visible only to admins)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Controls who can see this community in listings
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter className="mt-6">
                <Button type="button" variant="outline" onClick={() => setIsFormOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={createCommunityMutation.isPending || updateCommunityMutation.isPending}
                >
                  {createCommunityMutation.isPending || updateCommunityMutation.isPending
                    ? 'Saving...'
                    : editingCommunity
                      ? 'Update Community'
                      : 'Add Community'
                  }
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the community "{communityToDelete?.name}"? 
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmDelete}
              disabled={deleteCommunityMutation.isPending}
            >
              {deleteCommunityMutation.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CommunityManagement;

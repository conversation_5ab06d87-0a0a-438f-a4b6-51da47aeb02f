import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Building2,
  Search,
  Users,
  DollarSign,
  Calendar,
  AlertCircle,
  Eye,
  Settings
} from 'lucide-react';
import { getHOARevenueSummary, HOARevenueSummary } from '@/services/hoaRevenueService';
import { useNavigate } from 'react-router-dom';
import HOADetailsModal from './HOADetailsModal';

const AllHOAsManagement = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedHOAId, setSelectedHOAId] = useState<string | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Fetch all HOAs revenue summary
  const { data: hoaRevenueSummary, isLoading, isError, error } = useQuery({
    queryKey: ['hoaRevenueSummary'],
    queryFn: getHOARevenueSummary,
  });

  // Filter HOAs based on search term
  const filteredHOAs = hoaRevenueSummary?.filter(hoa =>
    hoa.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const getTierBadgeColor = (tier: string) => {
    switch (tier?.toLowerCase()) {
      case 'basic':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'standard':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'premium':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'trialing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'canceled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-10 w-full max-w-md" />
        <div className="grid gap-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {(error as any)?.message || 'Failed to load HOA information. Please try again later.'}
        </AlertDescription>
      </Alert>
    );
  }

  if (!hoaRevenueSummary || hoaRevenueSummary.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Building2 className="h-6 w-6" />
            <h1 className="text-2xl font-bold">All HOAs Management</h1>
          </div>
          <Button onClick={() => navigate('/admin/hoa-approvals')}>
            View Pending Approvals
          </Button>
        </div>

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No HOAs Found</AlertTitle>
          <AlertDescription>
            No HOAs are currently registered on the platform. Check the HOA Approvals page for pending registrations.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Building2 className="h-6 w-6" />
          <h1 className="text-2xl font-bold">All HOAs Management</h1>
        </div>
        <Button onClick={() => navigate('/admin/hoa-approvals')}>
          View Pending Approvals
        </Button>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search HOAs by name, city, or state..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total HOAs</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{hoaRevenueSummary.length}</div>
            <p className="text-xs text-muted-foreground">
              {filteredHOAs.length !== hoaRevenueSummary.length &&
                `${filteredHOAs.length} shown`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${hoaRevenueSummary.reduce((total, hoa) => total + (hoa.totalRevenue || 0), 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              From all HOAs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {hoaRevenueSummary.filter(hoa => hoa.subscriptionStatus === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Out of {hoaRevenueSummary.length} total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* HOAs List */}
      <div className="grid gap-4">
        {filteredHOAs.map((hoa) => (
          <Card key={hoa._id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{hoa.name}</CardTitle>
                  <CardDescription>
                    Code: {hoa.code}
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedHOAId(hoa._id);
                      setIsDetailsModalOpen(true);
                    }}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate(`/subscriptions/${hoa._id}`)}
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    Manage Subscription
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                  <p className="text-lg font-semibold">${(hoa.totalRevenue || 0).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Subscription Revenue</p>
                  <p className="text-lg font-semibold">${(hoa.subscriptionRevenue || 0).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Subscription</p>
                  <div className="flex gap-1 mt-1">
                    {hoa.subscriptionTier && hoa.subscriptionTier !== 'none' && (
                      <Badge className={getTierBadgeColor(hoa.subscriptionTier)}>
                        {hoa.subscriptionTier.charAt(0).toUpperCase() + hoa.subscriptionTier.slice(1)}
                      </Badge>
                    )}
                    {hoa.subscriptionStatus && (
                      <Badge className={getStatusBadgeColor(hoa.subscriptionStatus)}>
                        {hoa.subscriptionStatus}
                      </Badge>
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Finance Revenue</p>
                  <p className="text-sm">
                    ${(hoa.financeRevenue || 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredHOAs.length === 0 && searchTerm && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Results Found</AlertTitle>
          <AlertDescription>
            No HOAs match your search criteria. Try adjusting your search terms.
          </AlertDescription>
        </Alert>
      )}

      {/* HOA Details Modal */}
      <HOADetailsModal
        hoaId={selectedHOAId}
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedHOAId(null);
        }}
      />
    </div>
  );
};

export default AllHOAsManagement;

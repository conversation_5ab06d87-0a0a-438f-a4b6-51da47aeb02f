import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, DollarSign, Calendar, AlertTriangle, Building2 } from 'lucide-react';
import FinanceOverview from './FinanceOverview';
import TaskList from './TaskList';
import NotificationCenter from './NotificationCenter';
import DocumentsWidget from './DocumentsWidget';
import RoleInfoCard from './RoleInfoCard';
import HOARevenueSelector from './HOARevenueSelector';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import api from '@/lib/axios';
import { getHOARevenueSummary } from '@/services/hoaRevenueService';

const Dashboard = () => {
  const navigate = useNavigate();
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  // Define clear role variables
  const isCompanyAdmin = user?.role === 'company_admin';
  const isAdmin = user?.role === 'admin';
  const isMember = user?.role === 'member';
  const isOperationalAdmin = isAdmin; // Only regular admins can perform operations
  const isAnyAdmin = isAdmin || isCompanyAdmin;

  // For regular HOA admins, always use their assigned HOA
  const defaultHoaId = isAdmin && !isCompanyAdmin && user?.hoaId ? user.hoaId : null;
  const [selectedHoaId, setSelectedHoaId] = useState<string | null>(defaultHoaId);

  // For members, always use their community
  const userCommunityId = user?.hoaId || null;

  // Get community selection from localStorage (set by Sidebar) - now supports multiple communities
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });
  const [selectedCommunityIds, setSelectedCommunityIds] = useState<string[]>(() => {
    const stored = localStorage.getItem('selectedCommunityIds');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);
  const [isMultipleCommunities, setIsMultipleCommunities] = useState<boolean>(false);

  // Listen for community selection changes from Sidebar
  useEffect(() => {
    const handleCommunityChange = (event: CustomEvent) => {
      const { communityId, communityIds } = event.detail;
      console.log('Dashboard: Received community selection change:', { communityId, communityIds });

      // Handle both single and multiple community selection
      if (communityIds && Array.isArray(communityIds)) {
        // Multiple communities selected
        setSelectedCommunityIds(communityIds);
        setSelectedCommunityId(null); // Clear single selection
        setIsMultipleCommunities(true);

        // Store multiple communities in localStorage
        localStorage.setItem('selectedCommunityIds', JSON.stringify(communityIds));
        localStorage.removeItem('selectedCommunityId');

        // Set display name for multiple communities
        setSelectedCommunityName(`${communityIds.length} Communities Selected`);

        // For multiple communities, we might need to handle HOA differently
        // For now, reset HOA selection when multiple communities are selected
        if (isCompanyAdmin) {
          setSelectedHoaId(null);
        }

        console.log('Dashboard: Multiple communities selected:', communityIds);
      } else if (communityId) {
        // Single community selected
        setSelectedCommunityId(communityId);
        setSelectedCommunityIds([]); // Clear multiple selection
        setIsMultipleCommunities(false);

        // Store single community in localStorage
        localStorage.setItem('selectedCommunityId', communityId);
        localStorage.removeItem('selectedCommunityIds');

        // Fetch community details and update HOA selection if needed
        api.get(`/api/communities/${communityId}`)
          .then(response => {
            const community = response.data;
            if (community) {
              setSelectedCommunityName(`${community.name} (${community.streetAddress || 'No address'})`);

              // If community has an HOA, update the selected HOA for consistency
              if (community.hoaId && community.hoaId !== selectedHoaId) {
                console.log('Dashboard: Updating selected HOA based on community:', {
                  communityId: communityId,
                  communityName: community.name,
                  hoaId: community.hoaId,
                  previousHoaId: selectedHoaId
                });
                setSelectedHoaId(community.hoaId);
              }
            }
          })
          .catch(error => {
            console.error('Error fetching community details:', error);
            setSelectedCommunityName('Selected Community');
          });
      } else {
        // "All Communities" or no selection
        setSelectedCommunityId(null);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);
        setSelectedCommunityName(null);

        // Clear localStorage
        localStorage.removeItem('selectedCommunityId');
        localStorage.removeItem('selectedCommunityIds');

        // Reset HOA selection when "All Communities" is selected
        if (isCompanyAdmin) {
          setSelectedHoaId(null);
        }
      }
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, [selectedHoaId, isCompanyAdmin]);

  // Fetch settings for due date
  const { data: settings } = useQuery({
    queryKey: ['settings'],
    queryFn: async () => {
      try {
        console.log('Fetching settings...');
        const response = await api.get('/api/settings');
        console.log('Settings response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Error fetching settings:', error);
        return null;
      }
    }
  });

  // Fetch HOA information if user is associated with an HOA
  const { data: hoaData } = useQuery({
    queryKey: ['userHoa'],
    queryFn: async () => {
      try {
        if (!user.hoaId) return null;
        const response = await api.get(`/api/hoa/${user.hoaId}`);
        return response.data.data;
      } catch (error) {
        console.error('Error fetching HOA data:', error);
        return null;
      }
    },
    enabled: !!user.hoaId
  });

  // Fetch pending HOAs for company admin
  const { data: pendingHOAs = [] } = useQuery({
    queryKey: ['pendingHOAs'],
    queryFn: async () => {
      try {
        const response = await api.get('/api/admin/hoa/pending');
        return response.data || [];
      } catch (error) {
        console.error('Error fetching pending HOAs:', error);
        return [];
      }
    },
    enabled: isCompanyAdmin
  });

  // Fetch HOA revenue summary for company admin
  const { data: hoaRevenueSummary = [] } = useQuery({
    queryKey: ['hoaRevenueSummary'],
    queryFn: getHOARevenueSummary,
    enabled: isCompanyAdmin
  });

  // Calculate total revenue from all HOAs
  const calculateTotalRevenue = () => {
    if (!hoaRevenueSummary || hoaRevenueSummary.length === 0) return 0;

    return hoaRevenueSummary.reduce((total, hoa) => {
      return total + (hoa.totalRevenue || 0);
    }, 0);
  };

  // Get revenue for selected HOA
  const getSelectedHOARevenue = () => {
    if (!selectedHoaId || !hoaRevenueSummary) return 0;

    const selectedHOA = hoaRevenueSummary.find(hoa => hoa._id === selectedHoaId);
    return selectedHOA?.totalRevenue || 0;
  };

  // Fetch members count (approved and pending)
  const { data: membersData } = useQuery({
    queryKey: ['dashboardMembers', selectedHoaId, selectedCommunityId, selectedCommunityIds, userCommunityId],
    queryFn: async () => {
      try {
        let endpoint;

        // Company admin can see all, filtered by selected HOA, or filtered by community/communities
        if (isCompanyAdmin) {
          if (selectedCommunityIds.length > 0) {
            // Multiple communities selected
            const communityParams = selectedCommunityIds.map(id => `communityId=${id}`).join('&');
            endpoint = `/api/members/approved?${communityParams}`;
          } else if (selectedCommunityId) {
            // Single community selected
            endpoint = `/api/members/approved?communityId=${selectedCommunityId}`;
          } else if (selectedHoaId) {
            endpoint = `/api/members/approved?hoaId=${selectedHoaId}`;
          } else {
            endpoint = '/api/members/approved';
          }
        }
        // Regular HOA admin always sees their HOA, or filtered by community/communities
        else if (isAdmin) {
          if (selectedCommunityIds.length > 0) {
            // Multiple communities selected (within their HOA)
            const communityParams = selectedCommunityIds.map(id => `communityId=${id}`).join('&');
            endpoint = `/api/members/approved?${communityParams}`;
          } else if (selectedCommunityId) {
            // Single community selected
            endpoint = `/api/members/approved?communityId=${selectedCommunityId}`;
          } else if (user.hoaId) {
            endpoint = `/api/members/approved?hoaId=${user.hoaId}`;
          } else {
            endpoint = '/api/members/approved';
          }
        }
        // Members only see their community
        else if (userCommunityId) {
          endpoint = `/api/members/approved?communityId=${userCommunityId}`;
        }
        // Fallback
        else {
          endpoint = '/api/members/approved';
        }

        console.log('Fetching members using endpoint:', endpoint);
        const approvedRes = await api.get(endpoint);

        // Only fetch pending members if user is admin
        let pendingCount = 0;
        if (isAnyAdmin) {
          try {
            let pendingEndpoint;

            if (isCompanyAdmin) {
              pendingEndpoint = selectedHoaId
                ? `/api/auth/pending?hoaId=${selectedHoaId}`
                : '/api/auth/pending';
            } else if (user.hoaId) {
              pendingEndpoint = `/api/auth/pending?hoaId=${user.hoaId}`;
            }

            if (pendingEndpoint) {
              const pendingRes = await api.get(pendingEndpoint);
              pendingCount = pendingRes.data?.length || 0;
            }
          } catch (pendingError) {
            console.error('Error fetching pending members:', pendingError);
          }
        }

        console.log('Dashboard Members API Call:', {
          endpoint,
          selectedHoaId,
          selectedCommunityId,
          userCommunityId,
          userRole: user?.role
        });

        console.log('Members API Response:', {
          approved: approvedRes.data,
          pendingCount,
          selectedHoaId,
          userCommunityId
        });

        return {
          total: approvedRes.data.users?.length || 0,
          pending: pendingCount
        };
      } catch (error) {
        console.error('Error fetching members:', error);
        return { total: 0, pending: 0 };
      }
    }
  });

  // Fetch HOA balance and recent transactions
  const { data: financeData } = useQuery({
    queryKey: ['dashboardFinance', selectedHoaId, selectedCommunityId, selectedCommunityIds, userCommunityId],
    queryFn: async () => {
      try {
        let endpoint;

        // Try different finance endpoints based on user role and selection
        const endpoints = [];

        // Company admin can see all, filtered by selected HOA, or filtered by community/communities
        if (isCompanyAdmin) {
          if (selectedCommunityIds.length > 0) {
            // Multiple communities selected - aggregate data
            const communityParams = selectedCommunityIds.map(id => `communityId=${id}`).join('&');
            endpoints.push(`/api/finances/monthly?${communityParams}`);
            endpoints.push(`/api/finances?${communityParams}`);
          } else if (selectedCommunityId) {
            // Single community selected
            endpoints.push(`/api/finances/monthly?communityId=${selectedCommunityId}`);
            endpoints.push(`/api/finances?communityId=${selectedCommunityId}`);
          } else if (selectedHoaId) {
            endpoints.push(`/api/finances/monthly?hoaId=${selectedHoaId}`);
            endpoints.push(`/api/finances?hoaId=${selectedHoaId}`);
          } else {
            endpoints.push('/api/finances/monthly');
            endpoints.push('/api/finances');
          }
        }
        // Regular HOA admin always sees their HOA, or filtered by community/communities
        else if (isAdmin) {
          if (selectedCommunityIds.length > 0) {
            // Multiple communities selected (within their HOA)
            const communityParams = selectedCommunityIds.map(id => `communityId=${id}`).join('&');
            endpoints.push(`/api/finances/monthly?${communityParams}`);
            endpoints.push(`/api/finances?${communityParams}`);
          } else if (selectedCommunityId) {
            // Single community selected
            endpoints.push(`/api/finances/monthly?communityId=${selectedCommunityId}`);
            endpoints.push(`/api/finances?communityId=${selectedCommunityId}`);
          } else if (user.hoaId) {
            endpoints.push(`/api/finances/monthly?hoaId=${user.hoaId}`);
            endpoints.push(`/api/finances?hoaId=${user.hoaId}`);
          } else {
            endpoints.push('/api/finances/monthly');
            endpoints.push('/api/finances');
          }
        }
        // Members only see their community
        else if (userCommunityId) {
          endpoints.push(`/api/finances/monthly?communityId=${userCommunityId}`);
          endpoints.push(`/api/finances?communityId=${userCommunityId}`);
        }
        // Fallback
        else {
          endpoints.push('/api/finances/monthly');
          endpoints.push('/api/finances');
        }

        // Try endpoints in order until one works
        let res = null;
        let data = null;

        for (const testEndpoint of endpoints) {
          try {
            console.log('Trying finance endpoint:', testEndpoint);
            res = await api.get(testEndpoint);
            data = res.data;
            endpoint = testEndpoint;

            if (data && (Array.isArray(data) || (typeof data === 'object' && Object.keys(data).length > 0))) {
              console.log('Finance endpoint successful:', testEndpoint);
              break;
            }
          } catch (error) {
            console.log('Finance endpoint failed:', testEndpoint, error.message);
            continue;
          }
        }

        if (!res || !data) {
          console.error('All finance endpoints failed');
          return { balance: 0, monthlyChange: 0 };
        }

        console.log('Dashboard Finance API Call:', {
          endpoint,
          selectedHoaId,
          selectedCommunityId,
          userCommunityId,
          userRole: user?.role
        });

        console.log('Finance API Response:', {
          status: res.status,
          data: data,
          dataType: typeof data,
          isArray: Array.isArray(data),
          dataKeys: data ? Object.keys(data) : null
        });

        // Handle different response structures
        let financeData = data;

        // If data is wrapped in a data property
        if (data && data.data && Array.isArray(data.data)) {
          financeData = data.data;
          console.log('Using nested data array:', financeData);
        }
        // If data has a finances property
        else if (data && data.finances && Array.isArray(data.finances)) {
          financeData = data.finances;
          console.log('Using finances array:', financeData);
        }
        // If data has balance and income properties directly
        else if (data && typeof data === 'object' && (data.balance !== undefined || data.income !== undefined)) {
          console.log('Using direct balance/income data:', data);
          return {
            balance: data.balance || data.totalBalance || 0,
            monthlyChange: data.income || data.monthlyIncome || data.monthlyChange || 0
          };
        }

        // Handle array data
        if (!financeData || !Array.isArray(financeData)) {
          console.log('No valid finance data found, returning zeros');
          return { balance: 0, monthlyChange: 0 };
        }

        console.log('Processing finance array:', financeData);

        // Calculate balance from array data
        if (financeData.length === 0) {
          return { balance: 0, monthlyChange: 0 };
        }

        // Try different calculation methods
        const currentMonth = financeData[financeData.length - 1] || {};
        console.log('Current month data:', currentMonth);

        // Method 1: income - expenses
        let balance = (currentMonth.income || 0) - (currentMonth.expenses || 0);

        // Method 2: use balance field if available
        if (currentMonth.balance !== undefined) {
          balance = currentMonth.balance;
        }

        // Method 3: sum all months if it's cumulative data
        if (balance === 0 && financeData.length > 1) {
          balance = financeData.reduce((total, month) => {
            return total + ((month.income || 0) - (month.expenses || 0));
          }, 0);
          console.log('Calculated cumulative balance:', balance);
        }

        const monthlyChange = currentMonth.income || currentMonth.monthlyIncome || 0;

        console.log('Final finance calculation:', { balance, monthlyChange });

        return { balance, monthlyChange };
      } catch (error) {
        console.error('Error fetching finance data:', error);
        return { balance: 0, monthlyChange: 0 };
      }
    }
  });

  // Fetch tasks count
  const { data: tasksData } = useQuery({
    queryKey: ['dashboardTasks', selectedHoaId, selectedCommunityId, selectedCommunityIds, userCommunityId],
    queryFn: async () => {
      try {
        let endpoint;

        // Company admin can see all, filtered by selected HOA, or filtered by community/communities
        if (isCompanyAdmin) {
          if (selectedCommunityIds.length > 0) {
            // Multiple communities selected
            const communityParams = selectedCommunityIds.map(id => `communityId=${id}`).join('&');
            endpoint = `/api/tasks?${communityParams}`;
          } else if (selectedCommunityId) {
            // Single community selected
            endpoint = `/api/tasks?communityId=${selectedCommunityId}`;
          } else if (selectedHoaId) {
            endpoint = `/api/tasks?hoaId=${selectedHoaId}`;
          } else {
            endpoint = '/api/tasks';
          }
        }
        // Regular HOA admin always sees their HOA, or filtered by community/communities
        else if (isAdmin) {
          if (selectedCommunityIds.length > 0) {
            // Multiple communities selected (within their HOA)
            const communityParams = selectedCommunityIds.map(id => `communityId=${id}`).join('&');
            endpoint = `/api/tasks?${communityParams}`;
          } else if (selectedCommunityId) {
            // Single community selected
            endpoint = `/api/tasks?communityId=${selectedCommunityId}`;
          } else if (user.hoaId) {
            endpoint = `/api/tasks?hoaId=${user.hoaId}`;
          } else {
            endpoint = '/api/tasks';
          }
        }
        // Members only see their community
        else if (userCommunityId) {
          endpoint = `/api/tasks?communityId=${userCommunityId}`;
        }
        // Fallback
        else {
          endpoint = '/api/tasks';
        }

        console.log('Dashboard Tasks API Call:', {
          endpoint,
          selectedHoaId,
          selectedCommunityId,
          userCommunityId,
          userRole: user?.role
        });
        console.log('Fetching tasks using endpoint:', endpoint);
        const res = await api.get(endpoint);
        const tasks = res.data.tasks || [];

        console.log('Tasks data:', tasks.length);

        const openTasks = tasks.filter((task: any) =>
          task.status === 'Not Started' || task.status === 'In Progress'
        );

        const urgentTasks = openTasks.filter((task: any) =>
          task.priority === 'High'
        );

        return {
          total: openTasks.length,
          urgent: urgentTasks.length
        };
      } catch (error) {
        console.error('Error fetching tasks:', error);
        return { total: 0, urgent: 0 };
      }
    }
  });

  // Calculate next due date based on settings
  const getNextDueDate = () => {
    if (!settings?.dueDate?.day) {
      return {
        date: 'Not set',
        daysRemaining: 0
      };
    }

    const today = new Date();
    const dueDay = settings.dueDate.day;
    let nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, dueDay);

    // If today's date is before this month's due date, use this month
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), dueDay);
    if (today < thisMonth) {
      nextMonth = thisMonth;
    }

    const daysRemaining = Math.ceil((nextMonth.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    return {
      date: nextMonth.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      daysRemaining: Math.max(0, daysRemaining)
    };
  };

  const nextDue = getNextDueDate();

  // Define stats based on user role
  const companyAdminStats = [
    {
      title: 'Total HOAs',
      value: selectedHoaId ? '1' : (hoaRevenueSummary?.length || '0'),
      description: selectedHoaId ? 'Currently viewing 1 HOA' : 'Registered on the platform',
      icon: <Building2 className="h-5 w-5" />,
      onClick: () => navigate('/admin/all-hoas'),
      adminOnly: false
    },
    {
      title: 'Total Members',
      value: membersData?.total || '0',
      description: `${membersData?.pending || 0} pending approvals`,
      icon: <Users className="h-5 w-5" />,
      onClick: () => navigate('/admin/members'),
      adminOnly: false
    },
    {
      title: 'Platform Revenue',
      value: selectedHoaId ?
        `$${getSelectedHOARevenue().toLocaleString()}` :
        `$${calculateTotalRevenue().toLocaleString()}`,
      description: selectedHoaId ?
        `Total revenue from selected HOA` :
        `Total revenue from all HOAs`,
      icon: <DollarSign className="h-5 w-5" />,
      onClick: () => navigate('/finances'),
      adminOnly: false
    },
    {
      title: 'Pending Approvals',
      value: pendingHOAs?.length || '0',
      description: 'HOAs awaiting verification',
      icon: <AlertTriangle className="h-5 w-5" />,
      onClick: () => navigate('/admin/hoa-approvals'),
      adminOnly: false
    },
  ];

  // Define stats based on user role
  const adminStats = [
    {
      title: 'HOA Balance',
      value: `$${(financeData?.balance || 0).toLocaleString()}`,
      description: `+$${(financeData?.monthlyChange || 0).toLocaleString()} this month`,
      icon: <DollarSign className="h-5 w-5" />,
      onClick: () => navigate('/finances'),
      adminOnly: false
    },
    {
      title: 'Total Members',
      value: membersData?.total || '0',
      description: `${membersData?.pending || 0} pending approvals`,
      icon: <Users className="h-5 w-5" />,
      onClick: () => navigate('/admin/members'),
      adminOnly: false
    },
    {
      title: 'Open Tasks',
      value: tasksData?.total || '0',
      description: `${tasksData?.urgent || 0} requires immediate attention`,
      icon: <AlertTriangle className="h-5 w-5" />,
      onClick: () => navigate('/tasks'),
      adminOnly: false
    },
  ];

  // Regular admin and member stats
  const regularStats = [
    {
      title: 'HOA Balance',
      value: `$${(financeData?.balance || 0).toLocaleString()}`,
      description: `+$${(financeData?.monthlyChange || 0).toLocaleString()} this month`,
      icon: <DollarSign className="h-5 w-5" />,
      onClick: () => navigate('/finances'),
      adminOnly: false
    },
    {
      title: 'Due Date',
      value: nextDue.date,
      description: `${nextDue.daysRemaining} days remaining`,
      icon: <Calendar className="h-5 w-5" />,
      onClick: () => navigate('/settings'),
      adminOnly: false
    },
    {
      title: 'Open Tasks',
      value: tasksData?.total || '0',
      description: `${tasksData?.urgent || 0} requires immediate attention`,
      icon: <AlertTriangle className="h-5 w-5" />,
      onClick: () => navigate('/tasks'),
      adminOnly: false
    },
  ];

  // Choose which stats to display based on user role
  const stats = isCompanyAdmin ? companyAdminStats : isAdmin ? adminStats : regularStats;

  return (
    <div className="space-y-6 animate-in">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <h1 className="font-bold">
          {isCompanyAdmin
            ? (selectedHoaId
                ? "HOA Dashboard"
                : "Platform Overview")
            : "Dashboard"
          }
        </h1>
        <div className="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
          {isCompanyAdmin ? (
            <>
              <Button variant="outline" className="w-full sm:w-auto" onClick={() => navigate('/admin/all-hoas')}>
                Manage HOAs
              </Button>
              <Button variant="outline" className="w-full sm:w-auto" onClick={() => navigate('/admin/hoa-approvals')}>
                HOA Approvals
              </Button>
              <Button className="w-full sm:w-auto" onClick={() => navigate('/admin/management')}>
                Admin Management
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" className="w-full sm:w-auto" onClick={() => navigate('/finances/budget')}>
                Budget Planner
              </Button>
              <Button variant="outline" className="w-full sm:w-auto" onClick={() => navigate('/finances/reports')}>
                Financial Reports
              </Button>
              <Button className="w-full sm:w-auto" onClick={() => navigate('/payments')}>
                Make Payment
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Selectors - Only visible for admins */}
      {isAnyAdmin && (
        <div className="flex flex-col md:flex-row gap-4">
          {/* HOA Revenue Selector - Only visible for company admins */}
          {isCompanyAdmin && (
            <div className="w-full md:w-80">
              <HOARevenueSelector
                onHOAChange={setSelectedHoaId}
                selectedHOAId={selectedHoaId}
              />
            </div>
          )}
        </div>
      )}

      {/* Role Information Card */}
      <RoleInfoCard
        role={user.role}
        hoaName={hoaData?.hoaCommunityName || 'your HOA community'}
        selectedStreet={selectedCommunityName}
      />

      <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        {stats.filter(stat => !stat.adminOnly || isAdmin).map((stat, index) => (
          <Card key={index} className="hover:bg-accent cursor-pointer" onClick={stat.onClick}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3 sm:p-4 md:p-6 md:pb-2">
              <CardTitle className="text-xs sm:text-sm font-bold leading-tight">
                {stat.title}
              </CardTitle>
              <div className="flex-shrink-0">{stat.icon}</div>
            </CardHeader>
            <CardContent className="p-3 pt-0 sm:p-4 sm:pt-0 md:p-6 md:pt-0">
              <div className="text-lg sm:text-xl md:text-2xl font-bold">{stat.value}</div>
              <p className="text-xs sm:text-sm text-muted-foreground mt-1 line-clamp-2 leading-tight">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader className="p-4 md:p-6">
              <CardTitle>Tasks Overview</CardTitle>
              <CardDescription>Recent community tasks and their status</CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-0 md:p-6 md:pt-0">
              <TaskList
              limit={5}
              hoaId={selectedHoaId}
              communityId={isMultipleCommunities ? null : selectedCommunityId}
            />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="p-4 md:p-6">
              <CardTitle>Financial Overview</CardTitle>
              <CardDescription>Monthly income and expenses</CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-0 md:p-6 md:pt-0">
              <FinanceOverview
                hoaId={selectedHoaId}
                communityId={isMultipleCommunities ? null : selectedCommunityId}
              />
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader className="p-4 md:p-6">
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest updates and notifications</CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-0 md:p-6 md:pt-0">
              <NotificationCenter
                limit={10}
                hoaId={selectedHoaId}
                communityId={isMultipleCommunities ? null : selectedCommunityId}
              />
            </CardContent>
          </Card>

          <DocumentsWidget
            hoaId={selectedHoaId}
            communityId={isMultipleCommunities ? null : selectedCommunityId}
          />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

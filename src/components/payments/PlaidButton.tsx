import { usePlaidLink } from 'react-plaid-link';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import api from '@/lib/axios';
import PAYMENT_CONTEXT from '@/context/context';

const PlaidButton = ({ amount }) => {
    const [linkToken, setLinkToken] = useState<string | null>(null);

    useEffect(() => {
        // Create link token
        async function createLinkToken() {
            await api.post(PAYMENT_CONTEXT.PLAID_CREATE_LINK_TOKEN).then((res) => {
                setLinkToken(res.data.linkToken);
            })
        }

        createLinkToken();
    }, []);

    const onSuccess = async (linkToken: string) => {
        const res = await api.post(PAYMENT_CONTEXT.PLAID_EXCHANGE_PUBLIC_TOKEN, { 
            linkToken 
        });

        const { user, bankAccountId} = await res.data;

        // Charge HOA Payment
        const payment = await api.post(PAYMENT_CONTEXT.PLAID_PROCESS_PAYMENT, {
            bankAccountId,
            amount: amount
        });

        const data = await payment.data;
    }  

    const config = {
        token: linkToken,
        onSuccess
    }

    const { open, ready } = usePlaidLink(config); 

    return (
        <Button
            className="mt-4 ml-2" 
            onClick={() => open()} 
            disabled={!ready}
        >
            Connect with Plaid
        </Button>
    );
};

export default PlaidButton;
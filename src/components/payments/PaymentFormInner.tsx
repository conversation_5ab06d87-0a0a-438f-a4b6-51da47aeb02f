import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  CardElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import api from '@/lib/axios';
import { Loader2 } from 'lucide-react';
import PAYMENT_CONTEXT from '@/context/context';
import { set } from 'date-fns';

// The inner payment form that uses Stripe hooks
const PaymentFormInner = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingEnabledAutoPay, setIsLoadingEnabledAutoPay] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  // Fetch the monthly due amount
  const { data: dueData, isLoading: isDueLoading } = useQuery({
    queryKey: ['monthlyDue'],
    queryFn: async () => {
      const response = await api.get(PAYMENT_CONTEXT.STRIPE_MONTHLY_DUE);
      return response.data;
    }
  });

  // Create payment intent mutation
  const createIntentMutation = useMutation({
    mutationFn: async (amount: number) => {
      const response = await api.post(PAYMENT_CONTEXT.STRIPE_PAYMENT_INTENT, { amount });
      return response.data;
    }
  });

  // Process payment mutation
  const processPaymentMutation = useMutation({
    mutationFn: async ({ paymentIntentId, paymentMethod }: { paymentIntentId: string, paymentMethod: string }) => {
      const response = await api.post(PAYMENT_CONTEXT.STRIPE_PROCESS_PAYMENT, {
        paymentIntentId,
        paymentMethod
      });
      return response.data;
    },
    onSuccess: () => {
      // Invalidate relevant queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['paymentHistory'] });
      queryClient.invalidateQueries({ queryKey: ['monthlyFinance'] });
      queryClient.invalidateQueries({ queryKey: ['allFinance'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardFinance'] });
    }
  });

  // Auto-pay: SetupIntent
  const createSetupIntentMutation = useMutation({
    mutationFn: async () => {
      const response = await api.post(PAYMENT_CONTEXT.STRIPE_SETUP_INTENT);
      return response.data;
    },
  });

  // Auto-pay: Subscription
  const createSubscriptionMutation = useMutation({
    mutationFn: async (paymentMethodId: string) => {
      const response = await api.post(PAYMENT_CONTEXT.STRIPE_SUBSCRIPTION, { paymentMethodId });
      return response.data;
    },
    onSuccess: () => {
      toast({
        title: 'Auto-Pay Enabled',
        description: 'Recurring payments have been enabled successfully'
      });
      queryClient.invalidateQueries({ queryKey: ['monthlyDue'] });
    }
  });

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet
      return;
    }

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      return;
    }

    setIsLoading(true);
    setPaymentError(null);

    try {
      // 1. Create a payment intent
      const amount = dueData?.amount || 100;
      const intentResponse = await createIntentMutation.mutateAsync(amount);

      // 2. Confirm the payment with the card element
      const { error, paymentIntent } = await stripe.confirmCardPayment(
        intentResponse.clientSecret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              // You could collect billing details here if needed
            },
          },
        }
      );

      if (error) {
        setPaymentError(error.message || 'Payment failed');
        toast({
          variant: 'destructive',
          title: 'Payment Failed',
          description: error.message || 'Your payment could not be processed.',
        });
      } else if (paymentIntent.status === 'succeeded') {
        // 3. Process the successful payment on the server
        await processPaymentMutation.mutateAsync({
          paymentIntentId: paymentIntent.id,
          paymentMethod: 'credit'
        });

        setPaymentSuccess(true);
        toast({
          title: 'Payment Successful',
          description: `Your payment of $${amount} has been processed successfully.`,
        });
      }
    } catch (err) {
      setPaymentError(err.message || 'An unexpected error occurred');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'An unexpected error occurred.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnabledAutoPay = async () => {
    if (!stripe) return;

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) return;

    setIsLoadingEnabledAutoPay(true);
    setPaymentError(null);

    try {
      const { clientSecret } = await createSetupIntentMutation.mutateAsync();

      const { error, setupIntent } = await stripe.confirmCardSetup(
        clientSecret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              // You could collect billing details here if needed
            },
          },
        }
      );

      if (error || !setupIntent) {
        throw new Error(error?.message || 'Failed to setup payment method');
      }

      await createSubscriptionMutation.mutateAsync(setupIntent.payment_method as string);
      setPaymentSuccess(true);
    } catch (err) {
      setPaymentError(err.message || 'An error occurred while enabling auto-pay.');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'An error occurred while enabling auto-pay.',
      });
    } finally {
      setIsLoadingEnabledAutoPay(false);
    }
  }

  const getFullMonth = (month: number) => {
    const date = new Date();
    date.setMonth(month - 1);
    return date.toLocaleString('default', { month: 'long' });
  };

  if (paymentSuccess) {
    return (
      <div className="text-center py-8">
        <h3 className="text-xl font-semibold text-green-600 mb-2">Payment Successful!</h3>
        <p className="mb-4">Your monthly dues have been paid. Thank you!</p>
      </div>
    );
  }

  return (
    <div>
      {dueData?.amount !== 0 ? (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Card Details
            </label>
            <div className="border rounded-md p-3">
              <CardElement
                options={{
                  style: {
                    base: {
                      fontSize: '16px',
                      color: '#424770',
                      '::placeholder': {
                        color: '#aab7c4',
                      },
                    },
                    invalid: {
                      color: '#9e2146',
                    },
                  },
                }}
              />
            </div>
          </div>

          {paymentError && (
            <div className="text-red-500 text-sm">{paymentError}</div>
          )}

          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-muted-foreground">
                Pay for Month: <span className="font-semibold">{getFullMonth(dueData?.month) || 'January'}</span>/<span className="font-semibold">{dueData?.year || 2023}</span>
              </p>
              <p className="text-sm text-muted-foreground">
                Amount Due: <span className="font-semibold">${dueData?.amount}</span>
              </p>
              {/* <p className="text-sm text-muted-foreground">
                Auto Payment: <span className="font-semibold">{dueData?.autoPayment ? 'Enabled' : 'Disabled'}</span>
              </p> */}
              <Button
                type="submit"
                disabled={!stripe || isLoading || isDueLoading}
                className="min-w-[120px] mt-2"
              >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Pay One Time'
              )}
              </Button>
              {/* <Button
                type="button"
                className='min-w-[120px] mt-2 ml-2'
                onClick={handleEnabledAutoPay}
                disabled={dueData?.autoPayment}>
                {isLoadingEnabledAutoPay ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  'Enable Auto Pay'
                )}
              </Button> */}
            </div>
          </div>
        </form>
      ) : (
        <div className="text-center py-8">
          <h3 className="text-xl font-semibold mb-2">No Due Payment</h3>
          <p className="mb-4">You have no dues to pay for this month.</p>
        </div>
      )}
    </div>
  );
};

export default PaymentFormInner;
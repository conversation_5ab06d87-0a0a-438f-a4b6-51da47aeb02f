import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { format, set } from 'date-fns';
import api from '@/lib/axios';
import PAYMENT_CONTEXT from '@/context/context';
import { Separator } from '../ui/separator';
import { Button } from '../ui/button';
import { toast } from 'sonner';

interface Payment {
  _id: string;
  amount: number;
  month: number;
  year: number;
  paymentDate: string;
  paymentMethod: string;
  status: string;
  receiptNumber?: string;
}

interface PaymentHistoryProps {
  userId?: string,
  fromPropertyTab?: boolean
}

const PaymentHistory: React.FC<PaymentHistoryProps> = ({ userId, fromPropertyTab = false }) => {

  const[isNotifying, setIsNotifying] = React.useState(false);

  // Fetch payment history
  const { data, isLoading, isError } = useQuery({
    queryKey: ['paymentHistory'],
    queryFn: async () => {
      const response = await api.get(userId ? `${PAYMENT_CONTEXT.HISTORY}?userId=${userId}` : `${PAYMENT_CONTEXT.HISTORY}`);
      return response.data
    },
    staleTime: 1000 * 60 * 5 // cache for 5 minutes
  });

  const getMonthName = (month: number) => {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return monthNames[month - 1];
  };

  const getPaymentMethodLabel = (method: string) => {
    const methods: Record<string, string> = {
      'credit': 'Credit Card',
      'debit': 'Debit Card',
      'cash': 'Cash',
      'check': 'Check',
      'bank_transfer': 'Bank Transfer'
    };
    return methods[method] || method;
  };

  const notifyOwner = async () => {
    try {
      setIsNotifying(true);
      const response = await api.get('/api/properties/notifyOwner/' + userId);
      setIsNotifying(false);
      toast.success('Owner notified successfully');
    } catch (error) {
      console.error('Error notifying owner:', error);
      setIsNotifying(false);
      toast.error('Failed to notify owner');
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
          <CardDescription>Loading your payment history...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
          <CardDescription className="text-red-500">
            Error loading payment history. Please try again later.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment History</CardTitle>
        <CardDescription>
          View recent HOA dues payments
        </CardDescription>
      </CardHeader>
      <CardContent>
        {data?.payments?.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No payment history found for this property.
          </div>
        ) : (
          <div className="space-y-4">
            {data?.missedPaymentInfo?.missedPayment ? (
              <div className="py-2">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">
                    HOA Fee - {getMonthName(data.missedPaymentInfo.currentMonth)} {data.missedPaymentInfo.currentYear}
                  </h4>
                  <p className="font-semibold">${data.missedPaymentInfo.amountDue}</p>
                </div>
                <div className="text-right">
                  <Badge 
                    variant={'destructive'}
                    className="mt-1"
                  >
                    Past Due
                  </Badge>
                </div>
                {fromPropertyTab && <div>
                  <Button type='button' onClick={() => notifyOwner()}>{isNotifying ? 'Notifying...' : 'Notify Owner'}</Button>
                </div> }
                <div className="mt-4">
                  <Separator></Separator>
                </div>
              </div>
            ): null}
            {data?.payments.map((payment: Payment) => (
              <div 
                key={payment._id} 
                className="flex justify-between items-center border-b pb-4"
              >
                <div>
                  <h4 className="font-medium">
                    HOA Fee - {getMonthName(payment.month)} {payment.year}
                  </h4>
                  <div className="text-sm text-muted-foreground">
                    <p>Paid on {format(new Date(payment.paymentDate), 'MMM d, yyyy')}</p>
                    <p>Method: {getPaymentMethodLabel(payment.paymentMethod)}</p>
                    {payment.receiptNumber && (
                      <p>Receipt: #{payment.receiptNumber}</p>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold">${payment.amount}</p>
                  <Badge 
                    variant={payment.status === 'completed' ? 'default' : 'outline'}
                    className="mt-1"
                  >
                    {payment.status === 'completed' ? 'Paid' : payment.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PaymentHistory;

import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import PaymentFormInner from './PaymentFormInner';

// Initialize Stripe with your publishable key
const stripeKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
const stripePromise = stripeKey ? loadStripe(stripeKey) : null;

// Check if Stripe is configured
const isStripeConfigured = !!stripeKey;

// The outer component that provides the Stripe context
const PaymentForm = () => {
  if (!isStripeConfigured) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Payment Not Available</CardTitle>
          <CardDescription>
            Stripe payment processing is not configured
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center">
            <p className="mb-4">The payment system is currently unavailable.</p>
            <p className="text-sm text-muted-foreground">
              Please contact the administrator to set up Stripe payment processing.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment</CardTitle>
        <CardDescription>
          Pay your monthly HOA dues securely with a credit or debit card. 
          We do not store your card details in our system. The payment is processed directly by Stripe.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {stripePromise ? (
          <Elements stripe={stripePromise}>
            <PaymentFormInner />
          </Elements>
        ) : (
          <div className="py-8 text-center">
            <p>Loading payment system...</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex-col items-center">
        <Separator className="mb-4" />
        <div className="text-sm text-center text-muted-foreground">
          <p><a href="https://stripe.com/">Powered by Stripe</a></p>
        </div>
      </CardFooter>
    </Card>
  );
};

export default PaymentForm;

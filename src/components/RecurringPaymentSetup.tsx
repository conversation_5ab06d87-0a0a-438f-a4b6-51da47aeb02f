import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Calendar, DollarSign, User, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import api from '@/services/api';

interface RecurringPaymentSetupProps {
  hoaId: string;
  onSuccess?: () => void;
}

const RecurringPaymentSetup: React.FC<RecurringPaymentSetupProps> = ({ hoaId, onSuccess }) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    vendorName: '',
    vendorEmail: '',
    vendorPhone: '',
    amount: '',
    description: '',
    category: 'landscaping',
    frequency: 'biweekly',
    startDate: '',
    paymentMethod: 'bank_payout',
    bankAccount: {
      accountNumber: '',
      routingNumber: '',
      accountHolderName: '',
      bankName: ''
    }
  });

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('bankAccount.')) {
      const bankField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        bankAccount: {
          ...prev.bankAccount,
          [bankField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await api.post('/api/vendor-payouts/recurring', {
        hoaId,
        vendorName: formData.vendorName,
        vendorEmail: formData.vendorEmail,
        vendorPhone: formData.vendorPhone,
        amount: parseFloat(formData.amount),
        description: formData.description,
        category: formData.category,
        frequency: formData.frequency,
        startDate: formData.startDate,
        paymentMethod: formData.paymentMethod,
        bankAccount: formData.paymentMethod === 'bank_payout' ? formData.bankAccount : undefined
      });

      if (response.data.success) {
        toast({
          title: 'Recurring Payment Created',
          description: `Automated ${formData.frequency} payments to ${formData.vendorName} have been set up.`,
        });
        
        // Reset form
        setFormData({
          vendorName: '',
          vendorEmail: '',
          vendorPhone: '',
          amount: '',
          description: '',
          category: 'landscaping',
          frequency: 'biweekly',
          startDate: '',
          paymentMethod: 'bank_payout',
          bankAccount: {
            accountNumber: '',
            routingNumber: '',
            accountHolderName: '',
            bankName: ''
          }
        });

        onSuccess?.();
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to create recurring payment',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Set Up Recurring Vendor Payment
        </CardTitle>
        <CardDescription>
          Automate payments to vendors like lawn care, maintenance, and other services
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Vendor Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Vendor Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="vendorName">Vendor Name *</Label>
                <Input
                  id="vendorName"
                  value={formData.vendorName}
                  onChange={(e) => handleInputChange('vendorName', e.target.value)}
                  placeholder="e.g., Green Lawn Services"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="vendorEmail">Email</Label>
                <Input
                  id="vendorEmail"
                  type="email"
                  value={formData.vendorEmail}
                  onChange={(e) => handleInputChange('vendorEmail', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <Label htmlFor="vendorPhone">Phone</Label>
                <Input
                  id="vendorPhone"
                  value={formData.vendorPhone}
                  onChange={(e) => handleInputChange('vendorPhone', e.target.value)}
                  placeholder="(*************"
                />
              </div>
              
              <div>
                <Label htmlFor="category">Service Category</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="landscaping">Landscaping</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                    <SelectItem value="utilities">Utilities</SelectItem>
                    <SelectItem value="legal">Legal</SelectItem>
                    <SelectItem value="insurance">Insurance</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Payment Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Payment Details
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="amount">Amount *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) => handleInputChange('amount', e.target.value)}
                  placeholder="150.00"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="frequency">Payment Frequency *</Label>
                <Select value={formData.frequency} onValueChange={(value) => handleInputChange('frequency', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="biweekly">Every 2 Weeks</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="startDate">Start Date *</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  required
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="e.g., Bi-weekly lawn maintenance and landscaping services"
                required
              />
            </div>
          </div>

          {/* Bank Account Information */}
          {formData.paymentMethod === 'bank_payout' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Vendor Bank Account</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="accountHolderName">Account Holder Name *</Label>
                  <Input
                    id="accountHolderName"
                    value={formData.bankAccount.accountHolderName}
                    onChange={(e) => handleInputChange('bankAccount.accountHolderName', e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="bankName">Bank Name</Label>
                  <Input
                    id="bankName"
                    value={formData.bankAccount.bankName}
                    onChange={(e) => handleInputChange('bankAccount.bankName', e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="routingNumber">Routing Number *</Label>
                  <Input
                    id="routingNumber"
                    value={formData.bankAccount.routingNumber}
                    onChange={(e) => handleInputChange('bankAccount.routingNumber', e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="accountNumber">Account Number *</Label>
                  <Input
                    id="accountNumber"
                    value={formData.bankAccount.accountNumber}
                    onChange={(e) => handleInputChange('bankAccount.accountNumber', e.target.value)}
                    required
                  />
                </div>
              </div>
            </div>
          )}

          <div className="flex gap-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? 'Creating...' : 'Create Recurring Payment'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default RecurringPaymentSetup;

import React from 'react';
import { format } from 'date-fns';
import { Pencil, Trash2, MoreVertical, X, Check, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

interface MessageBubbleProps {
  message: {
    _id: string;
    content: string;
    createdAt: string;
    edited?: boolean;
    sender: {
      _id: string;
    };
  };
  isCurrentUser: boolean;
  isEditing: boolean;
  editedContent: string;
  editInputRef: React.RefObject<HTMLTextAreaElement>;
  setEditedContent: (content: string) => void;
  saveEditedMessage: () => void;
  cancelEditingMessage: () => void;
  startEditingMessage: (message: any) => void;
  handleDeleteMessage: (messageId: string) => void;
  isPending: boolean;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isCurrentUser,
  isEditing,
  editedContent,
  editInputRef,
  setEditedContent,
  saveEditedMessage,
  cancelEditingMessage,
  startEditingMessage,
  handleDeleteMessage,
  isPending
}) => {
  return (
    <div
      key={message._id}
      className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
    >
      <div
        className={`max-w-[70%] rounded-lg p-3 relative group ${
          isCurrentUser
            ? 'bg-primary text-primary-foreground'
            : 'bg-accent'
        }`}
      >
        {isEditing ? (
          <div className="space-y-2">
            <Textarea
              ref={editInputRef}
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="min-h-[80px] text-sm bg-background text-foreground"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                  e.preventDefault();
                  saveEditedMessage();
                } else if (e.key === 'Escape') {
                  e.preventDefault();
                  cancelEditingMessage();
                }
              }}
            />
            <div className="flex justify-end gap-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={cancelEditingMessage}
              >
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={saveEditedMessage}
                disabled={!editedContent.trim() || isPending}
              >
                {isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-1" />
                ) : (
                  <Check className="h-4 w-4 mr-1" />
                )}
                Save
              </Button>
            </div>
          </div>
        ) : (
          <>
            {isCurrentUser && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => startEditingMessage(message)}>
                    <Pencil className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleDeleteMessage(message._id)}
                    className="text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            <div className="whitespace-pre-line break-words">
              {message.content}
            </div>
            <div className="text-xs mt-1 opacity-70 flex items-center gap-1">
              {format(new Date(message.createdAt), 'MMM d, h:mm a')}
              {message.edited && (
                <span className="italic">(edited)</span>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default MessageBubble;

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Loader2, FileText, Download, ExternalLink } from 'lucide-react';
import { format } from 'date-fns';
import api from '@/lib/axios';

interface BillingHistoryProps {
  hoaId: string;
}

interface SubscriptionPayment {
  _id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'succeeded' | 'failed' | 'refunded';
  billingPeriodStart: string;
  billingPeriodEnd: string;
  stripeInvoiceId: string;
  receiptUrl: string;
  receiptNumber: string;
  createdAt: string;
}

const BillingHistory: React.FC<BillingHistoryProps> = ({ hoaId }) => {
  // Fetch billing history
  const { data, isLoading, isError } = useQuery({
    queryKey: ['billingHistory', hoaId],
    queryFn: async () => {
      try {
        const response = await api.get(`/api/subscriptions/hoa/${hoaId}/payments`);
        return response.data;
      } catch (error) {
        console.error('Error fetching billing history:', error);
        return { payments: [] };
      }
    },
    enabled: !!hoaId
  });
  
  const payments = data?.payments || [];
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>
            View your subscription payment history
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            Error loading billing history. Please try again later.
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Billing History</CardTitle>
        <CardDescription>
          View your subscription payment history
        </CardDescription>
      </CardHeader>
      <CardContent>
        {payments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No billing history found. Payments will appear here once processed.
          </div>
        ) : (
          <div className="space-y-6">
            {payments.map((payment: SubscriptionPayment) => (
              <div key={payment._id} className="border rounded-md p-4">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="font-medium">
                      {format(new Date(payment.createdAt), 'MMMM d, yyyy')} Payment
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Receipt #{payment.receiptNumber}
                    </p>
                  </div>
                  <Badge 
                    variant={
                      payment.status === 'succeeded' ? 'default' : 
                      payment.status === 'failed' ? 'destructive' : 
                      'outline'
                    }
                  >
                    {payment.status === 'succeeded' ? 'Paid' : 
                     payment.status === 'failed' ? 'Failed' : 
                     payment.status}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Billing Period</p>
                    <p>
                      {format(new Date(payment.billingPeriodStart), 'MMM d, yyyy')} - {' '}
                      {format(new Date(payment.billingPeriodEnd), 'MMM d, yyyy')}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Amount</p>
                    <p className="font-medium">
                      ${payment.amount} {payment.currency.toUpperCase()}
                    </p>
                  </div>
                </div>
                
                {payment.receiptUrl && (
                  <div className="flex justify-end">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => window.open(payment.receiptUrl, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View Receipt
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BillingHistory;

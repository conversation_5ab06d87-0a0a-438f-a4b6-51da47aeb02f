import React, { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { createHOA } from '@/services/hoaService';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Loader2, Building2, Copy, CheckCircle, CreditCard, ChevronDown } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
const US_STATES = [
  { code: 'AL', name: 'Alabama' },
  { code: 'AK', name: 'Alaska' },
  { code: 'AZ', name: 'Arizona' },
  { code: 'AR', name: 'Arkansas' },
  { code: 'CA', name: 'California' },
  { code: 'CO', name: 'Colorado' },
  { code: 'CT', name: 'Connecticut' },
  { code: 'DE', name: 'Delaware' },
  { code: 'FL', name: 'Florida' },
  { code: 'GA', name: 'Georgia' },
  { code: 'HI', name: 'Hawaii' },
  { code: 'ID', name: 'Idaho' },
  { code: 'IL', name: 'Illinois' },
  { code: 'IN', name: 'Indiana' },
  { code: 'IA', name: 'Iowa' },
  { code: 'KS', name: 'Kansas' },
  { code: 'KY', name: 'Kentucky' },
  { code: 'LA', name: 'Louisiana' },
  { code: 'ME', name: 'Maine' },
  { code: 'MD', name: 'Maryland' },
  { code: 'MA', name: 'Massachusetts' },
  { code: 'MI', name: 'Michigan' },
  { code: 'MN', name: 'Minnesota' },
  { code: 'MS', name: 'Mississippi' },
  { code: 'MO', name: 'Missouri' },
  { code: 'MT', name: 'Montana' },
  { code: 'NE', name: 'Nebraska' },
  { code: 'NV', name: 'Nevada' },
  { code: 'NH', name: 'New Hampshire' },
  { code: 'NJ', name: 'New Jersey' },
  { code: 'NM', name: 'New Mexico' },
  { code: 'NY', name: 'New York' },
  { code: 'NC', name: 'North Carolina' },
  { code: 'ND', name: 'North Dakota' },
  { code: 'OH', name: 'Ohio' },
  { code: 'OK', name: 'Oklahoma' },
  { code: 'OR', name: 'Oregon' },
  { code: 'PA', name: 'Pennsylvania' },
  { code: 'RI', name: 'Rhode Island' },
  { code: 'SC', name: 'South Carolina' },
  { code: 'SD', name: 'South Dakota' },
  { code: 'TN', name: 'Tennessee' },
  { code: 'TX', name: 'Texas' },
  { code: 'UT', name: 'Utah' },
  { code: 'VT', name: 'Vermont' },
  { code: 'VA', name: 'Virginia' },
  { code: 'WA', name: 'Washington' },
  { code: 'WV', name: 'West Virginia' },
  { code: 'WI', name: 'Wisconsin' },
  { code: 'WY', name: 'Wyoming' }
];

const HOARegistration = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    hoaCommunityName: '',
    hoaStreetAddress: '',
    hoaCity: '',
    hoaState: '',
    hoaZipCode: '',
    hoaPaymentInfo: {
      paymentMethod: 'check',
      paymentInstructions: '',
      dueDate: 1,
      paymentAmount: 0
    },
    subscription: {
      tier: 'basic',
      unitCount: 50
    }
  });
  const [generatedCode, setGeneratedCode] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('details');

  // Subscription tier information
  const TIERS = {
    basic: {
      name: 'Basic',
      description: 'For small HOAs with up to 50 units',
      price: 150,
      maxUnits: 50,
      features: [
        'All core features',
        'Up to 50 units',
        'Basic support',
        'Document management',
        'Task management'
      ]
    },
    standard: {
      name: 'Standard',
      description: 'For medium HOAs with up to 150 units',
      price: 350,
      maxUnits: 150,
      features: [
        'All Basic features',
        'Up to 150 units',
        'Priority support',
        'Advanced reporting',
        'Community management'
      ]
    },
    premium: {
      name: 'Premium',
      description: 'For large HOAs with up to 300 units',
      price: 600,
      maxUnits: 300,
      features: [
        'All Standard features',
        'Up to 300 units',
        'Premium support',
        'Advanced analytics',
        'Custom branding'
      ]
    },
    enterprise: {
      name: 'Enterprise',
      description: 'For very large HOAs with unlimited units',
      price: 800,
      maxUnits: 1000,
      features: [
        'All Premium features',
        'Unlimited units',
        'Dedicated support',
        'Custom integrations',
        'White-label solution'
      ]
    }
  };

  const createHOAMutation = useMutation({
    mutationFn: createHOA,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['hoas'] });
      setGeneratedCode(data.hoaCommunityCode);
      toast({
        title: 'HOA Created',
        description: `Successfully created ${data.hoaCommunityName} with code ${data.hoaCommunityCode}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create HOA',
        variant: 'destructive',
      });
    }
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name.startsWith('hoaPaymentInfo.')) {
      const paymentInfoField = name.split('.')[1];
      setFormData({
        ...formData,
        hoaPaymentInfo: {
          ...formData.hoaPaymentInfo,
          [paymentInfoField]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSelectChange = (value: string) => {
    setFormData({
      ...formData,
      hoaPaymentInfo: {
        ...formData.hoaPaymentInfo,
        paymentMethod: value
      }
    });
  };

  const handleDueDateChange = (value: string) => {
    setFormData({
      ...formData,
      hoaPaymentInfo: {
        ...formData.hoaPaymentInfo,
        dueDate: parseInt(value, 10)
      }
    });
  };

  const handleTierChange = (tier: string) => {
    // Update the tier and set the default unit count for that tier
    setFormData({
      ...formData,
      subscription: {
        ...formData.subscription,
        tier,
        unitCount: TIERS[tier as keyof typeof TIERS].maxUnits / 2 // Default to half the max units
      }
    });
  };

  const handleUnitCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const unitCount = parseInt(e.target.value, 10);
    setFormData({
      ...formData,
      subscription: {
        ...formData.subscription,
        unitCount: isNaN(unitCount) ? 0 : unitCount
      }
    });
  };

  const handleStateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      hoaState: value
    });
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createHOAMutation.mutate(formData);
  };

  const copyToClipboard = () => {
    if (generatedCode) {
      navigator.clipboard.writeText(generatedCode);
      toast({
        title: 'Copied',
        description: 'HOA code copied to clipboard',
      });
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-6 w-6" />
          Register New HOA Community
        </CardTitle>
        <CardDescription>
          Create a new HOA community and generate a unique registration code
        </CardDescription>
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md text-sm">
          <p className="font-medium mb-1">HOA Registration Process:</p>
          <ol className="list-decimal pl-5 space-y-1">
            <li>Fill out this form to register your HOA</li>
            <li>Your registration will be reviewed by company administrators</li>
            <li>Once approved, you'll receive a community code to share with your HOA administrators</li>
            <li>HOA administrators can then register using the resident registration form with this code</li>
          </ol>
          <p className="mt-2 text-sm font-medium text-blue-700">Note: You must be logged in as a company administrator to register an HOA.</p>
        </div>
      </CardHeader>
      <CardContent>
        {generatedCode && (
          <Alert className="mb-6 bg-green-50 border-green-200">
            <AlertTitle className="text-green-800 flex items-center gap-2">
              HOA Registration Code
              <Button
                variant="outline"
                size="icon"
                className="h-6 w-6 ml-2"
                onClick={copyToClipboard}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </AlertTitle>
            <AlertDescription className="text-green-700 font-mono text-lg">
              {generatedCode}
            </AlertDescription>
            <p className="mt-2 text-sm text-green-700">
              Share this code with the HOA administrator. They should use this code when registering through the resident registration form to be assigned admin privileges.
            </p>
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="details">
                <Building2 className="h-4 w-4 mr-2" />
                HOA Details
              </TabsTrigger>
              <TabsTrigger value="subscription">
                <CreditCard className="h-4 w-4 mr-2" />
                Subscription
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="mt-0">
              <div className="grid gap-6">
                <div className="grid gap-3">
                  <Label htmlFor="hoaCommunityName">HOA Community Name</Label>
                  <Input
                    id="hoaCommunityName"
                    name="hoaCommunityName"
                    value={formData.hoaCommunityName}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="hoaStreetAddress">Street Address</Label>
                  <Input
                    id="hoaStreetAddress"
                    name="hoaStreetAddress"
                    value={formData.hoaStreetAddress}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="grid gap-3">
                    <Label htmlFor="hoaCity">City</Label>
                    <Input
                      id="hoaCity"
                      name="hoaCity"
                      value={formData.hoaCity}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="hoaState">State</Label>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-between"
                        >
                          {formData.hoaState
                            ? US_STATES.find(
                                (state) => state.code === formData.hoaState
                              )?.name
                            : 'Select a state'}
                          <ChevronDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </DropdownMenuTrigger>
                      {US_STATES.map((state) => (
                        <DropdownMenuContent key={state.code}>
                          <DropdownMenuItem
                            onClick={() => handleStateChange(state.code)}
                          >
                            {state.name}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      ))} 
                          )
                    </DropdownMenu>
                    {/* <select
                      id="hoaState"
                      name="hoaState"
                      value={formData.hoaState}
                      onChange={handleStateChange}
                      required
                      className="border border-gray-300 rounded-md p-2 bg-white text-gray-900"
                    >
                      <option value="">Select a state</option>
                      {US_STATES.map((state) => (
                        <option key={state.code} value={state.code}>
                          {state.name}
                        </option>
                      ))}
                    </select> */}
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="hoaZipCode">Zip Code</Label>
                    <Input
                      id="hoaZipCode"
                      name="hoaZipCode"
                      value={formData.hoaZipCode}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="paymentMethod">Payment Method</Label>
                  <Select
                    value={formData.hoaPaymentInfo.paymentMethod}
                    onValueChange={handleSelectChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="check">Check</SelectItem>
                      <SelectItem value="bankTransfer">Bank Transfer</SelectItem>
                      <SelectItem value="online">Online Payment</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="paymentInstructions">Payment Instructions</Label>
                  <Input
                    id="paymentInstructions"
                    name="hoaPaymentInfo.paymentInstructions"
                    value={formData.hoaPaymentInfo.paymentInstructions}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="dueDate">Payment Due Date (Day of Month)</Label>
                  <Select
                    value={formData.hoaPaymentInfo.dueDate.toString()}
                    onValueChange={handleDueDateChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select due date" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                        <SelectItem key={day} value={day.toString()}>
                          {day}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-end">
                  <Button
                    type="button"
                    onClick={() => setActiveTab('subscription')}
                  >
                    Next: Choose Subscription
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="subscription" className="mt-0">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Choose a Subscription Plan</h3>
                  <p className="text-muted-foreground mb-4">
                    Select a subscription tier based on the number of units in your HOA.
                  </p>
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-blue-700 mb-4">
                    <div className="flex items-start">
                      <div className="mr-2 mt-0.5">
                        <CheckCircle className="h-5 w-5 text-blue-500" />
                      </div>
                      <div>
                        <p className="font-medium">30-Day Free Trial with All Plans</p>
                        <p className="text-sm mt-1">
                          All subscription plans include a 30-day free trial. You won't be charged until the trial period ends.
                          You can cancel anytime during the trial period.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(TIERS).map(([key, tier]) => (
                    <Card
                      key={key}
                      className={`cursor-pointer transition-all ${formData.subscription.tier === key ? 'ring-2 ring-primary' : 'hover:shadow-md'}`}
                      onClick={() => handleTierChange(key)}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-center">
                          <CardTitle>{tier.name}</CardTitle>
                          {formData.subscription.tier === key && (
                            <Badge variant="default">Selected</Badge>
                          )}
                        </div>
                        <CardDescription>{tier.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center">
                          <p className="text-2xl font-bold">${tier.price}<span className="text-sm font-normal">/month</span></p>
                          <Badge variant="outline" className="ml-2 text-blue-500 border-blue-200">30-day free</Badge>
                        </div>
                        <ul className="mt-4 space-y-2">
                          {tier.features.map((feature, index) => (
                            <li key={index} className="flex items-center">
                              <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                              <span className="text-sm">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="unitCount">Number of Units</Label>
                  <Input
                    id="unitCount"
                    type="number"
                    min="1"
                    max={TIERS[formData.subscription.tier as keyof typeof TIERS].maxUnits}
                    value={formData.subscription.unitCount}
                    onChange={handleUnitCountChange}
                    required
                  />
                  <p className="text-sm text-muted-foreground">
                    Maximum units for {TIERS[formData.subscription.tier as keyof typeof TIERS].name} tier: {TIERS[formData.subscription.tier as keyof typeof TIERS].maxUnits}
                  </p>
                </div>

                <div className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setActiveTab('details')}
                  >
                    Back to Details
                  </Button>
                  <Button
                    type="submit"
                    onClick={handleSubmit}
                    disabled={createHOAMutation.isPending}
                  >
                    {createHOAMutation.isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Register HOA
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </form>
      </CardContent>
      <CardFooter className="flex justify-end">
        {activeTab === 'details' && (
          <Button
            type="button"
            onClick={() => setActiveTab('subscription')}
          >
            Next: Choose Subscription
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default HOARegistration;

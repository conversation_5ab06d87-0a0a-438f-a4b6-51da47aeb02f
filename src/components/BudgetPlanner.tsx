import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Loader2, Plus, Save, Trash2, FileText, DollarSign, BarChart3 } from 'lucide-react';
import api from '@/lib/axios';

// Budget categories for reference
const BUDGET_CATEGORIES = {
  income: [
    'Membership Dues',
    'Special Assessments',
    'Late Fees',
    'Interest Income',
    'Rental Income',
    'Other Income'
  ],
  expense: [
    'Landscaping',
    'Pool Maintenance',
    'Insurance',
    'Utilities',
    'Reserve Fund',
    'Administrative',
    'Repairs & Maintenance',
    'Security',
    'Legal & Professional',
    'Management Fees',
    'Cleaning Services',
    'Snow Removal',
    'Pest Control',
    'Trash Removal',
    'Other Expenses'
  ]
};

interface LineItem {
  id?: string;
  _id?: string;
  category: string;
  description: string;
  type: 'income' | 'expense';
  budgetedAmount: number;
  actualAmount: number;
  variance: number;
  notes?: string;
}

interface Budget {
  id?: string;
  _id?: string;
  name: string;
  description?: string;
  year: number;
  status: 'draft' | 'active' | 'archived';
  hoaId?: string;
  communityId?: string;
  createdBy?: string;
  updatedBy?: string;
  totalBudgetedIncome: number;
  totalBudgetedExpenses: number;
  totalActualIncome: number;
  totalActualExpenses: number;
  lineItems: LineItem[];
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

const BudgetPlanner: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get user data to determine HOA ID and role FIRST
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const userHoaId = user.hoaId;
  const isCompanyAdmin = user.role === 'company_admin';

  const [selectedBudgetId, setSelectedBudgetId] = useState<string | null>(null);
  const [isAddingBudget, setIsAddingBudget] = useState(false);
  const [isAddingLineItem, setIsAddingLineItem] = useState(false);
  const [newBudget, setNewBudget] = useState<Partial<Budget>>({
    name: '',
    year: new Date().getFullYear(),
    status: 'draft',
    lineItems: []
  });
  const [newLineItem, setNewLineItem] = useState<Partial<LineItem>>({
    category: '',
    description: '',
    type: 'expense',
    budgetedAmount: 0,
    actualAmount: 0,
    variance: 0
  });

  // Fetch HOAs for company admin only
  const { data: hoas = [] } = useQuery({
    queryKey: ['hoas'],
    queryFn: async () => {
      try {
        const response = await api.get('/api/hoa');
        return response.data.data || [];
      } catch (error) {
        console.error('Error fetching HOAs:', error);
        return [];
      }
    },
    enabled: isCompanyAdmin // Only fetch if user is company admin
  });

  // Fetch budgets from the API
  const { data: budgets = [], isLoading, error } = useQuery({
    queryKey: ['budgets', userHoaId, isCompanyAdmin],
    queryFn: async () => {
      try {
        let response;

        // Company admins can see all budgets or fetch by selected HOA
        if (isCompanyAdmin) {
          console.log('Fetching all budgets for company admin');
          try {
            // Try the budgets endpoint first
            response = await api.get('/api/budgets');
          } catch (err) {
            console.error('Error with /api/budgets endpoint:', err);
            // Try the finances/budgets endpoint as fallback
            try {
              console.log('Trying finances/budgets endpoint');
              response = await api.get('/api/finances/budgets');
            } catch (fallbackErr) {
              console.error('Error with fallback endpoint:', fallbackErr);
              // Return empty array if both endpoints fail
              return [];
            }
          }
        } else if (userHoaId) {
          console.log(`Fetching budgets for HOA: ${userHoaId}`);
          try {
            // Try the budgets/hoa endpoint first
            response = await api.get(`/api/budgets/hoa/${userHoaId}`);
          } catch (err) {
            console.error(`Error with /api/budgets/hoa/${userHoaId} endpoint:`, err);
            // Try the finances/budgets/hoa endpoint as fallback
            try {
              console.log('Trying finances/budgets/hoa endpoint');
              response = await api.get(`/api/finances/budgets/hoa/${userHoaId}`);
            } catch (fallbackErr) {
              console.error('Error with fallback endpoint:', fallbackErr);
              // Return empty array if both endpoints fail
              return [];
            }
          }
        } else {
          throw new Error('No HOA ID found');
        }

        console.log('Budget response:', response.data);
        // Handle different response formats
        if (Array.isArray(response.data)) {
          return response.data;
        } else if (response.data.budgets) {
          return response.data.budgets;
        } else if (response.data.data) {
          return response.data.data;
        }
        return [];
      } catch (error) {
        console.error('Error fetching budgets:', error);
        return [];
      }
    },
    enabled: isCompanyAdmin || !!userHoaId,
    retry: 1, // Only retry once to avoid excessive failed requests
    retryDelay: 1000 // Wait 1 second before retrying
  });

  // Set the first budget as selected by default
  useEffect(() => {
    if (budgets.length > 0 && !selectedBudgetId) {
      setSelectedBudgetId(budgets[0].id);
    }
  }, [budgets, selectedBudgetId]);

  // Get the selected budget
  const selectedBudget = budgets.find(budget => budget.id === selectedBudgetId) || null;

  // Create budget mutation
  const createBudgetMutation = useMutation({
    mutationFn: async (budget: Partial<Budget>) => {
      // For company admins, we need to ensure an HOA ID is selected
      if (isCompanyAdmin && !budget.hoaId) {
        throw new Error('Please select an HOA for this budget');
      }

      // Add HOA ID to the budget
      const budgetWithHoaId = {
        ...budget,
        hoaId: budget.hoaId || userHoaId, // Use provided HOA ID or user's HOA ID
        totalBudgetedIncome: 0,
        totalBudgetedExpenses: 0,
        totalActualIncome: 0,
        totalActualExpenses: 0,
        lineItems: []
      };

      console.log('Creating budget with data:', budgetWithHoaId);

      try {
        const response = await api.post('/api/budgets', budgetWithHoaId);
        return response.data.budget;
      } catch (error) {
        console.error('Error creating budget:', error);

        // Check if the endpoint exists
        if (error.response?.status === 404) {
          throw new Error('Budget API endpoint not available. Please contact support.');
        }

        throw error;
      }
    },
    onSuccess: () => {
      toast({
        title: 'Budget Created',
        description: 'The budget has been created successfully.',
      });
      setIsAddingBudget(false);
      setNewBudget({
        name: '',
        year: new Date().getFullYear(),
        status: 'draft',
        lineItems: []
      });
      queryClient.invalidateQueries({ queryKey: ['budgets'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create budget',
        variant: 'destructive',
      });
    }
  });

  // Add line item mutation
  const addLineItemMutation = useMutation({
    mutationFn: async (lineItem: Partial<LineItem>) => {
      if (!selectedBudgetId) {
        throw new Error('No budget selected');
      }

      // Get the current budget
      const currentBudget = budgets.find(b => b._id === selectedBudgetId || b.id === selectedBudgetId);
      if (!currentBudget) {
        throw new Error('Selected budget not found');
      }

      // Add the new line item to the budget
      const updatedLineItems = [
        ...(currentBudget.lineItems || []),
        {
          ...lineItem,
          id: Math.random().toString(36).substring(7),
          actualAmount: 0,
          variance: -lineItem.budgetedAmount || 0
        }
      ];

      // Calculate new totals
      const totalBudgetedIncome = updatedLineItems
        .filter(item => item.type === 'income')
        .reduce((sum, item) => sum + (item.budgetedAmount || 0), 0);

      const totalBudgetedExpenses = updatedLineItems
        .filter(item => item.type === 'expense')
        .reduce((sum, item) => sum + (item.budgetedAmount || 0), 0);

      console.log('Updating budget with new line item:', {
        budgetId: selectedBudgetId,
        lineItems: updatedLineItems.length,
        totalBudgetedIncome,
        totalBudgetedExpenses
      });

      try {
        // Update the budget with the new line item
        // Use _id if available (from MongoDB) or fallback to id
        const budgetId = currentBudget._id || selectedBudgetId;
        const response = await api.put(`/api/budgets/${budgetId}`, {
          lineItems: updatedLineItems,
          totalBudgetedIncome,
          totalBudgetedExpenses
        });

        return response.data.budget;
      } catch (error) {
        console.error('Error adding line item:', error);

        // Check if the endpoint exists
        if (error.response?.status === 404) {
          throw new Error('Budget API endpoint not available. Please contact support.');
        }

        throw error;
      }
    },
    onSuccess: () => {
      toast({
        title: 'Line Item Added',
        description: 'The budget line item has been added successfully.',
      });
      setIsAddingLineItem(false);
      setNewLineItem({
        category: '',
        description: '',
        type: 'expense',
        budgetedAmount: 0,
        actualAmount: 0,
        variance: 0
      });
      queryClient.invalidateQueries({ queryKey: ['budgets'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add line item',
        variant: 'destructive',
      });
    }
  });

  const handleCreateBudget = () => {
    if (!newBudget.name || !newBudget.year) {
      toast({
        title: 'Missing Fields',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    createBudgetMutation.mutate(newBudget as Budget);
  };

  const handleAddLineItem = () => {
    if (!newLineItem.category || !newLineItem.type || newLineItem.budgetedAmount === undefined) {
      toast({
        title: 'Missing Fields',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    addLineItemMutation.mutate(newLineItem as LineItem);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2">Loading budgets...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-500 mb-4">Error loading budgets</div>
        <p className="text-sm text-muted-foreground mb-4 max-w-md text-center">
          {error instanceof Error
            ? error.message
            : "There was a problem connecting to the budget service. This feature may not be available yet."}
        </p>
        <div className="flex gap-4">
          <Button onClick={() => queryClient.invalidateQueries({ queryKey: ['budgets'] })}>
            Try Again
          </Button>
          <Button variant="outline" onClick={() => navigate('/finances')}>
            Back to Finances
          </Button>
        </div>
      </div>
    );
  }

  // Company admins don't need an HOA ID
  if (!isCompanyAdmin && !userHoaId) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-amber-500 mb-4">No HOA association found</div>
        <p className="text-sm text-muted-foreground mb-4">
          You need to be associated with an HOA to manage budgets.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">
          Budget Planner
        </h2>
        {!isCompanyAdmin && (
          <Button onClick={() => setIsAddingBudget(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            New Budget
          </Button>
        )}
      </div>

      {budgets.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium mb-2">No Budgets Found</h3>
            <p className="text-gray-500 mb-4">Create your first budget to start planning your HOA finances.</p>
            <Button onClick={() => setIsAddingBudget(true)}>Create Budget</Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Budgets</CardTitle>
                <CardDescription>Select a budget to view or edit</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {budgets.map(budget => (
                    <Button
                      key={budget.id}
                      variant={selectedBudgetId === budget.id ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => setSelectedBudgetId(budget.id || null)}
                    >
                      <div className="flex flex-col items-start">
                        <span>{budget.name}</span>
                        <span className="text-xs text-gray-500">{budget.year} - {budget.status}</span>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="lg:col-span-3">
            {selectedBudget ? (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>{selectedBudget.name}</CardTitle>
                    <CardDescription>
                      {selectedBudget.year} - {selectedBudget.status.charAt(0).toUpperCase() + selectedBudget.status.slice(1)}
                    </CardDescription>
                  </div>
                  {!isCompanyAdmin && (
                    <Button variant="outline" onClick={() => setIsAddingLineItem(true)} className="flex items-center gap-2">
                      <Plus className="h-4 w-4" />
                      Add Line Item
                    </Button>
                  )}
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="overview">
                    <TabsList className="mb-4">
                      <TabsTrigger value="overview" className="flex items-center gap-2">
                        <BarChart3 className="h-4 w-4" />
                        Overview
                      </TabsTrigger>
                      <TabsTrigger value="income" className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Income
                      </TabsTrigger>
                      <TabsTrigger value="expenses" className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Expenses
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-gray-500">Total Budgeted</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <p className="text-sm text-gray-500">Income</p>
                                <p className="text-2xl font-bold text-green-600">${selectedBudget.totalBudgetedIncome.toLocaleString()}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">Expenses</p>
                                <p className="text-2xl font-bold text-red-600">${selectedBudget.totalBudgetedExpenses.toLocaleString()}</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-gray-500">Total Actual</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <p className="text-sm text-gray-500">Income</p>
                                <p className="text-2xl font-bold text-green-600">${selectedBudget.totalActualIncome.toLocaleString()}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">Expenses</p>
                                <p className="text-2xl font-bold text-red-600">${selectedBudget.totalActualExpenses.toLocaleString()}</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>

                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Category</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead className="text-right">Budgeted</TableHead>
                            <TableHead className="text-right">Actual</TableHead>
                            <TableHead className="text-right">Variance</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {selectedBudget.lineItems.map(item => (
                            <TableRow key={item.id}>
                              <TableCell className="font-medium">{item.category}</TableCell>
                              <TableCell>{item.type.charAt(0).toUpperCase() + item.type.slice(1)}</TableCell>
                              <TableCell className="text-right">${item.budgetedAmount.toLocaleString()}</TableCell>
                              <TableCell className="text-right">${item.actualAmount.toLocaleString()}</TableCell>
                              <TableCell className={`text-right ${item.variance > 0 ? 'text-green-600' : item.variance < 0 ? 'text-red-600' : ''}`}>
                                ${Math.abs(item.variance).toLocaleString()}
                                {item.variance !== 0 && (item.variance > 0 ? ' under' : ' over')}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TabsContent>

                    <TabsContent value="income">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Category</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead className="text-right">Budgeted</TableHead>
                            <TableHead className="text-right">Actual</TableHead>
                            <TableHead className="text-right">Variance</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {selectedBudget.lineItems
                            .filter(item => item.type === 'income')
                            .map(item => (
                              <TableRow key={item.id}>
                                <TableCell className="font-medium">{item.category}</TableCell>
                                <TableCell>{item.description}</TableCell>
                                <TableCell className="text-right">${item.budgetedAmount.toLocaleString()}</TableCell>
                                <TableCell className="text-right">${item.actualAmount.toLocaleString()}</TableCell>
                                <TableCell className={`text-right ${item.variance > 0 ? 'text-green-600' : item.variance < 0 ? 'text-red-600' : ''}`}>
                                  ${Math.abs(item.variance).toLocaleString()}
                                  {item.variance !== 0 && (item.variance > 0 ? ' under' : ' over')}
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </TabsContent>

                    <TabsContent value="expenses">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Category</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead className="text-right">Budgeted</TableHead>
                            <TableHead className="text-right">Actual</TableHead>
                            <TableHead className="text-right">Variance</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {selectedBudget.lineItems
                            .filter(item => item.type === 'expense')
                            .map(item => (
                              <TableRow key={item.id}>
                                <TableCell className="font-medium">{item.category}</TableCell>
                                <TableCell>{item.description}</TableCell>
                                <TableCell className="text-right">${item.budgetedAmount.toLocaleString()}</TableCell>
                                <TableCell className="text-right">${item.actualAmount.toLocaleString()}</TableCell>
                                <TableCell className={`text-right ${item.variance > 0 ? 'text-green-600' : item.variance < 0 ? 'text-red-600' : ''}`}>
                                  ${Math.abs(item.variance).toLocaleString()}
                                  {item.variance !== 0 && (item.variance > 0 ? ' under' : ' over')}
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Budget Selected</h3>
                  <p className="text-gray-500">Select a budget from the list or create a new one.</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}

      {/* Create Budget Dialog */}
      <Dialog open={isAddingBudget} onOpenChange={setIsAddingBudget}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Budget</DialogTitle>
            <DialogDescription>
              Create a new budget for your HOA. You can add line items after creating the budget.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={newBudget.name}
                onChange={(e) => setNewBudget({ ...newBudget, name: e.target.value })}
                className="col-span-3"
                placeholder="Annual Budget 2024"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="year" className="text-right">
                Year
              </Label>
              <Input
                id="year"
                type="number"
                value={newBudget.year}
                onChange={(e) => setNewBudget({ ...newBudget, year: parseInt(e.target.value) })}
                className="col-span-3"
                placeholder="2024"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select
                value={newBudget.status}
                onValueChange={(value) => setNewBudget({ ...newBudget, status: value as 'draft' | 'active' | 'archived' })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {isCompanyAdmin && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="hoaId" className="text-right">
                  HOA
                </Label>
                <Select
                  value={newBudget.hoaId}
                  onValueChange={(value) => setNewBudget({ ...newBudget, hoaId: value })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select HOA" />
                  </SelectTrigger>
                  <SelectContent>
                    {hoas.map((hoa: any) => (
                      <SelectItem key={hoa._id} value={hoa._id}>
                        {hoa.hoaCommunityName || hoa.hoaName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Textarea
                id="description"
                value={newBudget.description || ''}
                onChange={(e) => setNewBudget({ ...newBudget, description: e.target.value })}
                className="col-span-3"
                placeholder="Budget description"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingBudget(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateBudget} disabled={createBudgetMutation.isPending}>
              {createBudgetMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Budget
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Line Item Dialog */}
      <Dialog open={isAddingLineItem} onOpenChange={setIsAddingLineItem}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Budget Line Item</DialogTitle>
            <DialogDescription>
              Add a new line item to the budget.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="category" className="text-right">
                Category
              </Label>
              <Select
                value={newLineItem.category}
                onValueChange={(value) => setNewLineItem({ ...newLineItem, category: value })}
              >
                <SelectTrigger id="category" className="col-span-3">
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="" disabled>Choose a category</SelectItem>
                  {newLineItem.type === 'income' ? (
                    BUDGET_CATEGORIES.income.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))
                  ) : (
                    BUDGET_CATEGORIES.expense.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))
                  )}
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Input
                id="description"
                value={newLineItem.description}
                onChange={(e) => setNewLineItem({ ...newLineItem, description: e.target.value })}
                className="col-span-3"
                placeholder="Regular lawn and garden maintenance"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Type
              </Label>
              <Select
                value={newLineItem.type}
                onValueChange={(value) => setNewLineItem({ ...newLineItem, type: value as 'income' | 'expense' })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="income">Income</SelectItem>
                  <SelectItem value="expense">Expense</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="budgetedAmount" className="text-right">
                Budgeted Amount
              </Label>
              <Input
                id="budgetedAmount"
                type="number"
                value={newLineItem.budgetedAmount}
                onChange={(e) => setNewLineItem({ ...newLineItem, budgetedAmount: parseFloat(e.target.value) })}
                className="col-span-3"
                placeholder="0.00"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingLineItem(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddLineItem} disabled={addLineItemMutation.isPending}>
              {addLineItemMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Add Line Item
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BudgetPlanner;

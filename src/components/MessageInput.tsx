import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Send, Loader2 } from 'lucide-react';

interface MessageInputProps {
  newMessage: string;
  setNewMessage: (message: string) => void;
  handleSendMessage: () => void;
  isPending: boolean;
  socketConnected: boolean;
  conversationId: string | undefined;
}

const MessageInput: React.FC<MessageInputProps> = ({
  newMessage,
  setNewMessage,
  handleSendMessage,
  isPending,
  socketConnected,
  conversationId
}) => {
  return (
    <div className="p-4 border-t">
      <div className="flex gap-2">
        <Textarea
          placeholder="Type your message..."
          className="resize-none"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              console.log('Enter key pressed, calling handleSendMessage');
              if (newMessage.trim()) {
                handleSendMessage();
              }
            }
          }}
        />
        <Button
          type="button"
          onClick={() => {
            console.log('Send button clicked, calling handleSendMessage');
            if (newMessage.trim()) {
              handleSendMessage();
            }
          }}
          disabled={!newMessage.trim() || isPending}
        >
          {isPending ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Send className="h-4 w-4" />
          )}
        </Button>
      </div>
      {/* Debug info and refresh button */}
      <div className="flex justify-between items-center mt-2">
        <div className="text-xs text-muted-foreground">
          {socketConnected ? 'Socket: Connected' : 'Socket: Disconnected'} |
          Message length: {newMessage.length} |
          {conversationId ? `Conversation ID: ${conversationId.substring(0, 8)}...` : 'New conversation'} |
          Env: {import.meta.env.PROD ? 'Production' : 'Development'} |
          API: {import.meta.env.VITE_API_URL ? import.meta.env.VITE_API_URL.substring(0, 15) + '...' : 'Not set'}
        </div>

        {/* PRODUCTION FIX: Add a refresh button */}
        <button
          className="text-xs px-2 py-1 bg-primary text-primary-foreground rounded-md font-medium hover:bg-primary/90"
          onClick={() => {
            // Force a refresh of the page
            window.location.reload();
          }}
        >
          Refresh
        </button>
      </div>
    </div>
  );
};

export default MessageInput;

/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { Upload, Loader2 } from 'lucide-react';
import api from '@/lib/axios';

const DocumentUpload = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [file, setFile] = useState<File | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('miscellaneous');
  const [isPublic, setIsPublic] = useState(true);

  // Upload document mutation
  const uploadMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await api.post('/api/documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    },
    onSuccess: () => {
      // Reset form
      setFile(null);
      setTitle('');
      setDescription('');
      setCategory('miscellaneous');
      setIsPublic(true);

      // Invalidate all document-related queries to refresh the lists
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      queryClient.invalidateQueries({ queryKey: ['recentDocuments'] });

      console.log('Document upload successful - invalidated queries');

      toast({
        title: 'Document Uploaded',
        description: 'Your document has been uploaded successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Upload Failed',
        description: error.message || 'Failed to upload document. Please try again.',
        variant: 'destructive',
      });
    }
  });

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);

      // Auto-fill title with file name if not already set
      if (!title) {
        setTitle(selectedFile.name.split('.').slice(0, -1).join('.'));
      }
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      toast({
        title: 'No File Selected',
        description: 'Please select a file to upload.',
        variant: 'destructive',
      });
      return;
    }

    // Get user data from localStorage to include hoaId and communityId
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userHoaId = Array.isArray(user.hoaId) ? user.hoaId[0] : user.hoaId;
    const userCommunityId = Array.isArray(user.communityId) ? user.communityId[0] : user.communityId;

    console.log('Document upload - User associations:', {
      userHoaId,
      userCommunityId,
      userRole: user.role,
      category
    });

    const formData = new FormData();
    formData.append('document', file);
    formData.append('title', title || file.name);
    formData.append('description', description);
    formData.append('category', category);
    formData.append('isPublic', isPublic.toString());

    // Include hoaId if available
    if (userHoaId) {
      formData.append('hoaId', userHoaId);
    }

    // Include communityId if available
    if (userCommunityId) {
      formData.append('communityId', userCommunityId);
    }

    uploadMutation.mutate(formData);
  };

  return (
    <Card>
      <CardHeader className="responsive-spacing-sm">
        <CardTitle className="text-lg sm:text-xl">Upload Document</CardTitle>
        <CardDescription className="text-sm sm:text-base">
          Add important documents for your community
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4 responsive-spacing-sm">
          <div className="space-y-2">
            <Label htmlFor="file">Document File</Label>
            <Input
              id="file"
              type="file"
              onChange={handleFileChange}
              accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png"
              className="min-h-[44px] cursor-pointer"
            />
            <p className="text-xs sm:text-sm text-muted-foreground">
              Accepted formats: PDF, Word, Excel, PowerPoint, Text, and Images. Max size: 10MB.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter document title"
              className="min-h-[44px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter a brief description of the document"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="financial">Financial</SelectItem>
                <SelectItem value="property_information">Property Information</SelectItem>
                <SelectItem value="meetings">Meetings</SelectItem>
                <SelectItem value="rules">Rules</SelectItem>
                <SelectItem value="bids">Bids</SelectItem>
                <SelectItem value="miscellaneous">Miscellaneous</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="public"
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
            <Label htmlFor="public">Make document visible to all members</Label>
          </div>
        </CardContent>
        <CardFooter className="responsive-spacing-sm">
          <Button
            type="submit"
            className="w-full min-h-[44px] text-base"
            disabled={uploadMutation.isPending || !file}
          >
            {uploadMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload Document
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};

export default DocumentUpload;

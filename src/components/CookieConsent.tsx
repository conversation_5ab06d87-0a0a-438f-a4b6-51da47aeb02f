/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 HoaFlo L.L.C. All rights reserved.
 */

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON>ie, X } from 'lucide-react';
import {
  hasChoiceMade,
  acceptAllCookies,
  declineNonEssentialCookies,
  initializeTrackingServices,
  getCookieConsent,
  setCookieConsent,
  clearCookieConsent
} from '@/utils/cookieUtils';

interface CookieConsentProps {
  onAccept?: () => void;
  onDecline?: () => void;
}

const CookieConsent: React.FC<CookieConsentProps> = ({ onAccept, onDecline }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if user has already made a choice
    if (!hasChoiceMade()) {
      // Show banner after a short delay to avoid jarring experience
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAccept = () => {
    acceptAllCookies();
    initializeTrackingServices();
    setIsVisible(false);
    onAccept?.();
  };

  const handleDecline = () => {
    declineNonEssentialCookies();
    setIsVisible(false);
    onDecline?.();
  };

  const handleDismiss = () => {
    setIsVisible(false);
    // Don't save preference, will show again on next visit
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-black/20 backdrop-blur-sm">
      <div className="container mx-auto max-w-4xl">
        <Card className="shadow-lg border-2">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <Cookie className="h-6 w-6 text-blue-600 mt-1" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold mb-2">We use cookies</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  We use cookies and similar technologies to enhance your experience, analyze site usage, and assist in our marketing efforts. 
                  By clicking "Accept All", you consent to our use of cookies.{' '}
                  <Link 
                    to="/cookie-policy" 
                    className="text-blue-600 hover:underline font-medium"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Learn more about our Cookie Policy
                  </Link>
                </p>
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button 
                    onClick={handleAccept}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Accept All Cookies
                  </Button>
                  <Button 
                    onClick={handleDecline}
                    variant="outline"
                  >
                    Decline Non-Essential
                  </Button>
                  <Link to="/cookie-policy" target="_blank" rel="noopener noreferrer">
                    <Button variant="ghost" className="w-full sm:w-auto">
                      Cookie Settings
                    </Button>
                  </Link>
                </div>
              </div>
              <div className="flex-shrink-0">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDismiss}
                  className="h-8 w-8"
                  aria-label="Dismiss cookie notice"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Hook to check cookie consent status
export const useCookieConsent = () => {
  const [consent, setConsent] = useState<string | null>(null);

  useEffect(() => {
    const storedConsent = getCookieConsent();
    setConsent(storedConsent);
  }, []);

  const updateConsent = (newConsent: 'accepted' | 'declined') => {
    setCookieConsent(newConsent);
    setConsent(newConsent);
    if (newConsent === 'accepted') {
      initializeTrackingServices();
    }
  };

  const clearConsent = () => {
    clearCookieConsent();
    setConsent(null);
  };

  return {
    consent,
    hasAccepted: consent === 'accepted',
    hasDeclined: consent === 'declined',
    hasChoiceMade: consent !== null,
    updateConsent,
    clearConsent
  };
};

export default CookieConsent;

/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { Search, FileText, Download, Calendar, User, Trash2, AlertCircle, FolderEdit } from 'lucide-react';
import { format } from 'date-fns';
import api from '@/lib/axios';

interface Document {
  _id: string;
  title: string;
  description: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  category: string;
  uploadedBy: {
    _id: string;
    username: string;
    email: string;
  };
  uploadedAt: string;
  downloadUrl: string;
}

const DocumentList = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null);
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);
  const [documentToUpdate, setDocumentToUpdate] = useState<Document | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('');

  // Check if user is admin
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isAdmin = user?.role === 'admin';

  // Fetch all documents
  const { data: documents = [], isLoading } = useQuery({
    queryKey: ['documents', activeCategory],
    queryFn: async () => {
      const endpoint = activeCategory === 'all'
        ? '/api/documents'
        : `/api/documents/category/${activeCategory}`;

      const response = await api.get(endpoint);

      console.log('DocumentList - API Response:', {
        endpoint,
        responseData: response.data,
        hasSuccess: !!response.data.success,
        hasDocuments: !!response.data.documents,
        documentsLength: response.data.documents?.length,
        isArray: Array.isArray(response.data)
      });

      // Handle both old and new response formats
      if (response.data.success && response.data.documents) {
        console.log('Using new format - documents:', response.data.documents.length);
        return response.data.documents;
      }

      // Handle legacy format
      if (Array.isArray(response.data)) {
        console.log('Using legacy format - documents:', response.data.length);
        return response.data;
      }

      // Fallback to empty array if data is not in expected format
      console.log('No documents found or unrecognized format');
      return [];
    }
  });

  // Ensure documents is always an array
  const documentsArray = Array.isArray(documents) ? documents : [];

  // Filter documents based on search term
  const filteredDocuments = documentsArray.filter((doc: Document) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      doc.title.toLowerCase().includes(searchLower) ||
      (doc.description && doc.description.toLowerCase().includes(searchLower))
    );
  });

  // Format file size
  const formatFileSize = (bytes: number | undefined | null) => {
    if (!bytes || isNaN(bytes) || bytes <= 0) return 'Unknown size';
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  // Get icon for file type
  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('image')) return '🖼️';
    if (fileType.includes('word') || fileType.includes('document')) return '📝';
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
    if (fileType.includes('presentation') || fileType.includes('powerpoint')) return '📽️';
    return '📁';
  };

  // Get category label
  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'financial': return 'Financial';
      case 'property_information': return 'Property Information';
      case 'miscellaneous': return 'Miscellaneous';
      case 'meetings': return 'Meetings';
      case 'rules': return 'Rules';
      case 'bids': return 'Bids';
      default: return category;
    }
  };

  // Update document category mutation
  const updateCategoryMutation = useMutation({
    mutationFn: async ({ documentId, category }: { documentId: string, category: string }) => {
      try {
        console.log('Updating document category:', { documentId, category });

        // First make a GET request to ensure we have the latest CSRF token
        await api.get('/api/documents');

        // Then perform the update with the updated CSRF token
        const response = await api.patch(`/api/documents/${documentId}/category`, { category });
        return response.data;
      } catch (error) {
        console.error('Error in update category mutation:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      queryClient.invalidateQueries({ queryKey: ['recentDocuments'] });

      toast({
        title: 'Category Updated',
        description: 'The document category has been updated successfully.',
      });

      setCategoryDialogOpen(false);
      setDocumentToUpdate(null);
    },
    onError: (error: any) => {
      console.error('Update category mutation error:', error);
      toast({
        title: 'Update Failed',
        description: error.message || 'Failed to update document category. Please try again.',
        variant: 'destructive',
      });
    }
  });

  // Delete document mutation
  const deleteMutation = useMutation({
    mutationFn: async (documentId: string) => {
      try {
        console.log('Deleting document with ID:', documentId);

        // First make a GET request to ensure we have the latest CSRF token
        await api.get('/api/documents');

        // Then perform the delete with the updated CSRF token
        const response = await api.delete(`/api/documents/${documentId}`);
        return response.data;
      } catch (error) {
        console.error('Error in delete mutation:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      queryClient.invalidateQueries({ queryKey: ['recentDocuments'] });

      toast({
        title: 'Document Deleted',
        description: 'The document has been deleted successfully.',
      });

      setDeleteDialogOpen(false);
      setDocumentToDelete(null);
    },
    onError: (error: any) => {
      console.error('Delete mutation error:', error);
      toast({
        title: 'Delete Failed',
        description: error.message || 'Failed to delete document. Please try again.',
        variant: 'destructive',
      });
    }
  });

  // Handle document download - use hidden iframe for clean download
  const handleDownload = async (doc: Document) => {
    try {
      console.log('Attempting to download document:', doc.title);
      console.log('Download URL:', doc.downloadUrl);

      // Show loading state
      toast({
        title: "Download Starting",
        description: `Preparing ${doc.title} for download...`,
      });

      // Create a hidden iframe to trigger download without flicker or preview
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = doc.downloadUrl;

      // Add iframe to DOM
      document.body.appendChild(iframe);

      // Remove iframe after download starts
      setTimeout(() => {
        document.body.removeChild(iframe);
        toast({
          title: "Download Initiated",
          description: `${doc.title} download has been started`,
        });
      }, 2000);

    } catch (error) {
      console.error('Download error:', error);

      toast({
        title: "Download Failed",
        description: "Failed to download file. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle change category button click
  const handleChangeCategoryClick = (doc: Document) => {
    setDocumentToUpdate(doc);
    setSelectedCategory(doc.category);
    setCategoryDialogOpen(true);
  };

  // Handle category change confirmation
  const handleCategoryChangeConfirm = () => {
    if (documentToUpdate && selectedCategory) {
      updateCategoryMutation.mutate({
        documentId: documentToUpdate._id,
        category: selectedCategory
      });
    }
  };

  // Handle delete button click
  const handleDeleteClick = (doc: Document) => {
    setDocumentToDelete(doc);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (documentToDelete) {
      deleteMutation.mutate(documentToDelete._id);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>HOA Documents</CardTitle>
        <CardDescription>
          Access important documents for your community
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4 sm:mb-6">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search documents..."
              className="pl-8 min-h-[44px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <Tabs defaultValue="all" value={activeCategory} onValueChange={setActiveCategory}>
          <TabsList className="mb-4 w-full overflow-x-auto flex-nowrap justify-start">
            <TabsTrigger value="all" className="min-h-[44px] px-3 sm:px-4 text-sm">All</TabsTrigger>
            <TabsTrigger value="financial" className="min-h-[44px] px-3 sm:px-4 text-sm">Financial</TabsTrigger>
            <TabsTrigger value="property_information" className="min-h-[44px] px-2 sm:px-4 text-sm">Property Info</TabsTrigger>
            <TabsTrigger value="meetings" className="min-h-[44px] px-3 sm:px-4 text-sm">Meetings</TabsTrigger>
            <TabsTrigger value="rules" className="min-h-[44px] px-3 sm:px-4 text-sm">Rules</TabsTrigger>
            <TabsTrigger value="bids" className="min-h-[44px] px-3 sm:px-4 text-sm">Bids</TabsTrigger>
            <TabsTrigger value="miscellaneous" className="min-h-[44px] px-3 sm:px-4 text-sm">Misc</TabsTrigger>
          </TabsList>

          <TabsContent value={activeCategory} className="space-y-4">
            {isLoading ? (
              // Loading skeletons
              Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-4 p-4 border rounded-md">
                  <Skeleton className="h-12 w-12 rounded-md" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                </div>
              ))
            ) : filteredDocuments.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No documents found
              </div>
            ) : (
              filteredDocuments.map((doc: Document) => (
                <div key={doc._id} className="document-card responsive-card border rounded-md hover:bg-accent/10 transition-colors">
                  {/* Document Header with Icon and Title */}
                  <div className="document-header flex items-start gap-3 sm:gap-4 mb-3">
                    <div className="document-icon text-3xl sm:text-4xl flex-shrink-0">{getFileIcon(doc.fileType)}</div>
                    <div className="document-title-section flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-start gap-2 mb-2">
                        <h3 className="document-title font-medium text-base sm:text-lg break-words hyphens-auto leading-tight flex-1 min-w-0">
                          {doc.title}
                        </h3>
                        <Badge variant="secondary" className="self-start flex-shrink-0 text-xs">
                          {getCategoryLabel(doc.category)}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Document Content Stack */}
                  <div className="document-content-stack space-y-3">
                    {/* Description */}
                    {doc.description && (
                      <div className="document-description">
                        <p className="text-sm text-muted-foreground leading-relaxed break-words">
                          {doc.description}
                        </p>
                      </div>
                    )}

                    {/* Metadata Badges */}
                    <div className="document-metadata flex flex-wrap gap-2">
                      <Badge variant="outline" className="flex items-center gap-1 text-xs">
                        <FileText className="h-3 w-3 flex-shrink-0" />
                        <span className="whitespace-nowrap">{formatFileSize(doc.fileSize)}</span>
                      </Badge>
                      <Badge variant="outline" className="flex items-center gap-1 text-xs">
                        <Calendar className="h-3 w-3 flex-shrink-0" />
                        <span className="whitespace-nowrap">{format(new Date(doc.uploadedAt), 'MMM d, yyyy')}</span>
                      </Badge>
                      <Badge variant="outline" className="flex items-center gap-1 text-xs">
                        <User className="h-3 w-3 flex-shrink-0" />
                        <span className="whitespace-nowrap">{doc.uploadedBy?.username || 'Admin'}</span>
                      </Badge>
                    </div>

                    {/* Action Buttons */}
                    <div className="document-actions flex flex-col xs:flex-row gap-2 pt-1">
                      <Button
                        variant="outline"
                        size="sm"
                        className="document-action-btn flex items-center gap-2 min-h-[44px] justify-center text-sm px-4 flex-1 xs:flex-none"
                        onClick={() => handleDownload(doc)}
                        title="Download document"
                      >
                        <Download className="h-4 w-4 flex-shrink-0" />
                        <span>Download</span>
                      </Button>

                      {isAdmin && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            className="document-action-btn flex items-center gap-2 min-h-[44px] justify-center text-sm px-4 flex-1 xs:flex-none"
                            onClick={() => handleChangeCategoryClick(doc)}
                            title="Change document category"
                          >
                            <FolderEdit className="h-4 w-4 flex-shrink-0" />
                            <span className="hidden sm:inline">Change Category</span>
                            <span className="sm:hidden">Edit</span>
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            className="document-action-btn flex items-center gap-2 text-destructive hover:bg-destructive/10 min-h-[44px] justify-center text-sm px-4 flex-1 xs:flex-none"
                            onClick={() => handleDeleteClick(doc)}
                            title="Delete document"
                          >
                            <Trash2 className="h-4 w-4 flex-shrink-0" />
                            <span>Delete</span>
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Confirm Deletion
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the document "{documentToDelete?.title}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2 sm:justify-start">
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleteMutation.isPending}
            >
              {deleteMutation.isPending ? 'Deleting...' : 'Delete Document'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Change Category Dialog */}
      <Dialog open={categoryDialogOpen} onOpenChange={setCategoryDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FolderEdit className="h-5 w-5" />
              Change Document Category
            </DialogTitle>
            <DialogDescription>
              Select a new category for "{documentToUpdate?.title}".
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="financial">Financial</SelectItem>
                <SelectItem value="property_information">Property Information</SelectItem>
                <SelectItem value="meetings">Meetings</SelectItem>
                <SelectItem value="rules">Rules</SelectItem>
                <SelectItem value="bids">Bids</SelectItem>
                <SelectItem value="miscellaneous">Miscellaneous</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <DialogFooter className="gap-2 sm:justify-start">
            <Button
              type="button"
              onClick={handleCategoryChangeConfirm}
              disabled={updateCategoryMutation.isPending || !selectedCategory}
            >
              {updateCategoryMutation.isPending ? 'Updating...' : 'Update Category'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setCategoryDialogOpen(false)}
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default DocumentList;

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Building2,
  MapPin,
  Phone,
  Mail,
  Calendar,
  DollarSign,
  Users,
  AlertCircle,
  ExternalLink
} from 'lucide-react';
import api from '@/lib/axios';
import { useNavigate } from 'react-router-dom';

interface HOADetailsModalProps {
  hoaId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

interface HOADetails {
  _id: string;
  hoaCommunityName: string;
  hoaCommunityCode: string;
  hoaStreetAddress?: string;
  hoaCity: string;
  hoaState: string;
  hoaZipCode: string;
  contactEmail?: string;
  contactPhone?: string;
  einNumber?: string;
  verificationStatus: string;
  subscription?: {
    tier: string;
    status: string;
    unitCount?: number;
  };
  createdAt: string;
  verifiedAt?: string;
  verifiedBy?: string;
}

const HOADetailsModal: React.FC<HOADetailsModalProps> = ({ hoaId, isOpen, onClose }) => {
  const navigate = useNavigate();

  // Fetch HOA details
  const { data: hoaDetails, isLoading, isError, error } = useQuery({
    queryKey: ['hoaDetails', hoaId],
    queryFn: async () => {
      if (!hoaId) return null;
      const response = await api.get(`/api/hoa/${hoaId}`);
      return response.data.data as HOADetails;
    },
    enabled: !!hoaId && isOpen,
  });

  const getStatusBadgeColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'trialing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'canceled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTierBadgeColor = (tier: string) => {
    switch (tier?.toLowerCase()) {
      case 'basic':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'standard':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'premium':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            HOA Details
          </DialogTitle>
          <DialogDescription>
            View detailed information about this HOA community
          </DialogDescription>
        </DialogHeader>

        {isLoading && (
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-20" />
              <Skeleton className="h-20" />
              <Skeleton className="h-20" />
              <Skeleton className="h-20" />
            </div>
          </div>
        )}

        {isError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {(error as any)?.message || 'Failed to load HOA details. Please try again.'}
            </AlertDescription>
          </Alert>
        )}

        {hoaDetails && (
          <div className="space-y-6">
            {/* Header */}
            <div className="border-b pb-4">
              <h2 className="text-2xl font-bold">{hoaDetails.hoaCommunityName}</h2>
              <p className="text-muted-foreground">Code: {hoaDetails.hoaCommunityCode}</p>
              <div className="flex gap-2 mt-2">
                <Badge className={getStatusBadgeColor(hoaDetails.verificationStatus)}>
                  {hoaDetails.verificationStatus}
                </Badge>
                {hoaDetails.subscription && (
                  <>
                    <Badge className={getTierBadgeColor(hoaDetails.subscription.tier)}>
                      {hoaDetails.subscription.tier}
                    </Badge>
                    <Badge className={getStatusBadgeColor(hoaDetails.subscription.status)}>
                      {hoaDetails.subscription.status}
                    </Badge>
                  </>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location
                </h3>
                <div className="text-sm space-y-1">
                  {hoaDetails.hoaStreetAddress && (
                    <p>{hoaDetails.hoaStreetAddress}</p>
                  )}
                  <p>{hoaDetails.hoaCity}, {hoaDetails.hoaState} {hoaDetails.hoaZipCode}</p>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-semibold">Contact Information</h3>
                <div className="text-sm space-y-2">
                  {hoaDetails.contactEmail && (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{hoaDetails.contactEmail}</span>
                    </div>
                  )}
                  {hoaDetails.contactPhone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{hoaDetails.contactPhone}</span>
                    </div>
                  )}
                  {hoaDetails.einNumber && (
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span>EIN: {hoaDetails.einNumber}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Subscription Details */}
            {hoaDetails.subscription && (
              <div className="space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Subscription Details
                </h3>
                <div className="grid md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="font-medium">Tier</p>
                    <p className="capitalize">{hoaDetails.subscription.tier}</p>
                  </div>
                  <div>
                    <p className="font-medium">Status</p>
                    <p className="capitalize">{hoaDetails.subscription.status}</p>
                  </div>
                  {hoaDetails.subscription.unitCount && (
                    <div>
                      <p className="font-medium">Unit Count</p>
                      <p>{hoaDetails.subscription.unitCount}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Registration Details */}
            <div className="space-y-4">
              <h3 className="font-semibold flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Registration Details
              </h3>
              <div className="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium">Registered</p>
                  <p>{new Date(hoaDetails.createdAt).toLocaleDateString()}</p>
                </div>
                {hoaDetails.verifiedAt && (
                  <div>
                    <p className="font-medium">Verified</p>
                    <p>{new Date(hoaDetails.verifiedAt).toLocaleDateString()}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => {
                  navigate(`/subscriptions/${hoaDetails._id}`);
                  onClose();
                }}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Manage Subscription
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  navigate('/admin/hoa-approvals');
                  onClose();
                }}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                View in Approvals
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default HOADetailsModal;

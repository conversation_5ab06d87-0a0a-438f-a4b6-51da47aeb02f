import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, CheckCircle, AlertTriangle, CreditCard } from 'lucide-react';
import { format, set } from 'date-fns';
import api from '@/lib/axios';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { useNavigate } from 'react-router-dom';

// Load Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

// Subscription tier information
const TIERS = {
  basic: {
    name: 'Basic',
    description: 'For small HOAs with up to 50 units',
    price: 150,
    features: [
      'All core features',
      'Up to 50 units',
      'Basic support',
      'Document management',
      'Task management'
    ]
  },
  standard: {
    name: 'Standard',
    description: 'For medium HOAs with up to 150 units',
    price: 350,
    features: [
      'All Basic features',
      'Up to 150 units',
      'Priority support',
      'Advanced reporting',
      'Community management'
    ]
  },
  premium: {
    name: 'Premium',
    description: 'For large HOAs with up to 300 units',
    price: 600,
    features: [
      'All Standard features',
      'Up to 300 units',
      'Premium support',
      'Advanced analytics',
      'Custom branding'
    ]
  },
  enterprise: {
    name: 'Enterprise',
    description: 'For very large HOAs with unlimited units',
    price: 800,
    features: [
      'All Premium features',
      'Unlimited units',
      'Dedicated support',
      'Custom integrations',
      'White-label solution'
    ]
  }
};

type SubscriptionResponse = {
  data,
  hoaId: string;
};

// Tier selection component
const TierSelector = ({ selectedTier, onSelectTier, isLoading }) => {
  return (
    <>
      <div className="mb-4 bg-blue-50 border border-blue-200 rounded-md p-3 text-blue-700">
        <div className="flex items-start">
          <div className="mr-2 mt-0.5">
            <CheckCircle className="h-5 w-5 text-blue-500" />
          </div>
          <div>
            <p className="font-medium">7-Day Free Trial with All Plans</p>
            <p className="text-sm mt-1">
              All subscription plans include a 7-day free trial. You won't be charged until the trial period ends.
              You can cancel anytime during the trial period.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {Object.entries(TIERS).map(([key, tier]) => (
          <Card
            key={key}
            className={`cursor-pointer transition-all ${selectedTier === key ? 'ring-2 ring-primary' : 'hover:shadow-md'}`}
            onClick={() => onSelectTier(key)}
          >
            <CardHeader>
              <CardTitle>{tier.name}</CardTitle>
              <CardDescription>{tier.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center mb-2">
                <p className="text-2xl font-bold">${tier.price}<span className="text-sm font-normal">/month</span></p>
                <Badge variant="outline" className="ml-2 text-blue-500 border-blue-200">7-day free</Badge>
              </div>
              <ul className="mt-4 space-y-2">
                {tier.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                variant={selectedTier === key ? "default" : "outline"}
                className="w-full"
                disabled={isLoading}
              >
                {selectedTier === key ? 'Selected' : 'Select'}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </>
  );
};

// Payment form component
const PaymentForm = ({ hoaId, tier, unitCount, onSuccess, onCancel }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Create subscription mutation
  const createSubscriptionMutation = useMutation<SubscriptionResponse, Error, {
    hoaId: string;
    tier: string;
    unitCount: number;
    paymentMethodId: string;
  }>({
    mutationFn: async ({ hoaId, tier, unitCount, paymentMethodId }) => {
      const response = await api.post('/api/subscriptions', {
        hoaId,
        tier,
        unitCount,
        paymentMethodId
      });
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['subscription', hoaId] });
      onSuccess(data);
    },
    onError: (error) => {
      console.error('Error creating subscription:', error);
      setError('Failed to create subscription');
      setIsLoading(false);
    }
  });

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Create payment method
      const cardElement = elements.getElement(CardElement);
      const { error, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
      });

      if (error) {
        throw new Error(error.message);
      }

      // Create subscription with payment method
      await createSubscriptionMutation.mutateAsync({
        hoaId,
        tier,
        unitCount,
        paymentMethodId: paymentMethod.id
      });

    } catch (err) {
      console.error('Payment error:', err);
      setError(err.message);
      toast({
        title: 'Payment Failed',
        description: err.message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Card Details</label>
          <div className="border rounded-md p-3">
            <CardElement
              options={{
                style: {
                  base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                      color: '#aab7c4',
                    },
                  },
                  invalid: {
                    color: '#9e2146',
                  },
                },
              }}
            />
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
            <AlertTriangle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{error}</span>
          </div>
        )}

        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Back
          </Button>
          <Button
            type="submit"
            disabled={!stripe || isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Subscribe Now
              </>
            )}
          </Button>
        </div>
      </div>
    </form>
  );
};

// Main subscription management component
const SubscriptionManagement = ({ hoaId }) => {
  const navigate = useNavigate();
  const [selectedTier, setSelectedTier] = useState('basic');
  const [unitCount, setUnitCount] = useState(50);
  const [step, setStep] = useState('select'); // 'select', 'payment', 'success', 'cancel'
  const { toast } = useToast();

  // Community selection state (same as other components)
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });
  const [selectedCommunityIds, setSelectedCommunityIds] = useState<string[]>(() => {
    const stored = localStorage.getItem('selectedCommunityIds');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);
  const [isMultipleCommunities, setIsMultipleCommunities] = useState<boolean>(() => {
    // Initialize from localStorage immediately - check both single and multiple community selection
    try {
      const storedCommunityIds = localStorage.getItem('selectedCommunityIds');
      const storedCommunityId = localStorage.getItem('selectedCommunityId');

      console.log('SubscriptionManagement: Initial state check:', {
        storedCommunityIds,
        storedCommunityId
      });

      if (storedCommunityIds) {
        const ids = JSON.parse(storedCommunityIds);
        const isMultiple = Array.isArray(ids) && ids.length > 1;
        console.log('SubscriptionManagement: Multiple communities detected:', isMultiple, ids);
        return isMultiple;
      }

      if (storedCommunityId) {
        console.log('SubscriptionManagement: Single community detected:', storedCommunityId);
        return false; // Single community = not multiple
      }

      console.log('SubscriptionManagement: No community selection detected');
      return false;
    } catch (e) {
      console.log('SubscriptionManagement: Error parsing localStorage:', e);
      return false;
    }
  });
  const [availableCommunitiesInDropdown, setAvailableCommunitiesInDropdown] = useState<string[]>([]);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const userCommunityId = user?.communityId;
  const isCompanyAdmin = user.role === 'company_admin';

  // Listen for community selection changes from Sidebar
  useEffect(() => {
    // Initialize from localStorage
    const storedCommunityId = localStorage.getItem('selectedCommunityId');
    const storedCommunityIds = localStorage.getItem('selectedCommunityIds');

    console.log('SubscriptionManagement: Initializing community state from localStorage:', {
      storedCommunityId,
      storedCommunityIds
    });

    if (storedCommunityIds) {
      const ids = JSON.parse(storedCommunityIds);
      setSelectedCommunityIds(ids);
      setIsMultipleCommunities(true);
      setSelectedCommunityName(`${ids.length} Communities Selected`);
      console.log('SubscriptionManagement: Set multiple communities on mount:', ids);
    } else if (storedCommunityId) {
      setSelectedCommunityId(storedCommunityId);
      setIsMultipleCommunities(false);
      console.log('SubscriptionManagement: Set single community on mount:', storedCommunityId);
    } else {
      // No community selection - reset to default state
      setSelectedCommunityId(null);
      setSelectedCommunityIds([]);
      setIsMultipleCommunities(false);
      setSelectedCommunityName(null);
      console.log('SubscriptionManagement: No community selection on mount');
    }

    const handleCommunityChange = async (event: CustomEvent) => {
      const { communityId, communityIds, availableCommunities } = event.detail;
      console.log('SubscriptionManagement: Received community selection change:', { communityId, communityIds, availableCommunities });

      // Store available communities from dropdown for "All Communities" use
      if (availableCommunities && Array.isArray(availableCommunities)) {
        const communityIdsList = availableCommunities.map((community: any) => community._id || community.id);
        setAvailableCommunitiesInDropdown(communityIdsList);
      }

      // Handle both single and multiple community selection
      if (communityIds && Array.isArray(communityIds)) {
        // Multiple communities selected
        setSelectedCommunityIds(communityIds);
        setSelectedCommunityId(null);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`${communityIds.length} Communities Selected`);

        console.log('SubscriptionManagement: Multiple communities selected:', communityIds);
      } else if (communityId && communityId !== 'all') {
        // Single community selected
        setSelectedCommunityId(communityId);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);

        // Fetch community details for display name
        try {
          const response = await api.get(`/api/communities/${communityId}`);
          const community = response.data;
          if (community) {
            setSelectedCommunityName(`${community.name} (${community.streetAddress || 'No address'})`);
          }
        } catch (error) {
          console.error('Error fetching community details:', error);
          setSelectedCommunityName('Selected Community');
        }
      } else {
        // "All Communities" selection
        setSelectedCommunityId(null);
        const communitiesToUse = communityIds && communityIds.length > 0 ? communityIds : availableCommunitiesInDropdown;
        setSelectedCommunityIds(communitiesToUse);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`All Communities (${communitiesToUse.length})`);
      }
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, [availableCommunitiesInDropdown]);

  // Additional effect to ensure proper state initialization on component mount
  useEffect(() => {
    // Double-check community state after component mounts
    const checkCommunityState = () => {
      const storedCommunityIds = localStorage.getItem('selectedCommunityIds');
      const storedCommunityId = localStorage.getItem('selectedCommunityId');

      if (storedCommunityIds) {
        const ids = JSON.parse(storedCommunityIds);
        if (ids.length > 1) {
          console.log('SubscriptionManagement: Confirmed multiple communities state:', ids);
          setIsMultipleCommunities(true);
          setSelectedCommunityIds(ids);
          setSelectedCommunityId(null);
        }
      } else if (storedCommunityId) {
        console.log('SubscriptionManagement: Confirmed single community state:', storedCommunityId);
        setIsMultipleCommunities(false);
        setSelectedCommunityId(storedCommunityId);
        setSelectedCommunityIds([]);
      }
    };

    // Check immediately and also after a short delay to ensure localStorage is read
    checkCommunityState();
    const timeoutId = setTimeout(checkCommunityState, 100);

    return () => clearTimeout(timeoutId);
  }, []); // Run only on mount

  // Determine which HOA ID to use for API calls
  const effectiveHoaId = selectedCommunityId ? null : hoaId; // Use null when community is selected to trigger community-based fetch

  // Debug the query enabled conditions
  const hoaQueryEnabled = !!(effectiveHoaId || selectedCommunityId) &&
                          !(isMultipleCommunities && selectedCommunityIds.length > 1);

  console.log('SubscriptionManagement: HOA Query enabled check:', {
    effectiveHoaId,
    selectedCommunityId,
    isMultipleCommunities,
    selectedCommunityIdsLength: selectedCommunityIds.length,
    hoaQueryEnabled
  });

  // Fetch HOA details
  const { data: hoa, isLoading: isLoadingHoa } = useQuery({
    queryKey: ['hoa', effectiveHoaId, selectedCommunityId],
    queryFn: async () => {
      console.log('SubscriptionManagement: HOA Query function called - THIS SHOULD NOT HAPPEN WITH ALL COMMUNITIES');
      if (selectedCommunityId) {
        // Fetch HOA data based on selected community
        console.log('SubscriptionManagement: Fetching HOA for community:', selectedCommunityId);
        const response = await api.get(`/api/hoa/by-community/${selectedCommunityId}`);
        return response.data;
      } else {
        // Use original hoaId
        console.log('SubscriptionManagement: Fetching HOA by ID:', hoaId);
        const response = await api.get(`/api/hoa/${hoaId}`);
        return response.data;
      }
    },
    enabled: hoaQueryEnabled
  });

  // Debug the subscription query enabled conditions
  const subscriptionQueryEnabled = !!(effectiveHoaId || selectedCommunityId) &&
                                   !(isMultipleCommunities && selectedCommunityIds.length > 1);

  console.log('SubscriptionManagement: Subscription Query enabled check:', {
    effectiveHoaId,
    selectedCommunityId,
    isMultipleCommunities,
    selectedCommunityIdsLength: selectedCommunityIds.length,
    subscriptionQueryEnabled
  });

  // Fetch subscription details
  const { data: subscription, isLoading: isLoadingSubscription } = useQuery({
    queryKey: ['subscription', effectiveHoaId, selectedCommunityId],
    queryFn: async () => {
      console.log('SubscriptionManagement: Subscription Query function called - THIS SHOULD NOT HAPPEN WITH ALL COMMUNITIES');
      try {
        if (selectedCommunityId) {
          // Fetch subscription data based on selected community
          console.log('SubscriptionManagement: Fetching subscription for community:', selectedCommunityId);
          const response = await api.get(`/api/subscriptions/by-community/${selectedCommunityId}`);
          return response.data;
        } else {
          // Use original hoaId
          console.log('SubscriptionManagement: Fetching subscription by HOA ID:', hoaId);
          const response = await api.get(`/api/subscriptions/hoa/${hoaId}`);
          return response.data;
        }
      } catch (error) {
        if (error.response?.status === 404) {
          return null; // No subscription found
        }
        throw error;
      }
    },
    enabled: subscriptionQueryEnabled
  });

  const { 
    data: cancelSubscription, 
    isLoading: isLoadingCancelSub,
    refetch: refetchCancelSubscription
   } = useQuery({
    queryKey: ['cancel', subscription?.subscription?._id],
    queryFn: async () => {
      try {
        const response = await api.delete(`/api/subscriptions/${subscription?.subscription?._id}`);
        return response.data;
      } catch (error) {
        if (error.response?.status === 404) {
          return null;
        } 
        throw error;
      }
    },
    enabled: false
  })

  // Set initial tier based on HOA data
  useEffect(() => {
    if (hoa?.subscription?.tier) {
      setSelectedTier(hoa.subscription.tier);
    }
    if (hoa?.subscription?.unitCount) {
      setUnitCount(hoa.subscription.unitCount);
    }

    if (subscription?.subscription) {
    setStep('view');
  }
  }, [hoa, subscription]);

  const handleSelectTier = (tier) => {
    setSelectedTier(tier);
  };

  const handleProceedToPayment = () => {
    setStep('payment');
  };

  const handleActiveSubscription = () => {
    setStep('select');
  };

  const sendCancelSubscription = async () => {
    try {
      const { data } = await refetchCancelSubscription();

      toast({
        title: 'Subscription Cancelled',
        description: `Your ${selectedTier} subscription has been cancelled successfully.`,
        variant: 'default'
      })

      setStep('view');
    } catch(error) {
      console.error('Cancel subscription failed:', error);
      toast({
        title: 'Error',
        description: 'Failed to cancel subscription',
        variant: 'destructive'
      })
    }
  };

  const handlePaymentSuccess = () => {
    setStep('success');
    toast({
      title: 'Subscription Created',
      description: `Your ${selectedTier} subscription has been created successfully.`,
      variant: 'default'
    });
  };

  const handleCancel = () => {
    setStep('view');
  };

  const handleCancelSubscription = () => {
    setStep('cancel')
  }

  const isLoading = isLoadingHoa || isLoadingSubscription || isLoadingCancelSub;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // If subscription exists, show subscription details
  if (step === 'view' && subscription?.subscription) {
    const sub = subscription.subscription;
    const stripeDetails = subscription.stripeDetails;

    return (
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Current Subscription</CardTitle>
              <CardDescription>
                {hoa?.hoaCommunityName || 'Your HOA'} is currently on the {TIERS[sub.tier]?.name || sub.tier} plan
              </CardDescription>
            </div>
            <Badge
              variant={
                sub.status === 'active' ? 'default' :
                sub.status === 'trialing' ? 'secondary' :
                'destructive'
              }
            >
              {sub.status === 'active' ? 'Active' :
               sub.status === 'trialing' ? 'Free Trial' :
               sub.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Subscription Details</h3>
                <div className="mt-2 space-y-2">
                  <p><span className="font-medium">Plan:</span> {TIERS[sub.tier]?.name || sub.tier}</p>
                  <p><span className="font-medium">Price:</span> ${sub.totalPrice}/month</p>
                  <p><span className="font-medium">Units:</span> {sub.unitCount}</p>
                  <p><span className="font-medium">Status:</span> {sub.status}</p>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Billing Period</h3>
                <div className="mt-2 space-y-2">
                  <p>
                    <span className="font-medium">Current period:</span>{' '}
                    {stripeDetails?.currentPeriodStart && (
                      <>
                        {format(new Date(stripeDetails.currentPeriodStart), 'MMM d, yyyy')} to{' '}
                        {format(new Date(stripeDetails.currentPeriodEnd), 'MMM d, yyyy')}
                      </>
                    )}
                  </p>
                  {sub.status === 'trialing' && (
                    <p className="text-blue-600 font-medium mt-2">
                      Your free trial ends on {format(new Date(sub.metadata.trialEnd), 'MMMM d, yyyy')}
                    </p>
                  )}
                  {stripeDetails?.cancelAtPeriodEnd && (
                    <p className="text-amber-600 mt-2">
                      Your subscription will be canceled at the end of the current billing period.
                    </p>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Payment Method</h3>
              {stripeDetails?.defaultPaymentMethod ? (
                <div className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  <span>
                    {stripeDetails.defaultPaymentMethod.card.brand.toUpperCase()} ending in{' '}
                    {stripeDetails.defaultPaymentMethod.card.last4}
                  </span>
                </div>
              ) : (
                <p>No payment method on file</p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => handleActiveSubscription()}>
            Change Plan
          </Button>
          {sub.status === 'active' && !stripeDetails?.cancelAtPeriodEnd && (
            <Button variant="destructive" onClick={handleCancelSubscription}>
              Cancel Subscription
            </Button>
          )}
        </CardFooter>
      </Card>
    );
  }

  if (step === 'cancel') {
    const stripeDetails = subscription.stripeDetails;
    return (
      <Card>
        <CardHeader>
          <div className="flex justify-center items-center">
            <div>
              <CardTitle>Cancel Subscription</CardTitle>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <h3 className="text-sm text-center font-medium text-muted-foreground">
              Are you sure to cancel HOA Subscription? All your data will be lost after {format(new Date(stripeDetails.currentPeriodEnd), 'MMM d, yyyy')}.
              You need to contact Street HOA Admin to active your HOA Subscription if you change your mind.
            </h3>
          </div>
        </CardContent>
        <CardFooter className="flex row justify-center">
          <Button variant="destructive" onClick={handleCancel}>
            Cancel
          </Button>
          <div className="col ml-4">
            <Button onClick={sendCancelSubscription}>
              Continue to Cancel
            </Button>
          </div>
        </CardFooter>
      </Card>
    );
  }

  // Debug logging for render decision
  console.log('SubscriptionManagement: Render decision check:', {
    isMultipleCommunities,
    selectedCommunityId,
    selectedCommunityIds,
    selectedCommunityIdsLength: selectedCommunityIds.length,
    shouldShowMessage: isMultipleCommunities && selectedCommunityIds.length > 1
  });

  // Show friendly message ONLY when multiple communities are actually selected
  // Check both the state flag AND that we actually have multiple community IDs
  if (isMultipleCommunities && selectedCommunityIds.length > 1) {
    console.log('SubscriptionManagement: Rendering friendly message for multiple communities');
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Management</CardTitle>
          <CardDescription>
            Community selection required
          </CardDescription>
        </CardHeader>
        <CardContent className="p-8 text-center">
          <div className="flex flex-col items-center space-y-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <CreditCard className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Please Choose a Community</h3>
              <p className="text-sm text-gray-500 mt-2 max-w-md">
                To view and manage subscription details, please select a specific community from the sidebar.
                Subscription management is available on a per-community basis.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Subscription Management</CardTitle>
        <CardDescription>
          Choose a subscription plan for {hoa?.hoaCommunityName || 'your HOA'}
          {/* Show community selection status */}
          {selectedCommunityName && (
            <div className="text-sm text-muted-foreground mt-1">
              Managing subscription for: {selectedCommunityName}
            </div>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {step === 'select' && (
          <>
            <TierSelector
              selectedTier={selectedTier}
              onSelectTier={handleSelectTier}
              isLoading={isLoading}
            />
            <div className="row flex mt-4">
              <div className="col">
                <Button variant="destructive" onClick={handleCancel}>
                  Cancel
                </Button>
              </div>
              <div className="col ml-4">
                <Button onClick={handleProceedToPayment}>
                  Continue to Payment
                </Button>
              </div>
            </div>
          </>
        )}

        {step === 'payment' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium">Payment Details</h3>
              <p className="text-muted-foreground">
                You're subscribing to the {TIERS[selectedTier].name} plan at ${TIERS[selectedTier].price}/month
              </p>
              <div className="mt-2 bg-blue-50 border border-blue-200 rounded-md p-3 text-blue-700 flex items-start">
                <div className="mr-2 mt-0.5">
                  <CheckCircle className="h-5 w-5 text-blue-500" />
                </div>
                <div>
                  <p className="font-medium">30-Day Free Trial</p>
                  <p className="text-sm mt-1">
                    Your subscription includes a 30-day free trial. You won't be charged until the trial period ends.
                    You can cancel anytime during the trial period.
                  </p>
                </div>
              </div>
            </div>

            <Elements stripe={stripePromise}>
              <PaymentForm
                hoaId={hoaId}
                tier={selectedTier}
                unitCount={unitCount}
                onSuccess={handlePaymentSuccess}
                onCancel={handleCancel}
              />
            </Elements>
          </div>
        )}

        {step === 'success' && (
          <div className="text-center py-8">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-medium mb-2">Subscription Created Successfully!</h3>
            <p className="text-muted-foreground mb-2">
              Your {TIERS[selectedTier].name} subscription is now active with a 30-day free trial.
            </p>
            <p className="text-blue-600 text-sm mb-6">
              Your free trial starts today. You won't be charged until the trial period ends.
            </p>
            <Button onClick={() => window.location.reload()}>
              View Subscription Details
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SubscriptionManagement;

import React from 'react';
import { cn } from '@/lib/utils';

interface PageHeaderProps {
  heading: string;
  subheading?: string;
  icon?: React.ReactNode;
  className?: string;
}

export const PageHeader = ({
  heading,
  subheading,
  icon,
  className,
}: PageHeaderProps) => {
  return (
    <div className={cn("mb-6", className)}>
      <div className="flex items-center gap-2 mb-1">
        {icon && <div className="text-primary">{icon}</div>}
        <h1 className="text-2xl font-bold tracking-tight">{heading}</h1>
      </div>
      {subheading && (
        <p className="text-muted-foreground">{subheading}</p>
      )}
    </div>
  );
};

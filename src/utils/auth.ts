/**
 * Authentication utility functions
 */

export interface User {
  _id: string;
  email: string;
  username: string;
  fullName: string;
  role: string;
  token: string;
  profilePhoto?: string;
}

/**
 * Get user data from local storage
 * @returns User data object or null
 */
export function getUserFromLocalStorage(): User | null {
  const userStr = localStorage.getItem('user');
  if (!userStr) return null;

  try {
    return JSON.parse(userStr);
  } catch (error) {
    console.error('Error parsing user from localStorage:', error);
    return null;
  }
}

/**
 * Save user data to local storage
 * @param user User data to save
 */
export function setUserInLocalStorage(user: User): void {
  localStorage.setItem('user', JSON.stringify(user));
}

/**
 * Remove user data from local storage
 */
export function removeUserFromLocalStorage(): void {
  localStorage.removeItem('user');
}

/**
 * Check if user is authenticated
 * @returns boolean
 */
export function isAuthenticated(): boolean {
  const user = getUserFromLocalStorage();
  return !!user?.token;
}

/**
 * Check if user has admin role
 * @returns boolean
 */
export function isAdmin(): boolean {
  const user = getUserFromLocalStorage();
  return user?.role === 'admin';
}

/**
 * Check if user has member role
 * @returns boolean
 */
export function isMember(): boolean {
  const user = getUserFromLocalStorage();
  return user?.role === 'member';
}
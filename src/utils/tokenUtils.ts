/**
 * Utility functions for handling JWT tokens
 */

/**
 * Check if a token is likely expired based on login time
 * @param loginTime ISO string of login time
 * @returns boolean indicating if token is likely expired
 */
export function isTokenLikelyExpired(loginTime: string | undefined): boolean {
  if (!loginTime) return true;
  
  try {
    const loginDate = new Date(loginTime);
    const now = new Date();
    
    // Calculate hours since login
    const hoursSinceLogin = (now.getTime() - loginDate.getTime()) / (1000 * 60 * 60);
    
    // Token expires after 24 hours (matching backend setting)
    return hoursSinceLogin >= 23.5; // Check slightly before actual expiration
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Assume expired on error
  }
}

/**
 * Parse JWT token to get expiration time
 * Note: This doesn't validate the token, just extracts the expiration
 * @param token JWT token
 * @returns expiration date or null if invalid
 */
export function getTokenExpiration(token: string): Date | null {
  try {
    // Split the token and get the payload part
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    // Decode the payload
    const payload = JSON.parse(atob(parts[1]));
    
    // Check if exp claim exists
    if (!payload.exp) return null;
    
    // Convert exp to date
    return new Date(payload.exp * 1000);
  } catch (error) {
    console.error('Error parsing token:', error);
    return null;
  }
}

/**
 * Check if token is expired based on its exp claim
 * @param token JWT token
 * @returns boolean indicating if token is expired
 */
export function isTokenExpired(token: string): boolean {
  if (!token) return true;
  
  const expiration = getTokenExpiration(token);
  if (!expiration) return true;
  
  return expiration <= new Date();
}

/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

/**
 * Utility functions for handling image URLs
 */

// Get the API base URL from environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

/**
 * Ensures a profile photo URL is correctly formatted
 * @param url The profile photo URL or path
 * @returns A properly formatted URL
 */
export const getProfilePhotoUrl = (url: string | null | undefined): string | null => {
  if (!url) return null;

  console.log('Processing profile photo URL:', url);

  // If it's already a full URL, ensure it's properly encoded
  if (url.startsWith('http')) {
    console.log('URL is already absolute, encoding if needed');

    // Check if it's pointing to the old Heroku domain
    if (url.includes('hoa-management-app.herokuapp.com')) {
      console.log('Detected old Heroku domain, updating to current API URL');
      // Extract the path part after /uploads/
      const pathMatch = url.match(/\/uploads\/(.+)$/);
      if (pathMatch && pathMatch[1]) {
        const filename = pathMatch[1];
        const newUrl = `${API_BASE_URL}/uploads/${encodeURIComponent(filename)}`;
        console.log('Updated URL:', newUrl);
        return newUrl;
      }
    }

    // Parse the URL to properly encode it
    try {
      // Split the URL into base part and filename
      const lastSlashIndex = url.lastIndexOf('/');
      if (lastSlashIndex !== -1) {
        const basePart = url.substring(0, lastSlashIndex + 1);
        const filename = url.substring(lastSlashIndex + 1);
        // Encode only the filename part
        const encodedUrl = basePart + encodeURIComponent(filename);
        console.log('Encoded URL:', encodedUrl);
        return encodedUrl;
      }
      return url;
    } catch (e) {
      console.error('Error encoding URL:', e);
      return url;
    }
  }

  // If it starts with /uploads, add the base URL and encode
  if (url.startsWith('/uploads')) {
    // Split the URL into base part and filename
    const lastSlashIndex = url.lastIndexOf('/');
    if (lastSlashIndex !== -1) {
      const basePart = url.substring(0, lastSlashIndex + 1);
      const filename = url.substring(lastSlashIndex + 1);
      // Encode only the filename part
      const encodedPath = basePart + encodeURIComponent(filename);
      const fullUrl = `${API_BASE_URL}${encodedPath}`;
      console.log('Adding base URL to encoded /uploads path:', fullUrl);
      return fullUrl;
    }
    const fullUrl = `${API_BASE_URL}${url}`;
    console.log('Adding base URL to /uploads path:', fullUrl);
    return fullUrl;
  }

  // If it doesn't start with /uploads, add it and encode the filename
  const fullUrl = `${API_BASE_URL}/uploads/${encodeURIComponent(url)}`;
  console.log('Creating full URL with /uploads prefix and encoding:', fullUrl);
  return fullUrl;
};

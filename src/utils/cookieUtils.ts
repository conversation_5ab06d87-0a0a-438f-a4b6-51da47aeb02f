/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 */

export const COOKIE_CONSENT_KEY = 'hoa-cookie-consent';
export const COOKIE_PREFERENCES_KEY = 'hoa-cookie-preferences';

export interface CookiePreferences {
  essential: boolean;
  performance: boolean;
  functionality: boolean;
  targeting: boolean;
}

export const DEFAULT_COOKIE_PREFERENCES: CookiePreferences = {
  essential: true, // Always true, cannot be disabled
  performance: false,
  functionality: false,
  targeting: false,
};

/**
 * Get the current cookie consent status
 */
export const getCookieConsent = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(COOKIE_CONSENT_KEY);
};

/**
 * Set cookie consent status
 */
export const setCookieConsent = (consent: 'accepted' | 'declined'): void => {
  if (typeof window === 'undefined') return;
  localStorage.setItem(COOKIE_CONSENT_KEY, consent);
};

/**
 * Clear cookie consent (will show banner again)
 */
export const clearCookieConsent = (): void => {
  if (typeof window === 'undefined') return;
  localStorage.removeItem(COOKIE_CONSENT_KEY);
};

/**
 * Get cookie preferences
 */
export const getCookiePreferences = (): CookiePreferences => {
  if (typeof window === 'undefined') return DEFAULT_COOKIE_PREFERENCES;
  
  const stored = localStorage.getItem(COOKIE_PREFERENCES_KEY);
  if (!stored) return DEFAULT_COOKIE_PREFERENCES;
  
  try {
    const parsed = JSON.parse(stored);
    return {
      essential: true, // Always true
      performance: parsed.performance ?? DEFAULT_COOKIE_PREFERENCES.performance,
      functionality: parsed.functionality ?? DEFAULT_COOKIE_PREFERENCES.functionality,
      targeting: parsed.targeting ?? DEFAULT_COOKIE_PREFERENCES.targeting,
    };
  } catch {
    return DEFAULT_COOKIE_PREFERENCES;
  }
};

/**
 * Set cookie preferences
 */
export const setCookiePreferences = (preferences: Partial<CookiePreferences>): void => {
  if (typeof window === 'undefined') return;
  
  const current = getCookiePreferences();
  const updated = {
    ...current,
    ...preferences,
    essential: true, // Always true
  };
  
  localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(updated));
};

/**
 * Check if user has accepted cookies
 */
export const hasAcceptedCookies = (): boolean => {
  return getCookieConsent() === 'accepted';
};

/**
 * Check if user has declined cookies
 */
export const hasDeclinedCookies = (): boolean => {
  return getCookieConsent() === 'declined';
};

/**
 * Check if user has made any choice about cookies
 */
export const hasChoiceMade = (): boolean => {
  const consent = getCookieConsent();
  return consent === 'accepted' || consent === 'declined';
};

/**
 * Accept all cookies (sets all preferences to true)
 */
export const acceptAllCookies = (): void => {
  setCookieConsent('accepted');
  setCookiePreferences({
    essential: true,
    performance: true,
    functionality: true,
    targeting: true,
  });
};

/**
 * Decline non-essential cookies (sets only essential to true)
 */
export const declineNonEssentialCookies = (): void => {
  setCookieConsent('declined');
  setCookiePreferences({
    essential: true,
    performance: false,
    functionality: false,
    targeting: false,
  });
};

/**
 * Check if a specific cookie type is allowed
 */
export const isCookieTypeAllowed = (type: keyof CookiePreferences): boolean => {
  if (type === 'essential') return true; // Essential cookies are always allowed
  
  const consent = getCookieConsent();
  if (consent === 'declined') return false;
  if (consent !== 'accepted') return false;
  
  const preferences = getCookiePreferences();
  return preferences[type];
};

/**
 * Initialize Google Analytics or other tracking services based on consent
 */
export const initializeTrackingServices = (): void => {
  if (!hasAcceptedCookies()) return;
  
  const preferences = getCookiePreferences();
  
  // Initialize performance tracking (e.g., Google Analytics)
  if (preferences.performance) {
    // Add your Google Analytics initialization here
    console.log('Performance tracking enabled');
  }
  
  // Initialize functionality cookies
  if (preferences.functionality) {
    // Add functionality cookie initialization here
    console.log('Functionality cookies enabled');
  }
  
  // Initialize targeting/advertising cookies
  if (preferences.targeting) {
    // Add advertising cookie initialization here
    console.log('Targeting cookies enabled');
  }
};

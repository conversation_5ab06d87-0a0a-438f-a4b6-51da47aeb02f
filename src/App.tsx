/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// src/App.tsx
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import "@/styles/responsive.css";
import { BrowserRouter, Routes, Route, Navigate, Outlet, useNavigate } from "react-router-dom";
import Layout from "@/components/Layout";
import { SocketProvider } from "@/context/SocketContext";
import CookieConsent from "@/components/CookieConsent";
import AddTaskForm from './components/forms/AddTaskForm';
import { useEffect } from 'react';
import { isTokenLikelyExpired } from './utils/tokenUtils';

import LandingPage from './pages/LandingPage';
import Login from './pages/Login';
import Register from './pages/Register';
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Tasks from "./pages/Tasks";
import AdminApprovals from './pages/AdminApprovals';
import AdminAnnouncements from './pages/AdminAnnouncements';
import Announcements from './pages/Announcements';
import Messages from './pages/Messages';
import DirectMessages from './pages/DirectMessages'; // Import the new direct messages component
import AdminMaster from './pages/AdminMaster';
import Finances from "./pages/Finances";
import Members from './pages/Members';
import Calendar from './pages/Calendar';
import Notifications from './pages/Notifications';
import Settings from './pages/Settings';
import ResetPassword from './pages/ResetPassword';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfUse from './pages/TermsOfUse';
import CookiePolicy from './pages/CookiePolicy';
import MakePayment from './pages/payments/MakePayment';
import Documents from './pages/Documents';
import HOAOfficePage from './pages/HOAOfficePage';
import AdminManagementPage from './pages/AdminManagementPage';
import SecuritySettings from './pages/SecuritySettings';
import HOARegistrationPage from './pages/HOARegistrationPage';
import PublicHOARegistrationPage from './pages/PublicHOARegistrationPage';
import PropertyManagementPage from './pages/PropertyManagementPage';
import CommunityManagementPage from './pages/CommunityManagementPage';
import SubscriptionPage from './pages/SubscriptionPage';
import SubscriptionActivatePage from './pages/SubscriptionActivatePage';
import SubscriptionSuccessPage from './pages/SubscriptionSuccessPage';
import SubscriptionCancelPage from './pages/SubscriptionCancelPage';
import FinancialReportsPage from './pages/FinancialReportsPage';
import BudgetPlannerPage from './pages/BudgetPlannerPage';
import HOAApprovals from './pages/HOAApprovals';
import AllHOAsManagementPage from './pages/AllHOAsManagementPage';


const queryClient = new QueryClient();

// Enhanced function to check if user is logged in with token expiration check
const isLoggedIn = () => {
  try {
    const userStr = localStorage.getItem('user');
    console.log('User in localStorage:', userStr ? 'exists' : 'not found');

    if (!userStr) return false;

    const user = JSON.parse(userStr);
    console.log('User parsed:', {
      hasToken: !!user?.token,
      role: user?.role,
      loginTime: user?.loginTime
    });

    // Check if token is likely expired based on login time
    if (isTokenLikelyExpired(user?.loginTime)) {
      console.warn('Token likely expired based on login time');
      localStorage.removeItem('user'); // Clear expired token
      return false;
    }

    return !!user?.token;
  } catch (e) {
    console.error('Error checking login status:', e);
    return false;
  }
};

// Protected Layout wrapper with token validation
const ProtectedLayout = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Check token on component mount and set up periodic checks
    const checkToken = () => {
      try {
        const userStr = localStorage.getItem('user');
        if (!userStr) return false;

        const user = JSON.parse(userStr);
        if (isTokenLikelyExpired(user?.loginTime)) {
          console.warn('Token expired during session, redirecting to login');
          localStorage.removeItem('user');
          navigate('/login?expired=true', { replace: true });
        }
      } catch (e) {
        console.error('Error in token check:', e);
      }
    };

    // Check immediately
    checkToken();

    // Set up periodic check every 5 minutes
    const interval = setInterval(checkToken, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [navigate]);

  if (!isLoggedIn()) {
    return <Navigate to="/login" replace />;
  }

  return (
    <Layout>
      <Outlet />
    </Layout>
  );
};

// Import the GlobalErrorBoundary
import GlobalErrorBoundary from './components/GlobalErrorBoundary';
import SubscriptionCompletePage from "./pages/SubscriptionCompletePage";
import SubscriptionRefreshPage from "./pages/SubscriptionRefreshPage";

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <GlobalErrorBoundary>
            <Routes>
              {/* Public routes */}
              <Route path="/" element={
                isLoggedIn() ? <Navigate to="/dashboard" replace /> : <LandingPage />
              } />
              <Route path="/login" element={
                isLoggedIn() ? <Navigate to="/dashboard" replace /> : <Login />
              } />
              <Route path="/register" element={
                isLoggedIn() ? <Navigate to="/dashboard" replace /> : <Register />
              } />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route path="/privacy" element={<PrivacyPolicy />} />
              <Route path="/privacy-policy" element={<PrivacyPolicy />} />
              <Route path="/terms" element={<TermsOfUse />} />
              <Route path="/terms-of-use" element={<TermsOfUse />} />
              <Route path="/cookie-policy" element={<CookiePolicy />} />
              <Route path="/cookies" element={<CookiePolicy />} />
              {/* Public routes for HOA registration and subscription activation */}
              <Route path="/hoa-registration" element={<PublicHOARegistrationPage />} />
              <Route path="/subscription/activate/:token" element={<SubscriptionActivatePage />} />
              <Route path="/subscriptions/success" element={<SubscriptionSuccessPage />} />
              <Route path="/subscriptions/cancel" element={<SubscriptionCancelPage />} />
              <Route path="/subscriptions/return" element={<SubscriptionCompletePage />} />
              <Route path="/subscriptions/refresh" element={<SubscriptionRefreshPage />} />
            {/* Protected routes */}
            <Route element={<ProtectedLayout />}>
              <Route path="/dashboard" element={
                <SocketProvider>
                  <Index />
                </SocketProvider>
              } />
              <Route path="/tasks" element={
                <SocketProvider>
                  <Tasks />
                </SocketProvider>
              } />
              <Route path="/tasks/new" element={
                <SocketProvider>
                  <AddTaskForm />
                </SocketProvider>
              } />
              <Route path="/finances" element={
                <SocketProvider>
                  <Finances />
                </SocketProvider>
              } />
              <Route path="/finances/new" element={
                <SocketProvider>
                  <Finances />
                </SocketProvider>
              } />
              <Route path="/finances/reports" element={
                <SocketProvider>
                  <FinancialReportsPage />
                </SocketProvider>
              } />
              <Route path="/finances/budget" element={
                <SocketProvider>
                  <BudgetPlannerPage />
                </SocketProvider>
              } />
              <Route path="/admin/members" element={
                <SocketProvider>
                  <Members />
                </SocketProvider>
              } />
              <Route path="/notifications" element={
                <SocketProvider>
                  <Notifications />
                </SocketProvider>
              } />
              <Route path="/announcements" element={
                <SocketProvider>
                  <Announcements />
                </SocketProvider>
              } />
              {/* Redirect old messages routes to new direct messages */}
              <Route path="/messages" element={<Navigate to="/direct-messages" replace />} />
              <Route path="/messages/:conversationId" element={
                <SocketProvider>
                  <Messages />
                </SocketProvider>
              } />
              {/* Direct messaging routes that don't use socket.io */}
              <Route path="/direct-messages" element={<DirectMessages />} />
              <Route path="/direct-messages/:conversationId" element={<DirectMessages />} />
              <Route path="/settings" element={
                <SocketProvider>
                  <Settings />
                </SocketProvider>
              } />
              <Route path="/security" element={
                <SocketProvider>
                  <SecuritySettings />
                </SocketProvider>
              } />
              <Route path="/admin/approvals" element={
                <SocketProvider>
                  <AdminApprovals />
                </SocketProvider>
              } />
              <Route path="/admin/hoa-approvals" element={
                <SocketProvider>
                  <HOAApprovals />
                </SocketProvider>
              } />
              <Route path="/admin/all-hoas" element={
                <SocketProvider>
                  {/* Only allow company admins to access all HOAs management */}
                  {JSON.parse(localStorage.getItem('user') || '{}')?.role === 'company_admin' ? (
                    <AllHOAsManagementPage />
                  ) : (
                    <Navigate to="/dashboard" replace />
                  )}
                </SocketProvider>
              } />
              <Route path="/admin/announcements" element={
                <SocketProvider>
                  <AdminAnnouncements />
                </SocketProvider>
              } />
              <Route path="/admin/master" element={
                <SocketProvider>
                  <AdminMaster />
                </SocketProvider>
              } />
              <Route path="/calendar" element={
                <SocketProvider>
                  <Calendar />
                </SocketProvider>
              } />
              <Route path="/payments" element={
                <SocketProvider>
                  <MakePayment />
                </SocketProvider>
              } />
              <Route path="/documents" element={
                <SocketProvider>
                  <Documents />
                </SocketProvider>
              } />
              {/* HOA Office - accessible to both company admins and HOA admins */}
              <Route path="/admin/hoa-office" element={
                <SocketProvider>
                  <HOAOfficePage />
                </SocketProvider>
              } />
              <Route path="/admin/management" element={
                <SocketProvider>
                  <AdminManagementPage />
                </SocketProvider>
              } />
              <Route path="/admin/hoa-registration" element={
                <SocketProvider>
                  {/* Only allow company admins to access HOA registration */}
                  {JSON.parse(localStorage.getItem('user') || '{}')?.role === 'company_admin' ? (
                    <HOARegistrationPage />
                  ) : (
                    <Navigate to="/dashboard" replace />
                  )}
                </SocketProvider>
              } />
              <Route path="/admin/property-management" element={
                <SocketProvider>
                  <PropertyManagementPage />
                </SocketProvider>
              } />
              <Route path="/community-management" element={
                <SocketProvider>
                  <CommunityManagementPage />
                </SocketProvider>
              } />
              <Route path="/subscriptions" element={
                <SocketProvider>
                  <SubscriptionPage />
                </SocketProvider>
              } />
              <Route path="/subscriptions/:hoaId" element={
                <SocketProvider>
                  <SubscriptionPage />
                </SocketProvider>
              } />
            </Route>

            {/* Catch all */}
            <Route path="*" element={<NotFound />} />
          </Routes>
          </GlobalErrorBoundary>
          <CookieConsent />
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;

import api from '@/lib/axios';

export interface MaintenanceRequest {
  _id: string;
  property: {
    _id: string;
    address: string;
    type: string;
    status: string;
  };
  description: string;
  status: 'pending' | 'in_progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  dateSubmitted: string;
  resident: {
    _id: string;
    fullName: string;
  };
  assignedTo?: string;
  estimatedCompletion?: string;
  notes?: string;
  images?: string[];
  hoaId: string;
  createdAt: string;
  updatedAt: string;
}

export interface MaintenanceRequestCreateData {
  property: string;
  description: string;
  priority?: 'low' | 'medium' | 'high';
  resident?: string;
  assignedTo?: string;
  estimatedCompletion?: string;
  notes?: string;
  images?: string[];
  hoaId?: string;
}

export interface MaintenanceRequestUpdateData extends Partial<MaintenanceRequestCreateData> {
  status?: 'pending' | 'in_progress' | 'completed';
}

/**
 * Get all maintenance requests for the current user's HOA
 */
export const getMaintenanceRequests = async (): Promise<MaintenanceRequest[]> => {
  const response = await api.get('/api/maintenance');
  return response.data.data;
};

/**
 * Get a maintenance request by ID
 */
export const getMaintenanceRequest = async (id: string): Promise<MaintenanceRequest> => {
  const response = await api.get(`/api/maintenance/${id}`);
  return response.data.data;
};

/**
 * Create a new maintenance request
 */
export const createMaintenanceRequest = async (data: MaintenanceRequestCreateData): Promise<MaintenanceRequest> => {
  const response = await api.post('/api/maintenance', data);
  return response.data.data;
};

/**
 * Update a maintenance request
 */
export const updateMaintenanceRequest = async (id: string, data: MaintenanceRequestUpdateData): Promise<MaintenanceRequest> => {
  const response = await api.put(`/api/maintenance/${id}`, data);
  return response.data.data;
};

/**
 * Delete a maintenance request
 */
export const deleteMaintenanceRequest = async (id: string): Promise<void> => {
  await api.delete(`/api/maintenance/${id}`);
};

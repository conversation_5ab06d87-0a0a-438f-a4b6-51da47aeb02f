/**
 * DirectMessageService.ts
 *
 * A direct messaging service that bypasses socket.io and uses direct API calls
 * for maximum reliability in production environments.
 */

import axios from 'axios';

// Define types
interface User {
  _id: string;
  username?: string;
  fullName?: string;
  email?: string;
  role?: string;
}

interface Message {
  _id: string;
  sender: User;
  content: string;
  createdAt: string;
  read: boolean;
  edited?: boolean;
  editedAt?: string;
  deleted?: boolean;
  deletedAt?: string;
  conversation?: string;
}

interface Conversation {
  _id: string;
  participants: User[];
  lastMessage: Message;
  title?: string;
  unreadCount: number;
  updatedAt: string;
}

// Create the service
class DirectMessageService {
  private apiUrl: string;
  private token: string | null = null;
  private pollingInterval: number = 3000; // 3 seconds
  private conversationsPollingId: NodeJS.Timeout | null = null;
  private messagesPollingId: NodeJS.Timeout | null = null;
  private currentConversationId: string | null = null;
  private onConversationsUpdate: ((conversations: Conversation[]) => void) | null = null;
  private onMessagesUpdate: ((messages: Message[]) => void) | null = null;
  private lastFetchTime: number = 0;

  constructor() {
    this.apiUrl = import.meta.env.VITE_API_URL || 'https://hoa-management-app-dad2f9d126ae.herokuapp.com';
    this.updateToken();

    // Log initialization
    console.log('DirectMessageService initialized with API URL:', this.apiUrl);
  }

  // Update the token from localStorage
  private updateToken(): void {
    try {
      const userData = localStorage.getItem('user');
      if (userData) {
        const user = JSON.parse(userData);
        this.token = user.token;
        console.log('Token updated, length:', this.token?.length);
      } else {
        console.warn('No user data found in localStorage');
        this.token = null;
      }
    } catch (error) {
      console.error('Error updating token:', error);
      this.token = null;
    }
  }

  // Get headers for API requests
  private getHeaders(): Record<string, string> {
    this.updateToken(); // Always get the latest token
    return {
      'Content-Type': 'application/json',
      'Authorization': this.token ? `Bearer ${this.token}` : ''
    };
  }

  // Start polling for conversations
  public startPollingConversations(callback: (conversations: Conversation[]) => void): void {
    this.onConversationsUpdate = callback;

    // Clear any existing polling
    if (this.conversationsPollingId) {
      clearInterval(this.conversationsPollingId);
    }

    // Fetch immediately
    this.fetchConversations();

    // Then set up polling
    this.conversationsPollingId = setInterval(() => {
      this.fetchConversations();
    }, this.pollingInterval);

    console.log('Started polling for conversations');
  }

  // Stop polling for conversations
  public stopPollingConversations(): void {
    if (this.conversationsPollingId) {
      clearInterval(this.conversationsPollingId);
      this.conversationsPollingId = null;
      console.log('Stopped polling for conversations');
    }
  }

  // Start polling for messages in a specific conversation
  public startPollingMessages(conversationId: string, callback: (messages: Message[]) => void): void {
    this.currentConversationId = conversationId;
    this.onMessagesUpdate = callback;

    // Clear any existing polling
    if (this.messagesPollingId) {
      clearInterval(this.messagesPollingId);
    }

    // Fetch immediately using direct fetch for reliability
    this.fetchMessagesDirectly(conversationId, callback);

    // Then set up polling
    this.messagesPollingId = setInterval(() => {
      this.fetchMessagesDirectly(conversationId, callback);
    }, this.pollingInterval);

    console.log('Started polling for messages in conversation:', conversationId);
  }

  // Direct fetch method for maximum reliability
  public async fetchMessagesDirectly(conversationId: string, callback: (data: Message[]) => void): Promise<void> {
    try {
      console.log(`Direct fetch for messages in conversation: ${conversationId}`);

      // Use axios instead of fetch for better error handling and CORS support
      try {
        console.log('Using axios for direct fetch');
        const response = await axios.get(`${this.apiUrl}/api/messages/conversations/${conversationId}`, {
          headers: this.getHeaders()
        });

        const data = response.data;
        console.log(`AXIOS: Direct fetch found ${data.length} messages`);

        if (data && Array.isArray(data)) {
          this.processMessages(data, conversationId, callback);
          return;
        }
      } catch (axiosError) {
        console.warn('Axios fetch failed, falling back to fetch API:', axiosError);
      }

      // Fallback to fetch API if axios fails
      console.log('Falling back to fetch API');
      const response = await fetch(`${this.apiUrl}/api/messages/conversations/${conversationId}`, {
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        mode: 'cors',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Failed to fetch messages (${response.status}): ${errorText}`);

        // Try one more time with a different approach
        console.log('Trying one more approach with XMLHttpRequest');
        this.fetchWithXHR(conversationId, callback);
        return;
      }

      const data = await response.json();
      console.log(`FETCH: Direct fetch found ${data.length} messages`);

      if (data && Array.isArray(data)) {
        this.processMessages(data, conversationId, callback);
        return;
      }

      console.error('Invalid data format received:', data);
      throw new Error('Invalid data format received from server');
    } catch (error) {
      console.error('Error in direct fetch for messages:', error);

      // Try one more time with a different approach
      console.log('Error occurred, trying with XMLHttpRequest as last resort');
      try {
        this.fetchWithXHR(conversationId, callback);
      } catch (xhrError) {
        console.error('All fetch methods failed:', xhrError);
        throw error;
      }
    }
  }

  // Helper method to process messages
  private processMessages(data: any[], conversationId: string, callback: (data: Message[]) => void): void {
    // Log the first message for debugging
    if (data.length > 0) {
      console.log('First message sample:', {
        id: data[0]._id,
        sender: data[0].sender?._id || 'unknown',
        content: data[0].content?.substring(0, 20) + (data[0].content?.length > 20 ? '...' : ''),
        conversation: data[0].conversation,
        deleted: data[0].deleted
      });
    }

    // Filter out messages that don't belong to this conversation
    const filteredMessages = data.filter(msg =>
      msg.conversation === conversationId ||
      msg.conversation?.toString() === conversationId.toString()
    );

    console.log(`After filtering, ${filteredMessages.length} messages remain`);

    // Fix any messages with missing sender info
    const fixedMessages = filteredMessages.map(msg => {
      if (!msg.sender || !msg.sender._id) {
        console.warn('Fixing message with missing sender:', msg._id);
        // Create a placeholder sender if needed
        return {
          ...msg,
          sender: msg.sender || {
            _id: 'unknown',
            username: 'Unknown User',
            fullName: 'Unknown User'
          }
        };
      }
      return msg;
    });

    // Call the callback with the fixed messages
    if (callback) {
      console.log(`Calling callback with ${fixedMessages.length} messages`);
      callback(fixedMessages);

      // Also update our internal state if this is the current conversation
      if (this.currentConversationId === conversationId && this.onMessagesUpdate) {
        this.onMessagesUpdate(fixedMessages);
      }
    } else {
      console.warn('No callback provided for fetchMessagesDirectly');
    }
  }

  // Fallback method using XMLHttpRequest
  private fetchWithXHR(conversationId: string, callback: (data: Message[]) => void): void {
    console.log('Fetching with XMLHttpRequest');

    const xhr = new XMLHttpRequest();
    xhr.open('GET', `${this.apiUrl}/api/messages/conversations/${conversationId}`, true);
    xhr.setRequestHeader('Authorization', `Bearer ${this.token}`);
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.withCredentials = true;

    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const data = JSON.parse(xhr.responseText);
          console.log(`XHR: Direct fetch found ${data.length} messages`);

          if (data && Array.isArray(data)) {
            this.processMessages(data, conversationId, callback);
          } else {
            console.error('Invalid data format received from XHR:', data);
          }
        } catch (parseError) {
          console.error('Error parsing XHR response:', parseError);
        }
      } else {
        console.error('XHR request failed:', xhr.status, xhr.statusText);
      }
    };

    xhr.onerror = () => {
      console.error('XHR network error');
    };

    xhr.send();
  }

  // Stop polling for messages
  public stopPollingMessages(): void {
    if (this.messagesPollingId) {
      clearInterval(this.messagesPollingId);
      this.messagesPollingId = null;
      this.currentConversationId = null;
      console.log('Stopped polling for messages');
    }
  }

  // Fetch conversations
  public async fetchConversations(): Promise<void> {
    try {
      // Throttle requests to avoid overwhelming the server
      const now = Date.now();
      if (now - this.lastFetchTime < 1000) { // Don't fetch more than once per second
        return;
      }
      this.lastFetchTime = now;

      console.log('Fetching conversations directly...');

      // Log the request details for debugging
      console.log('Request URL:', `${this.apiUrl}/api/messages/conversations`);
      console.log('Request headers:', {
        ...this.getHeaders(),
        'Authorization': this.token ? `Bearer ${this.token.substring(0, 10)}...` : 'No token'
      });

      // Try with axios first
      try {
        const response = await axios.get(`${this.apiUrl}/api/messages/conversations`, {
          headers: this.getHeaders()
        });

        console.log('Conversations API response status:', response.status);
        console.log('Conversations fetched successfully:', response.data.length);

        // Log the first conversation for debugging
        if (response.data && response.data.length > 0) {
          const firstConv = response.data[0];
          console.log('First conversation sample:', {
            id: firstConv._id,
            participantsCount: firstConv.participants?.length || 0,
            hasLastMessage: !!firstConv.lastMessage,
            updatedAt: firstConv.updatedAt
          });
        }

        if (this.onConversationsUpdate) {
          this.onConversationsUpdate(response.data);
        }
        return;
      } catch (axiosError) {
        console.warn('Axios fetch for conversations failed, falling back to fetch API:', axiosError);
      }

      // Fallback to fetch API
      console.log('Falling back to fetch API for conversations');
      try {
        const response = await fetch(`${this.apiUrl}/api/messages/conversations`, {
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          },
          mode: 'cors',
          credentials: 'include'
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Failed to fetch conversations (${response.status}): ${errorText}`);

          // Try one more time with XMLHttpRequest
          this.fetchConversationsWithXHR();
          return;
        }

        const data = await response.json();
        console.log('FETCH: Conversations fetched successfully:', data.length);

        if (this.onConversationsUpdate) {
          this.onConversationsUpdate(data);
        }
      } catch (fetchError) {
        console.error('Fetch API error:', fetchError);

        // Try one more time with XMLHttpRequest as last resort
        this.fetchConversationsWithXHR();
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);

      // More detailed error logging
      if (axios.isAxiosError(error)) {
        console.error('Axios error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          config: {
            url: error.config?.url,
            method: error.config?.method,
            headers: {
              ...error.config?.headers,
              'Authorization': 'Bearer [REDACTED]'
            }
          }
        });
      }

      // Try one more time with XMLHttpRequest as last resort
      this.fetchConversationsWithXHR();
    }
  }

  // Fallback method for fetching conversations using XMLHttpRequest
  private fetchConversationsWithXHR(): void {
    console.log('Fetching conversations with XMLHttpRequest');

    const xhr = new XMLHttpRequest();
    xhr.open('GET', `${this.apiUrl}/api/messages/conversations`, true);
    xhr.setRequestHeader('Authorization', `Bearer ${this.token}`);
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.withCredentials = true;

    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const data = JSON.parse(xhr.responseText);
          console.log(`XHR: Fetched ${data.length} conversations`);

          if (this.onConversationsUpdate) {
            this.onConversationsUpdate(data);
          }
        } catch (parseError) {
          console.error('Error parsing XHR response for conversations:', parseError);
        }
      } else {
        console.error('XHR request for conversations failed:', xhr.status, xhr.statusText);
      }
    };

    xhr.onerror = () => {
      console.error('XHR network error for conversations');
    };

    xhr.send();
  }

  // Fetch messages for a conversation
  public async fetchMessages(conversationId: string): Promise<void> {
    try {
      // Throttle requests to avoid overwhelming the server
      const now = Date.now();
      if (now - this.lastFetchTime < 1000) { // Don't fetch more than once per second
        return;
      }
      this.lastFetchTime = now;

      console.log(`Fetching messages for conversation ${conversationId} directly...`);

      // Log the request details for debugging
      console.log('Request URL:', `${this.apiUrl}/api/messages/conversations/${conversationId}`);
      console.log('Request headers:', {
        ...this.getHeaders(),
        'Authorization': this.token ? `Bearer ${this.token.substring(0, 10)}...` : 'No token'
      });

      // First try the standard endpoint
      let response;
      try {
        response = await axios.get(`${this.apiUrl}/api/messages/conversations/${conversationId}`, {
          headers: this.getHeaders()
        });
      } catch (error) {
        console.error('Error with standard message fetch, trying fallback approach:', error);

        // If the standard endpoint fails, try to get the conversation details first
        const detailsResponse = await axios.get(`${this.apiUrl}/api/messages/conversations/${conversationId}/details`, {
          headers: this.getHeaders()
        });

        console.log('Fetched conversation details:', detailsResponse.data);

        // Try the messages endpoint again
        response = await axios.get(`${this.apiUrl}/api/messages/conversations/${conversationId}`, {
          headers: this.getHeaders()
        });
      }

      // Ensure messages are for this conversation only
      if (response.data && Array.isArray(response.data)) {
        const filteredMessages = response.data.filter(msg =>
          msg.conversation === conversationId ||
          msg.conversation?.toString() === conversationId.toString()
        );

        if (filteredMessages.length !== response.data.length) {
          console.warn(`Filtered out ${response.data.length - filteredMessages.length} messages that didn't belong to conversation ${conversationId}`);
          response.data = filteredMessages;
        }

        // If we still have no messages, try to get messages by sender/recipient
        if (filteredMessages.length === 0) {
          console.log('No messages found after filtering, trying to find messages by sender/recipient');

          try {
            // Get the current user
            const userData = localStorage.getItem('user');
            if (userData) {
              const currentUser = JSON.parse(userData);

              // Get conversation details to find the other participant
              const detailsResponse = await axios.get(`${this.apiUrl}/api/messages/conversations/${conversationId}/details`, {
                headers: this.getHeaders()
              });

              if (detailsResponse.data && detailsResponse.data.participants) {
                const otherParticipant = detailsResponse.data.participants.find(
                  (p: User) => p._id !== currentUser._id
                );

                if (otherParticipant) {
                  console.log('Found other participant:', otherParticipant._id);

                  // Send a test message to ensure there's at least one message
                  const testMessageResult = await this.sendMessage({
                    recipientId: otherParticipant._id,
                    content: "Hello! I'm troubleshooting our conversation.",
                    conversationId
                  });

                  console.log('Test message sent:', testMessageResult);

                  // Try fetching messages again
                  const retryResponse = await axios.get(`${this.apiUrl}/api/messages/conversations/${conversationId}`, {
                    headers: this.getHeaders()
                  });

                  if (retryResponse.data && Array.isArray(retryResponse.data) && retryResponse.data.length > 0) {
                    console.log(`Found ${retryResponse.data.length} messages after sending test message`);
                    response.data = retryResponse.data;
                  }
                }
              }
            }
          } catch (fallbackError) {
            console.error('Error in fallback message fetching:', fallbackError);
          }
        }
      }

      console.log('Messages API response status:', response.status);
      console.log('Messages fetched successfully:', response.data.length);

      // Log a sample message for debugging
      if (response.data && response.data.length > 0) {
        const sampleMessage = response.data[0];
        console.log('Sample message:', {
          id: sampleMessage._id,
          sender: sampleMessage.sender?._id,
          content: sampleMessage.content?.substring(0, 20) + '...',
          createdAt: sampleMessage.createdAt
        });

        // Check if messages have proper sender information
        const missingInfo = response.data.filter(msg => !msg.sender || !msg.sender._id);
        if (missingInfo.length > 0) {
          console.warn(`Found ${missingInfo.length} messages with missing sender information`);

          // Try to fix messages with missing sender info
          response.data = response.data.map(msg => {
            if (!msg.sender || !msg.sender._id) {
              console.warn('Fixing message with missing sender:', msg._id);
              // Create a placeholder sender if needed
              return {
                ...msg,
                sender: msg.sender || {
                  _id: 'unknown',
                  username: 'Unknown User',
                  fullName: 'Unknown User'
                }
              };
            }
            return msg;
          });
        }
      }

      if (this.onMessagesUpdate && this.currentConversationId === conversationId) {
        this.onMessagesUpdate(response.data);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);

      // More detailed error logging
      if (axios.isAxiosError(error)) {
        console.error('Axios error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          config: {
            url: error.config?.url,
            method: error.config?.method,
            headers: {
              ...error.config?.headers,
              'Authorization': 'Bearer [REDACTED]'
            }
          }
        });

        // If the conversation was not found, we might need to create it
        if (error.response?.status === 404) {
          console.log('Conversation not found. This might be a new conversation that needs to be created.');
        }
      }
    }
  }

  // Send a message
  public async sendMessage(data: { recipientId: string; content: string; conversationId?: string }): Promise<any> {
    try {
      console.log('Sending message directly:', {
        recipientId: data.recipientId,
        content: data.content?.substring(0, 20) + (data.content?.length > 20 ? '...' : ''),
        conversationId: data.conversationId
      });

      // Log the request details for debugging
      console.log('Request URL:', `${this.apiUrl}/api/messages/send`);
      console.log('Request headers:', {
        ...this.getHeaders(),
        'Authorization': this.token ? `Bearer ${this.token.substring(0, 10)}...` : 'No token'
      });

      const response = await axios.post(`${this.apiUrl}/api/messages/send`, data, {
        headers: this.getHeaders()
      });

      console.log('Message sent successfully. Response status:', response.status);
      console.log('Response data:', {
        messageId: response.data.message?._id,
        conversationId: response.data.conversationId,
        sender: response.data.message?.sender?._id
      });

      // Make sure the message has a populated sender
      if (response.data.message && !response.data.message.sender) {
        console.log('Message missing sender information, adding current user as sender');
        try {
          const userData = localStorage.getItem('user');
          if (userData) {
            const user = JSON.parse(userData);
            response.data.message.sender = {
              _id: user._id,
              username: user.username,
              fullName: user.fullName,
              email: user.email,
              role: user.role
            };
          }
        } catch (error) {
          console.error('Error adding sender to message:', error);
        }
      }

      // Force refresh conversations after a delay
      setTimeout(() => {
        console.log('Refreshing conversations after sending message');
        this.fetchConversations();
      }, 1000); // Longer delay to ensure the server has processed the message

      return response.data;
    } catch (error) {
      console.error('Error sending message:', error);

      // More detailed error logging
      if (axios.isAxiosError(error)) {
        console.error('Axios error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          config: {
            url: error.config?.url,
            method: error.config?.method,
            headers: {
              ...error.config?.headers,
              'Authorization': 'Bearer [REDACTED]'
            },
            data: {
              recipientId: data.recipientId,
              content: data.content?.substring(0, 20) + (data.content?.length > 20 ? '...' : ''),
              conversationId: data.conversationId
            }
          }
        });
      }

      throw error;
    }
  }

  // Delete a message
  public async deleteMessage(messageId: string): Promise<any> {
    try {
      console.log('Deleting message directly:', messageId);

      // Use fetch instead of axios for more reliable error handling
      const response = await fetch(`${this.apiUrl}/api/messages/${messageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error deleting message (${response.status}):`, errorText);
        throw new Error(`Failed to delete message: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Message deleted successfully:', data);

      // Force refresh conversations and messages
      this.fetchConversations();
      if (this.currentConversationId) {
        this.fetchMessagesDirectly(this.currentConversationId, this.onMessagesUpdate!);
      }

      return data;
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }

  // Edit a message
  public async editMessage(messageId: string, content: string): Promise<any> {
    try {
      console.log('Editing message directly:', { messageId, content });
      const response = await axios.put(`${this.apiUrl}/api/messages/${messageId}`, { content }, {
        headers: this.getHeaders()
      });

      console.log('Message edited successfully:', response.data);

      // Force refresh conversations and messages
      this.fetchConversations();
      if (this.currentConversationId) {
        this.fetchMessages(this.currentConversationId);
      }

      return response.data;
    } catch (error) {
      console.error('Error editing message:', error);
      throw error;
    }
  }

  // Delete a conversation
  public async hideConversation(conversationId: string): Promise<any> {
    try {
      console.log('Deleting conversation:', conversationId);

      // Use fetch for more reliable error handling
      const response = await fetch(`${this.apiUrl}/api/messages/conversations/${conversationId}/hide`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error deleting conversation (${response.status}):`, errorText);
        throw new Error(`Failed to delete conversation: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Conversation deleted successfully:', data);

      // Update the local conversations cache to remove this conversation
      if (this.conversations && Array.isArray(this.conversations)) {
        this.conversations = this.conversations.filter(c => c._id !== conversationId);
      }

      return data;
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  }

  // This method is no longer needed but kept for compatibility
  public async unhideConversation(conversationId: string): Promise<any> {
    console.warn('unhideConversation is deprecated - conversations are now permanently deleted');
    return { success: false, message: 'This feature has been removed. Conversations are now permanently deleted.' };
  }

  // This method is no longer needed but kept for compatibility
  public async fetchAllConversations(): Promise<any> {
    console.warn('fetchAllConversations is deprecated - use fetchConversations instead');
    return this.fetchConversations();
  }
}

// Create and export a singleton instance
const directMessageService = new DirectMessageService();
export default directMessageService;

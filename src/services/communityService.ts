import api from '@/lib/axios';

export interface Community {
  _id: string;
  name: string;
  description?: string;
  streetAddress?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  communityCode: string;
  hoaId: string;
  type?: 'single-family' | 'townhouse' | 'condo' | 'apartment' | 'mixed' | 'other';
  unitCount?: number;
  rules?: string;
  status: 'active' | 'inactive';
  visibility?: 'public' | 'private' | 'hoa_only';
  permissions?: {
    memberVisibility?: boolean;
    financeVisibility?: boolean;
    documentVisibility?: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CommunityCreateData {
  name: string;
  description?: string;
  streetAddress?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  hoaId: string;
  type?: 'single-family' | 'townhouse' | 'condo' | 'apartment' | 'mixed' | 'other';
  unitCount?: number;
  rules?: string;
  visibility?: 'public' | 'private' | 'hoa_only';
  permissions?: {
    memberVisibility?: boolean;
    financeVisibility?: boolean;
    documentVisibility?: boolean;
  };
}

export interface CommunityUpdateData extends Partial<CommunityCreateData> {
  status?: 'active' | 'inactive';
}

/**
 * Get all communities
 */
export const getCommunities = async (): Promise<Community[]> => {
  const response = await api.get('/api/communities');
  return response.data;
};

/**
 * Get communities by HOA ID
 */
export const getCommunitiesByHoa = async (hoaId: string): Promise<Community[]> => {
  const response = await api.get(`/api/communities/hoa/${hoaId}`);
  return response.data;
};

/**
 * Get a community by ID
 */
export const getCommunity = async (id: string): Promise<Community> => {
  const response = await api.get(`/api/communities/${id}`);
  return response.data;
};

/**
 * Get a community by code
 */
export const getCommunityByCode = async (code: string): Promise<Community> => {
  const response = await api.get(`/api/communities/code/${code}`);
  return response.data;
};

/**
 * Create a new community
 */
export const createCommunity = async (data: CommunityCreateData): Promise<Community> => {
  const response = await api.post('/api/communities', data);
  return response.data;
};

/**
 * Update a community
 */
export const updateCommunity = async (id: string, data: CommunityUpdateData): Promise<Community> => {
  const response = await api.put(`/api/communities/${id}`, data);
  return response.data;
};

/**
 * Delete a community
 */
export const deleteCommunity = async (id: string): Promise<void> => {
  await api.delete(`/api/communities/${id}`);
};

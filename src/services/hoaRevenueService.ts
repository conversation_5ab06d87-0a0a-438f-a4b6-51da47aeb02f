import api from '@/lib/axios';

export interface HOARevenueSummary {
  _id: string;
  name: string;
  code: string;
  subscriptionRevenue: number;
  financeRevenue: number;
  totalRevenue: number;
  subscriptionStatus: string;
  subscriptionTier: string;
}

export interface HOARevenueDetail {
  hoa: {
    _id: string;
    name: string;
    code: string;
    subscriptionStatus: string;
    subscriptionTier: string;
  };
  subscriptionPayments: Array<{
    _id: string;
    amount: number;
    status: string;
    createdAt: string;
  }>;
  financeEntries: Array<{
    _id: string;
    amount: number;
    category: string;
    note: string;
    createdAt: string;
  }>;
  monthlyRevenue: Array<{
    month: string;
    revenue: number;
  }>;
}

/**
 * Get revenue summary for all HOAs
 * @returns Promise<HOARevenueSummary[]>
 */
export const getHOARevenueSummary = async (): Promise<HOARevenueSummary[]> => {
  try {
    const response = await api.get('/api/hoa-revenue/summary');
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching HOA revenue summary:', error);
    throw error;
  }
};

/**
 * Get detailed revenue data for a specific HOA
 * @param hoaId - The ID of the HOA
 * @returns Promise<HOARevenueDetail>
 */
export const getHOARevenueDetail = async (hoaId: string): Promise<HOARevenueDetail> => {
  try {
    const response = await api.get(`/api/hoa-revenue/${hoaId}`);
    return response.data.data || null;
  } catch (error) {
    console.error(`Error fetching HOA revenue detail for HOA ${hoaId}:`, error);
    throw error;
  }
};

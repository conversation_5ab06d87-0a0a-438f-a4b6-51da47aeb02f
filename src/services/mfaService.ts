import api from '@/lib/axios';

export interface MFAStatus {
  mfaEnabled: boolean;
  phoneNumber: string | null;
}

/**
 * Get MFA status for the current user
 */
export const getMFAStatus = async (): Promise<MFAStatus> => {
  const response = await api.get('/api/mfa/status');
  return response.data.data;
};

/**
 * Enable MFA for the current user
 */
export const enableMFA = async (phoneNumber: string): Promise<MFAStatus> => {
  const response = await api.post('/api/mfa/enable', { phoneNumber });
  return response.data.data;
};

/**
 * Disable MFA for the current user
 */
export const disableMFA = async (): Promise<MFAStatus> => {
  const response = await api.post('/api/mfa/disable');
  return response.data.data;
};

/**
 * Send verification code to the user's phone number
 */
export const sendVerificationCode = async (phoneNumber: string): Promise<void> => {
  await api.post('/api/mfa/send-verification', { phoneNumber });
};

/**
 * Verify code and complete login
 */
export const verifyCode = async (phoneNumber: string, code: string): Promise<{ token: string; user: any }> => {
  const response = await api.post('/api/mfa/verify', { phoneNumber, code });
  return {
    token: response.data.token,
    user: response.data.user
  };
};

import api from '@/lib/axios';

export interface HOA {
  subscription: any;
  _id: string;
  hoaCommunityName: string;
  hoaCommunityCode: string;
  hoaStreetAddress: string;
  hoaCity: string;
  hoaState: string;
  hoaZipCode: string;
  hoaPaymentInfo: {
    paymentAmount: number;
    paymentMethod: string;
    paymentInstructions: string;
    dueDate: number;
  };
  createdAt: string;
  updatedAt: string;
  lastUpdated?: string;
}

export interface HOAUpdateData {
  hoaCommunityName?: string;
  hoaStreetAddress?: string;
  hoaCity?: string;
  hoaState?: string;
  hoaZipCode?: string;
  hoaPaymentInfo?: {
    paymentMethod?: string;
    paymentInstructions?: string;
    dueDate?: number;
    paymentAmount?: number;
  };
  lastUpdated?: string;
}

/**
 * Get HOA by ID
 */
export const getHOAById = async (id: string): Promise<HOA> => {
  const response = await api.get(`/api/hoa/${id}`);
  return response.data.data;
};

/**
 * Get HOA by community code
 */
export const getHOAByCode = async (code: string): Promise<HOA> => {
  const response = await api.get(`/api/hoa/code/${code}`);
  return response.data.data;
};

/**
 * Get all HOAs (admin only)
 */
export const getAllHOAs = async (): Promise<HOA[]> => {
  const response = await api.get('/api/hoa');
  return response.data.data;
};

/**
 * Create a new HOA (admin only)
 */
export const createHOA = async (hoaData: Omit<HOA, '_id' | 'createdAt' | 'updatedAt' | 'hoaCommunityCode'>): Promise<HOA> => {
  const response = await api.post('/api/hoa', hoaData);
  return response.data.data;
};

/**
 * Register a new HOA (public)
 */
export const registerHOA = async (formData: FormData): Promise<any> => {
  const response = await api.post('/api/hoa/register', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data.data;
};

/**
 * Update an existing HOA (admin only)
 */
export const updateHOA = async (id: string, hoaData: HOAUpdateData): Promise<HOA> => {
  const response = await api.put(`/api/hoa/${id}`, hoaData);
  return response.data.data;
};

/**
 * Get the current user's HOA
 * This uses the user's hoaId to fetch the HOA details
 */
export const getCurrentUserHOA = async (): Promise<HOA | null> => {
  try {
    // First get the current user to get their hoaId
    const userResponse = await api.get('/api/auth/me');
    const user = userResponse.data.user;

    // If user has no hoaId, return null
    if (!user.hoaId) {
      return null;
    }

    // Fetch the HOA details using the hoaId
    return await getHOAById(user.hoaId._id);
  } catch (error) {
    console.error('Error fetching current user HOA:', error);
    return null;
  }
};

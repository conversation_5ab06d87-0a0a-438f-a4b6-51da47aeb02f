import api from '@/lib/axios';

export interface Property {
  _id: string;
  address: string;
  type: string;
  status: 'occupied' | 'vacant' | 'maintenance';
  resident: {
    _id: string;
    fullName: string;
  };
  nextInspection?: string;
  yearBuilt?: string;
  squareFeet?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  documents?: Array<{
    _id: string;
    name: string;
    fileType: string;
    fileUrl: string;
    uploadDate: string;
  }>;
  maintenanceHistory?: Array<{
    _id: string;
    description: string;
    status: string;
    cost?: number;
    date: string;
  }>;
  hoaId: string;
  communityId?: string;
  createdBy?: {
    _id: string;
    username: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

export interface PropertyCreateData {
  address: string;
  type: string;
  status?: 'occupied' | 'vacant' | 'maintenance';
  nextInspection?: string;
  yearBuilt?: string;
  squareFeet?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  hoaId?: string;
  communityId?: string;
  resident: {
    _id: string;
    fullName: string;
  }
}

export interface PropertyUpdateData extends Partial<PropertyCreateData> {
  documents?: Array<{
    name: string;
    fileType: string;
    fileUrl: string;
  }>;
  maintenanceHistory?: Array<{
    description: string;
    status: string;
    cost?: number;
    date?: string;
  }>;
}

/**
 * Get all properties for the current user's HOA
 */
export const getProperties = async (): Promise<Property[]> => {
  const response = await api.get('/api/properties');
  return response.data.data;
};

/**
 * Get all list of member of current HOA
 */
export const getAllMembers = async () => {
  const response = await api.get('/api/members/approved');
  return response.data.users;
}

/**
 * Get a property by ID
 */
export const getProperty = async (id: string): Promise<Property> => {
  const response = await api.get(`/api/properties/${id}`);
  return response.data.data;
};

/**
 * Create a new property
 */
export const createProperty = async (data: PropertyCreateData): Promise<Property> => {
  const response = await api.post('/api/properties', data);
  return response.data.data;
};

/**
 * Update a property
 */
export const updateProperty = async (id: string, data: PropertyUpdateData): Promise<Property> => {
  const response = await api.put(`/api/properties/${id}`, data);
  return response.data.data;
};

/**
 * Delete a property
 * @param id The ID of the property to delete
 * @param forceDelete Whether to force delete the property even if it has maintenance history
 */
export const deleteProperty = async (id: string, forceDelete: boolean = false): Promise<void> => {
  try {
    // First check if the property exists and if there are any dependencies
    const checkResponse = await api.get(`/api/properties/${id}`);
    const property = checkResponse.data.data;

    // Check if property has maintenance requests or other dependencies
    // Only check if maintenanceHistory exists and is an array with items
    if (!forceDelete &&
        property.maintenanceHistory &&
        Array.isArray(property.maintenanceHistory) &&
        property.maintenanceHistory.length > 0) {
      console.log("Property has maintenance history:", property.maintenanceHistory);
      throw new Error('Cannot delete property with maintenance history. Please archive it instead or use force delete.');
    } else {
      console.log("Property has no maintenance history or force delete is enabled");
    }

    // If all checks pass or force delete is enabled, proceed with deletion
    // Add the skipMaintenanceCheck parameter if force delete is enabled
    await api.delete(`/api/properties/${id}${forceDelete ? '?skipMaintenanceCheck=true' : ''}`);
  } catch (error: any) {
    console.error('Error deleting property:', error);

    // Handle specific error cases
    if (error.response?.status === 400) {
      // This is likely a validation error from the server (e.g., has maintenance history)
      throw new Error(error.response.data.message || 'Cannot delete this property. Try archiving it instead or use force delete.');
    } else if (error.response?.status === 500) {
      // Check for the specific maintenance module error
      if (error.response.data?.error?.includes("Cannot find module '../models/maintenance'")) {
        // If it's the maintenance module error, we can still try to delete
        try {
          // Try direct deletion without the maintenance check
          await api.delete(`/api/properties/${id}?skipMaintenanceCheck=true`);
          return; // If successful, just return
        } catch (secondError: any) {
          console.error('Second attempt to delete property failed:', secondError);
          // If the second attempt also fails, throw a more specific error
          throw new Error('Server error: The property could not be deleted. Try archiving it instead.');
        }
      }

      throw new Error('Server error: The property may have associated records. Try archiving it instead or use force delete.');
    } else if (error.response?.status === 404) {
      throw new Error('Property not found. It may have been already deleted.');
    } else if (error.response?.status === 403) {
      throw new Error('You do not have permission to delete this property.');
    } else if (error.message) {
      // Use the error message if it exists
      throw new Error(error.message);
    } else {
      // Generic error
      throw new Error('Failed to delete property. Try archiving it instead or use force delete.');
    }
  }
};

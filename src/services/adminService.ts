import api from '@/lib/axios';

export interface AdminUser {
  _id: string;
  username: string;
  email: string;
  fullName: string;
  propertyAddress: string;
  role: string;
  hoaId?: string;
  hoaCommunityCode?: string;
  phoneNumber?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAdminData {
  username: string;
  email: string;
  password: string;
  fullName: string;
  propertyAddress: string;
  hoaId?: string;
  phoneNumber?: string;
}

export interface UpdateAdminData {
  username?: string;
  email?: string;
  password?: string;
  fullName?: string;
  propertyAddress?: string;
  hoaId?: string;
  phoneNumber?: string;
}

/**
 * Get all admin users
 */
export const getAllAdmins = async (): Promise<AdminUser[]> => {
  const response = await api.get('/api/admin');
  return response.data.data;
};

/**
 * Create a new admin user
 */
export const createAdmin = async (adminData: CreateAdminData): Promise<AdminUser> => {
  const response = await api.post('/api/admin', adminData);
  return response.data.data;
};

/**
 * Update an existing admin user
 */
export const updateAdmin = async (id: string, adminData: UpdateAdminData): Promise<AdminUser> => {
  const response = await api.put(`/api/admin/${id}`, adminData);
  return response.data.data;
};

/**
 * Delete an admin user
 */
export const deleteAdmin = async (id: string): Promise<void> => {
  await api.delete(`/api/admin/${id}`);
};

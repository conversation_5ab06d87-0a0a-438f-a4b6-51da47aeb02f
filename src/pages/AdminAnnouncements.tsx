import React, { useState, useEffect } from 'react';
import api from '@/lib/axios';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Users, Building2, MapPin, Mail, CheckCircle, Paperclip } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import FileUpload, { FileWithPreview } from '@/components/ui/file-upload';

// Types for targeting
interface Resident {
  id: string;
  email: string;
  username: string;
  fullName: string;
  streetAddress: string;
}

interface Street {
  id: string;
  name: string;
  code: string;
  description?: string;
  residentCount?: number;
}

interface HOA {
  id: string;
  name: string;
  code: string;
}

const AdminAnnouncements = () => {
  const { user, token } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Form state
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [sendEmail, setSendEmail] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // File attachment state
  const [attachments, setAttachments] = useState<FileWithPreview[]>([]);

  // Sidebar selection state
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);

  // Targeting state
  const [targetingMode, setTargetingMode] = useState<'all' | 'specific'>('all');
  const [availableResidents, setAvailableResidents] = useState<Resident[]>([]);
  const [selectedResidents, setSelectedResidents] = useState<string[]>([]);
  const [availableStreets, setAvailableStreets] = useState<Street[]>([]);
  const [availableHOAs, setAvailableHOAs] = useState<HOA[]>([]);
  const [selectedHOA, setSelectedHOA] = useState<string | null>(null);
  const [selectedStreet, setSelectedStreet] = useState<string | null>(null);
  const [isLoadingTargets, setIsLoadingTargets] = useState(false);

  // Get user role info
  const isCompanyAdmin = user?.role === 'company_admin';
  const isHOAAdmin = user?.role === 'admin';

  // Redirect if not admin or company admin
  useEffect(() => {
    if (user && user.role !== 'admin' && user.role !== 'company_admin') {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  // Initialize from sidebar selection and listen for changes
  useEffect(() => {
    // Initialize from localStorage
    const storedCommunityId = localStorage.getItem('selectedCommunityId');
    if (storedCommunityId) {
      setSelectedCommunityId(storedCommunityId);
      fetchCommunityDetails(storedCommunityId);
    }

    const handleCommunityChange = async (event: CustomEvent) => {
      const { communityId } = event.detail;
      console.log('AdminAnnouncements: Received community selection change:', { communityId });

      if (communityId && communityId !== 'all') {
        setSelectedCommunityId(communityId);
        await fetchCommunityDetails(communityId);
      } else {
        // Clear selection
        setSelectedCommunityId(null);
        setSelectedCommunityName(null);
        clearTargetingData();
      }
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, []);

  // Re-fetch community details when user becomes available
  useEffect(() => {
    if (user && selectedCommunityId) {
      console.log('🔍 User became available, re-fetching community details for:', selectedCommunityId);
      fetchCommunityDetails(selectedCommunityId);
    }
  }, [user, selectedCommunityId]);

  // Fetch community details and initialize targeting options
  const fetchCommunityDetails = async (communityId: string) => {
    try {
      console.log('🔍 Fetching community details for ID:', communityId);
      console.log('🔍 User availability check:', { user, hasUser: !!user, userRole: user?.role });

      // Wait for user to be available
      if (!user) {
        console.log('🔍 User not available yet, skipping resident fetch');
        return;
      }

      const response = await api.get(`/api/communities/${communityId}`);
      const community = response.data;
      console.log('🔍 Community details response:', community);

      if (community) {
        setSelectedCommunityName(`${community.name} (${community.streetAddress || 'No address'})`);
        console.log('🔍 Set community name:', `${community.name} (${community.streetAddress || 'No address'})`);

        // Get fresh user role values
        const currentIsHOAAdmin = user.role === 'admin';
        const currentIsCompanyAdmin = user.role === 'company_admin';

        // Initialize targeting based on user role
        console.log('🔍 User role check:', {
          isHOAAdmin: currentIsHOAAdmin,
          isCompanyAdmin: currentIsCompanyAdmin,
          userRole: user.role,
          communityId,
          userEmail: user.email
        });

        if (currentIsHOAAdmin) {
          console.log('🔍 User is HOA admin, fetching residents for street:', communityId);
          await fetchResidentsForStreet(communityId);
        } else if (currentIsCompanyAdmin) {
          console.log('🔍 User is company admin, fetching residents for street (company admin can see all):', communityId);
          // Company admins can also see street-level residents for testing/management
          await fetchResidentsForStreetAsCompanyAdmin(communityId);
        } else {
          console.log('🔍 User role not recognized for resident fetching:', {
            isHOAAdmin: currentIsHOAAdmin,
            isCompanyAdmin: currentIsCompanyAdmin,
            userRole: user.role,
            userEmail: user.email
          });
        }
      }
    } catch (error) {
      console.error('❌ Error fetching community details:', error);
      setSelectedCommunityName('Selected Community');
    }
  };

  // Clear targeting data
  const clearTargetingData = () => {
    setAvailableResidents([]);
    setSelectedResidents([]);
    setAvailableStreets([]);
    setAvailableHOAs([]);
    setSelectedHOA(null);
    setSelectedStreet(null);
    setTargetingMode('all');
  };

  // Fetch residents for a specific street (HOA Admin)
  const fetchResidentsForStreet = async (streetId: string) => {
    try {
      console.log('🔍 Fetching residents for street ID:', streetId);
      setIsLoadingTargets(true);
      const response = await api.get(`/api/announcements/streets/${streetId}/residents`);
      console.log('🔍 Residents API response:', response.data);

      if (response.data.success) {
        setAvailableResidents(response.data.residents);
        console.log(`✅ Loaded ${response.data.residents.length} residents for street:`, response.data.street?.name);
        console.log('🔍 Residents data:', response.data.residents);
      } else {
        console.log('❌ API returned success: false');
      }
    } catch (error) {
      console.error('❌ Error fetching residents:', error);
      toast({
        title: "Error",
        description: "Failed to load residents for targeting",
        variant: "destructive",
      });
    } finally {
      setIsLoadingTargets(false);
    }
  };

  // Fetch residents for a specific street (Company Admin - has broader access)
  const fetchResidentsForStreetAsCompanyAdmin = async (streetId: string) => {
    try {
      console.log('🔍 Company admin fetching residents for street ID:', streetId);
      setIsLoadingTargets(true);

      // Company admins use a different endpoint that bypasses HOA restrictions
      const response = await api.get(`/api/announcements/company-admin/streets/${streetId}/residents`);
      console.log('🔍 Company admin residents API response:', response.data);

      if (response.data.success) {
        setAvailableResidents(response.data.residents);
        console.log(`✅ Company admin loaded ${response.data.residents.length} residents for street:`, response.data.street?.name);
        console.log('🔍 Residents data:', response.data.residents);
      } else {
        console.log('❌ API returned success: false');
      }
    } catch (error) {
      console.error('❌ Error fetching residents as company admin:', error);
      // Fallback to regular endpoint if company admin endpoint doesn't exist
      console.log('🔄 Falling back to regular residents endpoint...');
      await fetchResidentsForStreet(streetId);
    } finally {
      setIsLoadingTargets(false);
    }
  };

  // Fetch HOA targeting options (Company Admin)
  const fetchHOATargetingOptions = async (hoaId: string) => {
    try {
      setIsLoadingTargets(true);
      const response = await api.get(`/api/announcements/hoas/${hoaId}/targeting`);
      if (response.data.success) {
        setAvailableStreets(response.data.targetingOptions.streets);
        setSelectedHOA(hoaId);
        console.log(`Loaded targeting options for HOA: ${response.data.hoa.name}`);
      }
    } catch (error) {
      console.error('Error fetching HOA targeting options:', error);
      toast({
        title: "Error",
        description: "Failed to load targeting options",
        variant: "destructive",
      });
    } finally {
      setIsLoadingTargets(false);
    }
  };

  // Handle targeting mode change
  const handleTargetingModeChange = (mode: 'all' | 'specific') => {
    setTargetingMode(mode);
    setSelectedResidents([]);
    setSelectedStreet(null);
  };

  // Handle resident selection
  const handleResidentToggle = (residentId: string) => {
    setSelectedResidents(prev =>
      prev.includes(residentId)
        ? prev.filter(id => id !== residentId)
        : [...prev, residentId]
    );
  };

  // Handle select all residents
  const handleSelectAllResidents = () => {
    if (selectedResidents.length === availableResidents.length) {
      setSelectedResidents([]);
    } else {
      setSelectedResidents(availableResidents.map(r => r.id));
    }
  };

  // Enhanced submit handler for role-based targeting
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    if (!subject.trim() || !message.trim()) {
      setError('Subject and message are required');
      setIsLoading(false);
      return;
    }

    if (!selectedCommunityId) {
      setError('Please select a street/community from the sidebar first');
      setIsLoading(false);
      return;
    }

    try {
      let endpoint = '';
      // Determine if we should send emails
      const shouldSendEmails = targetingMode === 'specific' ? true : sendEmail;

      // Create FormData for file uploads
      const formData = new FormData();
      formData.append('subject', subject.trim());
      formData.append('message', message.trim());
      formData.append('sendEmail', shouldSendEmails.toString());

      // Add file attachments
      attachments.forEach((file, index) => {
        formData.append('attachments', file);
      });

      if (isHOAAdmin) {
        // HOA Admin: Send to street
        endpoint = '/api/announcements/send/street';
        formData.append('streetId', selectedCommunityId);

        if (targetingMode === 'specific' && selectedResidents.length > 0) {
          // Send recipients as JSON string to match backend expectations
          // selectedResidents is already an array of IDs (strings)
          formData.append('recipients', JSON.stringify(selectedResidents));
        } else {
          formData.append('recipients', 'all');
        }
      } else if (isCompanyAdmin) {
        // Company Admin: Can send to specific streets (like HOA admin) or entire HOAs
        if (selectedCommunityId && availableResidents.length > 0) {
          // Send to specific street (same as HOA admin)
          endpoint = '/api/announcements/send/street';
          formData.append('streetId', selectedCommunityId);

          if (targetingMode === 'specific' && selectedResidents.length > 0) {
            // Send recipients as JSON string to match backend expectations
            // selectedResidents is already an array of IDs (strings)
            formData.append('recipients', JSON.stringify(selectedResidents));
          } else {
            formData.append('recipients', 'all');
          }
        } else if (selectedHOA) {
          // Send to entire HOA
          endpoint = '/api/announcements/send/hoa';
          formData.append('hoaId', selectedHOA);
          formData.append('targetType', 'all');
        } else {
          setError('Please select a street or HOA to send announcements to');
          setIsLoading(false);
          return;
        }
      } else {
        setError('Insufficient permissions to send announcements');
        setIsLoading(false);
        return;
      }

      console.log('🔍 Sending announcement:', {
        endpoint,
        hasAttachments: attachments.length > 0,
        attachmentCount: attachments.length,
        isCompanyAdmin,
        isHOAAdmin,
        selectedCommunityId,
        selectedHOA,
        targetingMode,
        selectedResidents: selectedResidents.length,
        availableResidents: availableResidents.length
      });

      const response = await api.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      const data = response.data;

      if (data.success) {
        const recipientCount = data.recipientCount || 0;
        const emailCount = data.emailsSent || 0;
        const emailsSentActually = shouldSendEmails && emailCount > 0;

        setSuccess(`Announcement sent successfully to ${recipientCount} recipients${emailsSentActually ? ` (${emailCount} emails sent)` : ''}`);
        toast({
          title: 'Announcement Sent',
          description: `Your announcement has been sent to ${recipientCount} recipients${emailsSentActually ? ' via notification and email' : ' via notification only'}`,
        });

        // Reset form
        setSubject('');
        setMessage('');
        setSendEmail(false);
        setSelectedResidents([]);
        setTargetingMode('all');
        setAttachments([]);
      } else {
        throw new Error(data.message || 'Failed to send announcement');
      }
    } catch (err: any) {
      console.error('Error sending announcement:', err);

      // Handle axios error responses
      if (err.response) {
        // The request was made and the server responded with a status code outside of 2xx
        console.error('Server error response:', err.response.data);
        setError(err.response.data.message || err.response.data.error || 'Server error');
      } else if (err.request) {
        // The request was made but no response was received
        console.error('No response received:', err.request);
        setError('No response from server. Please check your connection.');
      } else {
        // Something happened in setting up the request
        setError(err.message || 'An unknown error occurred');
      }

      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to send announcement. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Form validation
  const isFormDisabled = isLoading || !selectedCommunityId;
  const canSendAnnouncement = selectedCommunityId &&
    (targetingMode === 'all' || (targetingMode === 'specific' && selectedResidents.length > 0));

  return (
    <div className="container mx-auto py-8">
      {/* Header with targeting status */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Send Announcements</h1>
        <p className="text-sm text-muted-foreground mt-1">
          {isHOAAdmin ? 'Send announcements to residents in your HOA streets' : 'Send announcements to HOA members'}
        </p>

        {/* Targeting Status Display */}
        {selectedCommunityId && selectedCommunityName ? (
          <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2">
              {isHOAAdmin ? <MapPin className="h-4 w-4 text-green-600" /> : <Building2 className="h-4 w-4 text-green-600" />}
              <p className="text-sm text-green-800 font-medium">
                {isHOAAdmin ? 'Sending to street:' : 'Sending to HOA:'} {selectedCommunityName}
              </p>
            </div>

            {/* Targeting Summary */}
            {targetingMode === 'all' ? (
              <p className="text-xs text-green-700 mt-1">
                📢 All members ({availableResidents.length} residents)
              </p>
            ) : targetingMode === 'specific' && selectedResidents.length > 0 ? (
              <p className="text-xs text-green-700 mt-1">
                👥 {selectedResidents.length} specific residents selected
              </p>
            ) : null}
          </div>
        ) : (
          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <p className="text-sm text-blue-800 font-medium">
                No Target Selected
              </p>
            </div>
            <p className="text-xs text-blue-700 mt-1">
              Please select a {isHOAAdmin ? 'street' : 'HOA'} from the sidebar to begin targeting residents.
            </p>
          </div>
        )}
      </div>

      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="responsive-spacing-sm">
          <CardTitle className="text-lg sm:text-xl">Create Announcement</CardTitle>
          <CardDescription className="text-sm sm:text-base">
            Send an announcement to members in the selected community(ies). Optionally send via email as well.
          </CardDescription>
        </CardHeader>

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4 responsive-spacing-sm">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="bg-green-50 text-green-800 border-green-200">
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>{success}</AlertDescription>
              </Alert>
            )}

            {/* Targeting Interface */}
            {selectedCommunityId && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg border">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-blue-600" />
                  <Label className="text-sm font-medium">Select Recipients</Label>
                </div>

                {/* Targeting Mode Selection */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="all-members"
                      name="targeting"
                      checked={targetingMode === 'all'}
                      onChange={() => handleTargetingModeChange('all')}
                      className="h-4 w-4 text-blue-600"
                    />
                    <Label htmlFor="all-members" className="text-sm">
                      All Members ({availableResidents.length} residents)
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="specific-members"
                      name="targeting"
                      checked={targetingMode === 'specific'}
                      onChange={() => handleTargetingModeChange('specific')}
                      className="h-4 w-4 text-blue-600"
                    />
                    <Label htmlFor="specific-members" className="text-sm">
                      Specific Members
                    </Label>
                  </div>
                </div>

                {/* Individual Resident Selection */}
                {targetingMode === 'specific' && (
                  <div className="space-y-3">
                    {isLoadingTargets ? (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Loading residents...
                      </div>
                    ) : availableResidents.length > 0 ? (
                      <>
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">Select Individual Residents:</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleSelectAllResidents}
                            className="text-xs"
                          >
                            {selectedResidents.length === availableResidents.length ? 'Deselect All' : 'Select All'}
                          </Button>
                        </div>

                        <div className="max-h-48 overflow-y-auto space-y-2 border rounded p-2">
                          {availableResidents.map((resident) => (
                            <div key={resident.id} className="flex items-center space-x-2">
                              <Checkbox
                                id={`resident-${resident.id}`}
                                checked={selectedResidents.includes(resident.id)}
                                onCheckedChange={() => handleResidentToggle(resident.id)}
                              />
                              <Label htmlFor={`resident-${resident.id}`} className="text-sm flex-1 cursor-pointer">
                                <div className="flex items-center gap-2">
                                  <Mail className="h-3 w-3 text-gray-400" />
                                  <span className="font-medium">{resident.fullName || resident.username}</span>
                                  <span className="text-gray-500">({resident.email})</span>
                                </div>
                                {resident.streetAddress && (
                                  <div className="text-xs text-gray-400 ml-5">
                                    {resident.streetAddress}
                                  </div>
                                )}
                              </Label>
                            </div>
                          ))}
                        </div>

                        <div className="text-xs text-gray-600">
                          {selectedResidents.length} of {availableResidents.length} residents selected
                        </div>
                      </>
                    ) : (
                      <div className="text-sm text-gray-500">
                        No residents found for this street.
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder={isFormDisabled ? "Select a specific community first" : "Enter announcement subject"}
                disabled={isFormDisabled}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder={isFormDisabled ? "Select a specific community first" : "Enter announcement message"}
                rows={6}
                disabled={isFormDisabled}
                required
              />
            </div>

            {/* File Attachments */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Paperclip className="h-4 w-4" />
                File Attachments
                <span className="text-xs text-muted-foreground">(Optional)</span>
              </Label>
              <FileUpload
                files={attachments}
                onFilesChange={setAttachments}
                maxFiles={5}
                maxSize={25 * 1024 * 1024} // 25MB
                disabled={isFormDisabled}
                className="w-full"
              />
              {attachments.length > 0 && (
                <div className="text-xs text-muted-foreground">
                  {attachments.length} file{attachments.length > 1 ? 's' : ''} attached
                </div>
              )}
            </div>

            {/* Email sending info */}
            {targetingMode === 'specific' && selectedResidents.length > 0 && (
              <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
                📧 Emails will be sent to the {selectedResidents.length} selected recipient{selectedResidents.length > 1 ? 's' : ''}
              </div>
            )}

            {/* Only show email checkbox when targeting all members */}
            {targetingMode === 'all' && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sendEmail"
                    checked={sendEmail}
                    onCheckedChange={(checked) => setSendEmail(checked as boolean)}
                    disabled={isFormDisabled}
                  />
                  <Label htmlFor="sendEmail" className={isFormDisabled ? "cursor-not-allowed text-gray-400" : "cursor-pointer"}>
                    Also send via email to all members
                  </Label>
                </div>
                {!sendEmail && (
                  <div className="text-sm text-gray-600 bg-yellow-50 p-3 rounded-md">
                    📱 Only in-app notifications will be sent to all members
                  </div>
                )}
              </div>
            )}
          </CardContent>

          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/dashboard')}
              disabled={isLoading}
            >
              Cancel
            </Button>

            <Button type="submit" disabled={!canSendAnnouncement || isFormDisabled}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : !selectedCommunityId ? (
                `Select ${isHOAAdmin ? 'Street' : 'HOA'} First`
              ) : targetingMode === 'specific' && selectedResidents.length === 0 ? (
                'Select Recipients First'
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Send Announcement
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default AdminAnnouncements;
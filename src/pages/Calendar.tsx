import React, { useEffect, useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar as CalendarIcon, Clock, User, Edit, Trash2 } from 'lucide-react';
import api from '@/lib/axios';
import { useNavigate } from 'react-router-dom';
import { format, parseISO, isValid, isFuture, isToday, addDays, compareAsc } from 'date-fns';

const Calendar = () => {
  const [events, setEvents] = useState([]);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [newTitle, setNewTitle] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState<any>(null);

  const [selectedEvent, setSelectedEvent] = useState<any | null>(null);
  const [editedTitle, setEditedTitle] = useState('');
  const [showEventDialog, setShowEventDialog] = useState(false);

  // Multiple community support (same as other components)
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });
  const [selectedCommunityIds, setSelectedCommunityIds] = useState<string[]>(() => {
    const stored = localStorage.getItem('selectedCommunityIds');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);
  const [isMultipleCommunities, setIsMultipleCommunities] = useState<boolean>(false);
  const [availableCommunitiesInDropdown, setAvailableCommunitiesInDropdown] = useState<string[]>([]);

  const { toast } = useToast();
  const navigate = useNavigate();

  // Get user role info
  const isCompanyAdmin = user?.role === 'company_admin';
  const isAdmin = user?.role === 'admin';
  const userCommunityId = user?.communityId;

  useEffect(() => {
    // Check if user is logged in
    const userData = localStorage.getItem('user');
    if (!userData) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to manage events.',
        variant: 'destructive',
      });
      navigate('/login');
      return;
    }
    const parsedUser = JSON.parse(userData);
    setUser(parsedUser);
  }, []);

  // Listen for community selection changes from Sidebar
  useEffect(() => {
    // Initialize from localStorage
    const storedCommunityId = localStorage.getItem('selectedCommunityId');
    const storedCommunityIds = localStorage.getItem('selectedCommunityIds');

    if (storedCommunityIds) {
      const ids = JSON.parse(storedCommunityIds);
      setSelectedCommunityIds(ids);
      setIsMultipleCommunities(true);
      setSelectedCommunityName(`${ids.length} Communities Selected`);
    } else if (storedCommunityId) {
      setSelectedCommunityId(storedCommunityId);
      setIsMultipleCommunities(false);
    }

    const handleCommunityChange = async (event: CustomEvent) => {
      const { communityId, communityIds, availableCommunities } = event.detail;
      console.log('Calendar: Received community selection change:', { communityId, communityIds, availableCommunities });

      // Store available communities from dropdown for "All Communities" use
      if (availableCommunities && Array.isArray(availableCommunities)) {
        const communityIdsList = availableCommunities.map((community: any) => community._id || community.id);
        setAvailableCommunitiesInDropdown(communityIdsList);
        console.log('Calendar: Updated available communities from dropdown:', communityIdsList);
      }

      // Handle both single and multiple community selection
      if (communityIds && Array.isArray(communityIds)) {
        // Multiple communities selected
        setSelectedCommunityIds(communityIds);
        setSelectedCommunityId(null);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`${communityIds.length} Communities Selected`);

        console.log('Calendar: Multiple communities selected:', communityIds);
      } else if (communityId && communityId !== 'all') {
        // Single community selected
        setSelectedCommunityId(communityId);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);

        // Fetch community details for display name
        try {
          const response = await api.get(`/api/communities/${communityId}`);
          const community = response.data;
          if (community) {
            setSelectedCommunityName(`${community.name} (${community.streetAddress || 'No address'})`);
          }
        } catch (error) {
          console.error('Error fetching community details:', error);
          setSelectedCommunityName('Selected Community');
        }
      } else {
        // "All Communities" selection - use communityIds from event or fallback to availableCommunitiesInDropdown
        setSelectedCommunityId(null);

        // Use communityIds from the event if available, otherwise use stored dropdown communities
        const communitiesToUse = communityIds && communityIds.length > 0 ? communityIds : availableCommunitiesInDropdown;
        setSelectedCommunityIds(communitiesToUse);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`All Communities (${communitiesToUse.length})`);

        console.log('Calendar: "All Communities" selected, using communities:', communitiesToUse);
        console.log('Calendar: communityIds from event:', communityIds);
        console.log('Calendar: availableCommunitiesInDropdown:', availableCommunitiesInDropdown);
      }

      // Don't fetch events here - let useEffect handle it after state updates
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, []);

  // Fetch events when community selection changes
  useEffect(() => {
    if (user) {
      fetchEvents();
    }
  }, [selectedCommunityId, selectedCommunityIds, user]);

  const fetchEvents = async () => {
    try {
      setIsLoading(true);

      // Build endpoint with community filtering
      let endpoint = '/api/events';
      const params = [];

      // Role-based filtering using communityId - handle "All Communities" properly
      if (isCompanyAdmin) {
        // Company admin can see all, but apply community filters if specified
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities")
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('Calendar: Company admin fetching events for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          // Single community selected
          params.push(`communityId=${selectedCommunityId}`);
          console.log('Calendar: Company admin fetching events for single community:', selectedCommunityId);
        }
        // If no filters, company admin sees all events (this is intentional)
      } else if (isAdmin) {
        // Admin can see events from communities they have access to
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities" = all accessible communities)
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('Calendar: Admin fetching events for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          params.push(`communityId=${selectedCommunityId}`);
          console.log('Calendar: Admin fetching events for single community:', selectedCommunityId);
        } else if (userCommunityId) {
          // Default to their community if no specific selection
          params.push(`communityId=${userCommunityId}`);
          console.log('Calendar: Admin fetching events for their default community:', userCommunityId);
        }
      } else {
        // Members can only see events from their community(ies)
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities" = all accessible communities)
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('Calendar: Member fetching events for their communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          params.push(`communityId=${selectedCommunityId}`);
          console.log('Calendar: Member fetching events for their community:', selectedCommunityId);
        } else if (userCommunityId) {
          // Default to their community if no specific selection
          params.push(`communityId=${userCommunityId}`);
          console.log('Calendar: Member fetching events for their default community:', userCommunityId);
        }
      }

      // IMPORTANT: Always ensure we have some filtering for non-company-admin users
      if (!isCompanyAdmin && params.length === 0) {
        console.warn('Calendar: No filtering applied for non-company-admin user, applying default community filter');
        // Apply default user-based filtering with communityId only
        if (userCommunityId) {
          params.push(`communityId=${userCommunityId}`);
          console.log('Calendar: Applied default community filter for security:', userCommunityId);
        }
      }

      if (params.length > 0) {
        endpoint += `?${params.join('&')}`;
      }

      console.log('Calendar: Fetching events from endpoint:', endpoint);
      console.log('Calendar: Current state - selectedCommunityId:', selectedCommunityId);
      console.log('Calendar: Current state - selectedCommunityIds:', selectedCommunityIds);
      console.log('Calendar: Current state - isMultipleCommunities:', isMultipleCommunities);
      const res = await api.get(endpoint);

      // Log the event data structure to help debug creator info
      console.log('Events data structure:', res.data[0]);

      // Sort events by date for consistent display
      const sortedEvents = res.data.sort((a, b) => compareAsc(parseISO(a.date), parseISO(b.date)));

      // Process events to ensure creator info is properly formatted
      const processedEvents = sortedEvents.map(event => {
        // Log the creator structure for debugging
        console.log('Event creator structure:', event.title, event.createdBy);
        return event;
      });

      setEvents(processedEvents);
    } catch (err) {
      console.error('Failed to fetch events:', err);
      if (err.response?.status === 401) {
        toast({
          title: 'Session Expired',
          description: 'Please log in again to continue.',
          variant: 'destructive',
        });
        navigate('/login');
        return;
      }
      toast({
        title: 'Error',
        description: 'Failed to fetch events. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddEvent = async () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to add events.',
        variant: 'destructive',
      });
      navigate('/login');
      return;
    }

    if (!newTitle?.trim() || !selectedDate) {
      toast({
        title: 'Error',
        description: 'Please provide both a title and a date for the event.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);
      // Format the date to YYYY-MM-DD
      const formattedDate = new Date(selectedDate).toISOString().split('T')[0];

      // Determine which community ID to use for the event
      let eventCommunityId = null;

      if (selectedCommunityId) {
        // Single community selected
        eventCommunityId = selectedCommunityId;
      } else if (selectedCommunityIds.length === 1) {
        // Only one community in the "multiple" selection
        eventCommunityId = selectedCommunityIds[0];
      } else if (selectedCommunityIds.length > 1) {
        // Multiple communities selected - need to ask user which community to create event in
        toast({
          title: 'Select Specific Community',
          description: 'Please select a specific community to create the event in, not "All Communities".',
          variant: 'destructive',
        });
        setIsLoading(false);
        return;
      } else if (userCommunityId) {
        // Fallback to user's default community
        eventCommunityId = userCommunityId;
      } else {
        // No community context available
        toast({
          title: 'Community Required',
          description: 'Please select a community to create the event in.',
          variant: 'destructive',
        });
        setIsLoading(false);
        return;
      }

      console.log('Creating event with communityId:', eventCommunityId);

      const res = await api.post('/api/events', {
        title: newTitle.trim(),
        date: formattedDate,
        communityId: eventCommunityId, // Include community ID in request
      });

      // Only update UI and close dialog if the request was successful
      setEvents((prev) => {
        const newEvents = [...prev, res.data];
        // Sort events by date for consistent display
        return newEvents.sort((a, b) => compareAsc(parseISO(a.date), parseISO(b.date)));
      });
      setNewTitle('');
      setSelectedDate(null);

      toast({
        title: 'Success',
        description: 'Event created successfully.',
      });
    } catch (err) {
      console.error('Failed to create event:', err);

      // Handle different types of errors
      if (err.response?.status === 401) {
        toast({
          title: 'Session Expired',
          description: 'Please log in again to continue.',
          variant: 'destructive',
        });
        navigate('/login');
      } else if (err.response?.status === 400) {
        // Handle validation errors
        const errorMessage = err.response.data.errors?.[0]?.message ||
                           err.response.data.message ||
                           'Invalid event data. Please check your input.';
        toast({
          title: 'Validation Error',
          description: errorMessage,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to create event. Please try again.',
          variant: 'destructive',
        });
      }
      // Don't close the dialog on error so user can fix the input
      return;
    } finally {
      setIsLoading(false);
    }
  };

  const canManageEvent = (event: any) => {
    if (!user) return false;
    // Company admins cannot manage events (view only)
    if (user.role === 'company_admin') return false;
    // Allow regular admins and users to manage their own events
    return user.role === 'admin' ||
      (event.createdBy?.userId &&
       (typeof event.createdBy.userId === 'string' ?
        event.createdBy.userId === user._id :
        event.createdBy.userId._id === user._id));
  };

  const handleDeleteEvent = async () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to delete events.',
        variant: 'destructive',
      });
      navigate('/login');
      return;
    }

    if (!selectedEvent) return;

    // Check if user can delete this event
    if (!canManageEvent(selectedEvent)) {
      toast({
        title: 'Permission Denied',
        description: 'You can only delete your own events.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);
      await api.delete(`/api/events/${selectedEvent._id}`);
      setEvents((prev) => prev.filter((e) => e._id !== selectedEvent._id));
      setShowEventDialog(false);
      setSelectedEvent(null);

      toast({
        title: 'Success',
        description: 'Event deleted successfully.',
      });
    } catch (err) {
      console.error('Failed to delete event:', err);
      if (err.response?.status === 401) {
        toast({
          title: 'Session Expired',
          description: 'Please log in again to continue.',
          variant: 'destructive',
        });
        navigate('/login');
        return;
      }
      toast({
        title: 'Error',
        description: err.response?.data?.message || 'Failed to delete event. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateEvent = async () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to update events.',
        variant: 'destructive',
      });
      navigate('/login');
      return;
    }

    if (!editedTitle?.trim() || !selectedEvent) {
      toast({
        title: 'Error',
        description: 'Please provide a title for the event.',
        variant: 'destructive',
      });
      return;
    }

    // Check if user can update this event
    if (!canManageEvent(selectedEvent)) {
      toast({
        title: 'Permission Denied',
        description: 'You can only update your own events.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);
      const res = await api.put(`/api/events/${selectedEvent._id}`, {
        title: editedTitle.trim(),
      });

      setEvents((prev) => {
        const updatedEvents = prev.map((e) => (e._id === selectedEvent._id ? res.data : e));
        // Sort events by date for consistent display
        return updatedEvents.sort((a, b) => compareAsc(parseISO(a.date), parseISO(b.date)));
      });
      setShowEventDialog(false);
      setSelectedEvent(null);

      toast({
        title: 'Success',
        description: 'Event updated successfully.',
      });
    } catch (err) {
      console.error('Failed to update event:', err);
      if (err.response?.status === 401) {
        toast({
          title: 'Session Expired',
          description: 'Please log in again to continue.',
          variant: 'destructive',
        });
        navigate('/login');
        return;
      }
      toast({
        title: 'Error',
        description: err.response?.data?.message || 'Failed to update event. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to group events by date
  const groupEventsByDate = (events) => {
    // Sort events by date
    const sortedEvents = [...events].sort((a, b) => {
      return compareAsc(parseISO(a.date), parseISO(b.date));
    });

    // Group events by date
    const groupedEvents = {};
    sortedEvents.forEach(event => {
      if (!groupedEvents[event.date]) {
        groupedEvents[event.date] = [];
      }
      groupedEvents[event.date].push(event);
    });

    return groupedEvents;
  };

  // Helper function to get upcoming events (today and future)
  const getUpcomingEvents = (events) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return events.filter(event => {
      const eventDate = parseISO(event.date);
      return isValid(eventDate) && (isToday(eventDate) || isFuture(eventDate));
    });
  };

  // Helper function to get past events
  const getPastEvents = (events) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return events.filter(event => {
      const eventDate = parseISO(event.date);
      return isValid(eventDate) && !isToday(eventDate) && !isFuture(eventDate);
    });
  };

  // Component for displaying events list
  const EventsList = ({ events }) => {
    const [filter, setFilter] = useState('upcoming'); // 'upcoming', 'past', 'all'
    const [searchQuery, setSearchQuery] = useState('');

    // Get counts for each category
    const upcomingCount = getUpcomingEvents(events).length;
    const pastCount = getPastEvents(events).length;
    const allCount = events.length;

    // Filter events based on selected filter and search query
    const filteredEvents = React.useMemo(() => {
      // First filter by time period
      let timeFilteredEvents;
      switch (filter) {
        case 'upcoming':
          timeFilteredEvents = getUpcomingEvents(events);
          break;
        case 'past':
          timeFilteredEvents = getPastEvents(events);
          break;
        case 'all':
        default:
          timeFilteredEvents = [...events];
      }

      // Then filter by search query if one exists
      if (searchQuery.trim() === '') {
        return timeFilteredEvents;
      }

      const query = searchQuery.toLowerCase().trim();
      return timeFilteredEvents.filter(event =>
        event.title.toLowerCase().includes(query) ||
        (event.createdBy?.username && event.createdBy.username.toLowerCase().includes(query))
      );
    }, [events, filter, searchQuery]);

    const groupedEvents = groupEventsByDate(filteredEvents);
    const dates = Object.keys(groupedEvents).sort((a, b) => {
      // For upcoming and all, sort by ascending date
      // For past, sort by descending date (most recent first)
      return filter === 'past'
        ? compareAsc(parseISO(b), parseISO(a))
        : compareAsc(parseISO(a), parseISO(b));
    });

    return (
      <Card className="mt-6">
        <CardHeader className="pb-3">
          <div className="flex flex-col space-y-3 sm:flex-row sm:justify-between sm:space-y-0 sm:items-center">
            <CardTitle>Events List</CardTitle>
            <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
              <Input
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-[300px]"
              />
              <div className="flex space-x-1">
                <Button
                  variant={filter === 'upcoming' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter('upcoming')}
                  className="flex-1 sm:flex-none"
                >
                  Upcoming
                  <Badge variant="secondary" className="ml-1 bg-primary/20">
                    {upcomingCount}
                  </Badge>
                </Button>
                <Button
                  variant={filter === 'past' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter('past')}
                  className="flex-1 sm:flex-none"
                >
                  Past
                  <Badge variant="secondary" className="ml-1 bg-primary/20">
                    {pastCount}
                  </Badge>
                </Button>
                <Button
                  variant={filter === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter('all')}
                  className="flex-1 sm:flex-none"
                >
                  All
                  <Badge variant="secondary" className="ml-1 bg-primary/20">
                    {allCount}
                  </Badge>
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4 max-h-[500px] overflow-y-auto">
          {dates.length === 0 ? (
            <p className="text-muted-foreground text-center py-4">
              {searchQuery.trim() !== '' ? 'No events match your search' :
               filter === 'upcoming' ? 'No upcoming events scheduled' :
               filter === 'past' ? 'No past events found' :
               'No events found'}
            </p>
          ) : (
            dates.map(date => (
              <div key={date} className="border-b pb-3 last:border-b-0">
                <h3 className="font-medium flex items-center gap-2 mb-2 sticky top-0 bg-card py-1">
                  <CalendarIcon className="h-4 w-4 text-primary" />
                  <span>{format(parseISO(date), 'EEEE, MMMM d, yyyy')}</span>
                  {isToday(parseISO(date)) && (
                    <Badge variant="outline" className="ml-2 bg-primary/10">Today</Badge>
                  )}
                </h3>
                <div className="space-y-2 pl-6">
                  {groupedEvents[date].map(event => (
                    <div
                      key={event._id}
                      className="flex justify-between items-center p-2 rounded-md hover:bg-accent/20 transition-colors border border-transparent hover:border-accent"
                    >
                      <div className="min-w-0 flex-1">
                        <p className="font-medium truncate">{event.title}</p>
                        <div className="flex items-center text-xs text-muted-foreground mt-1">
                          <User className="h-3 w-3 mr-1 flex-shrink-0" />
                          <span className="truncate">
                            {event.createdBy?.username ||
                             (event.createdBy?.userId && typeof event.createdBy.userId === 'object' && event.createdBy.userId.username) ||
                             (event.createdBy?.userId && typeof event.createdBy.userId === 'string' && 'Admin') ||
                             'Unknown'}
                          </span>
                        </div>
                      </div>
                      {canManageEvent(event) && (
                        <div className="flex gap-1 ml-2 flex-shrink-0">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedEvent(event);
                              setEditedTitle(event.title);
                              setShowEventDialog(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive hover:text-destructive"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedEvent(event);
                              handleDeleteEvent();
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>
    );
  };

  // Initial fetch is now handled by the useEffect above that watches community selection

  return (
    <>
      <div className="max-w-5xl mx-auto mt-10 pb-10">
        {/* Header with community selection status */}
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-2xl font-bold">HOA Calendar</h2>
            {/* Show community selection status */}
            {selectedCommunityName && (
              <p className="text-sm text-muted-foreground mt-1">
                Viewing: {selectedCommunityName}
              </p>
            )}
            {isMultipleCommunities && selectedCommunityIds.length > 0 && (
              <p className="text-xs text-blue-600 mt-1">
                {selectedCommunityIds.length === availableCommunitiesInDropdown.length
                  ? `Showing events from all ${selectedCommunityIds.length} accessible communities`
                  : `Events from ${selectedCommunityIds.length} selected communities`
                }
              </p>
            )}
          </div>
        </div>

        <div className="rounded border bg-white p-4 shadow">
          <FullCalendar
            plugins={[dayGridPlugin, interactionPlugin]}
            initialView="dayGridMonth"
            dateClick={(arg) => user && user.role !== 'company_admin' ? setSelectedDate(arg.dateStr) : null}
            eventClick={(info) => {
              const clickedEvent = events.find((e) => String(e._id) === String(info.event.id));
              if (clickedEvent && canManageEvent(clickedEvent)) {
                setSelectedEvent(clickedEvent);
                setEditedTitle(clickedEvent.title);
                setShowEventDialog(true);
              } else if (!user) {
                toast({
                  title: 'Authentication Required',
                  description: 'Please log in to manage events.',
                  variant: 'destructive',
                });
              } else {
                toast({
                  title: 'Permission Denied',
                  description: 'You do not have permission to manage this event.',
                  variant: 'destructive',
                });
              }
            }}
            events={events.map((e) => ({
              id: String(e._id),
              title: e.title,
              date: e.date, // FullCalendar can handle YYYY-MM-DD format directly
              allDay: true
            }))}
            height="auto"
          />
        </div>

        {/* Events List */}
        <EventsList events={events} />
      </div>

      {/* Add Event */}
      <Dialog open={!!selectedDate} onOpenChange={() => setSelectedDate(null)}>
        <DialogContent className="space-y-4">
          <h3 className="text-lg font-semibold">Add Event for {selectedDate}</h3>
          <Input
            placeholder="Event Title"
            value={newTitle}
            onChange={(e) => setNewTitle(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleAddEvent();
              }
            }}
          />
          <Button
            onClick={handleAddEvent}
            disabled={isLoading}
          >
            {isLoading ? 'Adding...' : 'Add Event'}
          </Button>
        </DialogContent>
      </Dialog>

      {/* Edit/Delete Event */}
      <Dialog open={showEventDialog} onOpenChange={() => setShowEventDialog(false)}>
        <DialogContent className="space-y-4">
          <h3 className="text-lg font-semibold">Edit Event</h3>
          <Input
            value={editedTitle}
            onChange={(e) => setEditedTitle(e.target.value)}
            placeholder="Event Title"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleUpdateEvent();
              }
            }}
          />
          <div className="flex justify-between">
            <Button
              variant="destructive"
              onClick={handleDeleteEvent}
              disabled={isLoading}
            >
              {isLoading ? 'Deleting...' : 'Delete'}
            </Button>
            <Button
              onClick={handleUpdateEvent}
              disabled={isLoading}
            >
              {isLoading ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Calendar;

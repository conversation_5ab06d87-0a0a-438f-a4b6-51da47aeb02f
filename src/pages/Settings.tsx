// src/pages/Settings.tsx
import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { ImagePlus, X, Mail, Trash2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import DueDateSettings from '@/components/DueDateSettings';
import { Separator } from '@/components/ui/separator';
import RoleBadge from '@/components/RoleBadge';
import api from '@/lib/axios';
import { getProfilePhotoUrl } from '@/utils/imageUtils';

const Settings = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isAdmin = user?.role === 'admin';
  const [email, setEmail] = useState(user.email || '');
  const [password, setPassword] = useState('');
  const [profilePhoto, setProfilePhoto] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [currentProfileUrl, setCurrentProfileUrl] = useState<string | null>(user.profilePhoto || null);
  const [notifyOnChanges, setNotifyOnChanges] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Effect to update profile photo URL when user data changes
  useEffect(() => {
    console.log('User data changed, updating profile photo URL');
    console.log('Current user data:', user);
    if (user && user.profilePhoto) {
      // Make sure we have a properly formatted URL
      const formattedUrl = getProfilePhotoUrl(user.profilePhoto);
      console.log('Setting current profile URL from user data:', {
        original: user.profilePhoto,
        formatted: formattedUrl
      });
      setCurrentProfileUrl(formattedUrl);
    } else {
      console.log('No profile photo in user data');
      setCurrentProfileUrl(null);
    }
  }, [user]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // List of allowed image MIME types
      const allowedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
        'image/bmp',
        'image/tiff'
      ];

      // Check for HEIC files
      if (file.name.toLowerCase().endsWith('.heic') ||
          file.type === 'image/heic' ||
          file.type === 'image/heif') {
        toast({
          title: "Unsupported file format",
          description: "HEIC/HEIF image format is not supported. Please convert to JPEG or PNG before uploading.",
          variant: "destructive"
        });
        return;
      }

      // Validate file type
      if (!allowedMimeTypes.includes(file.type)) {
        toast({
          title: "Invalid file type",
          description: "Please select a supported image file (JPEG, PNG, GIF, WebP, SVG, BMP, or TIFF)",
          variant: "destructive"
        });
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please select an image under 5MB",
          variant: "destructive"
        });
        return;
      }

      setProfilePhoto(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleRemovePhoto = () => {
    setProfilePhoto(null);
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDeleteProfilePhoto = async () => {
    try {
      setIsLoading(true);

      const response = await api.delete('/api/users/profile-photo');
      console.log('Delete photo response:', response.data);

      if (response.data.message === 'Profile photo deleted successfully') {
        // Update local storage with the updated user data
        const updatedUser = {
          ...user,
          profilePhoto: null
        };
        console.log('Updating localStorage after photo deletion:', updatedUser);
        localStorage.setItem('user', JSON.stringify(updatedUser));

        // Update state
        setCurrentProfileUrl(null);
        setPreviewUrl(null);
        setProfilePhoto(null);

        toast({
          title: "Success",
          description: "Your profile photo has been removed."
        });
      }
    } catch (error: any) {
      console.error('Failed to delete profile photo:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      toast({
        title: "Error",
        description: "Failed to delete profile photo. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestPasswordChange = async () => {
    setIsVerifying(true);
    setError(null);
    try {
      // Use the email from user object to ensure we're using the correct email
      const emailToReset = user.email;
      console.log('Requesting password reset for:', emailToReset);

      await api.post('/api/password/request-reset',
        { email: emailToReset },
        {
          headers: {
            'Authorization': `Bearer ${user.token}`
          }
        }
      );
      toast({
        title: "Verification email sent",
        description: "Please check your email for the password reset link.",
      });
    } catch (error) {
      console.error('Password reset request error:', error);
      setError('Failed to send verification email. Please try again.');
      toast({
        title: "Error",
        description: "Failed to send verification email. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError(null);
    const formData = new FormData();
    formData.append('email', email);
    formData.append('notifyOnChanges', notifyOnChanges.toString());
    if (profilePhoto) formData.append('profilePhoto', profilePhoto);

    try {
      const response = await api.put('/api/users/settings', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('Settings update response:', response.data);

      // Get the profile photo URL from the response
      const profilePhotoValue = response.data.user.profilePhoto;
      console.log('Received profile photo value:', profilePhotoValue);

      // Format the URL properly for display
      const formattedPhotoUrl = getProfilePhotoUrl(profilePhotoValue);
      console.log('Formatted profile photo URL:', formattedPhotoUrl);

      // Update local storage with new user data
      const updatedUser = {
        ...user,
        email,
        profilePhoto: profilePhotoValue // Store the original value from the server
      };

      console.log('Updating localStorage with:', updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));

      // Update current profile URL with properly formatted URL for display
      setCurrentProfileUrl(formattedPhotoUrl);
      setPreviewUrl(null);
      setProfilePhoto(null);

      console.log('Profile photo updated:', {
        original: profilePhotoValue,
        formatted: formattedPhotoUrl,
        currentProfileUrl: formattedPhotoUrl
      });

      toast({
        title: "Settings saved",
        description: "Your settings have been updated successfully."
      });
    } catch (error) {
      setError('Failed to save settings. Please try again.');
      console.error('Failed to save settings:', error);
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8 mt-10 pb-10">
      {/* HOA Settings Section - Admin Only */}
      {isAdmin && (
        <>
          <div>
            <h2 className="text-2xl font-bold">HOA Settings</h2>
            <p className="text-muted-foreground">Manage HOA-wide settings and configurations</p>
          </div>
          <DueDateSettings />
          <Separator className="my-8" />
        </>
      )}

      {/* Account Settings Section */}
      <div>
        <h2 className="text-2xl font-bold">Account Settings</h2>
        <p className="text-muted-foreground">Manage your personal account preferences</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
          <CardDescription>Update your profile details and preferences</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative">
              <Avatar className="h-20 w-20">
                {(previewUrl || currentProfileUrl) ? (
                  <AvatarImage src={previewUrl || currentProfileUrl} alt="Profile" />
                ) : (
                  <AvatarFallback>
                    {user.username?.charAt(0).toUpperCase() || 'U'}
                  </AvatarFallback>
                )}
              </Avatar>
              {(currentProfileUrl || previewUrl) && (
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
                  onClick={handleDeleteProfilePhoto}
                  disabled={isLoading}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
            <div>
              <h3 className="font-medium">{user.username}</h3>
              <div className="mt-1 mb-2">
                <RoleBadge role={user.role} />
              </div>
              <div className="flex gap-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isLoading}
                >
                  <ImagePlus className="h-4 w-4 mr-2" />
                  Upload Photo
                </Button>
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/jpeg,image/png,image/gif,image/webp,image/svg+xml,image/bmp,image/tiff"
                  onChange={handleFileSelect}
                />
              </div>
            </div>
          </div>

          {error && (
            <div className="p-3 bg-red-100 text-red-700 rounded mb-4">
              {error}
            </div>
          )}

          <div className="space-y-6">
            <div className="space-y-2">
              <Label>Email</Label>
              <Input
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <Label>Password</Label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleRequestPasswordChange}
                  disabled={isVerifying}
                  className="flex items-center gap-2"
                >
                  <Mail className="h-4 w-4" />
                  Request Password Change
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                For security, we'll send a verification link to your email to change your password.
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="notifications"
                checked={notifyOnChanges}
                onCheckedChange={setNotifyOnChanges}
                disabled={isLoading}
              />
              <Label htmlFor="notifications">Get email alerts for important updates</Label>
            </div>

            <Button
              onClick={handleSave}
              disabled={isLoading}
              className="w-full"
            >
              Save Changes
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Settings;

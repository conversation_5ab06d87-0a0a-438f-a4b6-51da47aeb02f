import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Megaphone, 
  Calendar, 
  Search, 
  SortAsc, 
  SortDesc, 
  User,
  AlertCircle
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import api from '@/lib/axios';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';

interface Announcement {
  _id: string;
  title: string;
  description: string;
  createdAt: string;
  metadata?: {
    subject?: string;
    sentBy?: string;
    sentById?: string;
  };
  read: boolean;
}

const Announcements = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc');
  const isAdmin = user?.role === 'admin' || user?.role === 'company_admin';

  // Multiple community support (same as other components)
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });
  const [selectedCommunityIds, setSelectedCommunityIds] = useState<string[]>(() => {
    const stored = localStorage.getItem('selectedCommunityIds');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);
  const [isMultipleCommunities, setIsMultipleCommunities] = useState<boolean>(false);
  const [availableCommunitiesInDropdown, setAvailableCommunitiesInDropdown] = useState<string[]>([]);

  // Get user role info
  const isCompanyAdmin = user?.role === 'company_admin';
  const userCommunityId = user?.communityId;

  // Listen for community selection changes from Sidebar
  React.useEffect(() => {
    // Initialize from localStorage
    const storedCommunityId = localStorage.getItem('selectedCommunityId');
    const storedCommunityIds = localStorage.getItem('selectedCommunityIds');

    if (storedCommunityIds) {
      const ids = JSON.parse(storedCommunityIds);
      setSelectedCommunityIds(ids);
      setIsMultipleCommunities(true);
      setSelectedCommunityName(`${ids.length} Communities Selected`);
    } else if (storedCommunityId) {
      setSelectedCommunityId(storedCommunityId);
      setIsMultipleCommunities(false);
    }

    const handleCommunityChange = async (event: CustomEvent) => {
      const { communityId, communityIds, availableCommunities } = event.detail;
      console.log('Announcements: Received community selection change:', { communityId, communityIds, availableCommunities });

      // Store available communities from dropdown for "All Communities" use
      if (availableCommunities && Array.isArray(availableCommunities)) {
        const communityIdsList = availableCommunities.map((community: any) => community._id || community.id);
        setAvailableCommunitiesInDropdown(communityIdsList);
        console.log('Announcements: Updated available communities from dropdown:', communityIdsList);
      }

      // Handle both single and multiple community selection
      if (communityIds && Array.isArray(communityIds)) {
        // Multiple communities selected
        setSelectedCommunityIds(communityIds);
        setSelectedCommunityId(null);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`${communityIds.length} Communities Selected`);

        console.log('Announcements: Multiple communities selected:', communityIds);
      } else if (communityId && communityId !== 'all') {
        // Single community selected
        setSelectedCommunityId(communityId);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);

        // Fetch community details for display name
        try {
          const response = await api.get(`/api/communities/${communityId}`);
          const community = response.data;
          if (community) {
            setSelectedCommunityName(`${community.name} (${community.streetAddress || 'No address'})`);
          }
        } catch (error) {
          console.error('Error fetching community details:', error);
          setSelectedCommunityName('Selected Community');
        }
      } else {
        // "All Communities" selection - use communityIds from event or fallback to availableCommunitiesInDropdown
        setSelectedCommunityId(null);

        // Use communityIds from the event if available, otherwise use stored dropdown communities
        const communitiesToUse = communityIds && communityIds.length > 0 ? communityIds : availableCommunitiesInDropdown;
        setSelectedCommunityIds(communitiesToUse);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`All Communities (${communitiesToUse.length})`);

        console.log('Announcements: "All Communities" selected, using communities:', communitiesToUse);
      }
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, [availableCommunitiesInDropdown]);

  // Fetch announcements from notifications endpoint with community filtering
  const { data: notifications, isLoading, isError } = useQuery({
    queryKey: ['notifications', selectedCommunityId, selectedCommunityIds],
    queryFn: async () => {
      // Build endpoint with community filtering
      let endpoint = '/api/notifications';
      const params = [];

      // Role-based filtering using communityId (same as other components)
      if (isCompanyAdmin) {
        // Company admin can see all, but apply community filters if specified
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities")
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('Announcements: Company admin fetching for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          // Single community selected
          params.push(`communityId=${selectedCommunityId}`);
          console.log('Announcements: Company admin fetching for single community:', selectedCommunityId);
        }
        // If no filters, company admin sees all announcements (this is intentional)
      } else {
        // Admin and Members can see announcements from communities they have access to
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities" = all accessible communities)
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('Announcements: User fetching for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          params.push(`communityId=${selectedCommunityId}`);
          console.log('Announcements: User fetching for single community:', selectedCommunityId);
        } else if (userCommunityId) {
          // Default to their community if no specific selection
          params.push(`communityId=${userCommunityId}`);
          console.log('Announcements: User fetching for their default community:', userCommunityId);
        }
      }

      // IMPORTANT: Always ensure we have some filtering for non-company-admin users
      if (!isCompanyAdmin && params.length === 0) {
        console.warn('Announcements: No filtering applied for non-company-admin user, applying default community filter');
        // Apply default user-based filtering with communityId only
        if (userCommunityId) {
          params.push(`communityId=${userCommunityId}`);
          console.log('Announcements: Applied default community filter for security:', userCommunityId);
        }
      }

      if (params.length > 0) {
        endpoint += `?${params.join('&')}`;
      }

      console.log('Announcements: Fetching from endpoint:', endpoint);
      const response = await api.get(endpoint);
      return response.data;
    }
  });

  // Filter announcements from all notifications
  const announcements = React.useMemo(() => {
    if (!notifications) return [];
    
    return notifications
      .filter((notification: any) => 
        notification.type === 'announcement' || 
        (notification.type === 'info' && notification.metadata?.isAnnouncement)
      )
      .map((announcement: any) => ({
        _id: announcement._id,
        title: announcement.title,
        description: announcement.description,
        createdAt: announcement.createdAt,
        metadata: announcement.metadata,
        read: announcement.read
      }));
  }, [notifications]);

  // Filter and sort announcements
  const filteredAnnouncements = React.useMemo(() => {
    if (!announcements) return [];

    return [...announcements]
      .filter((announcement: Announcement) => {
        const searchLower = searchTerm.toLowerCase();
        return (
          announcement.title.toLowerCase().includes(searchLower) ||
          announcement.description.toLowerCase().includes(searchLower) ||
          (announcement.metadata?.sentBy && 
            announcement.metadata.sentBy.toLowerCase().includes(searchLower))
        );
      })
      .sort((a: Announcement, b: Announcement) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
      });
  }, [announcements, searchTerm, sortOrder]);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Announcements</h1>
        </div>
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-20 w-full" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-4 w-1/3" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load announcements. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header with community selection status */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold">Announcements</h1>
          {/* Show community selection status */}
          {selectedCommunityName && (
            <p className="text-sm text-muted-foreground mt-1">
              Viewing: {selectedCommunityName}
            </p>
          )}
          {isMultipleCommunities && selectedCommunityIds.length > 0 && (
            <p className="text-xs text-blue-600 mt-1">
              {selectedCommunityIds.length === availableCommunitiesInDropdown.length
                ? `Showing announcements from all ${selectedCommunityIds.length} accessible communities`
                : `Announcements from ${selectedCommunityIds.length} selected communities`
              }
            </p>
          )}
        </div>

        {isAdmin && (
          <Button
            onClick={() => navigate('/admin/announcements')}
            className="w-full sm:w-auto min-h-[44px]"
          >
            <Megaphone className="mr-2 h-4 w-4" />
            Create Announcement
          </Button>
        )}
      </div>

      <div className="flex flex-col gap-3 sm:flex-row sm:gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search announcements..."
            className="pl-8 min-h-[44px]"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <Select
          value={sortOrder}
          onValueChange={(value) => setSortOrder(value as 'asc' | 'desc')}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Sort by date" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="desc">
              <div className="flex items-center">
                <SortDesc className="mr-2 h-4 w-4" />
                Newest first
              </div>
            </SelectItem>
            <SelectItem value="asc">
              <div className="flex items-center">
                <SortAsc className="mr-2 h-4 w-4" />
                Oldest first
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {filteredAnnouncements.length === 0 ? (
        <div className="text-center py-12">
          <Megaphone className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">No announcements found</h3>
          <p className="mt-2 text-muted-foreground">
            {searchTerm ? 'Try a different search term' : 'Check back later for community updates'}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredAnnouncements.map((announcement: Announcement) => (
            <Card key={announcement._id} className={announcement.read ? '' : 'border-l-4 border-l-blue-500'}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{announcement.title}</CardTitle>
                    <CardDescription className="flex items-center mt-1">
                      <Calendar className="mr-1 h-3 w-3" />
                      {announcement.createdAt ? (
                        (() => {
                          try {
                            const date = new Date(announcement.createdAt);
                            return isNaN(date.getTime()) ? 'Invalid date' : format(date, 'PPP');
                          } catch (error) {
                            return 'Invalid date';
                          }
                        })()
                      ) : (
                        'No date'
                      )}
                      {announcement.metadata?.sentBy && (
                        <>
                          <span className="mx-1">•</span>
                          <User className="mr-1 h-3 w-3" />
                          {announcement.metadata.sentBy}
                        </>
                      )}
                    </CardDescription>
                  </div>
                  {!announcement.read && (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">New</Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="whitespace-pre-line">{announcement.description}</div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default Announcements;

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import FinanceOverview from '@/components/FinanceOverview';
import FinanceForm from '@/components/forms/FinanceForm';
import { Button } from '@/components/ui/button';
import { BarChart3, Calculator } from 'lucide-react';

const Finances = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedHoaId, setSelectedHoaId] = useState<string | null>(null);
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(null);

  // Multiple community support (same as Dashboard)
  const [selectedCommunityIds, setSelectedCommunityIds] = useState<string[]>(() => {
    const stored = localStorage.getItem('selectedCommunityIds');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);
  const [isMultipleCommunities, setIsMultipleCommunities] = useState<boolean>(false);

  // Get user role
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isCompanyAdmin = user?.role === 'company_admin';
  const isAdmin = user?.role === 'admin';

  // Extract query parameters from URL (fallback for direct navigation)
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const hoaId = searchParams.get('hoaId');
    const communityId = searchParams.get('communityId');

    if (hoaId) setSelectedHoaId(hoaId);
    if (communityId) setSelectedCommunityId(communityId);
  }, [location.search]);

  // Listen for community selection changes from Sidebar (same as Dashboard)
  useEffect(() => {
    // Initialize from localStorage
    const storedCommunityId = localStorage.getItem('selectedCommunityId');
    const storedCommunityIds = localStorage.getItem('selectedCommunityIds');

    if (storedCommunityIds) {
      const ids = JSON.parse(storedCommunityIds);
      setSelectedCommunityIds(ids);
      setIsMultipleCommunities(true);
      setSelectedCommunityName(`${ids.length} Communities Selected`);
    } else if (storedCommunityId) {
      setSelectedCommunityId(storedCommunityId);
      setIsMultipleCommunities(false);
    }

    const handleCommunityChange = (event: CustomEvent) => {
      const { communityId, communityIds } = event.detail;
      console.log('Finances: Received community selection change:', { communityId, communityIds });

      // Handle both single and multiple community selection
      if (communityIds && Array.isArray(communityIds)) {
        // Multiple communities selected
        setSelectedCommunityIds(communityIds);
        setSelectedCommunityId(null);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`${communityIds.length} Communities Selected`);

        console.log('Finances: Multiple communities selected:', communityIds);
      } else if (communityId) {
        // Single community selected
        setSelectedCommunityId(communityId);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);

        // Fetch community name for display
        if (communityId !== 'all') {
          // You could fetch community details here if needed
          setSelectedCommunityName('Selected Community');
        } else {
          setSelectedCommunityName(null);
        }
      } else {
        // "All Communities" or no selection
        setSelectedCommunityId(null);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);
        setSelectedCommunityName(null);
      }
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Finances</h1>
          {/* Show community selection status */}
          {selectedCommunityName && (
            <p className="text-sm text-muted-foreground mt-1">
              Viewing: {selectedCommunityName}
            </p>
          )}
          {isMultipleCommunities && selectedCommunityIds.length > 0 && (
            <p className="text-xs text-blue-600 mt-1">
              Data aggregated from {selectedCommunityIds.length} communities
            </p>
          )}
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => navigate('/finances/budget')}
          >
            <Calculator className="h-4 w-4" />
            Budget Planner
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => navigate('/finances/reports')}
          >
            <BarChart3 className="h-4 w-4" />
            Financial Reports
          </Button>
        </div>
      </div>

      {isCompanyAdmin ? (
        // Company Admin View - Full width oversight layout
        <div className="space-y-6">
          <FinanceOverview
            hoaId={selectedHoaId}
            communityId={isMultipleCommunities ? null : selectedCommunityId}
            communityIds={isMultipleCommunities ? selectedCommunityIds : undefined}
          />
        </div>
      ) : (
        // Regular Admin/Member View - Two column layout with form
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <FinanceForm
              hoaId={selectedHoaId}
              communityId={isMultipleCommunities ? null : selectedCommunityId}
            />
          </div>
          <div>
            <FinanceOverview
              hoaId={selectedHoaId}
              communityId={isMultipleCommunities ? null : selectedCommunityId}
              communityIds={isMultipleCommunities ? selectedCommunityIds : undefined}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Finances;

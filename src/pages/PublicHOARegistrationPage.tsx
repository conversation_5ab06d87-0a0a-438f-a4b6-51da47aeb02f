import React from 'react';
import { Helmet } from 'react-helmet';
import { Link } from 'react-router-dom';
import { Building2 } from 'lucide-react';
import PublicHOARegistration from '@/components/PublicHOARegistration';

const PublicHOARegistrationPage = () => {
  return (
    <>
      <Helmet>
        <title>HOA Registration | Street Harmony</title>
      </Helmet>
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="mb-8 text-center">
              <h1 className="text-3xl font-bold flex items-center justify-center gap-2 mb-4">
                <Building2 className="h-8 w-8" />
                HOA Registration
              </h1>
              <p className="text-gray-600">
                Register your HOA community with our management system. Your registration will be reviewed by our administrators.
              </p>
              <div className="mt-4 text-sm text-blue-600">
                <p>Are you a resident looking to join an existing HOA? <Link to="/register" className="underline font-medium">Register as a resident here</Link></p>
              </div>
            </div>
            
            <PublicHOARegistration />
            
            <div className="mt-8 text-center text-sm text-gray-500">
              <p>Already have an account? <Link to="/login" className="text-blue-600 hover:underline">Log in here</Link></p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PublicHOARegistrationPage;

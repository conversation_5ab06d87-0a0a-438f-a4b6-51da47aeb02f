import { useEffect } from 'react';
import { PageHeader } from '@/components/PageHeader';
import MFASettings from '@/components/MFASettings';
import { Shield, Lock } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const SecuritySettings = () => {
  // Set document title using useEffect
  useEffect(() => {
    document.title = "Security Settings | Street Harmony";
  }, []);

  const navigate = useNavigate();

  return (
    <div className="flex flex-col gap-4 md:gap-8">
      <PageHeader
        heading="Security Settings"
        subheading="Manage your account security settings"
        icon={<Shield className="h-6 w-6" />}
      />

      <div className="grid gap-4 md:grid-cols-2">
        {/* MFA Settings Card */}
        <div className="w-full">
          <MFASettings />
        </div>

        {/* Password Management Card */}
        <Card className="w-full">
          <CardHeader>
            <div className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              <CardTitle>Password Management</CardTitle>
            </div>
            <CardDescription>
              Update your password regularly to keep your account secure
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                It's recommended to use a strong password that includes uppercase and lowercase letters,
                numbers, and special characters.
              </p>
              <Button onClick={() => navigate('/reset-password')}>
                Change Password
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SecuritySettings;

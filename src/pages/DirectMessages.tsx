/**
 * DirectMessages.tsx
 *
 * A completely new implementation of the messaging page that uses direct API calls
 * instead of relying on socket.io and React Query, for maximum reliability in production.
 */

import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';

import { Textarea } from '@/components/ui/textarea';
import {
  AlertCircle,
  MessageSquare,
  Send,
  Loader2,
  Trash,
  Pencil,
  X,
  Check,
  RefreshCw,
  RotateCw
} from 'lucide-react';
import directMessageService from '@/services/DirectMessageService';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

// Define types
interface User {
  _id: string;
  username?: string;
  fullName?: string;
  email?: string;
  role?: string;
  profilePicture?: string;
  token?: string;
}

interface Message {
  _id: string;
  sender: User;
  content: string;
  createdAt: string;
  read: boolean;
  edited?: boolean;
  editedAt?: string;
  deleted?: boolean;
  deletedAt?: string;
  conversation?: string;
}

interface Conversation {
  _id: string;
  participants: User[];
  lastMessage: Message;
  title?: string;
  unreadCount: number;
  updatedAt: string;
  hiddenFor?: string[]; // Array of user IDs for whom this conversation is hidden
}

const DirectMessages: React.FC = () => {
  const { conversationId } = useParams<{ conversationId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  // State
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [newConversationOpen, setNewConversationOpen] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState('');

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const editInputRef = useRef<HTMLTextAreaElement>(null);

  // Get current user from localStorage
  const getCurrentUser = (): User => {
    try {
      const userData = localStorage.getItem('user');
      if (userData) {
        return JSON.parse(userData);
      }
      return { _id: '' };
    } catch (error) {
      console.error('Error getting current user:', error);
      return { _id: '' };
    }
  };

  const currentUser = getCurrentUser();

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    try {
      console.log('Attempting to scroll to bottom');

      // First try using the container directly (most reliable method)
      const container = document.getElementById('messages-container');
      if (container) {
        console.log('Using container to scroll');
        // Use a small timeout to ensure the DOM has updated
        setTimeout(() => {
          container.scrollTop = container.scrollHeight;
        }, 50);
        return;
      }

      // Then try using the ref as fallback
      if (messagesEndRef.current) {
        console.log('Using messagesEndRef to scroll');
        setTimeout(() => {
          messagesEndRef.current?.scrollIntoView({ behavior: 'auto' });
        }, 50);
        return;
      }

      // Last resort - try to find the last message element
      const lastMessage = document.querySelector('.message-item:last-child');
      if (lastMessage) {
        console.log('Using last message element to scroll');
        setTimeout(() => {
          lastMessage.scrollIntoView({ behavior: 'auto' });
        }, 50);
        return;
      }

      console.log('No scroll method available');
    } catch (error) {
      console.error('Error scrolling to bottom:', error);
    }
  };

  // Start polling for conversations
  useEffect(() => {
    console.log('Starting to poll for conversations');

    // Simple callback to update the conversations state
    const conversationsCallback = (data: any[]) => {
      console.log('Received conversations data:', data);
      setConversations(data);
      setLoading(false);
    };

    directMessageService.startPollingConversations(conversationsCallback);

    // Force an immediate fetch of conversations
    const fetchConversationsNow = async () => {
      try {
        console.log('Manually fetching conversations');
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations`, {
          headers: {
            'Authorization': `Bearer ${getCurrentUser().token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch conversations');
        }

        const data = await response.json();
        console.log('Manually fetched conversations:', data);
        setConversations(data);
        setLoading(false);

        // If we have a conversationId but it's not in the list, fetch its details
        if (conversationId && data.length > 0 && !data.some((c: Conversation) => c._id === conversationId)) {
          console.log(`Conversation ${conversationId} not found in list, fetching details`);
          fetchConversationDetails(conversationId);
        }
      } catch (error) {
        console.error('Error manually fetching conversations:', error);
      }
    };

    // Function to fetch a specific conversation's details
    const fetchConversationDetails = async (id: string) => {
      try {
        console.log(`Fetching details for conversation: ${id}`);
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations/${id}/details`, {
          headers: {
            'Authorization': `Bearer ${getCurrentUser().token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch conversation details: ${response.status}`);
        }

        const data = await response.json();
        console.log('Fetched conversation details:', data);

        // Add this conversation to our list if it's not already there
        setConversations(prevConversations => {
          if (!prevConversations.some((c: Conversation) => c._id === data._id)) {
            console.log('Adding conversation to list');
            return [...prevConversations, data];
          }
          return prevConversations;
        });
      } catch (error) {
        console.error('Error fetching conversation details:', error);
      }
    };

    fetchConversationsNow();

    return () => {
      directMessageService.stopPollingConversations();
    };
  }, [conversationId]);

  // Start polling for messages when conversationId changes
  useEffect(() => {
    if (conversationId) {
      setLoading(true);
      console.log(`Starting to poll for messages in conversation: ${conversationId}`);

      // First, try to fetch messages directly
      const fetchMessagesNow = async () => {
        try {
          console.log(`Manually fetching messages for conversation: ${conversationId}`);

          // Direct fetch for debugging
          try {
            const response = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations/${conversationId}`, {
              headers: {
                'Authorization': `Bearer ${getCurrentUser().token}`,
                'Content-Type': 'application/json'
              }
            });

            if (response.ok) {
              const messagesData = await response.json();
              console.log(`DIRECT FETCH: Found ${messagesData.length} messages for conversation ${conversationId}`);
              console.log('DIRECT FETCH: Messages data:', messagesData);

              // Directly set messages from fetch
              if (messagesData && Array.isArray(messagesData) && messagesData.length > 0) {
                console.log('DIRECT FETCH: Setting messages directly');
                setMessages(messagesData);
                setLoading(false);
                // Auto-scrolling disabled
              }
            } else {
              console.error('DIRECT FETCH: Failed to fetch messages:', response.status);
            }
          } catch (directError) {
            console.error('DIRECT FETCH: Error fetching messages:', directError);
          }

          // Also try the service method
          await directMessageService.fetchMessages(conversationId);
        } catch (error) {
          console.error('Error manually fetching messages:', error);
        }
      };

      fetchMessagesNow();

      // Then start polling
      directMessageService.startPollingMessages(conversationId, (data) => {
        console.log(`Received ${data.length} messages for conversation ${conversationId}`);
        if (data.length > 0) {
          console.log('First message sample:', {
            id: data[0]._id,
            sender: data[0].sender?._id,
            content: data[0].content?.substring(0, 20) + '...',
            createdAt: data[0].createdAt
          });

          // Log all messages for debugging
          console.log('All messages:', JSON.stringify(data));
        }

        // Set messages and update UI
        setMessages(data);
        setLoading(false);
        // Scroll to bottom after messages load
        // Auto-scrolling disabled
      });
    } else {
      directMessageService.stopPollingMessages();
      setMessages([]);
    }

    return () => {
      directMessageService.stopPollingMessages();
    };
  }, [conversationId]);

  // Scroll to bottom only on initial conversation load
  useEffect(() => {
    if (conversationId && messages.length > 0) {
      // Only scroll once when conversation is first loaded
      const timer = setTimeout(() => {
        scrollToBottom();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [conversationId]);

  // Focus edit input when editing
  useEffect(() => {
    if (editingMessageId && editInputRef.current) {
      editInputRef.current.focus();
    }
  }, [editingMessageId]);

  // Fetch users for new conversation
  const fetchUsers = async () => {
    try {
      setLoadingUsers(true);
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/members/approved`, {
        headers: {
          'Authorization': `Bearer ${getCurrentUser().token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      // Filter out current user
      const filteredUsers = data.users.filter((user: User) => user._id !== currentUser._id);
      setUsers(filteredUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch users',
        variant: 'destructive'
      });
    } finally {
      setLoadingUsers(false);
    }
  };

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      setSending(true);
      console.log('Sending message with:', {
        conversationId: conversationId || 'none',
        hasSelectedUser: !!selectedUser,
        selectedUserId: selectedUser?._id,
        messageLength: newMessage.length
      });

      if (conversationId) {
        // Get recipient ID from conversation
        const conversation = conversations.find(c => c._id === conversationId);
        if (!conversation) {
          console.error('Conversation not found in local state:', conversationId);

          // Try to fetch the conversation details directly
          try {
            const response = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations/${conversationId}/details`, {
              headers: {
                'Authorization': `Bearer ${getCurrentUser().token}`,
                'Content-Type': 'application/json'
              }
            });

            if (!response.ok) {
              throw new Error(`Failed to fetch conversation details: ${response.status}`);
            }

            const conversationDetails = await response.json();
            console.log('Fetched conversation details:', conversationDetails);

            // Find recipient from the fetched details
            const recipient = conversationDetails.participants.find(
              (p: User) => p._id !== currentUser._id
            );

            if (!recipient) {
              throw new Error('Recipient not found in conversation details');
            }

            console.log('Found recipient from API call:', recipient);

            const result = await directMessageService.sendMessage({
              recipientId: recipient._id,
              content: newMessage,
              conversationId
            });

            // Immediately add the new message to the UI
            if (result && result.message) {
              console.log('Adding new message to UI from API details:', result.message);
              setMessages(prevMessages => [...prevMessages, result.message]);
              // Scroll to bottom after adding the new message
              // Auto-scrolling disabled
            } else {
              console.log('Message sent but no message object returned, refreshing messages');
              directMessageService.fetchMessages(conversationId);
            }
          } catch (detailsError) {
            console.error('Error fetching conversation details:', detailsError);
            throw new Error('Could not determine recipient for this conversation');
          }
        } else {
          // Normal flow - conversation found in local state
          const recipient = conversation.participants.find(p => p._id !== currentUser._id);
          if (!recipient) {
            console.error('Recipient not found in conversation participants:', conversation.participants);
            throw new Error('Recipient not found in conversation');
          }

          console.log('Sending message to recipient:', recipient);
          const result = await directMessageService.sendMessage({
            recipientId: recipient._id,
            content: newMessage,
            conversationId
          });

          // Immediately add the new message to the UI
          if (result && result.message) {
            console.log('Adding new message to UI:', result.message);
            setMessages(prevMessages => [...prevMessages, result.message]);
            // Scroll to bottom after adding the new message
            // Auto-scrolling disabled
          } else {
            console.log('Message sent but no message object returned, refreshing messages');
            directMessageService.fetchMessages(conversationId);
          }
        }
      } else if (selectedUser) {
        // Start new conversation
        console.log('Starting new conversation with user:', selectedUser);
        const result = await directMessageService.sendMessage({
          recipientId: selectedUser._id,
          content: newMessage
        });

        console.log('New conversation created:', result);

        // Navigate to the new conversation
        if (result.conversationId) {
          // Force refresh conversations before navigating
          await new Promise<void>((resolve) => {
            const fetchConversationsNow = async () => {
              try {
                console.log('Refreshing conversations after creating new one');
                const response = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations`, {
                  headers: {
                    'Authorization': `Bearer ${getCurrentUser().token}`,
                    'Content-Type': 'application/json'
                  }
                });

                if (!response.ok) {
                  throw new Error('Failed to fetch conversations');
                }

                const data = await response.json();
                console.log('Refreshed conversations after creating new one:', data);
                setConversations(data);
                resolve();
              } catch (error) {
                console.error('Error refreshing conversations:', error);
                resolve();
              }
            };

            fetchConversationsNow();
          });

          // Navigate after a short delay to ensure state is updated
          setTimeout(() => {
            navigate(`/direct-messages/${result.conversationId}`);
          }, 300);
        }
      }

      // Clear input
      setNewMessage('');

      // Only scroll to bottom when sending a new message
      setTimeout(scrollToBottom, 300);
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message: ' + (error instanceof Error ? error.message : 'Unknown error'),
        variant: 'destructive'
      });
    } finally {
      setSending(false);
    }
  };

  // Handle editing a message
  const handleEditMessage = async () => {
    if (!editingMessageId || !editedContent.trim()) return;

    try {
      const result = await directMessageService.editMessage(editingMessageId, editedContent);

      // Immediately update the message in the UI
      if (result && result.message) {
        console.log('Updating edited message in UI:', result.message);
        setMessages(prevMessages =>
          prevMessages.map(msg =>
            msg._id === editingMessageId
              ? { ...msg, content: editedContent, edited: true, editedAt: new Date().toISOString() }
              : msg
          )
        );
      } else {
        // If no message returned, refresh all messages
        console.log('Message edited but no message object returned, refreshing all messages');
        if (conversationId) {
          directMessageService.fetchMessages(conversationId);
        }
      }

      setEditingMessageId(null);
      setEditedContent('');
    } catch (error) {
      console.error('Error editing message:', error);
      toast({
        title: 'Error',
        description: 'Failed to edit message',
        variant: 'destructive'
      });
    }
  };

  // Handle deleting a message
  const handleDeleteMessage = async (messageId: string) => {
    if (!window.confirm('Are you sure you want to delete this message?')) return;

    try {
      console.log(`Attempting to delete message: ${messageId}`);

      // Show loading toast
      toast({
        title: 'Deleting message...',
        description: 'Please wait',
        variant: 'default'
      });

      // Immediately update the UI to show the message as deleted (optimistic update)
      console.log('Optimistically marking message as deleted in UI:', messageId);
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg._id === messageId
            ? { ...msg, deleted: true, content: '', deletedAt: new Date().toISOString() }
            : msg
        )
      );

      // Make the actual API call using fetch directly for maximum reliability
      try {
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/${messageId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${getCurrentUser().token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Server responded with ${response.status}: ${errorText}`);
        }

        const result = await response.json();
        console.log('Delete message API response:', result);

        // Show success toast
        toast({
          title: 'Success',
          description: 'Message deleted successfully',
          variant: 'default'
        });

        // Also refresh messages after a short delay to ensure consistency
        if (conversationId) {
          console.log(`Scheduling refresh of conversation ${conversationId} after delete`);
          setTimeout(() => {
            try {
              console.log('Refreshing messages after delete');
              // Use direct fetch for maximum reliability
              fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations/${conversationId}`, {
                headers: {
                  'Authorization': `Bearer ${getCurrentUser().token}`,
                  'Content-Type': 'application/json'
                }
              })
              .then(response => {
                if (!response.ok) {
                  throw new Error(`Failed to fetch messages: ${response.status}`);
                }
                return response.json();
              })
              .then(data => {
                console.log(`Received ${data.length} messages after delete refresh`);
                if (Array.isArray(data)) {
                  setMessages(data);
                  // Auto-scrolling disabled
                }
              })
              .catch(error => {
                console.error('Error refreshing messages after delete:', error);
              });
            } catch (refreshError) {
              console.error('Error refreshing messages after delete:', refreshError);
            }
          }, 1000);
        }
      } catch (apiError) {
        console.error('API error deleting message:', apiError);

        // If the API call fails, revert the optimistic update
        console.log('Reverting optimistic delete due to API error');
        setMessages(prevMessages =>
          prevMessages.map(msg =>
            msg._id === messageId && msg.deleted
              ? { ...msg, deleted: false, content: msg.content || '[Message content unavailable]' }
              : msg
          )
        );

        // Show error toast with details
        toast({
          title: 'Error',
          description: `Failed to delete message: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`,
          variant: 'destructive'
        });

        // Try a direct fetch as a fallback
        if (conversationId) {
          console.log('Attempting direct fetch as fallback after delete error');
          try {
            const response = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations/${conversationId}`, {
              headers: {
                'Authorization': `Bearer ${getCurrentUser().token}`,
                'Content-Type': 'application/json'
              }
            });

            if (response.ok) {
              const data = await response.json();
              console.log(`Direct fetch found ${data.length} messages after delete error`);
              if (Array.isArray(data)) {
                setMessages(data);
                // Auto-scrolling disabled
              }
            }
          } catch (fallbackError) {
            console.error('Fallback fetch also failed:', fallbackError);
          }
        }
      }
    } catch (error) {
      console.error('Unhandled error in handleDeleteMessage:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred while deleting the message',
        variant: 'destructive'
      });
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get initials for avatar
  const getInitials = (name: string = '') => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Render conversation list
  const renderConversationList = () => {
    console.log('Rendering conversation list with:', conversations);

    // Check if we have a conversation ID but no conversations
    if (conversationId && (!conversations || conversations.length === 0 || !conversations.some(c => c._id === conversationId))) {
      return (
        <div className="text-center py-8">
          <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">Loading conversations...</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            We're retrieving your conversation data
          </p>
          <div className="mt-4 flex justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
          <Button
            className="mt-4"
            onClick={() => {
              // Force refresh conversations and fetch the current conversation
              const fetchConversationsAndCurrent = async () => {
                try {
                  console.log('Manually refreshing conversations and current conversation');

                  // First try to get the current conversation details
                  if (conversationId) {
                    try {
                      const detailsResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations/${conversationId}/details`, {
                        headers: {
                          'Authorization': `Bearer ${getCurrentUser().token}`,
                          'Content-Type': 'application/json'
                        }
                      });

                      if (detailsResponse.ok) {
                        const detailsData = await detailsResponse.json();
                        console.log('Fetched current conversation details:', detailsData);

                        // Add this conversation to our list
                        setConversations(prevConversations => {
                          // Check if it already exists
                          if (!prevConversations.some(c => c._id === detailsData._id)) {
                            return [detailsData, ...prevConversations];
                          }
                          return prevConversations;
                        });
                      }
                    } catch (detailsError) {
                      console.error('Error fetching conversation details:', detailsError);
                    }
                  }

                  // Then get all conversations
                  const response = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations`, {
                    headers: {
                      'Authorization': `Bearer ${getCurrentUser().token}`,
                      'Content-Type': 'application/json'
                    }
                  });

                  if (!response.ok) {
                    throw new Error('Failed to fetch conversations');
                  }

                  const data = await response.json();
                  console.log('Manually refreshed conversations:', data);

                  // Merge with any conversations we already have
                  setConversations(prevConversations => {
                    // Create a map of existing conversations
                    const existingMap = new Map(prevConversations.map(c => [c._id, c]));

                    // Add new conversations
                    data.forEach((c: Conversation) => {
                      existingMap.set(c._id, c);
                    });

                    // Convert back to array and sort by updatedAt
                    return Array.from(existingMap.values())
                      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
                  });
                } catch (error) {
                  console.error('Error manually refreshing conversations:', error);
                }
              };

              fetchConversationsAndCurrent();
            }}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Conversations
          </Button>
        </div>
      );
    }

    if (!conversations || conversations.length === 0) {
      return (
        <div className="text-center py-8">
          <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">No conversations yet</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            Start a new conversation to begin messaging
          </p>
          <Button
            className="mt-4"
            onClick={() => {
              // Force refresh conversations
              const fetchConversationsNow = async () => {
                try {
                  console.log('Manually refreshing conversations');
                  const response = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations`, {
                    headers: {
                      'Authorization': `Bearer ${getCurrentUser().token}`,
                      'Content-Type': 'application/json'
                    }
                  });

                  if (!response.ok) {
                    throw new Error('Failed to fetch conversations');
                  }

                  const data = await response.json();
                  console.log('Manually refreshed conversations:', data);
                  setConversations(data);
                } catch (error) {
                  console.error('Error manually refreshing conversations:', error);
                }
              };

              fetchConversationsNow();
            }}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Conversations
          </Button>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {conversations.map(conversation => {
          // Check if participants array exists and is not empty
          if (!conversation.participants || !Array.isArray(conversation.participants) || conversation.participants.length === 0) {
            console.log('Conversation has no valid participants:', conversation._id);
            return null;
          }

          // Find the other participant (not the current user)
          const otherParticipant = conversation.participants.find(
            p => p && p._id && p._id !== currentUser._id
          );

          // If no other participant found, use the first participant as fallback
          const participantToShow = otherParticipant || conversation.participants[0] || {
            _id: 'unknown',
            username: 'Unknown User',
            fullName: 'Unknown User'
          };

          console.log('Rendering conversation with participant:', participantToShow);

          return (
            <div
              key={conversation._id}
              className={`p-3 rounded-lg cursor-pointer hover:bg-accent ${
                conversationId === conversation._id ? 'bg-accent' : ''
              } ${conversation.hiddenFor && conversation.hiddenFor.includes(currentUser._id) ? 'opacity-60 border border-dashed border-muted-foreground' : ''}`}
              onClick={() => navigate(`/direct-messages/${conversation._id}`)}
            >
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarFallback>
                    {getInitials(participantToShow.fullName || participantToShow.username || 'Unknown')}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-1">
                      <p className="font-medium truncate">
                        {participantToShow.fullName || participantToShow.username || 'Unknown User'}
                      </p>
                      {conversation.hiddenFor && conversation.hiddenFor.includes(currentUser._id) && (
                        <span className="text-xs bg-muted text-muted-foreground px-1 rounded">
                          hidden
                        </span>
                      )}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {formatDate(conversation.updatedAt)}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground truncate">
                    {conversation.lastMessage?.deleted
                      ? 'Message deleted'
                      : conversation.lastMessage?.content || 'No messages yet'}
                  </p>
                </div>
                <div className="flex flex-col gap-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent navigation to conversation
                      if (window.confirm('Are you sure you want to permanently delete this conversation? This action cannot be undone.')) {
                        try {
                          // First, remove from local state immediately for better UX
                          console.log('Removing conversation from local state:', conversation._id);
                          console.log('Current conversations:', conversations.length);
                          setConversations(prevConversations => {
                            const filtered = prevConversations.filter(c => c._id !== conversation._id);
                            console.log('Filtered conversations:', filtered.length);
                            return filtered;
                          });

                          // If this was the active conversation, navigate back to messages
                          if (conversationId === conversation._id) {
                            navigate('/direct-messages');
                          }

                          // Then call the API
                          directMessageService.hideConversation(conversation._id)
                            .then(() => {
                              toast({
                                title: 'Success',
                                description: 'Conversation permanently deleted',
                                variant: 'default'
                              });
                            })
                            .catch(error => {
                              // If there's an error, add the conversation back
                              toast({
                                title: 'Error',
                                description: 'Failed to delete conversation: ' + error.message,
                                variant: 'destructive'
                              });

                              // Add the conversation back to the list
                              setConversations(prevConversations => {
                                if (!prevConversations.some(c => c._id === conversation._id)) {
                                  return [...prevConversations, conversation];
                                }
                                return prevConversations;
                              });
                            });
                        } catch (error) {
                          console.error('Error deleting conversation:', error);
                          toast({
                            title: 'Error',
                            description: 'Failed to delete conversation',
                            variant: 'destructive'
                          });
                        }
                      }
                    }}
                    className="p-1 text-muted-foreground hover:text-destructive"
                    title="Delete conversation"
                  >
                    <Trash className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Render message bubbles
  const renderMessages = () => {
    try {
      if (loading) {
        return (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        );
      }

    if (!conversationId && !selectedUser) {
      return (
        <div className="text-center py-8">
          <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">Select a conversation</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            Choose a conversation from the sidebar or start a new one
          </p>
        </div>
      );
    }

    if (messages.length === 0) {
      return (
        <div className="text-center py-8">
          <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">No messages yet</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            Send a message to start the conversation or try refreshing
          </p>
          <div className="mt-4 flex justify-center gap-2">
            <Button
              onClick={() => {
                if (conversationId) {
                  setLoading(true);
                  directMessageService.fetchMessages(conversationId)
                    .finally(() => setTimeout(() => setLoading(false), 500));
                }
              }}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Messages
            </Button>

            <Button
              variant="outline"
              onClick={async () => {
                if (conversationId) {
                  setLoading(true);
                  try {
                    console.log('Starting troubleshooting for conversation:', conversationId);

                    // First try to get the conversation details
                    const detailsResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations/${conversationId}/details`, {
                      headers: {
                        'Authorization': `Bearer ${getCurrentUser().token}`,
                        'Content-Type': 'application/json'
                      }
                    });

                    if (detailsResponse.ok) {
                      const detailsData = await detailsResponse.json();
                      console.log('Fetched conversation details for troubleshooting:', detailsData);

                      // Now try to get messages directly with a more flexible approach
                      try {
                        // First try the standard endpoint
                        const messagesResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations/${conversationId}`, {
                          headers: {
                            'Authorization': `Bearer ${getCurrentUser().token}`,
                            'Content-Type': 'application/json'
                          }
                        });

                        if (messagesResponse.ok) {
                          const messagesData = await messagesResponse.json();
                          console.log(`Fetched ${messagesData.length} messages directly`);

                          if (messagesData.length > 0) {
                            setMessages(messagesData);
                            toast({
                              title: 'Success',
                              description: `Found ${messagesData.length} messages`,
                              variant: 'default'
                            });
                          } else {
                            console.log('No messages found with standard endpoint, trying direct message lookup');

                            // If no messages found, try to find messages between these participants
                            if (detailsData.participants && detailsData.participants.length > 0) {
                              const otherParticipant = detailsData.participants.find(
                                (p: User) => p._id !== currentUser._id
                              );

                              if (otherParticipant) {
                                console.log('Trying to find messages with participant:', otherParticipant._id);

                                // Send a test message to ensure there's at least one message
                                const testMessageResult = await directMessageService.sendMessage({
                                  recipientId: otherParticipant._id,
                                  content: "Hello! I'm troubleshooting our conversation.",
                                  conversationId
                                });

                                console.log('Test message sent:', testMessageResult);

                                // Try fetching messages again
                                const retryResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/messages/conversations/${conversationId}`, {
                                  headers: {
                                    'Authorization': `Bearer ${getCurrentUser().token}`,
                                    'Content-Type': 'application/json'
                                  }
                                });

                                if (retryResponse.ok) {
                                  const retryData = await retryResponse.json();
                                  console.log(`Fetched ${retryData.length} messages after sending test message`);
                                  setMessages(retryData);

                                  toast({
                                    title: 'Success',
                                    description: `Found ${retryData.length} messages after troubleshooting`,
                                    variant: 'default'
                                  });
                                }
                              }
                            }
                          }
                        } else {
                          console.error('Failed to fetch messages:', messagesResponse.status);
                          toast({
                            title: 'Error',
                            description: `Failed to fetch messages: ${messagesResponse.status}`,
                            variant: 'destructive'
                          });
                        }
                      } catch (messagesError) {
                        console.error('Error fetching messages:', messagesError);
                        toast({
                          title: 'Error',
                          description: 'Error fetching messages',
                          variant: 'destructive'
                        });
                      }
                    } else {
                      console.error('Failed to fetch conversation details:', detailsResponse.status);
                      toast({
                        title: 'Error',
                        description: `Failed to fetch conversation details: ${detailsResponse.status}`,
                        variant: 'destructive'
                      });
                    }
                  } catch (error) {
                    console.error('Error in troubleshooting fetch:', error);
                    toast({
                      title: 'Error',
                      description: 'Troubleshooting failed',
                      variant: 'destructive'
                    });
                  } finally {
                    setLoading(false);
                  }
                }
              }}
            >
              <AlertCircle className="h-4 w-4 mr-2" />
              Troubleshoot
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4 p-4">
        {/* Hidden debug buttons - removed for cleaner UI */}

        {messages.length === 0 && !loading && (
          <div className="text-center py-4">
            <p className="text-sm text-muted-foreground">No messages found.</p>
          </div>
        )}

        {messages.map(message => {
          // Add safety check for message.sender
          if (!message || !message.sender) {
            console.error('Invalid message or missing sender:', message);
            return (
              <div key={message?._id || 'unknown'} className="flex justify-center">
                <div className="bg-muted px-3 py-2 rounded-md text-sm text-muted-foreground">
                  Invalid message format: {JSON.stringify(message)}
                </div>
              </div>
            );
          }

          // Check if the message is from the current user
          const isCurrentUser = message.sender._id === currentUser._id ||
                               message.sender._id.toString() === currentUser._id.toString();

          // Log for debugging
          if (message.sender._id !== currentUser._id && message.sender._id.toString() === currentUser._id.toString()) {
            console.log('ID comparison fixed by toString():', {
              messageId: message._id,
              senderId: message.sender._id,
              senderIdType: typeof message.sender._id,
              currentUserId: currentUser._id,
              currentUserIdType: typeof currentUser._id
            });
          }

          const isEditing = editingMessageId === message._id;

          if (message.deleted) {
            console.log(`Rendering deleted message: ${message._id}`);
            return (
              <div
                key={message._id}
                className="flex justify-center my-2 message-item deleted-message"
                data-message-id={message._id}
              >
                <div className="bg-muted px-3 py-2 rounded-md text-sm text-muted-foreground flex items-center">
                  <Trash className="h-3 w-3 mr-2 text-muted-foreground" />
                  <span>Message deleted</span>
                  <span className="text-xs ml-2 opacity-50">
                    {message.deletedAt ? formatDate(message.deletedAt) : ''}
                  </span>
                  {message.sender?._id === currentUser._id && (
                    <button
                      onClick={() => handleDeleteMessage(message._id)}
                      className="ml-3 opacity-50 hover:opacity-100 text-destructive"
                      title="Permanently delete message"
                    >
                      <Trash className="h-3 w-3" />
                    </button>
                  )}
                </div>
              </div>
            );
          }

          return (
            <div
              key={message._id}
              className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-3 message-item ${isCurrentUser ? 'current-user-message' : 'other-user-message'}`}
              data-message-id={message._id}
            >
              <div
                className={`max-w-[80%] ${
                  isCurrentUser
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-foreground'
                } px-3 py-2 rounded-lg`}
              >
                {isEditing ? (
                  <div className="space-y-2">
                    <Textarea
                      ref={editInputRef}
                      value={editedContent}
                      onChange={e => setEditedContent(e.target.value)}
                      className="min-h-[60px] bg-background text-foreground"
                    />
                    <div className="flex justify-end gap-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => {
                          setEditingMessageId(null);
                          setEditedContent('');
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        onClick={handleEditMessage}
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="text-sm">{message.content}</div>
                    <div className="text-xs mt-1 opacity-70 flex justify-between items-center">
                      <span>{formatDate(message.createdAt)}</span>
                      {message.edited && <span>(edited)</span>}
                      {isCurrentUser && (
                        <div className="flex gap-2 ml-2">
                          <button
                            onClick={() => {
                              setEditingMessageId(message._id);
                              setEditedContent(message.content);
                            }}
                            className="opacity-50 hover:opacity-100"
                            title="Edit message"
                          >
                            <Pencil className="h-3 w-3" />
                          </button>
                          <button
                            onClick={() => handleDeleteMessage(message._id)}
                            className="opacity-50 hover:opacity-100 text-destructive"
                            title="Delete message"
                          >
                            <Trash className="h-3 w-3" />
                          </button>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          );
        })}
        <div ref={messagesEndRef} />
      </div>
    );
    } catch (error) {
      console.error('Error rendering messages:', error);
      return (
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto text-destructive" />
          <h3 className="mt-4 text-lg font-medium">Error displaying messages</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            There was a problem displaying the messages. Please try refreshing.
          </p>
          <div className="mt-4 flex justify-center gap-2">
            <Button
              onClick={() => {
                if (conversationId) {
                  setLoading(true);
                  directMessageService.fetchMessages(conversationId)
                    .finally(() => setTimeout(() => setLoading(false), 500));
                }
              }}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Messages
            </Button>

            <Button
              variant="outline"
              onClick={() => {
                // Force reload the page
                window.location.reload();
              }}
            >
              <RotateCw className="h-4 w-4 mr-2" />
              Reload Page
            </Button>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="flex h-[calc(100vh-4rem)] overflow-hidden">
      {/* Sidebar */}
      <div className="w-80 border-r flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="font-semibold">Messages</h2>
          <div className="flex gap-2">
            <Dialog open={newConversationOpen} onOpenChange={setNewConversationOpen}>
              <DialogTrigger asChild>
                <Button
                  size="sm"
                  onClick={() => {
                    fetchUsers();
                    setSelectedUser(null);
                  }}
                >
                  New
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>New Conversation</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                  {loadingUsers ? (
                    <div className="flex justify-center py-4">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-[300px] overflow-y-auto">
                      {users.map(user => (
                        <div
                          key={user._id}
                          className={`p-3 rounded-lg cursor-pointer hover:bg-accent ${
                            selectedUser?._id === user._id ? 'bg-accent' : ''
                          }`}
                          onClick={() => {
                            setSelectedUser(user);
                            setNewConversationOpen(false);
                            // Clear conversation ID to start a new conversation
                            navigate('/direct-messages');
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarFallback>
                                {getInitials(user.fullName || user.username)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">
                                {user.fullName || user.username}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {user.email}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        <ScrollArea className="flex-1">
          {renderConversationList()}
        </ScrollArea>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Conversation header */}
        <div className="p-4 border-b">
          {conversationId ? (
            <div className="flex items-center">
              <h2 className="font-semibold">
                {(() => {
                  const conversation = conversations.find(c => c._id === conversationId);
                  if (!conversation) return 'Loading...';

                  // Safety check for participants
                  if (!conversation.participants || !Array.isArray(conversation.participants) || conversation.participants.length === 0) {
                    console.error('Conversation has no valid participants:', conversation._id);
                    return 'Unknown Participant';
                  }

                  const otherParticipant = conversation.participants.find(
                    p => p && p._id && p._id !== currentUser._id
                  );

                  return otherParticipant?.fullName || otherParticipant?.username || 'Unknown';
                })()}
              </h2>
              <Button
                variant="ghost"
                size="sm"
                className="ml-2"
                onClick={() => {
                  if (conversationId) {
                    directMessageService.fetchMessages(conversationId);
                  }
                }}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          ) : selectedUser ? (
            <h2 className="font-semibold">
              New conversation with {selectedUser.fullName || selectedUser.username}
            </h2>
          ) : (
            <h2 className="font-semibold">Select a conversation</h2>
          )}
        </div>

        {/* Messages */}
        <div
          className="flex-1 overflow-y-auto relative"
          id="messages-container"
          style={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            maxHeight: 'calc(100vh - 12rem)',
            overflowY: 'auto',
            overscrollBehavior: 'contain'
          }}
        >
          <div className="flex-grow"></div> {/* Spacer to push messages down */}
          {renderMessages()}

          {/* Scroll to bottom button - moved to left side to avoid blocking delete button */}
          <button
            onClick={scrollToBottom}
            className="absolute bottom-4 left-4 bg-primary text-primary-foreground rounded-full p-2 shadow-md hover:bg-primary/90 transition-opacity"
            title="Scroll to bottom"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </button>
        </div>

        {/* Message input */}
        {(conversationId || selectedUser) && (
          <div className="p-4 border-t">
            <div className="flex gap-2">
              <Textarea
                placeholder="Type your message..."
                className="resize-none"
                value={newMessage}
                onChange={e => setNewMessage(e.target.value)}
                onKeyDown={e => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (newMessage.trim()) {
                      handleSendMessage();
                    }
                  }
                }}
              />
              <Button
                type="button"
                onClick={handleSendMessage}
                disabled={!newMessage.trim() || sending}
              >
                {sending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
            {/* Environment info removed for cleaner UI */}
          </div>
        )}
      </div>
    </div>
  );
};

export default DirectMessages;

import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import api from '@/lib/axios';

const ResetPassword = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const token = searchParams.get('token');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showRequestNew, setShowRequestNew] = useState(false);
  const [email, setEmail] = useState('');
  const [isFirstTimeLogin, setIsFirstTimeLogin] = useState(false);

  // Check if we're coming from login with passwordResetRequired
  const locationState = location.state as {
    fromLogin?: boolean;
    email?: string;
    requiresReset?: boolean;
  } | null;

  useEffect(() => {
    // Check if we're coming from login with passwordResetRequired
    if (locationState?.fromLogin && locationState?.requiresReset) {
      console.log('Coming from login with password reset required');
      setIsFirstTimeLogin(true);

      // If email is provided in state, use it
      if (locationState.email) {
        setEmail(locationState.email);
      }

      // Show the password reset form directly
      setShowRequestNew(false);

      toast({
        title: "Set Your Password",
        description: "Please set a permanent password for your account.",
      });
    }
    // Only show the toast if there's no token AND we came from a direct link
    // (not from clicking the "Forgot password" link)
    else if (!token && window.location.search.includes('token=')) {
      setShowRequestNew(true);
      toast({
        title: "Invalid Reset Link",
        description: "Please request a new password reset link.",
        variant: "destructive"
      });
    } else if (!token) {
      // Just show the request form without error message if we're coming from login page
      setShowRequestNew(true);
    }
  }, [token, locationState]);

  const handleRequestNew = async () => {
    if (!email) {
      setError("Please enter your email address");
      return;
    }

    setIsLoading(true);
    try {
      const response = await api.post('/api/password/request-reset', { email });
      toast({
        title: "Reset Link Sent",
        description: response.data.message,
      });
      navigate('/login');
    } catch (error: any) {
      console.error('Error requesting reset:', error);
      const errorMessage = error.response?.data?.message || 'Failed to request password reset. Please try again.';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFirstTimePasswordSet = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (newPassword !== confirmPassword) {
      setError("Passwords don't match");
      return;
    }

    if (newPassword.length < 8) {
      setError("Password must be at least 8 characters long");
      return;
    }

    setIsLoading(true);
    try {
      // Get the user from localStorage
      const user = JSON.parse(localStorage.getItem('user') || '{}');

      if (!user || !user.token) {
        throw new Error('You must be logged in to set your password');
      }

      // Call the API to set the new password
      const response = await api.post('/api/users/set-password', {
        newPassword
      }, {
        headers: {
          Authorization: `Bearer ${user.token}`
        }
      });

      // Update the user in localStorage to remove passwordResetRequired flag
      if (user.passwordResetRequired) {
        const updatedUser = {
          ...user,
          passwordResetRequired: false
        };
        localStorage.setItem('user', JSON.stringify(updatedUser));
      }

      toast({
        title: "Success",
        description: response.data.message || "Password set successfully!",
      });

      // Navigate to dashboard
      setTimeout(() => {
        navigate('/dashboard');
      }, 1500);
    } catch (error: any) {
      console.error('Error setting password:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to set password. Please try again.';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // If this is a first-time login password set, use the other handler
    if (isFirstTimeLogin) {
      return handleFirstTimePasswordSet(e);
    }

    if (newPassword !== confirmPassword) {
      setError("Passwords don't match");
      return;
    }

    if (newPassword.length < 8) {
      setError("Password must be at least 8 characters long");
      return;
    }

    setIsLoading(true);
    try {
      const response = await api.post('/api/password/reset', {
        token,
        newPassword
      });

      if (response.data.warning) {
        toast({
          title: "Password Reset Successful",
          description: response.data.message,
          variant: "default"
        });

        toast({
          title: "Warning",
          description: response.data.warning,
          variant: "destructive"
        });
      } else {
        toast({
          title: "Success",
          description: response.data.message,
        });
      }

      // Short delay before redirecting to login
      setTimeout(() => {
        navigate('/login');
      }, 1500);
    } catch (error: any) {
      console.error('Password reset error:', error);
      const errorMessage = error.response?.data?.message || 'Failed to reset password. Please try again.';
      setError(errorMessage);

      // Check if we should show the request new link form
      if (error.response?.data?.shouldRequestNew) {
        setShowRequestNew(true);
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>{isFirstTimeLogin ? "Set Your Password" : "Reset Password"}</CardTitle>
          <CardDescription>
            {isFirstTimeLogin
              ? "Please set a permanent password for your account"
              : (showRequestNew
                ? (window.location.search.includes('token=')
                  ? "The reset link is invalid or expired. Please request a new one."
                  : "Enter your email to receive a password reset link")
                : "Enter your new password below")
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="p-3 mb-4 text-sm bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {showRequestNew ? (
            <form onSubmit={(e) => { e.preventDefault(); handleRequestNew(); }} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  disabled={isLoading}
                  autoFocus
                  autoComplete="email"
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? "Sending Reset Link..." : "Send Password Reset Link"}
              </Button>

              <Button
                type="button"
                variant="outline"
                className="w-full mt-2"
                onClick={() => navigate('/login')}
              >
                Back to Login
              </Button>
            </form>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="newPassword">New Password</Label>
                <Input
                  id="newPassword"
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="Enter your new password"
                  disabled={isLoading}
                  autoFocus
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm your new password"
                  disabled={isLoading}
                />
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? "Resetting Password..." : "Reset Password"}
              </Button>

              <Button
                type="button"
                variant="outline"
                className="w-full mt-2"
                onClick={() => setShowRequestNew(true)}
              >
                Request New Reset Link
              </Button>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ResetPassword;
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  MessageSquare,
  Search,
  PlusCircle,
  Loader2,
  AlertCircle,
  MoreVertical,
  Archive
} from 'lucide-react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useSendMessageHandler } from '@/components/SendMessageHandler';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import MessageList from '@/components/MessageList';
import MessageInput from '@/components/MessageInput';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { useSocketInstance } from '@/hooks/useSocket';
import { useSocket } from '@/context/SocketContext';
import api from '@/lib/axios';
import { getInitials } from '@/utils/stringUtils';

interface Conversation {
  _id: string;
  participants: User[];
  lastMessage: Message;
  title: string;
  unreadCount: number;
  updatedAt: string;
}

interface Message {
  _id: string;
  sender: User;
  content: string;
  createdAt: string;
  read: boolean;
  edited?: boolean;
  editedAt?: string;
  deleted?: boolean;
  deletedAt?: string;
}

interface User {
  _id: string;
  username: string;
  fullName: string;
  email: string;
  role: string;
  profilePicture?: string;
}

const Messages = () => {
  const { conversationId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const socket = useSocketInstance();
  const { isConnected: socketConnected } = useSocket();

  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [newConversationOpen, setNewConversationOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userSearchTerm, setUserSearchTerm] = useState('');
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState('');
  const editInputRef = useRef<HTMLTextAreaElement>(null);

  // Track recently sent messages to prevent duplicates from socket events
  // We'll store both message IDs and content-based keys for more robust duplicate detection
  const recentlySentMessagesRef = useRef<{[key: string]: boolean}>({});
  const processedMessageIdsRef = useRef<Set<string>>(new Set());

  // Fetch conversations
  const { data: conversations, isLoading: conversationsLoading, isError: conversationsError } = useQuery({
    queryKey: ['conversations'],
    queryFn: async () => {
      try {
        console.log('Fetching conversations...');
        const response = await api.get('/api/messages/conversations');
        console.log('Conversations response:', response.data);

        // Process conversations to handle deleted messages
        const deletedMessageIds = getDeletedMessageIds();

        // If we have deleted messages, check if any are the last message in a conversation
        if (deletedMessageIds.length > 0) {
          return response.data.map((conversation: Conversation) => {
            // If the last message is deleted, mark it as such
            if (conversation.lastMessage && deletedMessageIds.includes(conversation.lastMessage._id)) {
              return {
                ...conversation,
                lastMessage: {
                  ...conversation.lastMessage,
                  content: "Message deleted",
                  deleted: true
                }
              };
            }
            return conversation;
          });
        }

        return response.data;
      } catch (error) {
        console.error('Error fetching conversations:', error);
        throw error;
      }
    },
    retry: 1,
    staleTime: 10000, // Keep data fresh for 10 seconds
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnMount: true, // Refetch when component mounts
    refetchOnReconnect: true // Refetch when reconnecting
  });

  // Fetch conversation details when needed
  const { data: conversationDetails } = useQuery({
    queryKey: ['conversation-details', conversationId],
    queryFn: async () => {
      if (!conversationId) return null;
      try {
        console.log(`Fetching details for conversation ${conversationId}...`);
        const response = await api.get(`/api/messages/conversations/${conversationId}/details`);
        console.log('Conversation details response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Error fetching conversation details:', error);
        throw error;
      }
    },
    enabled: !!conversationId && !conversations?.find((c: Conversation) => c._id === conversationId),
    retry: 1
  });

  // Fetch messages for selected conversation
  const { data: messages, isLoading: messagesLoading, isError: messagesError } = useQuery({
    queryKey: ['messages', conversationId],
    queryFn: async () => {
      if (!conversationId) return null;
      try {
        console.log(`Fetching messages for conversation ${conversationId}...`);
        const response = await api.get(`/api/messages/conversations/${conversationId}`);
        console.log('Messages response:', response.data);

        // Add all message IDs to our processed set to prevent duplicates
        if (Array.isArray(response.data)) {
          response.data.forEach((message: Message) => {
            if (message && message._id) {
              processedMessageIdsRef.current.add(message._id);
              console.log('Added fetched message ID to processed set:', message._id);
            }
          });
        }

        return response.data;
      } catch (error) {
        console.error('Error fetching messages:', error);
        throw error;
      }
    },
    enabled: !!conversationId,
    retry: 1
  });

  // Fetch users for new conversation
  const { data: users, isLoading: usersLoading, isError: usersError } = useQuery<User[]>({
    queryKey: ['members', userSearchTerm],
    queryFn: async () => {
      try {
        console.log('Fetching approved members for new conversation...');
        const response = await api.get('/api/members/approved');
        console.log('Members response:', response.data);

        // Filter users based on search term if provided
        const allUsers = response.data.users || [];
        if (!userSearchTerm) return allUsers;

        const searchLower = userSearchTerm.toLowerCase();
        return allUsers.filter((user: User) =>
          (user.username && user.username.toLowerCase().includes(searchLower)) ||
          (user.fullName && user.fullName.toLowerCase().includes(searchLower)) ||
          (user.email && user.email.toLowerCase().includes(searchLower))
        );
      } catch (error) {
        console.error('Error fetching members:', error);
        throw error;
      }
    },
    enabled: newConversationOpen,
    retry: 1
  });

  // Send message mutation
  interface SendMessageResponse {
    message: Message;
    conversation: string;
  }

  const sendMessageMutation = useMutation<SendMessageResponse, Error, { recipientId: string; content: string; conversationId?: string }>({
    mutationFn: async (data: { recipientId: string; content: string; conversationId?: string }) => {
      try {
        console.log('Sending message with data:', data);
        // Add a small delay to ensure we can see the logs
        await new Promise(resolve => setTimeout(resolve, 100));

        // Store the content in our recently sent messages map to prevent duplicates
        if (data.conversationId) {
          const messageKey = `${data.conversationId}:${data.content}`;
          recentlySentMessagesRef.current[messageKey] = true;
          console.log('Added message to recently sent messages:', messageKey);
        }

        // Make the API call
        const response = await api.post('/api/messages/send', data);
        console.log('Send message response:', response.data);

        // If we got a message ID back, add it to our processed set
        if (response.data && response.data.message && response.data.message._id) {
          processedMessageIdsRef.current.add(response.data.message._id);
          console.log('Added message ID to processed set:', response.data.message._id);
        }

        return response.data;
      } catch (error) {
        console.error('Error sending message:', error);
        console.error('Error details:', {
          status: error?.status,
          message: error?.message,
          response: error?.response?.data
        });
        throw error;
      }
    },
    onMutate: async (data) => {
      // For existing conversations, we can do optimistic updates
      if (data.conversationId) {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({ queryKey: ['messages', data.conversationId] });
        await queryClient.cancelQueries({ queryKey: ['conversations'] });

        // Get current data
        const previousMessages = queryClient.getQueryData(['messages', data.conversationId]) as Message[];
        const previousConversations = queryClient.getQueryData(['conversations']) as Conversation[];

        // Get current user info
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');

        // Create optimistic message
        const optimisticMessage = {
          _id: `temp-${Date.now()}`,
          sender: {
            _id: currentUser._id,
            username: currentUser.username || '',
            fullName: currentUser.fullName || '',
            email: currentUser.email || '',
            role: currentUser.role || ''
          },
          content: data.content,
          createdAt: new Date().toISOString(),
          read: false
        };

        // Update messages cache
        if (previousMessages) {
          // Create a Map to deduplicate messages by ID
          const messagesMap = new Map();

          // Add all existing messages to the map
          previousMessages.forEach(msg => {
            messagesMap.set(msg._id, msg);
          });

          // Add the optimistic message
          messagesMap.set(optimisticMessage._id, optimisticMessage);

          // Convert back to array, preserving order
          const uniqueMessages = Array.from(messagesMap.values());

          // Sort by creation date to ensure proper order
          uniqueMessages.sort((a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );

          // Update the cache with deduplicated messages
          queryClient.setQueryData(['messages', data.conversationId], uniqueMessages);
        }

        // Update conversations cache
        if (previousConversations) {
          const updatedConversations = previousConversations.map(conv => {
            if (conv._id === data.conversationId) {
              return {
                ...conv,
                lastMessage: {
                  ...optimisticMessage,
                  sender: currentUser._id
                },
                updatedAt: new Date().toISOString()
              };
            }
            return conv;
          });

          // Create a completely new array with the updated conversation at the top
          // and deduplicate all conversations by ID
          const conversationToUpdate = updatedConversations.find(c => c._id === data.conversationId);
          if (conversationToUpdate) {
            const conversationMap = new Map();

            // Add the updated conversation first (so it's at the top)
            conversationMap.set(conversationToUpdate._id, conversationToUpdate);

            // Add all other conversations, ensuring no duplicates
            for (const conv of updatedConversations) {
              if (conv._id !== conversationToUpdate._id && !conversationMap.has(conv._id)) {
                conversationMap.set(conv._id, conv);
              }
            }

            // Convert map back to array
            const uniqueConversations = Array.from(conversationMap.values());

            // Update the cache with the deduplicated conversations
            queryClient.setQueryData(['conversations'], uniqueConversations);
          }
        }

        return { previousMessages, previousConversations };
      }

      return {};
    },
    onSuccess: (data, variables) => {
      console.log('Message sent successfully:', data);

      // Clear the message input
      setNewMessage('');

      // Track this message by ID to prevent duplicates from socket events
      if (data.message && data.message._id) {
        // Store the message ID in our Set
        processedMessageIdsRef.current.add(data.message._id);

        // Also store a content-based key as a backup detection method
        const messageKey = `${data.conversation}:${data.message.content}`;
        recentlySentMessagesRef.current[messageKey] = true;

        console.log('Added message to processed IDs:', data.message._id);
        console.log('Added message to recently sent:', messageKey);

        // Remove the content-based key after a delay to prevent memory leaks
        // We'll keep the message ID longer since it's more space-efficient
        setTimeout(() => {
          delete recentlySentMessagesRef.current[messageKey];
        }, 10000); // 10 seconds should be enough for the socket event to arrive

        // Clean up message IDs after a longer period
        setTimeout(() => {
          processedMessageIdsRef.current.delete(data.message._id);
        }, 60000); // 1 minute
      }

      // If this is a new conversation, navigate to it
      if (!conversationId && data.conversation) {
        console.log('Navigating to new conversation:', data.conversation);
        navigate(`/messages/${data.conversation}`);
      }

      // Always refetch conversations after sending a message
      queryClient.invalidateQueries({ queryKey: ['conversations'] });

      // Show success toast
      toast({
        title: 'Message sent',
        description: 'Your message has been sent successfully',
      });
    },
    onError: (error: unknown, variables, context) => {
      console.error('Error in send message mutation:', error);

      // Revert optimistic updates
      if (context && variables.conversationId) {
        if (context && typeof context === 'object' && 'previousMessages' in context) {
          queryClient.setQueryData(['messages', variables.conversationId], context.previousMessages);
        }
        if (context && typeof context === 'object' && 'previousConversations' in context) {
          queryClient.setQueryData(['conversations'], context.previousConversations);
        }
      }

      toast({
        title: 'Error sending message',
        description: error instanceof Error ? error.message : 'Something went wrong',
        variant: 'destructive'
      });
    }
  });

  // Edit message mutation
  interface EditMessageResponse {
    message: Message;
  }

  const editMessageMutation = useMutation<EditMessageResponse, unknown, { messageId: string; content: string }>({
    mutationFn: async ({ messageId, content }: { messageId: string; content: string }) => {
      try {
        console.log('Editing message:', { messageId, content });
        const response = await api.put(`/api/messages/${messageId}`, { content });
        console.log('Edit message response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Error editing message:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('Message edited successfully:', data);
      setEditingMessageId(null);
      setEditedContent('');

      // Update queries
      queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });

      toast({
        title: 'Message updated',
        description: 'Your message has been updated successfully',
      });
    },
    onError: (error: unknown) => {
      console.error('Error in edit message mutation:', error);
      const errorMessage = error instanceof Error ? error.message : 'Something went wrong';
      toast({
        title: 'Error updating message',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  });

  // Delete message mutation with enhanced error handling and persistent local deletion
  interface DeleteMessageResponse {
    message: Message;
    success?: boolean;
    messageId?: string;
    locallyHandled?: boolean;
    error?: unknown;
  }

  const deleteMessageMutation = useMutation<DeleteMessageResponse, unknown, { messageId: string }>({
    mutationFn: async ({ messageId }: { messageId: string }) => {
      try {
        console.log('Deleting message:', messageId);
        const response = await api.delete(`/api/messages/${messageId}`);
        console.log('Delete message response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Error deleting message:', error);

        // For any server error, we'll handle the deletion locally
        // This ensures the message appears deleted to the user regardless of server issues
        console.log('Handling message deletion locally due to server error');

        // Return a simulated success response
        return {
          success: true,
          messageId: messageId,
          locallyHandled: true,
          error: error
        };
      }
    },
    onMutate: async (messageId) => {
      // Cancel any outgoing refetches so they don't overwrite our optimistic update
      await queryClient.cancelQueries({ queryKey: ['messages', conversationId] });
      await queryClient.cancelQueries({ queryKey: ['conversations'] });

      // Snapshot the previous values
      const previousMessages = queryClient.getQueryData(['messages', conversationId]) as Message[];
      const previousConversations = queryClient.getQueryData(['conversations']) as Conversation[];

      // Optimistically update the messages
      if (previousMessages) {
        const updatedMessages = previousMessages.map(msg => {
          if (msg._id === messageId.messageId) {
            return {
              ...msg,
              deleted: true,
              content: '',
              deletedAt: new Date().toISOString()
            };
          }
          return msg;
        });

        // Update the messages cache
        queryClient.setQueryData(['messages', conversationId], updatedMessages);
      }

      // Also update the conversations list if this was the last message
      if (previousConversations) {
        const updatedConversations = previousConversations.map(conv => {
          if (conv.lastMessage && conv.lastMessage._id === messageId.messageId) {
            return {
              ...conv,
              lastMessage: {
                ...conv.lastMessage,
                content: "Message deleted",
                deleted: true
              }
            };
          }
          return conv;
        });

        // Update the conversations cache
        queryClient.setQueryData(['conversations'], updatedConversations);
      }

      // Return a context object with the snapshotted values
      return { previousMessages, previousConversations };
    },
    onSuccess: (data) => {
      console.log('Message deleted successfully:', data);

      // If the server request succeeded, invalidate queries to refetch fresh data
      if (!data.locallyHandled) {
        queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
        queryClient.invalidateQueries({ queryKey: ['conversations'] });
      }

      // Store the deleted message ID in localStorage to ensure it stays deleted
      // even after page refresh
      try {
        const deletedMessageIds = JSON.parse(localStorage.getItem('deletedMessageIds') || '[]');
        if (!deletedMessageIds.includes(data.messageId)) {
          deletedMessageIds.push(data.messageId);
          localStorage.setItem('deletedMessageIds', JSON.stringify(deletedMessageIds));
        }
      } catch (e) {
        console.error('Error storing deleted message ID in localStorage:', e);
      }

      toast({
        title: 'Message deleted',
        description: 'Your message has been deleted',
      });
    },
    onError: (error: unknown, messageId, context) => {
      console.error('Error in delete message mutation:', error);

      // Don't show error toast since we're handling the deletion locally
      console.log('Suppressing error toast as optimistic update was applied');

      // Store the deleted message ID in localStorage to ensure it stays deleted
      // even after page refresh
      try {
        const deletedMessageIds = JSON.parse(localStorage.getItem('deletedMessageIds') || '[]');
        if (!deletedMessageIds.includes(messageId)) {
          deletedMessageIds.push(messageId);
          localStorage.setItem('deletedMessageIds', JSON.stringify(deletedMessageIds));
        }
      } catch (e) {
        console.error('Error storing deleted message ID in localStorage:', e);
      }
    }
  });

  // Hide conversation mutation
  const hideConversationMutation = useMutation({
    mutationFn: async (conversationId: string) => {
      try {
        console.log('Hiding conversation:', conversationId);
        const response = await api.post(`/api/messages/conversations/${conversationId}/hide`);
        console.log('Hide conversation response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Error hiding conversation:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('Conversation hidden successfully:', data);
      // Update queries
      queryClient.invalidateQueries({ queryKey: ['conversations'] });

      // Navigate to messages home if the current conversation was hidden
      if (conversationId) {
        navigate('/messages');
      }

      toast({
        title: 'Conversation deleted',
        description: 'The conversation has been removed from your view',
      });
    },
    onError: (error: unknown) => {
      console.error('Error in hide conversation mutation:', error);
      const errorMessage = error instanceof Error
        ? error.message
        : typeof error === 'object' && error !== null && 'response' in error
          ? (error as {response?: {data?: {error?: string}}})?.response?.data?.error
          : 'Something went wrong';

      toast({
        title: 'Error deleting conversation',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  });

  // Get the message handling functions
  const { sendToExistingConversation, startNewConversation } = useSendMessageHandler();

  // Handle sending a message
  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    console.log('Handling send message with:', { newMessage, conversationId, selectedUser });

    // Get current user info for optimistic updates
    const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
    console.log('Current user from localStorage:', currentUser);

    if (conversationId) {
      // Send to existing conversation
      sendToExistingConversation(
        conversationId,
        newMessage,
        currentUser,
        conversationDetails,
        setNewMessage,
        sendMessageMutation,
        processedMessageIdsRef,
        recentlySentMessagesRef
      );
    } else if (selectedUser) {
      // Start a new conversation
      startNewConversation(
        selectedUser,
        newMessage,
        currentUser,
        setNewMessage,
        sendMessageMutation,
        processedMessageIdsRef,
        recentlySentMessagesRef
      );
    } else {
      console.error('Cannot send message: No conversation ID or selected user');
    }
  };

  // Handle selecting a user for a new conversation
  const handleSelectUserForNewConversation = (user: User) => {
    setSelectedUser(user);
    setNewConversationOpen(false);
  };

  // Handle editing a message
  const startEditingMessage = (message: Message) => {
    setEditingMessageId(message._id);
    setEditedContent(message.content);

    // Focus the edit input after it renders
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus();
      }
    }, 0);
  };

  // Handle saving an edited message
  const saveEditedMessage = () => {
    if (!editingMessageId || !editedContent.trim()) return;

    editMessageMutation.mutate({
      messageId: editingMessageId,
      content: editedContent
    });
  };

  // Handle canceling message edit
  const cancelEditingMessage = () => {
    setEditingMessageId(null);
    setEditedContent('');
  };

  // Handle deleting a message
  const handleDeleteMessage = (messageId: string) => {
    if (window.confirm('Are you sure you want to delete this message? This cannot be undone.')) {
      // Find the message in the current messages
      const messageToDelete = messages?.find(msg => msg._id === messageId);

      if (messageToDelete) {
        // Optimistically update the UI before the server responds
        const existingMessages4 = queryClient.getQueryData(['messages', conversationId]) as Message[];
        if (existingMessages4) {
          const updatedMessages = existingMessages4.map(msg => {
            if (msg._id === messageId) {
              return {
                ...msg,
                deleted: true,
                content: '',
                deletedAt: new Date().toISOString()
              };
            }
            return msg;
          });

          // Deduplicate messages before updating the cache
          const messagesMap = new Map();

          // Add all messages to the map (with deleted ones updated)
          updatedMessages.forEach(msg => {
            messagesMap.set(msg._id, msg);
          });

          // Convert back to array, preserving order
          const uniqueMessages = Array.from(messagesMap.values());

          // Sort by creation date to ensure proper order
          uniqueMessages.sort((a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );

          // Update the cache with deduplicated messages
          queryClient.setQueryData(['messages', conversationId], uniqueMessages);

          // Also update the conversations list if this was the last message
          const conversationsData4 = queryClient.getQueryData(['conversations']) as Conversation[];
          if (conversationsData4) {
            const updatedConversations = conversationsData4.map(conv => {
              if (conv.lastMessage && conv.lastMessage._id === messageId) {
                return {
                  ...conv,
                  lastMessage: {
                    ...conv.lastMessage,
                    content: "Message deleted",
                    deleted: true
                  }
                };
              }
              return conv;
            });

            // Update the conversations cache
            queryClient.setQueryData(['conversations'], updatedConversations);
          }
        }
      }

      // Still try the server request
      deleteMessageMutation.mutate({ messageId });
    }
  };

  // Handle hiding/deleting a conversation
  const handleHideConversation = (conversationId: string) => {
    if (window.confirm('Are you sure you want to delete this conversation? It will be removed from your view but will still be visible to the other person.')) {
      hideConversationMutation.mutate(conversationId);
    }
  };

  // Get deleted message IDs from localStorage
  const getDeletedMessageIds = React.useCallback(() => {
    try {
      const data = localStorage.getItem('deletedMessageIds');
      if (!data) return [];
      const parsed = JSON.parse(data);
      return Array.isArray(parsed) ? parsed : [];
    } catch (e) {
      console.error('Error parsing deletedMessageIds from localStorage:', e);
      return [];
    }
  }, []);


  // Filter and deduplicate conversations based on search term
  const filteredConversations = useMemo(() => {
    if (!Array.isArray(conversations)) {
      console.warn('Conversations is not an array:', conversations);
      return [];
    }

    const uniqueConversationsMap = new Map();

    const sortedConversations = [...conversations].sort((a, b) => {
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    });

    sortedConversations.forEach((conversation: Conversation) => {
      if (!uniqueConversationsMap.has(conversation._id)) {
        uniqueConversationsMap.set(conversation._id, conversation);
      }
    });

    const uniqueConversations = Array.from(uniqueConversationsMap.values())
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

    if (!searchTerm.trim()) return uniqueConversations;

    const searchLower = searchTerm.toLowerCase();
    return uniqueConversations.filter((conversation: Conversation) => (
      (conversation.title && conversation.title.toLowerCase().includes(searchLower)) ||
      conversation.participants.some(
        (p: User) =>
          (p.fullName && p.fullName.toLowerCase().includes(searchLower)) ||
          (p.username && p.username.toLowerCase().includes(searchLower)) ||
          (p.email && p.email.toLowerCase().includes(searchLower))
      )
    ));
  }, [conversations, searchTerm]);


  // Cleanup function for message tracking to prevent memory leaks
  useEffect(() => {
    // Set up a periodic cleanup of old message IDs
    const cleanupInterval = setInterval(() => {
      // Only keep message IDs from the last hour
      const oneHourAgo = Date.now() - 60 * 60 * 1000;

      // We don't have timestamps in our Set, so we'll just reset it periodically
      // This is a simple approach that works well enough for this use case
      if (processedMessageIdsRef.current.size > 1000) {
        console.log('Cleaning up processed message IDs, current size:', processedMessageIdsRef.current.size);
        processedMessageIdsRef.current = new Set();
      }

      // Clean up content-based keys
      const keysToDelete = [];
      for (const key in recentlySentMessagesRef.current) {
        keysToDelete.push(key);
      }

      if (keysToDelete.length > 0) {
        console.log('Cleaning up content-based message keys, count:', keysToDelete.length);
        keysToDelete.forEach(key => {
          delete recentlySentMessagesRef.current[key];
        });
      }
    }, 3600000); // Run every hour

    return () => {
      clearInterval(cleanupInterval);
    };
  }, []);

  // Listen for new messages via socket
  useEffect(() => {
    if (!socket) return;

    const handleNewMessage = (data: unknown) => {
      console.log('Received new message via socket:', data);

      if (!data || typeof data !== 'object' || !('message' in data) || !('conversation' in data)) {
        console.error('Invalid socket message data:', data);
        return;
      }

      const message = data.message as Message;
      const conversation = data.conversation as string;

      // Add more detailed logging for debugging
      console.log('Socket message details:', {
        messageId: message._id,
        conversationId: conversation,
        senderId: message.sender?._id,
        content: message.content?.substring(0, 20) + '...',
        timestamp: message.createdAt
      });

      // PRODUCTION FIX: Simplified duplicate detection
      // Only check by exact message ID match, which is the most reliable method
      if (message._id && processedMessageIdsRef.current.has(message._id)) {
        console.log('Ignoring duplicate message with ID:', message._id);
        return;
      }

      // Add this message ID to processed set to prevent future duplicates
      if (message._id) {
        processedMessageIdsRef.current.add(message._id);
        console.log('Added message ID to processed set:', message._id);
      }

      // Also add a content-based key as a backup
      const messageKey = `${conversation}:${message.content}`;
      recentlySentMessagesRef.current[messageKey] = true;
      console.log('Added message to content-based tracking:', messageKey);

      // Clear the message input if this is a message we just sent
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      if (message.sender && message.sender._id === currentUser._id) {
        setNewMessage('');
      }

      // Log detailed information about the message for debugging
      console.log('Processing new message:', {
        messageId: message._id,
        senderId: message.sender?._id,
        currentUserId: currentUser._id,
        isSentByCurrentUser: message.sender?._id === currentUser._id,
        conversationId: conversation,
        currentConversationId: conversationId,
        isForCurrentConversation: conversation === conversationId,
        content: message.content?.substring(0, 30) + (message.content?.length > 30 ? '...' : '')
      });

      // PRODUCTION FIX: Simplified cache update logic
      try {
        console.log(`Processing new message for conversation: ${conversation}`);

        // Simply invalidate the queries to force a fresh fetch from the server
        // This is more reliable than trying to manually update the cache
        queryClient.invalidateQueries({ queryKey: ['messages', conversation] });
        queryClient.invalidateQueries({ queryKey: ['conversations'] });

        // If we're viewing this conversation, also add the message directly to the cache
        // This provides immediate feedback while the fetch is happening
        if (conversationId === conversation) {
          console.log('Adding message directly to current conversation cache');

          // Get existing messages
          const existingMessages = queryClient.getQueryData(['messages', conversation]) as Message[] | undefined;

          if (existingMessages) {
            // Create a new array with the existing messages plus the new one
            const updatedMessages = [...existingMessages, message];

            // Sort by creation date
            updatedMessages.sort((a, b) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
            );

            // Update the cache
            queryClient.setQueryData(['messages', conversation], updatedMessages);
            console.log(`Updated cache with ${updatedMessages.length} messages`);
          } else {
            // If no existing messages, create a new array with just this message
            queryClient.setQueryData(['messages', conversation], [message]);
            console.log('Created new cache with single message');
          }
        }

        // PRODUCTION FIX: Simplified conversation cache update
        // Instead of complex manual cache manipulation, just invalidate the conversations query
        // This is more reliable and ensures we get fresh data from the server
        console.log('Invalidating conversations cache to get fresh data');
        queryClient.invalidateQueries({ queryKey: ['conversations'] });
      } catch (error) {
        console.error('Error updating cache for new message:', error);
        // Fallback to invalidating queries
        queryClient.invalidateQueries({ queryKey: ['messages', conversation] });
        queryClient.invalidateQueries({ queryKey: ['conversations'] });
      }

      // Show notification only if we're not in the conversation
      if (conversationId !== conversation) {
        toast({
          title: 'New Message',
          description: 'You have received a new message',
        });
      }
    };

    const handleMessageUpdated = (data: unknown) => {
      console.log('Message updated via socket:', data);
      if (
        typeof data === 'object' && data !== null &&
        'conversationId' in data && 'messageId' in data &&
        'content' in data && 'edited' in data && 'editedAt' in data
      ) {
        const { conversationId, messageId, content, edited, editedAt } = data as {
          conversationId: string;
          messageId: string;
          content: string;
          edited: boolean;
          editedAt: string;
        };
        try {
          const existingMessages6 = queryClient.getQueryData(['messages', conversationId]) as Message[];
          if (existingMessages6) {
            const updatedMessages = existingMessages6.map(msg => {
              if (msg._id === messageId) {
                return {
                  ...msg,
                  content,
                  edited,
                  editedAt
                };
              }
              return msg;
            });
            const messagesMap = new Map();
            updatedMessages.forEach(msg => {
              messagesMap.set(msg._id, msg);
            });
            const uniqueMessages = Array.from(messagesMap.values());
            uniqueMessages.sort((a, b) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
            );
            queryClient.setQueryData(['messages', conversationId], uniqueMessages);
          }
          const conversationsData6 = queryClient.getQueryData(['conversations']) as Conversation[];
          if (conversationsData6) {
            const updatedConversations = conversationsData6.map(conv => {
              if (conv._id === conversationId && conv.lastMessage && conv.lastMessage._id === messageId) {
                return {
                  ...conv,
                  lastMessage: {
                    ...conv.lastMessage,
                    content,
                    edited,
                    editedAt
                  }
                };
              }
              return conv;
            });
            updatedConversations.sort((a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
            queryClient.setQueryData(['conversations'], updatedConversations);
          }
        } catch (error) {
          console.error('Error updating cache for message update:', error);
          queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
        }
      }
    };

    const handleMessageDeleted = (data: unknown) => {
      console.log('Message deleted via socket:', data);
      if (
        typeof data === 'object' && data !== null &&
        'conversationId' in data && 'messageId' in data
      ) {
        const { conversationId, messageId } = data as { conversationId: string; messageId: string };
        try {
          // Add to deleted messages in localStorage
          const deletedMessageIds = getDeletedMessageIds();
          if (!deletedMessageIds.includes(messageId)) {
            deletedMessageIds.push(messageId);
            localStorage.setItem('deletedMessageIds', JSON.stringify(deletedMessageIds));
          }
          // Update messages cache
          const existingMessages7 = queryClient.getQueryData(['messages', conversationId]) as Message[];
          if (existingMessages7) {
            const updatedMessages = existingMessages7.map(msg => {
              if (msg._id === messageId) {
                return {
                  ...msg,
                  deleted: true,
                  content: '',
                  deletedAt: new Date().toISOString()
                };
              }
              return msg;
            });
            const messagesMap = new Map();
            updatedMessages.forEach(msg => {
              messagesMap.set(msg._id, msg);
            });
            const uniqueMessages = Array.from(messagesMap.values());
            uniqueMessages.sort((a, b) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
            );
            queryClient.setQueryData(['messages', conversationId], uniqueMessages);
          }
          // Update conversations cache
          const conversationsData7 = queryClient.getQueryData(['conversations']) as Conversation[];
          if (conversationsData7) {
            const updatedConversations = conversationsData7.map(conv => {
              if (conv._id === conversationId && conv.lastMessage && conv.lastMessage._id === messageId) {
                return {
                  ...conv,
                  lastMessage: {
                    ...conv.lastMessage,
                    content: "Message deleted",
                    deleted: true
                  }
                };
              }
              return conv;
            });
            updatedConversations.sort((a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
            queryClient.setQueryData(['conversations'], updatedConversations);
          }
        } catch (error) {
          console.error('Error updating cache for message deletion:', error);
          queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
          queryClient.invalidateQueries({ queryKey: ['conversations'] });
        }
      }
    };

    const handleUserStatusChange = (data: unknown) => {
      console.log('User status changed:', data);
      if (
        typeof data === 'object' && data !== null &&
        'userId' in data && 'isOnline' in data
      ) {
        const { userId, isOnline } = data as { userId: string; isOnline: boolean };
        try {
          const conversationsData8 = queryClient.getQueryData(['conversations']) as Conversation[];
          if (conversationsData8) {
            const updatedConversations = conversationsData8.map(conv => {
              const userIndex = conv.participants.findIndex(p => p._id === userId);
              if (userIndex >= 0) {
                const updatedParticipants = [...conv.participants];
                updatedParticipants[userIndex] = {
                  ...updatedParticipants[userIndex],
                  // @ts-expect-error: isOnline is not in User type but is used for UI
                  isOnline
                };
                return {
                  ...conv,
                  participants: updatedParticipants
                };
              }
              return conv;
            });
            updatedConversations.sort((a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
            queryClient.setQueryData(['conversations'], updatedConversations);
          }
        } catch (error) {
          console.error('Error updating cache for user status change:', error);
          queryClient.invalidateQueries({ queryKey: ['conversations'] });
        }
      }
    };

    // Handle conversation updates
    const handleConversationUpdated = (data: unknown) => {
      console.log('Conversation updated event received:', data);

      if (typeof data === 'object' && data !== null && 'conversationId' in data) {
        const { conversationId: updatedConversationId } = data as { conversationId: string };

        // Force refresh conversations list
        console.log('Refreshing conversations list due to conversation_updated event');
        queryClient.invalidateQueries({ queryKey: ['conversations'] });

        // If we're in this conversation, also refresh messages
        if (conversationId === updatedConversationId) {
          console.log('Refreshing messages for updated conversation:', conversationId);
          queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
        }
      }
    };

    // Register event handlers
    socket.on('new_message', handleNewMessage);
    socket.on('message_updated', handleMessageUpdated);
    socket.on('message_deleted', handleMessageDeleted);
    socket.on('userStatusChange', handleUserStatusChange);
    socket.on('conversation_updated', handleConversationUpdated);

    // Also listen for the custom event from the SocketContext
    const handleCustomConversationUpdated = (event: CustomEvent) => {
      console.log('Custom conversation_updated event received:', event.detail);

      // Force refresh conversations list
      queryClient.invalidateQueries({ queryKey: ['conversations'] });

      // If we're in this conversation, also refresh messages
      if (conversationId === event.detail.conversationId) {
        console.log('Refreshing messages for updated conversation:', conversationId);
        queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
      }
    };

    // Add event listener for the custom event
    window.addEventListener('conversation_updated', handleCustomConversationUpdated as EventListener);

    // Check if socket is connected
    if (!socket.connected) {
      console.log('Socket not connected, attempting to connect...');
      try {
        socket.connect();

        // Force a re-fetch of messages and conversations after initial connect
        if (conversationId) {
          setTimeout(() => {
            console.log('Forcing re-fetch of messages and conversations after socket connect');
            queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
            queryClient.invalidateQueries({ queryKey: ['conversations'] });
          }, 1000);
        }
      } catch (error) {
        console.error('Error connecting socket in Messages component:', error);
      }
    } else {
      console.log('Socket already connected:', socket.id);

      // Even if socket is connected, force a re-fetch to ensure we have the latest data
      // This helps in production where socket might be connected but data might be stale
      if (conversationId) {
        setTimeout(() => {
          console.log('Socket connected, forcing re-fetch of messages for conversation:', conversationId);
          queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
        }, 500);
      }
    }

    // PRODUCTION FIX: Simplified socket connection check
    // Set up a periodic check to ensure socket is connected and data is fresh
    const socketCheckInterval = setInterval(() => {
      if (!socket) {
        console.log('Socket check: socket instance is null');
        return;
      }

      // Log connection status
      console.log('Socket check: status =', socket.connected ? 'connected' : 'disconnected');

      // Always refresh data periodically regardless of socket status
      // This ensures we have fresh data even if socket events are missed
      console.log('Refreshing data in periodic check');

      // Always refresh conversations
      queryClient.invalidateQueries({ queryKey: ['conversations'] });

      // If we're in a conversation, also refresh messages
      if (conversationId) {
        console.log('Refreshing messages for conversation:', conversationId);
        queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
      }

      // If socket is disconnected, try to reconnect
      if (!socket.connected) {
        console.log('Socket disconnected, attempting to reconnect...');
        try {
          socket.connect();
        } catch (error) {
          console.error('Error reconnecting socket:', error);
        }
      }
    }, 3000); // Check every 3 seconds for production reliability

    return () => {
      // Clean up the interval when component unmounts
      clearInterval(socketCheckInterval);

      // Clean up event handlers
      socket.off('new_message', handleNewMessage);
      socket.off('message_updated', handleMessageUpdated);
      socket.off('message_deleted', handleMessageDeleted);
      socket.off('userStatusChange', handleUserStatusChange);
      socket.off('conversation_updated', handleConversationUpdated);

      // Remove the custom event listener
      window.removeEventListener('conversation_updated', handleCustomConversationUpdated as EventListener);
    };
  }, [socket, queryClient, toast, conversationId, getDeletedMessageIds]);

  // Auto-redirect to DirectMessages page
  useEffect(() => {
    // Wait a short time and then redirect to the DirectMessages page
    const redirectTimer = setTimeout(() => {
      console.log('Auto-redirecting to DirectMessages page');
      if (conversationId) {
        navigate(`/direct-messages/${conversationId}`);
      } else {
        navigate('/direct-messages');
      }
    }, 3000); // 3 seconds delay

    return () => {
      clearTimeout(redirectTimer);
    };
  }, [navigate, conversationId]);

  // Update selectedUser when conversation changes
  useEffect(() => {
    if (conversationId && conversations) {
      const currentConversation = conversations.find((c: Conversation) => c._id === conversationId);
      if (currentConversation && currentConversation.participants.length > 0) {
        // Get current user info
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');

        // Find the participant that is not the current user
        const otherParticipant = currentConversation.participants.find(
          (p: User) => p._id !== currentUser._id
        );

        if (otherParticipant) {
          console.log('Setting selected user from conversation:', otherParticipant);
          setSelectedUser(otherParticipant);
        } else if (currentConversation.participants.length > 0) {
          // If we can't find a non-current user (rare case), just use the first participant
          console.log('Using first participant as selected user:', currentConversation.participants[0]);
          setSelectedUser(currentConversation.participants[0]);
        }
      } else if (conversationDetails && conversationDetails.participants) {
        // Try to get participant from conversation details if available
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');

        // Find the participant that is not the current user
        const otherParticipant = conversationDetails.participants.find(
          (p: User) => p._id !== currentUser._id
        );

        if (otherParticipant) {
          console.log('Setting selected user from conversation details:', otherParticipant);
          setSelectedUser(otherParticipant);
        }
      }
    } else if (!conversationId) {
      // Clear selected user when no conversation is selected
      setSelectedUser(null);
    }
  }, [conversationId, conversations, conversationDetails]);

  // Clear message input when conversation changes
  useEffect(() => {
    setNewMessage('');
    console.log('Conversation changed, clearing message input');

    // Scroll to bottom of messages when conversation changes
    setTimeout(() => {
      const messagesContainer = document.getElementById('messages-container');
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }
    }, 300); // Longer timeout to ensure messages have loaded
  }, [conversationId]);

  // Filter out deleted messages from the cache when messages are loaded
  useEffect(() => {
    if (messages?.length) {
      const deletedMessageIds = getDeletedMessageIds();

      // If we have any locally deleted messages, update the cache
      if (deletedMessageIds.length > 0) {
        const existingMessages8 = queryClient.getQueryData(['messages', conversationId]) as Message[];
        if (existingMessages8) {
          // Check if any of our messages should be filtered out
          const hasDeletedMessages = existingMessages8.some(msg =>
            deletedMessageIds.includes(msg._id) && !msg.deleted
          );

          if (hasDeletedMessages) {
            // Update the cache with filtered messages
            const updatedMessages = existingMessages8.map(msg => {
              if (deletedMessageIds.includes(msg._id)) {
                return {
                  ...msg,
                  deleted: true,
                  content: '',
                  deletedAt: new Date().toISOString()
                };
              }
              return msg;
            });

            // Deduplicate messages before updating the cache
            const messagesMap = new Map();

            // Add all messages to the map (with deleted ones updated)
            updatedMessages.forEach(msg => {
              messagesMap.set(msg._id, msg);
            });

            // Convert back to array, preserving order
            const uniqueMessages = Array.from(messagesMap.values());

            // Sort by creation date to ensure proper order
            uniqueMessages.sort((a, b) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
            );

            // Update the cache with deduplicated messages
            queryClient.setQueryData(['messages', conversationId], uniqueMessages);
          }
        }
      }

      // Scroll to bottom of messages
      const messagesContainer = document.getElementById('messages-container');
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }
    }
  }, [messages, conversationId, queryClient, getDeletedMessageIds]);

  return (
    <div className="flex flex-col h-[calc(100vh-6rem)] overflow-hidden">
      {/* Banner to direct users to the new Direct Messages page */}
      <div className="bg-blue-100 border-l-4 border-blue-500 p-4 mb-2">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-blue-700">
              <strong>We've upgraded our messaging system!</strong> You're being redirected to our new, more reliable messaging experience.
            </p>
            <div className="mt-2">
              <Button
                variant="outline"
                className="bg-blue-500 text-white hover:bg-blue-600"
                onClick={() => {
                  // If we're in a conversation, redirect to the same conversation in the new system
                  if (conversationId) {
                    navigate(`/direct-messages/${conversationId}`);
                  } else {
                    navigate('/direct-messages');
                  }
                }}
              >
                Go to Direct Messages
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Conversations sidebar */}
      <div className="w-full sm:w-80 border-r flex flex-col">
        <div className="p-4 border-b">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <h2 className="font-bold text-lg">Messages</h2>
              {socketConnected ? (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1 text-xs py-0">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                  Connected
                </Badge>
              ) : (
                <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1 text-xs py-0">
                  <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                  Offline
                </Badge>
              )}
            </div>
            <Dialog open={newConversationOpen} onOpenChange={setNewConversationOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="icon">
                  <PlusCircle className="h-5 w-5" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>New Conversation</DialogTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    Select a user to start a new conversation
                  </p>
                </DialogHeader>
                <div className="space-y-4 mt-4">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search users..."
                      className="pl-8"
                      value={userSearchTerm}
                      onChange={(e) => setUserSearchTerm(e.target.value)}
                    />
                  </div>

                  {usersLoading ? (
                    <div className="flex justify-center py-4">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : usersError ? (
                    <div className="flex flex-col items-center justify-center py-4">
                      <AlertCircle className="h-6 w-6 text-destructive mb-2" />
                      <p className="text-center text-destructive font-medium">Failed to load users</p>
                      <p className="text-center text-xs text-muted-foreground mt-1">
                        There was an error retrieving the user list
                      </p>
                    </div>
                  ) : (
                    <ScrollArea className="h-[300px]">
                      <div className="space-y-2">
                        {users?.filter((user: User) => user._id !== JSON.parse(localStorage.getItem('user') || '{}')._id).map((user: User) => (
                          <div
                            key={user._id}
                            className="flex items-center p-2 rounded-md hover:bg-accent cursor-pointer"
                            onClick={() => handleSelectUserForNewConversation({
                              _id: user._id,
                              username: user.username,
                              fullName: user.fullName,
                              email: user.email,
                              role: user.role,
                              profilePicture: user.profilePicture
                            })}
                          >
                            <Avatar className="h-10 w-10 mr-3">
                              <AvatarFallback>{getInitials(user.fullName || user.username)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{user.fullName || user.username}</div>
                              <div className="text-xs text-muted-foreground">{user.email}</div>
                              {(user as any).isOnline && (
                                <div className="text-xs text-green-500 flex items-center mt-1">
                                  <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                                  Online
                                </div>
                              )}
                            </div>
                          </div>
                        ))}

                        {users?.length === 0 && (
                          <div className="text-center py-4 text-muted-foreground">
                            No users found
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search conversations..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <ScrollArea className="flex-1">
          {conversationsLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : conversationsError ? (
            <div className="flex flex-col items-center justify-center py-8 px-4">
              <AlertCircle className="h-8 w-8 text-destructive mb-2" />
              <p className="text-center text-destructive font-medium">Failed to load conversations</p>
              <p className="text-center text-sm text-muted-foreground mt-1">
                There was an error connecting to the messaging service
              </p>
            </div>
          ) : filteredConversations?.length > 0 ? (
            <div className="space-y-1 p-2">
              {filteredConversations.map((conversation: Conversation) => (
                <div
                  key={conversation._id}
                  className={`flex items-center p-3 rounded-md cursor-pointer group ${
                    conversationId === conversation._id ? 'bg-accent' : 'hover:bg-accent/50'
                  }`}
                >
                  <div
                    className="flex flex-1 items-center overflow-hidden"
                    onClick={() => {
                      // Get current user info
                      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');

                      // Find the participant that is not the current user
                      const otherParticipant = conversation.participants.find(
                        (p: User) => p._id !== currentUser._id
                      );

                      // Set the selected user before navigating
                      if (otherParticipant) {
                        setSelectedUser(otherParticipant);
                      } else if (conversation.participants.length > 0) {
                        // If we can't find a non-current user, use the first participant
                        setSelectedUser(conversation.participants[0]);
                      }

                      // Navigate to the conversation
                      navigate(`/messages/${conversation._id}`);
                    }}
                  >
                    <Avatar className="h-10 w-10 mr-3 flex-shrink-0">
                      <AvatarFallback>
                        {getInitials(conversation.participants[0]?.fullName || conversation.participants[0]?.username)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-center">
                        <div className="font-medium truncate">
                          {conversation.title || (conversation.participants[0]?.fullName || conversation.participants[0]?.username || "Unknown User")}
                        </div>
                        {conversation.unreadCount > 0 && (
                          <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800">
                            {conversation.unreadCount}
                          </Badge>
                        )}
                      </div>
                      {conversation.lastMessage && (
                        <div className="text-xs text-muted-foreground truncate">
                          {/* Check if the last message is in our deleted messages list */}
                          {getDeletedMessageIds().includes(conversation.lastMessage._id) || conversation.lastMessage.deleted ?
                            <i>Message deleted</i> :
                            <span>
                              {conversation.lastMessage.sender &&
                               conversation.lastMessage.sender._id === JSON.parse(localStorage.getItem('user') || '{}')._id ? 'You: ' : ''}
                              {conversation.lastMessage.content}
                            </span>
                          }
                        </div>
                      )}
                      <div className="text-xs text-muted-foreground mt-1">
                        {format(new Date(conversation.updatedAt), 'MMM d, h:mm a')}
                      </div>
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity ml-2 flex-shrink-0"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          handleHideConversation(conversation._id);
                        }}
                        className="text-red-600"
                      >
                        <Archive className="h-4 w-4 mr-2" />
                        Delete Conversation
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No conversations</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                {searchTerm ? 'No conversations match your search' : 'Start a new conversation'}
              </p>
            </div>
          )}
        </ScrollArea>
      </div>

      {/* Message area */}
      <div className="hidden sm:flex flex-col flex-1">
        {conversationId || selectedUser ? (
          <>
            {/* Conversation header */}
            <div className="p-4 border-b flex items-center">
              {selectedUser ? (
                <>
                  <Avatar className="h-10 w-10 mr-3">
                    <AvatarFallback>{getInitials(selectedUser.fullName || selectedUser.username)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{selectedUser.fullName || selectedUser.username}</div>
                    <div className="text-xs text-muted-foreground">{selectedUser.email}</div>
                  </div>
                </>
              ) : conversationId && conversations ? (
                <>
                  <Avatar className="h-10 w-10 mr-3">
                    <AvatarFallback>
                      {getInitials(
                        conversations.find((c: Conversation) => c._id === conversationId)?.participants[0]?.fullName ||
                        conversations.find((c: Conversation) => c._id === conversationId)?.participants[0]?.username ||
                        "Unknown User"
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">
                      {conversations.find((c: Conversation) => c._id === conversationId)?.title ||
                       conversations.find((c: Conversation) => c._id === conversationId)?.participants[0]?.fullName ||
                       conversations.find((c: Conversation) => c._id === conversationId)?.participants[0]?.username ||
                       "Unknown User"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {conversations.find((c: Conversation) => c._id === conversationId)?.participants[0]?.email || ""}
                    </div>
                  </div>
                </>
              ) : null}
            </div>

            {/* Messages */}
            <MessageList
              key={`message-list-${conversationId}-${messages?.length || 0}`}
              messages={messages}
              isLoading={messagesLoading}
              isError={messagesError}
              currentUserId={JSON.parse(localStorage.getItem('user') || '{}')._id}
              editingMessageId={editingMessageId}
              editedContent={editedContent}
              editInputRef={editInputRef}
              setEditedContent={setEditedContent}
              saveEditedMessage={saveEditedMessage}
              cancelEditingMessage={cancelEditingMessage}
              startEditingMessage={startEditingMessage}
              handleDeleteMessage={handleDeleteMessage}
              getDeletedMessageIds={getDeletedMessageIds}
              isPending={editMessageMutation.isPending}
            />

            {/* Message input */}
            <MessageInput
              newMessage={newMessage}
              setNewMessage={setNewMessage}
              handleSendMessage={handleSendMessage}
              isPending={sendMessageMutation.isPending}
              socketConnected={socketConnected}
              conversationId={conversationId}
            />
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-full">
            <MessageSquare className="h-16 w-16 text-muted-foreground" />
            <h2 className="mt-4 text-xl font-medium">Select a conversation</h2>
            <p className="mt-2 text-muted-foreground">
              Choose an existing conversation or start a new one
            </p>
          </div>
        )}
      </div>
      </div>
    </div>
  );
};

export default Messages;

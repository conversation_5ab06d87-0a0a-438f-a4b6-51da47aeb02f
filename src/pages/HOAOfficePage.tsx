import { PageHeader } from '@/components/PageHeader';
import HOAOffice from '@/components/HOAOffice';
import { Building2 } from 'lucide-react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

const HOAOfficePage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Set document title using useEffect instead of Helmet
  useEffect(() => {
    document.title = "HOA Office | Street Harmony";
  }, []);

  // Check if user is a company admin and redirect if so
  useEffect(() => {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const isCompanyAdmin = user?.role === 'company_admin';

    if (isCompanyAdmin) {
      toast({
        title: "Access Denied",
        description: "Company administrators have oversight access only. HOA Office is for operational management by HOA admins.",
        variant: "destructive"
      });
      navigate('/dashboard');
    }
  }, [navigate, toast]);

  return (
    <div className="flex flex-col gap-4 md:gap-8">
      <PageHeader
        heading="HOA Office"
        subheading="Manage your HOA community information"
        icon={<Building2 className="h-6 w-6" />}
      />
      <HOAOffice />
    </div>
  );
};

export default HOAOfficePage;

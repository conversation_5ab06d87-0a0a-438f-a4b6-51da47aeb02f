/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Building2, Users, FileText, Calendar, Shield, CreditCard } from 'lucide-react';

const LandingPage = () => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Hero Section */}
      <header className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="container mx-auto px-4 py-12 md:py-20">
          <div className="max-w-3xl mx-auto text-center">
            <div className="mb-6">
              <p className="text-sm md:text-base opacity-80 mb-1">Pelican App Solutions presents...</p>
              <h1 className="text-4xl md:text-5xl font-bold">
                HOA MANAGEMENT APP
              </h1>
            </div>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Simplify your community management with our comprehensive HOA solution
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/login">
                <Button size="lg" className="bg-white text-blue-700 hover:bg-gray-100">
                  Sign In
                </Button>
              </Link>
              <Link to="/register">
                <Button size="lg" className="bg-white text-black hover:bg-gray-100 border border-transparent">
                  Create Account
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Everything You Need to Manage Your Community</h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<Building2 className="h-10 w-10 text-blue-600" />}
              title="Property Management"
              description="Easily manage properties, residents, and maintenance requests in one place."
            />
            <FeatureCard
              icon={<Users className="h-10 w-10 text-blue-600" />}
              title="Member Directory"
              description="Keep track of all community members with a searchable, up-to-date directory."
            />
            <FeatureCard
              icon={<FileText className="h-10 w-10 text-blue-600" />}
              title="Document Repository"
              description="Store and share important community documents securely with all members."
            />
            <FeatureCard
              icon={<Calendar className="h-10 w-10 text-blue-600" />}
              title="Event Calendar"
              description="Schedule and promote community events, meetings, and important dates."
            />
            <FeatureCard
              icon={<Shield className="h-10 w-10 text-blue-600" />}
              title="Secure Access"
              description="Role-based permissions ensure information is only accessible to authorized users."
            />
            <FeatureCard
              icon={<CreditCard className="h-10 w-10 text-blue-600" />}
              title="Payment Processing"
              description="Collect dues and fees online with secure payment processing."
            />
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      {/* <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl">
          <h2 className="text-3xl font-bold text-center mb-12">Trusted by HOA Communities</h2>

          <div className="bg-blue-50 rounded-lg p-8 border border-blue-100">
            <p className="text-lg italic mb-6">
              "Street Harmony has transformed how we manage our community. From collecting dues to organizing events,
              everything is now streamlined and accessible to all our members. The support team is responsive and helpful."
            </p>
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">JD</div>
              <div className="ml-4">
                <p className="font-semibold">Jane Doe</p>
                <p className="text-sm text-gray-600">HOA President, Oakwood Community</p>
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* CTA Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to get started?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of communities that trust Street Harmony for their HOA management needs.
          </p>
          <div className="flex flex-col md:flex-row items-center justify-center gap-4 mb-8">
            <Link to="/register">
              <Button size="lg" className="bg-white text-black hover:bg-gray-100 border border-transparent">
                Create Your Account
              </Button>
            </Link>
          </div>
          <p className="text-sm text-gray-400 mt-4">
            Questions? Email us at <a href="mailto:<EMAIL>" className="text-blue-400 hover:underline"><EMAIL></a>
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-gray-300 py-8">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">HOA MANAGEMENT APP</h3>
              <p className="text-sm">
                A comprehensive HOA management solution by Pelican App Solutions designed to simplify community administration.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
              <div className="grid grid-cols-2 gap-x-4 text-sm">
                <ul className="space-y-2">
                  <li><Link to="/login" className="hover:text-white">Sign In</Link></li>
                  <li><Link to="/register" className="hover:text-white">Create Account</Link></li>
                </ul>
                <ul className="space-y-2">
                  <li><Link to="/privacy-policy" className="hover:text-white">Privacy Policy</Link></li>
                  <li><Link to="/terms-of-use" className="hover:text-white">Terms of Service</Link></li>
                  <li><Link to="/cookie-policy" className="hover:text-white">Cookie Policy</Link></li>
                </ul>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact</h3>
              <p className="text-sm">
                For support or inquiries, please contact us at:<br />
                <a href="mailto:<EMAIL>" className="hover:text-white font-medium">
                  <EMAIL>
                </a>
              </p>
              <p className="text-sm mt-2">
                We're here to help with any questions about your HOA management needs.
              </p>
            </div>
          </div>
          <div className="border-t border-gray-700 pt-6 text-center text-xs">
            <p>© {new Date().getFullYear()} HoaFlo L.L.C. All rights reserved.</p>
            <p className="mt-2">Registered in Louisiana, USA.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

// Feature Card Component
const FeatureCard = ({ icon, title, description }: { icon: React.ReactNode, title: string, description: string }) => {
  return (
    <Card className="border-0 shadow-sm hover:shadow-md transition-shadow">
      <CardContent className="pt-6">
        <div className="mb-4">{icon}</div>
        <h3 className="text-xl font-semibold mb-2">{title}</h3>
        <p className="text-gray-600">{description}</p>
      </CardContent>
    </Card>
  );
};

export default LandingPage;

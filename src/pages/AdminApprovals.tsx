// src/pages/AdminApprovals.tsx
import React, { useEffect, useState, useCallback } from 'react';
import api from '@/lib/axios';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

const AdminApprovals = () => {
  const [pendingUsers, setPendingUsers] = useState<any[]>([]);
  const [deniedUsers, setDeniedUsers] = useState<any[]>([]);
  const { toast } = useToast();
  const [isApproving, setIsApproving] = useState(false);
  const [showDenied, setShowDenied] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imageTitle, setImageTitle] = useState('');

  // Community selection state (same as other components)
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });
  const [selectedCommunityIds, setSelectedCommunityIds] = useState<string[]>(() => {
    const stored = localStorage.getItem('selectedCommunityIds');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);
  const [isMultipleCommunities, setIsMultipleCommunities] = useState<boolean>(false);
  const [availableCommunitiesInDropdown, setAvailableCommunitiesInDropdown] = useState<string[]>([]);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const userCommunityId = user?.communityId;
  const isCompanyAdmin = user.role === 'company_admin';

  // Listen for community selection changes from Sidebar
  useEffect(() => {
    // Initialize from localStorage
    const storedCommunityId = localStorage.getItem('selectedCommunityId');
    const storedCommunityIds = localStorage.getItem('selectedCommunityIds');

    if (storedCommunityIds) {
      const ids = JSON.parse(storedCommunityIds);
      setSelectedCommunityIds(ids);
      setIsMultipleCommunities(true);
      setSelectedCommunityName(`${ids.length} Communities Selected`);
    } else if (storedCommunityId) {
      setSelectedCommunityId(storedCommunityId);
      setIsMultipleCommunities(false);
    }

    const handleCommunityChange = async (event: CustomEvent) => {
      const { communityId, communityIds, availableCommunities } = event.detail;
      console.log('AdminApprovals: Received community selection change:', { communityId, communityIds, availableCommunities });

      // Store available communities from dropdown for "All Communities" use
      if (availableCommunities && Array.isArray(availableCommunities)) {
        const communityIdsList = availableCommunities.map((community: any) => community._id || community.id);
        setAvailableCommunitiesInDropdown(communityIdsList);
      }

      // Handle both single and multiple community selection
      if (communityIds && Array.isArray(communityIds)) {
        // Multiple communities selected
        setSelectedCommunityIds(communityIds);
        setSelectedCommunityId(null);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`${communityIds.length} Communities Selected`);

        console.log('AdminApprovals: Multiple communities selected:', communityIds);
      } else if (communityId && communityId !== 'all') {
        // Single community selected
        setSelectedCommunityId(communityId);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);

        // Fetch community details for display name
        try {
          const response = await api.get(`/api/communities/${communityId}`);
          const community = response.data;
          if (community) {
            setSelectedCommunityName(`${community.name} (${community.streetAddress || 'No address'})`);
          }
        } catch (error) {
          console.error('Error fetching community details:', error);
          setSelectedCommunityName('Selected Community');
        }
      } else {
        // "All Communities" selection
        setSelectedCommunityId(null);
        const communitiesToUse = communityIds && communityIds.length > 0 ? communityIds : availableCommunitiesInDropdown;
        setSelectedCommunityIds(communitiesToUse);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`All Communities (${communitiesToUse.length})`);
      }
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, [availableCommunitiesInDropdown]);

  const fetchPendingUsers = useCallback(async () => {
    try {
      // Build endpoint with community filtering
      let endpoint = '/api/auth/pending';
      const params = [];

      // Role-based filtering using communityId (same as other components)
      if (isCompanyAdmin) {
        // Company admin can see all, but apply community filters if specified
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities")
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('AdminApprovals: Company admin fetching pending users for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          // Single community selected
          params.push(`communityId=${selectedCommunityId}`);
          console.log('AdminApprovals: Company admin fetching pending users for single community:', selectedCommunityId);
        }
        // If no filters, company admin sees all pending users (this is intentional)
      } else {
        // Admin and Members see pending users from communities they have access to
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities" = all accessible communities)
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('AdminApprovals: User fetching pending users for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          params.push(`communityId=${selectedCommunityId}`);
          console.log('AdminApprovals: User fetching pending users for single community:', selectedCommunityId);
        } else if (userCommunityId) {
          // Default to their community if no specific selection
          params.push(`communityId=${userCommunityId}`);
          console.log('AdminApprovals: User fetching pending users for their default community:', userCommunityId);
        }
      }

      // IMPORTANT: Always ensure we have some filtering for non-company-admin users
      if (!isCompanyAdmin && params.length === 0) {
        console.warn('AdminApprovals: No filtering applied for non-company-admin user, applying default community filter');
        // Apply default user-based filtering with communityId only
        if (userCommunityId) {
          params.push(`communityId=${userCommunityId}`);
          console.log('AdminApprovals: Applied default community filter for security:', userCommunityId);
        }
      }

      if (params.length > 0) {
        endpoint += `?${params.join('&')}`;
      }

      console.log('AdminApprovals: Fetching pending users from endpoint:', endpoint);

      const res = await api.get(endpoint);
      console.log('AdminApprovals: Pending users response:', res.data);

      // Ensure we have an array - handle different response structures
      const pendingUsersArray = Array.isArray(res.data) ? res.data : (res.data?.users || res.data?.data || []);
      console.log('AdminApprovals: Processed pending users array:', pendingUsersArray);

      // Log each user's image paths
      pendingUsersArray.forEach((user: any) => {
        console.log('User image paths:', {
          username: user.username,
          profilePhoto: user.profilePhoto,
          licenseFront: user.licenseFront,
          licenseBack: user.licenseBack
        });
      });
      setPendingUsers(pendingUsersArray);
    } catch (err) {
      console.error('Failed to fetch pending users:', err);
      toast({
        title: "Error",
        description: "Failed to fetch pending users. Please try again.",
        variant: "destructive",
      });
    }
  }, [selectedCommunityId, selectedCommunityIds, isCompanyAdmin, userCommunityId, toast]);

  const fetchDeniedUsers = useCallback(async () => {
    try {
      // Build endpoint with community filtering (same logic as pending users)
      let endpoint = '/api/auth/denied';
      const params = [];

      // Role-based filtering using communityId
      if (isCompanyAdmin) {
        // Company admin can see all, but apply community filters if specified
        if (selectedCommunityIds.length > 0) {
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('AdminApprovals: Company admin fetching denied users for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          params.push(`communityId=${selectedCommunityId}`);
          console.log('AdminApprovals: Company admin fetching denied users for single community:', selectedCommunityId);
        }
      } else {
        // Admin and Members see denied users from communities they have access to
        if (selectedCommunityIds.length > 0) {
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('AdminApprovals: User fetching denied users for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          params.push(`communityId=${selectedCommunityId}`);
          console.log('AdminApprovals: User fetching denied users for single community:', selectedCommunityId);
        } else if (userCommunityId) {
          params.push(`communityId=${userCommunityId}`);
          console.log('AdminApprovals: User fetching denied users for their default community:', userCommunityId);
        }
      }

      // Security fallback for non-company-admin users
      if (!isCompanyAdmin && params.length === 0) {
        console.warn('AdminApprovals: No filtering applied for denied users, applying default community filter');
        if (userCommunityId) {
          params.push(`communityId=${userCommunityId}`);
          console.log('AdminApprovals: Applied default community filter for denied users:', userCommunityId);
        }
      }

      if (params.length > 0) {
        endpoint += `?${params.join('&')}`;
      }

      console.log('AdminApprovals: Fetching denied users from endpoint:', endpoint);

      const res = await api.get(endpoint);
      console.log('AdminApprovals: Denied users response:', res.data);

      // Ensure we have an array - handle different response structures
      const deniedUsersArray = Array.isArray(res.data) ? res.data : (res.data?.users || res.data?.data || []);
      console.log('AdminApprovals: Processed denied users array:', deniedUsersArray);

      setDeniedUsers(deniedUsersArray);
    } catch (err) {
      console.error('Failed to fetch denied users:', err);
      toast({
        title: "Error",
        description: "Failed to fetch denied users. Please try again.",
        variant: "destructive",
      });
    }
  }, [selectedCommunityId, selectedCommunityIds, isCompanyAdmin, userCommunityId, toast]);

  const approveUser = async (userId: string) => {
    if (!confirm('Are you sure you want to approve this user?')) return;

    setIsApproving(true);
    try {
      console.log('Approving user with ID:', userId);
      const response = await api.put(`/api/auth/approve/${userId}`);
      console.log('Approval response:', response.data);

      toast({
        title: "Success",
        description: "User has been approved and added as a member.",
        variant: "default",
      });

      // Refresh the pending users list
      await fetchPendingUsers();
    } catch (err: any) {
      console.error('Failed to approve user:', err);
      console.error('Error details:', {
        status: err.response?.status,
        data: err.response?.data,
        message: err.message
      });

      const errorMessage = err.response?.data?.message || err.message || "Failed to approve user. Please try again.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsApproving(false);
    }
  };

  const denyUser = async (userId: string) => {
    if (!confirm('Are you sure you want to deny and delete this user?')) return;

    try {
      await api.put(`/api/auth/deny/${userId}`);
      await fetchPendingUsers();
      toast({
        title: "Success",
        description: "User has been denied.",
        variant: "default",
      });
    } catch (err) {
      console.error('Failed to deny user:', err);
      toast({
        title: "Error",
        description: "Failed to deny user. Please try again.",
        variant: "destructive",
      });
    }
  };

  const openImageModal = (imageUrl: string, title: string) => {
    setSelectedImage(imageUrl);
    setImageTitle(title);
  };

  useEffect(() => {
    fetchPendingUsers();
  }, [fetchPendingUsers]);

  useEffect(() => {
    if (showDenied) {
      fetchDeniedUsers();
    }
  }, [showDenied, fetchDeniedUsers]);

  // Helper function to construct image URL
  const getImageUrl = (path: string) => {
    if (!path) return null;
    // If it's already a full URL, return it
    if (path.startsWith('http')) return path;
    // If it starts with /uploads, add the base URL
    if (path.startsWith('/uploads')) {
      return `${API_BASE_URL}${path}`;
    }
    // If it doesn't start with /uploads, add it
    return `${API_BASE_URL}/uploads/${path}`;
  };

  return (
    <div className="container mx-auto p-4 max-w-5xl">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">User Approvals</h1>
        </div>
        <Button
          onClick={() => setShowDenied(!showDenied)}
          variant="outline"
          className="w-32"
        >
          {showDenied ? 'Show Pending' : 'Show Denied'}
        </Button>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold mb-4">
          {showDenied ? 'Denied Users' : 'Pending Users'}
        </h2>

        {showDenied ? (
          <div className="grid gap-4">
            {deniedUsers.length === 0 ? (
              <Card className="overflow-hidden">
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center space-y-3">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">No Denied Users</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        There are currently no denied users to display.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              deniedUsers.map((user: any) => (
                <Card key={user._id} className="overflow-hidden">
                  <CardContent className="p-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Username</p>
                        <p className="font-medium">{user.username}</p>
                        <p className="text-sm text-gray-500 mt-2">Email</p>
                        <p className="font-medium">{user.email}</p>
                        <p className="text-sm text-gray-500 mt-2">Full Name</p>
                        <p className="font-medium">{user.fullName}</p>
                        <p className="text-sm text-gray-500 mt-2">Role</p>
                        <p className="font-medium capitalize">{user.role || 'member'}</p>
                        <p className="text-sm text-gray-500 mt-2">Property Address</p>
                        <p className="font-medium">{user.propertyAddress}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        ) : (
          <div className="grid gap-4">
            {pendingUsers.length === 0 ? (
              <Card className="overflow-hidden">
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center space-y-3">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">No Pending Users</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        There are currently no pending user approvals to review.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              pendingUsers.map((user: any) => (
              <Card key={user._id} className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex gap-6">
                    {/* User Information */}
                    <div className="flex-1">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                        <div>
                          <p className="text-sm text-gray-500">Username</p>
                          <p className="font-medium">{user.username}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Email</p>
                          <p className="font-medium">{user.email}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Full Name</p>
                          <p className="font-medium">{user.fullName}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Role</p>
                          <p className="font-medium capitalize">{user.role || 'member'}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Property Address</p>
                          <p className="font-medium">{user.propertyAddress}</p>
                        </div>
                      </div>

                      <div className="flex gap-2 mt-4">
                        <Button
                          onClick={() => approveUser(user._id)}
                          disabled={isApproving}
                          className="w-24"
                        >
                          {isApproving ? 'Approving...' : 'Approve'}
                        </Button>
                        <Button
                          onClick={() => denyUser(user._id)}
                          variant="destructive"
                          className="w-24"
                        >
                          Deny
                        </Button>
                      </div>
                    </div>

                    {/* Images Section */}
                    <div className="flex gap-4">
                      {user.profilePhoto && (
                        <div className="text-center">
                          <p className="text-sm text-gray-500 mb-1">Profile</p>
                          <img
                            src={getImageUrl(user.profilePhoto)}
                            alt={`${user.username}'s Profile`}
                            className="w-24 h-24 object-cover rounded-lg cursor-pointer hover:opacity-80 border border-gray-200"
                            onClick={() => openImageModal(getImageUrl(user.profilePhoto), 'Profile Photo')}
                            onError={(e) => {
                              console.error('Failed to load image:', user.profilePhoto);
                              e.currentTarget.src = '/placeholder.png';
                            }}
                          />
                        </div>
                      )}
                      {user.licenseFront && (
                        <div className="text-center">
                          <p className="text-sm text-gray-500 mb-1">License Front</p>
                          <img
                            src={getImageUrl(user.licenseFront)}
                            alt={`${user.username}'s License Front`}
                            className="w-24 h-24 object-cover rounded-lg cursor-pointer hover:opacity-80 border border-gray-200"
                            onClick={() => openImageModal(getImageUrl(user.licenseFront), 'License Front')}
                            onError={(e) => {
                              console.error('Failed to load image:', user.licenseFront);
                              e.currentTarget.src = '/placeholder.png';
                            }}
                          />
                        </div>
                      )}
                      {user.licenseBack && (
                        <div className="text-center">
                          <p className="text-sm text-gray-500 mb-1">License Back</p>
                          <img
                            src={getImageUrl(user.licenseBack)}
                            alt={`${user.username}'s License Back`}
                            className="w-24 h-24 object-cover rounded-lg cursor-pointer hover:opacity-80 border border-gray-200"
                            onClick={() => openImageModal(getImageUrl(user.licenseBack), 'License Back')}
                            onError={(e) => {
                              console.error('Failed to load image:', user.licenseBack);
                              e.currentTarget.src = '/placeholder.png';
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
              ))
            )}
          </div>
        )}
      </div>

      <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{imageTitle}</DialogTitle>
          </DialogHeader>
          {selectedImage && (
            <img
              src={selectedImage}
              alt={imageTitle}
              className="w-full h-auto object-contain"
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminApprovals;

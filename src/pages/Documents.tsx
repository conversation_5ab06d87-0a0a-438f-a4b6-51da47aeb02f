/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useQuery } from '@tanstack/react-query';
import DocumentList from '@/components/DocumentList';
import DocumentUpload from '@/components/DocumentUpload';
import api from '@/lib/axios';

const Documents = () => {
  // Check if user is admin (but exclude company admin from upload functionality)
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isOperationalAdmin = user?.role === 'admin';
  const isCompanyAdmin = user?.role === 'company_admin';
  const location = useLocation();
  const [selectedHoaId, setSelectedHoaId] = useState<string | null>(null);

  // Extract query parameters from URL
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const hoaId = searchParams.get('hoaId');
    if (hoaId) setSelectedHoaId(hoaId);
  }, [location.search]);

  // Fetch HOAs for company admin dropdown
  const { data: hoas = [] } = useQuery({
    queryKey: ['hoas'],
    queryFn: async () => {
      try {
        const response = await api.get('/api/hoa');
        return response.data.data || [];
      } catch (error) {
        console.error('Error fetching HOAs:', error);
        return [];
      }
    },
    enabled: isCompanyAdmin
  });

  return (
    <div className="container mx-auto py-4 sm:py-6 px-4 sm:px-6">
      <h1 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6">
        Documents
      </h1>

      {/* Company Admin HOA Selector */}
      {isCompanyAdmin && (
        <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-purple-900">Document Oversight</h3>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-purple-800 mb-2">
                Select HOA to Monitor
              </label>
              <Select value={selectedHoaId || 'all'} onValueChange={(value) => {
                const url = new URL(window.location.href);
                if (value && value !== 'all') {
                  url.searchParams.set('hoaId', value);
                  setSelectedHoaId(value);
                } else {
                  url.searchParams.delete('hoaId');
                  setSelectedHoaId(null);
                }
                window.history.pushState({}, '', url.toString());
              }}>
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="All HOAs" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All HOAs</SelectItem>
                  {hoas.map((hoa: any) => (
                    <SelectItem key={hoa._id} value={hoa._id}>
                      {hoa.hoaCommunityName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {selectedHoaId && (
              <div className="flex-1">
                <label className="block text-sm font-medium text-purple-800 mb-2">
                  Currently Viewing
                </label>
                <div className="p-2 bg-white border rounded text-sm">
                  {hoas.find((hoa: any) => hoa._id === selectedHoaId)?.hoaCommunityName || 'Selected HOA'}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        <div className={isOperationalAdmin ? "lg:col-span-2" : "lg:col-span-3"}>
          <DocumentList hoaId={selectedHoaId} />
        </div>

        {isOperationalAdmin && (
          <div className="lg:col-span-1">
            <DocumentUpload />
          </div>
        )}
      </div>
    </div>
  );
};

export default Documents;

import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import LoginForm from '@/components/forms/LoginForm';
import api from '@/lib/axios';
import { useToast } from '@/hooks/use-toast';
import { getProfilePhotoUrl } from '@/utils/imageUtils';
import MFAVerification from '@/components/MFAVerification';

const Login = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showMFA, setShowMFA] = useState(false);
  const [mfaPhoneNumber, setMfaPhoneNumber] = useState('');
  const [loginResponse, setLoginResponse] = useState<any>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  // Check if user is already logged in and test API connection on component mount
  useEffect(() => {
    // Check for URL parameters
    const queryParams = new URLSearchParams(location.search);
    const expired = queryParams.get('expired');
    const message = queryParams.get('message');

    if (expired) {
      setError('Your session has expired. Please log in again.');
      toast({
        title: "Session Expired",
        description: "Your session has expired. Please log in again.",
        variant: "destructive",
      });
    } else if (message) {
      setError(message);
      toast({
        title: "Login Required",
        description: message,
        variant: "default",
      });
    }
    // Check if user is already logged in
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const userData = JSON.parse(userStr);
        if (userData && userData.token) {
          console.log('User already logged in, redirecting to dashboard');
          navigate('/dashboard', { replace: true });
          return;
        }
      } catch (e) {
        console.error('Error parsing user data:', e);
        // Clear invalid data
        localStorage.removeItem('user');
      }
    }

    // Test API connection
    const testApiConnection = async () => {
      try {
        console.log('Testing API connection...');
        const response = await api.get('/');
        console.log('API connection successful:', response.data);
        toast({
          title: 'Backend Connection',
          description: 'Successfully connected to backend!',
        });
      } catch (error) {
        console.error('API connection failed:', error);
        toast({
          title: 'Backend Connection Failed',
          description: 'Could not connect to the backend server.',
          variant: 'destructive',
        });
      }
    };

    testApiConnection();
  }, [toast, navigate, location.search]);

  const handleLogin = async (username: string, password: string) => {
    setIsLoading(true);
    setError(null);
    try {
      console.log('Attempting login with:', { username });

      // Make sure we're using the correct URL
      const loginUrl = '/api/auth/login';
      console.log('Login URL:', loginUrl);
      console.log('API base URL:', api.defaults.baseURL);
      console.log('Full login URL:', api.defaults.baseURL + loginUrl);

      // Determine if the identifier looks like an email
      const isEmail = username.includes('@');

      // Prepare the login payload
      const loginPayload = isEmail
        ? { email: username.trim(), password }
        : { username: username.trim(), password };

      console.log('Sending login request with:', {
        ...loginPayload,
        password: password ? 'provided' : 'missing'
      });

      const res = await api.post(loginUrl, loginPayload);

      console.log('Login response:', res.data); // Debug log

      if (!res.data.user) {
        throw new Error('Invalid response from server');
      }

      // Check if MFA is required
      if (res.data.mfaRequired) {
        // Store login response for later use after MFA verification
        setLoginResponse(res.data);
        setMfaPhoneNumber(res.data.phoneNumber || '');
        setShowMFA(true);
        setIsLoading(false);
        return;
      }

      // If no MFA required, proceed with normal login
      if (!res.data.token) {
        throw new Error('Token missing from server response');
      }

      // Complete the login process
      completeLogin(res.data.user, res.data.token);
    } catch (err: any) {
      console.error('Login error:', err.response?.data || err); // Debug log

      // Extract detailed error information
      const errorData = err.response?.data || {};
      const errorMessage = errorData.message || err.message || 'Invalid username or password';
      const errorId = errorData.errorId || null;
      const errorHint = errorData.hint || null;
      const errorStatus = errorData.status || null;

      // Log detailed error information
      console.error('Login error details:', {
        message: errorMessage,
        errorId,
        hint: errorHint,
        status: errorStatus,
        response: err.response?.data
      });

      // Set appropriate error message based on status
      let displayMessage = errorMessage;

      if (errorStatus === 'pending') {
        displayMessage = 'Your account is pending approval by an administrator.';
      } else if (errorStatus === 'denied') {
        displayMessage = 'Your account application has been denied.';
      } else if (errorHint) {
        // Add the hint to the error message if available
        displayMessage = `${errorMessage}. ${errorHint}`;
      }

      setError(displayMessage);

      // Show toast with error ID for reference if available
      toast({
        title: "Login failed",
        description: errorId ? `${displayMessage} (Error ID: ${errorId})` : displayMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadEntitlements = async () => {
    try {
      const response = await api.get('/api/entitlements');
      localStorage.setItem('features', JSON.stringify(response.data.data.features));
    } catch (error) {
      console.error('Error fetching entitlements:', error);
      return [];
    }
  }

  // Function to complete the login process
  const completeLogin = (user: any, token: string) => {
    // Store user data with token in one object
    const userData = {
      ...user,
      token: token,
      // Ensure profile photo URL is properly formatted
      profilePhoto: getProfilePhotoUrl(user.profilePhoto),
      // Add login timestamp for token expiration tracking
      loginTime: new Date().toISOString()
    };

    console.log('Storing user data:', {
      ...userData,
      token: userData.token ? `${userData.token.substring(0, 10)}...` : 'missing',
      tokenLength: userData.token?.length
    });

    // Store new user data
    localStorage.setItem('user', JSON.stringify(userData));

    // Verify stored data
    const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
    console.log('Stored user data verification:', {
      hasToken: !!storedUser.token,
      tokenLength: storedUser.token?.length,
      role: storedUser.role,
      loginTime: storedUser.loginTime
    });

    if (!storedUser.token || !storedUser.role) {
      throw new Error('Failed to store user data');
    }

    toast({
      title: "Logged in successfully",
      description: `Welcome back, ${userData.username}!`,
    });

    // Fetch entitlements
    handleLoadEntitlements();

    // Navigate to dashboard
    console.log('Navigating to dashboard...');
    navigate('/dashboard', { replace: true });
    console.log('Navigation triggered');
  };

  // Handle successful MFA verification
  const handleMFASuccess = (token: string, user: any) => {
    completeLogin(user, token);
  };

  // Handle MFA cancellation
  const handleMFACancel = () => {
    setShowMFA(false);
    setLoginResponse(null);
    setMfaPhoneNumber('');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4 sm:p-6">
      <div className="max-w-md w-full mx-auto p-4 sm:p-6 bg-white border rounded-lg shadow-lg">
        {showMFA ? (
          <MFAVerification
            phoneNumber={mfaPhoneNumber}
            onVerificationSuccess={handleMFASuccess}
            onCancel={handleMFACancel}
          />
        ) : (
          <>
            <div className="text-center mb-4 sm:mb-6">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Welcome Back</h2>
              <p className="text-gray-600 mt-2 text-sm sm:text-base">Sign in to your account</p>
            </div>

            {error && (
              <div className={`p-3 ${error.includes('company admin') ? 'bg-blue-100 text-blue-700' : 'bg-red-100 text-red-700'} rounded mb-4`}>
                {error}
                {error.includes('company admin') && (
                  <p className="mt-2 text-sm">
                    <strong>Note:</strong> Only company administrators can register new HOA communities. If you are a resident, please use the <Link to="/register" className="underline">resident registration form</Link>.
                  </p>
                )}
              </div>
            )}

            <LoginForm
              onSubmit={handleLogin}
              isLoading={isLoading}
            />

            <div className="mt-6 text-center space-y-3">
              <p className="text-sm text-gray-600">
                <Link
                  to="/reset-password"
                  className="font-medium text-blue-600 hover:text-blue-500"
                >
                  Forgot your password?
                </Link>
              </p>
              <p className="text-sm text-gray-600">
                Don't have an account?{" "}
                <Link
                  to="/register"
                  className="font-medium text-blue-600 hover:text-blue-500"
                >
                  Register here
                </Link>
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Login;

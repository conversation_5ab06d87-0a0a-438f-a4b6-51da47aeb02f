import React, { useEffect, Suspense } from 'react';
import { Helmet } from 'react-helmet';
import { useNavigate, useLocation } from 'react-router-dom';
import BudgetPlanner from '@/components/BudgetPlanner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Calculator, FileText, BarChart3, ArrowLeft, Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { ErrorBoundary } from 'react-error-boundary';

// Error fallback component
const ErrorFallback = ({ error, resetErrorBoundary }: { error: Error, resetErrorBoundary: () => void }) => (
  <div className="p-6 border border-red-200 rounded-lg bg-red-50 text-center">
    <h3 className="text-lg font-medium text-red-800 mb-2">Something went wrong</h3>
    <p className="text-sm text-red-600 mb-4">{error.message}</p>
    <div className="flex gap-4 justify-center">
      <Button onClick={resetErrorBoundary}>Try again</Button>
      <Button variant="outline" onClick={() => window.location.href = '/finances'}>
        Back to Finances
      </Button>
    </div>
  </div>
);

// Loading fallback component
const LoadingFallback = () => (
  <div className="flex items-center justify-center h-64">
    <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
    <span className="ml-2">Loading budget planner...</span>
  </div>
);

const BudgetPlannerPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  // Handle any potential routing issues
  useEffect(() => {
    // If we have a hash or search params that might cause issues, clear them
    if (location.hash || location.search) {
      console.log('Clearing hash and search params from URL');
      navigate('/finances/budget', { replace: true });
    }
  }, [location, navigate]);

  // Show a toast when the component mounts to help with debugging
  useEffect(() => {
    console.log('BudgetPlannerPage mounted');
    toast({
      title: 'Budget Planner',
      description: 'Budget planner loaded successfully',
    });
  }, [toast]);

  return (
    <>
      <Helmet>
        <title>Budget Planner | Street Harmony</title>
      </Helmet>

      <div className="container mx-auto py-6">
        <div className="flex items-center mb-6">
          <Button
            variant="outline"
            size="sm"
            className="mr-4"
            onClick={() => navigate('/finances')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Finances
          </Button>
          <h1 className="text-2xl font-bold">Budget Management</h1>
        </div>

        <Tabs defaultValue="planner" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="planner" className="flex items-center gap-2">
              <Calculator className="h-4 w-4" />
              Budget Planner
            </TabsTrigger>
            <TabsTrigger value="reports" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Budget Reports
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Templates
            </TabsTrigger>
          </TabsList>

          <TabsContent value="planner">
            <ErrorBoundary FallbackComponent={ErrorFallback}>
              <Suspense fallback={<LoadingFallback />}>
                <BudgetPlanner />
              </Suspense>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="reports">
            <Card>
              <CardHeader>
                <CardTitle>Budget Reports</CardTitle>
                <CardDescription>
                  Generate and view reports based on your budget data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-500">
                    Budget reports will be available once you have created and activated a budget.
                  </p>

                  <div className="border rounded-md p-4 bg-gray-50">
                    <p className="text-center text-gray-600">No reports available yet</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates">
            <Card>
              <CardHeader>
                <CardTitle>Budget Templates</CardTitle>
                <CardDescription>
                  Use pre-defined templates to quickly create budgets
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-500">
                    Budget templates will help you create standardized budgets for your HOA.
                  </p>

                  <div className="border rounded-md p-4 bg-gray-50">
                    <p className="text-center text-gray-600">Templates coming soon</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default BudgetPlannerPage;

import React, { Suspense } from 'react';
import Dashboard from '@/components/Dashboard';
import { ErrorBoundary } from 'react-error-boundary';

const ErrorFallback = ({ error }: { error: Error }) => (
  <div className="p-4 text-red-500">
    <h2>Something went wrong:</h2>
    <pre>{error.message}</pre>
  </div>
);

const LoadingFallback = () => (
  <div className="p-4 text-center">Loading dashboard...</div>
);

const Index = () => {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Suspense fallback={<LoadingFallback />}>
        <Dashboard />
      </Suspense>
    </ErrorBoundary>
  );
};

export default Index;

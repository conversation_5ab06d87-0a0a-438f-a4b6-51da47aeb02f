import React, { useEffect, Suspense } from 'react';
import { He<PERSON><PERSON> } from 'react-helmet';
import CommunityManagement from '@/components/CommunityManagement';
import { Building2, Loader2 } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import { Button } from '@/components/ui/button';

// Error fallback component
const ErrorFallback = ({ error, resetErrorBoundary }: { error: Error, resetErrorBoundary: () => void }) => (
  <div className="p-6 border border-red-200 rounded-lg bg-red-50 text-center">
    <h3 className="text-lg font-medium text-red-800 mb-2">Something went wrong</h3>
    <p className="text-sm text-red-600 mb-4">{error.message}</p>
    <Button onClick={resetErrorBoundary}>Try again</Button>
  </div>
);

// Loading fallback component
const LoadingFallback = () => (
  <div className="flex items-center justify-center h-64">
    <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
    <span className="ml-2">Loading community management...</span>
  </div>
);

const CommunityManagementPage = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Handle any potential routing issues
  useEffect(() => {
    // If we have a hash or search params that might cause issues, clear them
    if (location.hash || location.search) {
      console.log('Clearing hash and search params from URL');
      navigate('/community-management', { replace: true });
    }
  }, [location, navigate]);

  // Log when the component mounts for debugging
  useEffect(() => {
    console.log('CommunityManagementPage mounted');
  }, []);

  return (
    <>
      <Helmet>
        <title>Community Management | Street Harmony</title>
      </Helmet>
      <div className="container mx-auto py-8">
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <Suspense fallback={<LoadingFallback />}>
            <CommunityManagement />
          </Suspense>
        </ErrorBoundary>
      </div>
    </>
  );
};

export default CommunityManagementPage;

import React, { useEffect, useState } from 'react';
import api from '@/lib/axios';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { PageHeader } from '@/components/PageHeader';
import { Building2, CheckCircle, XCircle, ExternalLink } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { format } from 'date-fns';

interface HOA {
  _id: string;
  hoaCommunityName: string;
  hoaCommunityCode: string;
  hoaStreetAddress: string;
  hoaCity: string;
  hoaState: string;
  hoaZipCode: string;
  contactEmail: string;
  contactPhone: string;
  einNumber: string;
  verificationStatus: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  subscription: {
    tier: string;
    unitCount: number;
    status: string;
  };
  verificationDocuments?: {
    registrationDoc?: {
      filename: string;
      originalName: string;
      documentId?: string;
    };
    einDoc?: {
      filename: string;
      originalName: string;
      documentId?: string;
    };
  };
}

const HOAApprovals = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [pendingHOAs, setPendingHOAs] = useState<HOA[]>([]);
  const [approvedHOAs, setApprovedHOAs] = useState<HOA[]>([]);
  const [rejectedHOAs, setRejectedHOAs] = useState<HOA[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('pending');
  const [selectedHOA, setSelectedHOA] = useState<HOA | null>(null);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [viewDocumentUrl, setViewDocumentUrl] = useState<string | null>(null);

  // Check if user is a company admin
  useEffect(() => {
    if (user && user.role !== 'company_admin') {
      toast({
        title: 'Access Denied',
        description: 'Only company administrators can access this page.',
        variant: 'destructive',
      });
      navigate('/dashboard');
    }
  }, [user, navigate, toast]);

  // Fetch HOAs
  const fetchHOAs = async () => {
    setIsLoading(true);
    try {
      // Fetch pending HOAs
      const pendingRes = await api.get('/api/admin/hoa/pending');
      setPendingHOAs(pendingRes.data.data || []);

      // Fetch approved HOAs
      const approvedRes = await api.get('/api/hoa?status=approved');
      setApprovedHOAs(approvedRes.data.data || []);

      // Fetch rejected HOAs
      const rejectedRes = await api.get('/api/hoa?status=rejected');
      setRejectedHOAs(rejectedRes.data.data || []);
    } catch (error) {
      console.error('Error fetching HOAs:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch HOA registrations.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchHOAs();
  }, []);

  // Handle HOA approval
  const handleApproveHOA = async () => {
    if (!selectedHOA) return;

    setIsProcessing(true);
    try {
      const response = await api.put(`/api/hoa/${selectedHOA._id}/verify`, {
        action: 'approve'
      });

      toast({
        title: 'Success',
        description: `${selectedHOA.hoaCommunityName} has been approved. A payment link has been sent to ${selectedHOA.contactEmail}.`,
      });

      // Refresh HOA list
      fetchHOAs();
    } catch (error) {
      console.error('Error approving HOA:', error);
      toast({
        title: 'Error',
        description: 'Failed to approve HOA. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
      setIsApproveDialogOpen(false);
    }
  };

  // Handle HOA rejection
  const handleRejectHOA = async () => {
    if (!selectedHOA) return;

    setIsProcessing(true);
    try {
      const response = await api.put(`/api/hoa/${selectedHOA._id}/verify`, {
        action: 'reject'
      });

      toast({
        title: 'Success',
        description: `${selectedHOA.hoaCommunityName} has been rejected.`,
      });

      // Refresh HOA list
      fetchHOAs();
    } catch (error) {
      console.error('Error rejecting HOA:', error);
      toast({
        title: 'Error',
        description: 'Failed to reject HOA. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
      setIsRejectDialogOpen(false);
    }
  };

  // View document
  const viewDocument = async (hoaId: string, documentId: string) => {
    try {
      const url = `${import.meta.env.VITE_API_URL}/api/documents/${documentId}/view`;
      setViewDocumentUrl(url);
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error viewing document:', error);
      toast({
        title: 'Error',
        description: 'Failed to view document. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container mx-auto py-6">
      <PageHeader
        heading="HOA Approvals"
        subheading="Review and approve HOA registration requests"
        icon={<Building2 className="h-6 w-6" />}
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="pending">
            Pending ({pendingHOAs.length})
          </TabsTrigger>
          <TabsTrigger value="approved">
            Approved ({approvedHOAs.length})
          </TabsTrigger>
          <TabsTrigger value="rejected">
            Rejected ({rejectedHOAs.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="mt-6">
          {isLoading ? (
            <div className="flex justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : pendingHOAs.length === 0 ? (
            <Alert>
              <AlertTitle>No pending HOA registrations</AlertTitle>
              <AlertDescription>
                There are currently no HOA registrations waiting for approval.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid gap-4 max-h-[calc(100vh-220px)] overflow-y-auto pr-2">
              {pendingHOAs.map((hoa) => (
                <Card key={hoa._id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{hoa.hoaCommunityName}</CardTitle>
                        <CardDescription>
                          Code: {hoa.hoaCommunityCode} | Registered: {format(new Date(hoa.createdAt), 'PPP')}
                        </CardDescription>
                      </div>
                      <Badge variant="outline">{hoa.subscription.tier} tier</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Contact Information</h3>
                        <p className="mt-1">{hoa.contactEmail}</p>
                        <p>{hoa.contactPhone}</p>
                        <p className="mt-2">EIN: {hoa.einNumber}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Address</h3>
                        <p className="mt-1">{hoa.hoaStreetAddress}</p>
                        <p>{hoa.hoaCity}, {hoa.hoaState} {hoa.hoaZipCode}</p>
                      </div>
                    </div>

                    {hoa.verificationDocuments && (
                      <div className="mt-4">
                        <h3 className="text-sm font-medium text-gray-500 mb-2">Documents</h3>
                        <div className="flex flex-wrap gap-2">
                          {hoa.verificationDocuments.registrationDoc && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => hoa.verificationDocuments?.registrationDoc?.documentId &&
                                viewDocument(hoa._id, hoa.verificationDocuments.registrationDoc.documentId)}
                              className="flex items-center gap-1"
                            >
                              <ExternalLink className="h-4 w-4" />
                              Registration Document
                            </Button>
                          )}
                          {hoa.verificationDocuments.einDoc && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => hoa.verificationDocuments?.einDoc?.documentId &&
                                viewDocument(hoa._id, hoa.verificationDocuments.einDoc.documentId)}
                              className="flex items-center gap-1"
                            >
                              <ExternalLink className="h-4 w-4" />
                              EIN Document
                            </Button>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                  <CardFooter className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSelectedHOA(hoa);
                        setIsRejectDialogOpen(true);
                      }}
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject
                    </Button>
                    <Button
                      onClick={() => {
                        setSelectedHOA(hoa);
                        setIsApproveDialogOpen(true);
                      }}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="approved" className="mt-6">
          {isLoading ? (
            <div className="flex justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : approvedHOAs.length === 0 ? (
            <Alert>
              <AlertTitle>No approved HOAs</AlertTitle>
              <AlertDescription>
                There are no approved HOA registrations yet.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid gap-4 max-h-[calc(100vh-220px)] overflow-y-auto pr-2">
              {approvedHOAs.map((hoa) => (
                <Card key={hoa._id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{hoa.hoaCommunityName}</CardTitle>
                        <CardDescription>
                          Code: {hoa.hoaCommunityCode} | Subscription: {hoa.subscription.status}
                        </CardDescription>
                      </div>
                      <Badge variant="outline" className="bg-green-50">{hoa.subscription.tier} tier</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Contact Information</h3>
                        <p className="mt-1">{hoa.contactEmail}</p>
                        <p>{hoa.contactPhone}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Address</h3>
                        <p className="mt-1">{hoa.hoaStreetAddress}</p>
                        <p>{hoa.hoaCity}, {hoa.hoaState} {hoa.hoaZipCode}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="rejected" className="mt-6">
          {isLoading ? (
            <div className="flex justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : rejectedHOAs.length === 0 ? (
            <Alert>
              <AlertTitle>No rejected HOAs</AlertTitle>
              <AlertDescription>
                There are no rejected HOA registrations.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid gap-4 max-h-[calc(100vh-220px)] overflow-y-auto pr-2">
              {rejectedHOAs.map((hoa) => (
                <Card key={hoa._id}>
                  <CardHeader>
                    <CardTitle>{hoa.hoaCommunityName}</CardTitle>
                    <CardDescription>
                      Code: {hoa.hoaCommunityCode} | Registered: {format(new Date(hoa.createdAt), 'PPP')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Contact Information</h3>
                        <p className="mt-1">{hoa.contactEmail}</p>
                        <p>{hoa.contactPhone}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Address</h3>
                        <p className="mt-1">{hoa.hoaStreetAddress}</p>
                        <p>{hoa.hoaCity}, {hoa.hoaState} {hoa.hoaZipCode}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Approve Dialog */}
      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve HOA Registration</DialogTitle>
            <DialogDescription>
              Are you sure you want to approve {selectedHOA?.hoaCommunityName}? This will send a payment link to {selectedHOA?.contactEmail}.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApproveDialogOpen(false)} disabled={isProcessing}>
              Cancel
            </Button>
            <Button onClick={handleApproveHOA} disabled={isProcessing}>
              {isProcessing ? 'Processing...' : 'Approve HOA'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject HOA Registration</DialogTitle>
            <DialogDescription>
              Are you sure you want to reject {selectedHOA?.hoaCommunityName}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)} disabled={isProcessing}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleRejectHOA} disabled={isProcessing}>
              {isProcessing ? 'Processing...' : 'Reject HOA'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default HOAApprovals;

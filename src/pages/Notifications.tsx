import React, { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Bell,
  Calendar,
  AlertTriangle,
  DollarSign,
  CheckCircle,
  Wrench,
  X,
  Trash2
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from '@/components/ui/use-toast';
import api from '@/lib/axios';

interface Notification {
  _id: string;
  type: 'payment' | 'task' | 'event' | 'alert' | 'info';
  title: string;
  description: string;
  time: string;
  read: boolean;
}

const NotificationIcon = ({ type }: { type: string }) => {
  switch (type.toLowerCase()) {
    case 'payment':
      return <DollarSign className="h-5 w-5 text-green-500" />;
    case 'task':
      return <Wrench className="h-5 w-5 text-blue-500" />;
    case 'event':
      return <Calendar className="h-5 w-5 text-purple-500" />;
    case 'alert':
      return <AlertTriangle className="h-5 w-5 text-red-500" />;
    case 'info':
      return <CheckCircle className="h-5 w-5 text-teal-500" />;
    default:
      return <Bell className="h-5 w-5 text-gray-500" />;
  }
};

const Notifications = () => {
  const queryClient = useQueryClient();
  const [isMarkingRead, setIsMarkingRead] = useState(false);

  const { data: notifications = [], isLoading, isError } = useQuery<Notification[]>({
    queryKey: ['notifications'],
    queryFn: async () => {
      const res = await api.get('/api/notifications');
      return res.data;
    },
    refetchInterval: 30000 // Refetch every 30 seconds
  });

  const handleMarkAsRead = async (notificationId: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    try {
      setIsMarkingRead(true);
      await api.post(`/api/notifications/${notificationId}/read`);

      // Update the cache to mark this notification as read
      queryClient.setQueryData(['notifications'], (oldData: Notification[] | undefined) => {
        if (!oldData) return [];
        return oldData.map(notification => {
          if (notification._id === notificationId) {
            return { ...notification, read: true };
          }
          return notification;
        });
      });

      toast({
        title: "Notification dismissed",
        description: "The notification has been marked as read.",
        duration: 3000
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast({
        title: "Error",
        description: "Failed to dismiss notification. Please try again.",
        variant: "destructive",
        duration: 3000
      });
    } finally {
      setIsMarkingRead(false);
    }
  };

  // Function to mark all notifications as read
  const markAllAsRead = async () => {
    try {
      setIsMarkingRead(true);
      const unreadNotifications = notifications.filter(n => !n.read);

      if (unreadNotifications.length === 0) {
        toast({
          title: "No unread notifications",
          description: "You don't have any unread notifications.",
          duration: 3000
        });
        return;
      }

      // Mark each unread notification as read
      await Promise.all(
        unreadNotifications.map(notification =>
          api.post(`/api/notifications/${notification._id}/read`)
        )
      );

      // Update the cache to mark all notifications as read
      queryClient.setQueryData(['notifications'], (oldData: Notification[] | undefined) => {
        if (!oldData) return [];
        return oldData.map(notification => ({ ...notification, read: true }));
      });

      toast({
        title: "All notifications dismissed",
        description: `${unreadNotifications.length} notifications have been marked as read.`,
        duration: 3000
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast({
        title: "Error",
        description: "Failed to dismiss all notifications. Please try again.",
        variant: "destructive",
        duration: 3000
      });
    } finally {
      setIsMarkingRead(false);
    }
  };

  if (isLoading) return <div className="flex justify-center p-8">Loading notifications...</div>;
  if (isError) return <div className="flex justify-center p-8 text-red-500">Error loading notifications</div>;

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-2xl font-bold">Notifications</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={markAllAsRead}
            disabled={isMarkingRead || notifications.filter(n => !n.read).length === 0}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Dismiss All
          </Button>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[500px] pr-3">
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div
                  key={notification._id}
                  className={`flex items-start p-4 rounded-lg border transition-colors relative group ${
                    notification.read ? 'bg-background' : 'bg-muted'
                  }`}
                >
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                    <NotificationIcon type={notification.type} />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <h4 className={`font-medium ${!notification.read && 'font-semibold'}`}>
                        {notification.title}
                      </h4>
                      {!notification.read && (
                        <Badge variant="secondary" className="bg-primary text-primary-foreground">
                          New
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {notification.description}
                    </p>
                    <div className="flex items-center mt-2 text-xs text-muted-foreground">
                      {notification.time}
                    </div>
                  </div>

                  {/* Dismiss button */}
                  {!notification.read && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => handleMarkAsRead(notification._id, e)}
                      disabled={isMarkingRead}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}

              {notifications.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No notifications found
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};

export default Notifications;

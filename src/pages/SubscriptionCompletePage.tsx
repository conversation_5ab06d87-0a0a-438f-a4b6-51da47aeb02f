import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import api from '@/lib/axios';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Home } from 'lucide-react';

const SubscriptionCompletePage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    const verifySession = async () => {
      if (!sessionId) {
        setIsLoading(false);
        return;
      }

      try {
        await api.get(`/api/subscriptions/retrieve-checkout/${sessionId}`);
        setIsLoading(false);
      } catch (err) {
        console.error('Error verifying session:', err);
        setIsLoading(false);
      }
    };

    verifySession();
  }, [sessionId]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 bg-green-100 p-3 rounded-full w-16 h-16 flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl">Your HOA Subscription Fully Activated!</CardTitle>
          <CardDescription>
            Your HOA subscription has been successfully activated.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-muted p-4 rounded-md">
            <h3 className="font-medium mb-2">What's Next?</h3>
            <ul className="space-y-2 text-sm">
              <li>• You can now access all features of your subscription tier</li>
              <li>• Your payment method will be charged after the trial period</li>
              <li>• You can cancel anytime during the trial period</li>
              <li>• You can upgrade or downgrade your subscription at any time</li>
              <li>• You can request the member of your HOA register as a new member, and pay for their HOA fee through the HOA portal</li>
            </ul>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button 
            size="lg"
            onClick={() => navigate('/login')}
            className="w-full max-w-xs"
          >
            <Home className="mr-2 h-4 w-4" />
            Go to Login
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SubscriptionCompletePage;

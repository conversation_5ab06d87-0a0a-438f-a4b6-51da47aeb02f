import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import api from '@/lib/axios';
import { loadStripe } from '@stripe/stripe-js';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, CheckCircle, AlertCircle, CreditCard } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface HOA {
  _id: string;
  hoaCommunityName: string;
  hoaCommunityCode: string;
  subscription: {
    tier: string;
    unitCount: number;
    status: string;
  };
}

const SubscriptionActivatePage = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hoa, setHoa] = useState<HOA | null>(null);
  const [tokenValid, setTokenValid] = useState(false);

  // Verify the token and get HOA details
  useEffect(() => {
    const verifyToken = async () => {
      try {
        setIsLoading(true);
        const response = await api.get(`/api/subscriptions/verify-token/${token}`);
        setHoa(response.data.hoa);
        setTokenValid(true);
      } catch (err: any) {
        console.error('Error verifying token:', err);
        setError(err.response?.data?.message || 'Invalid or expired activation link');
        setTokenValid(false);
      } finally {
        setIsLoading(false);
      }
    };

    if (token) {
      verifyToken();
    } else {
      setError('No activation token provided');
      setIsLoading(false);
    }
  }, [token]);

  // Handle subscription activation
  const handleActivateSubscription = async () => {
    if (!hoa) return;

    try {
      setIsProcessing(true);
      
      // Get the Stripe publishable key from environment variables
      const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
      if (!stripePublishableKey) {
        throw new Error('Stripe publishable key not found');
      }
      
      // Initialize Stripe
      const stripe = await loadStripe(stripePublishableKey);
      if (!stripe) {
        throw new Error('Failed to initialize Stripe');
      }
      
      // Create checkout session
      const response = await api.post('/api/subscriptions/create-checkout', {
        hoaId: hoa._id,
        token: token
      });
      
      const { sessionId } = response.data;
      
      // Redirect to Stripe Checkout
      const result = await stripe.redirectToCheckout({
        sessionId
      });
      
      if (result.error) {
        throw new Error(result.error.message);
      }
    } catch (err: any) {
      console.error('Error activating subscription:', err);
      setError(err.message || 'Failed to activate subscription');
      toast({
        title: 'Error',
        description: err.message || 'Failed to activate subscription',
        variant: 'destructive',
      });
      setIsProcessing(false);
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Verifying Activation Link</CardTitle>
            <CardDescription>Please wait while we verify your activation link</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render error state
  if (error || !tokenValid) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <CardTitle>Activation Link Invalid</CardTitle>
            <CardDescription>
              {error || 'The activation link is invalid or has expired'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                {error || 'The activation link is invalid or has expired. Please contact support for assistance.'}
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={() => navigate('/')}>
              Return to Home
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Render subscription activation page
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Activate Your HOA Subscription</CardTitle>
          <CardDescription>
            Complete your registration for {hoa?.hoaCommunityName}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-muted p-4 rounded-md">
            <h3 className="font-medium mb-2">Subscription Details</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-muted-foreground">HOA Name:</div>
              <div className="font-medium">{hoa?.hoaCommunityName}</div>
              
              <div className="text-muted-foreground">Community Code:</div>
              <div className="font-medium">{hoa?.hoaCommunityCode}</div>
              
              <div className="text-muted-foreground">Subscription Tier:</div>
              <div className="font-medium capitalize">{hoa?.subscription.tier}</div>
            </div>
          </div>
          
          <Alert className="bg-blue-50 border-blue-200">
            <CheckCircle className="h-4 w-4 text-blue-600" />
            <AlertTitle className="text-blue-800">30-Day Free Trial</AlertTitle>
            <AlertDescription className="text-blue-700">
              Your subscription includes a 30-day free trial. You won't be charged until the trial period ends.
            </AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button 
            size="lg"
            onClick={handleActivateSubscription}
            disabled={isProcessing}
            className="w-full max-w-xs"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Activate with Free Trial
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SubscriptionActivatePage;

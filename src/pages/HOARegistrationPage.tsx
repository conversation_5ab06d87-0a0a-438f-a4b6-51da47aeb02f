import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { useNavigate, Link } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import HOARegistration from '@/components/HOARegistration';
import { Building2, ShieldAlert } from 'lucide-react';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

const HOARegistrationPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Check if user is a company admin
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isCompanyAdmin = user?.role === 'company_admin';

  useEffect(() => {
    // If not a company admin, redirect to dashboard with a message
    if (!isCompanyAdmin) {
      toast({
        title: "Access Denied",
        description: "Only company administrators can register new HOAs.",
        variant: "destructive"
      });
      navigate('/dashboard');
    }
  }, [isCompanyAdmin, navigate, toast]);

  if (!isCompanyAdmin) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            Only company administrators can register new HOAs. Redirecting to dashboard...
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>HOA Registration | Street Harmony</title>
      </Helmet>
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Building2 className="h-8 w-8" />
              HOA Registration
            </h1>
            <p className="text-gray-600 mt-2">
              Register a new HOA community in the system. Only company administrators can perform this action.
            </p>
            <div className="mt-4 text-sm text-blue-600">
              <p>Are you a resident looking to join an existing HOA? <Link to="/register" className="underline font-medium">Register as a resident here</Link></p>
            </div>
          </div>
        </div>
        <HOARegistration />
      </div>
    </>
  );
};

export default HOARegistrationPage;

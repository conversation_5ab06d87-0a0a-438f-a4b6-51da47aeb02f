import React from 'react';
import MemberDetails from '@/components/MemberDetails';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { Users } from 'lucide-react';

const Members = () => {
  const navigate = useNavigate();
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isOperationalAdmin = user?.role === 'admin';
  const isCompanyAdmin = user?.role === 'company_admin';
  const isRegularUser = user?.role === 'member';

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">
          {isRegularUser ? 'Neighbors' : 'Members'}
        </h1>
        {isOperationalAdmin && (
          <div className="flex gap-3">
            <Button variant="outline" onClick={() => navigate('/admin/approvals')}>
              Pending Approvals
            </Button>
            <Button onClick={() => navigate('/members/invite')}>
              Invite Member
            </Button>
          </div>
        )}
      </div>

      {isRegularUser && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <Users className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Your Street Community
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>You can view other residents who live on your street within your HOA community.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <MemberDetails />
    </div>
  );
};

export default Members;

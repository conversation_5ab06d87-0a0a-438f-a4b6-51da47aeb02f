// src/pages/Tasks.tsx
import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useQuery } from '@tanstack/react-query';
import AddTaskForm from '@/components/forms/AddTaskForm';
import TaskList from '@/components/TaskList';
import api from '@/lib/axios';

const Tasks = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isCompanyAdmin = user?.role === 'company_admin';
  const isAdmin = user?.role === 'admin';
  const location = useLocation();
  const [selectedHoaId, setSelectedHoaId] = useState<string | null>(null);

  // Multiple community support (same as Dashboard and Finances)
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });
  const [selectedCommunityIds, setSelectedCommunityIds] = useState<string[]>(() => {
    const stored = localStorage.getItem('selectedCommunityIds');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);
  const [isMultipleCommunities, setIsMultipleCommunities] = useState<boolean>(false);

  // Extract query parameters from URL (fallback for direct navigation)
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const hoaId = searchParams.get('hoaId');
    const communityId = searchParams.get('communityId');

    if (hoaId) setSelectedHoaId(hoaId);
    if (communityId) setSelectedCommunityId(communityId);
  }, [location.search]);

  // Listen for community selection changes from Sidebar and determine hoaId
  useEffect(() => {
    // Initialize from localStorage
    const storedCommunityId = localStorage.getItem('selectedCommunityId');
    const storedCommunityIds = localStorage.getItem('selectedCommunityIds');

    if (storedCommunityIds) {
      const ids = JSON.parse(storedCommunityIds);
      setSelectedCommunityIds(ids);
      setIsMultipleCommunities(true);
      setSelectedCommunityName(`${ids.length} Communities Selected`);
    } else if (storedCommunityId) {
      setSelectedCommunityId(storedCommunityId);
      setIsMultipleCommunities(false);
    }

    const handleCommunityChange = async (event: CustomEvent) => {
      const { communityId, communityIds } = event.detail;
      console.log('Tasks: Received community selection change:', { communityId, communityIds });

      // Handle both single and multiple community selection
      if (communityIds && Array.isArray(communityIds)) {
        // Multiple communities selected
        setSelectedCommunityIds(communityIds);
        setSelectedCommunityId(null);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`${communityIds.length} Communities Selected`);



        console.log('Tasks: Multiple communities selected:', communityIds);
      } else if (communityId && communityId !== 'all') {
        // Single community selected
        setSelectedCommunityId(communityId);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);

        // Fetch community details for display name only
        try {
          const response = await api.get(`/api/communities/${communityId}`);
          const community = response.data;
          if (community) {
            setSelectedCommunityName(`${community.name} (${community.streetAddress || 'No address'})`);
          }
        } catch (error) {
          console.error('Error fetching community details:', error);
          setSelectedCommunityName('Selected Community');
        }
      } else {
        // "All Communities" or no selection
        setSelectedCommunityId(null);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);
        setSelectedCommunityName(null);
      }
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, [isCompanyAdmin, user?.hoaId]);

  // Fetch HOAs for company admin dropdown
  const { data: hoas = [] } = useQuery({
    queryKey: ['hoas'],
    queryFn: async () => {
      try {
        const response = await api.get('/api/hoa');
        return response.data.data || [];
      } catch (error) {
        console.error('Error fetching HOAs:', error);
        return [];
      }
    },
    enabled: isCompanyAdmin
  });

  return (
    <div className="space-y-6">
      {/* Header with community selection status */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold">Tasks</h1>
          {/* Show community selection status */}
          {selectedCommunityName && (
            <p className="text-sm text-muted-foreground mt-1">
              Viewing: {selectedCommunityName}
            </p>
          )}
          {isMultipleCommunities && selectedCommunityIds.length > 0 && (
            <p className="text-xs text-blue-600 mt-1">
              Tasks aggregated from {selectedCommunityIds.length} communities
            </p>
          )}
        </div>
      </div>

      {/* Company Admin HOA Selector */}
      {isCompanyAdmin && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-green-900">Task Oversight</h3>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-green-800 mb-2">
                Select HOA to Monitor
              </label>
              <Select value={selectedHoaId || 'all'} onValueChange={(value) => {
                const url = new URL(window.location.href);
                if (value && value !== 'all') {
                  url.searchParams.set('hoaId', value);
                  setSelectedHoaId(value);
                } else {
                  url.searchParams.delete('hoaId');
                  setSelectedHoaId(null);
                }
                window.history.pushState({}, '', url.toString());
              }}>
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="All HOAs" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All HOAs</SelectItem>
                  {hoas.map((hoa: any) => (
                    <SelectItem key={hoa._id} value={hoa._id}>
                      {hoa.hoaCommunityName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {selectedHoaId && (
              <div className="flex-1">
                <label className="block text-sm font-medium text-green-800 mb-2">
                  Currently Viewing
                </label>
                <div className="p-2 bg-white border rounded text-sm">
                  {hoas.find((hoa: any) => hoa._id === selectedHoaId)?.hoaCommunityName || 'Selected HOA'}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {!isCompanyAdmin && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Task</CardTitle>
            <CardDescription>Add a new task for the community</CardDescription>
          </CardHeader>
          <CardContent>
            <AddTaskForm />
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Active Tasks</CardTitle>
          <CardDescription>
            {isCompanyAdmin
              ? "Monitor tasks across all HOAs"
              : "View and manage community tasks"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TaskList
            communityId={isMultipleCommunities ? null : selectedCommunityId}
            communityIds={isMultipleCommunities ? selectedCommunityIds : undefined}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default Tasks;

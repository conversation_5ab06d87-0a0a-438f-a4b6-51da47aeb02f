import React, { useEffect, useState } from 'react';
import {  useSearchParams } from 'react-router-dom';
import api from '@/lib/axios';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Home } from 'lucide-react';

const SubscriptionRefreshPage = () => {
  const [params] = useSearchParams();
  const [link, setLink] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const accountId = params.get('acctId');

  useEffect(() => {
    const fetchNewLink = async () => {
      try {
        if (!accountId) {
          setError('Missing Stripe account ID.');
          setLoading(false);
          return;
        }
        const res = await api.post(`/api/stripe-payment/refresh`, { accountId });
        setLink(res.data.url);
      } catch (err) {
        console.error('Error refresh stripe session:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchNewLink();
  }, [accountId]);

  if (loading) return <p>Generating a new Stripe onboarding link...</p>;
  if (error) return <p>{error}</p>;

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 bg-green-100 p-3 rounded-full w-16 h-16 flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl">Stripe Setup Link</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-muted p-4 rounded-md">
            <p className="font-medium mb-2">This is the Stripe onboarding link for your HOA. Click the button below to open it in a new tab.</p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button 
            size="lg"
            onClick={() => window.open(link, '_blank')}
            className="w-full max-w-xs"
          >
            <Home className="mr-2 h-4 w-4" />
            Go to Stripe Onboarding
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SubscriptionRefreshPage;

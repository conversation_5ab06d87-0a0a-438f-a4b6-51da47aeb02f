import { PageHeader } from '@/components/PageHeader';
import AdminManagement from '@/components/AdminManagement';
import { Shield } from 'lucide-react';
import { useEffect } from 'react';

const AdminManagementPage = () => {
  // Set document title using useEffect instead of Helmet
  useEffect(() => {
    document.title = "Admin Management | Street Harmony";
  }, []);

  return (
    <div className="flex flex-col gap-4 md:gap-8">
      <PageHeader
        heading="Admin Management"
        subheading="Manage admin users for your HOA communities"
        icon={<Shield className="h-6 w-6" />}
      />
      <AdminManagement />
    </div>
  );
};

export default AdminManagementPage;

import React from 'react';
import { Helmet } from 'react-helmet';
import FinancialReports from '@/components/FinancialReports';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart3, Pie<PERSON><PERSON>, LineChart, FileText } from 'lucide-react';

const FinancialReportsPage = () => {
  return (
    <>
      <Helmet>
        <title>Financial Reports | Street Harmony</title>
      </Helmet>
      
      <div className="container mx-auto py-6">
        <Tabs defaultValue="reports" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="reports" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Reports
            </TabsTrigger>
            <TabsTrigger value="statements" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Statements
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="reports">
            <FinancialReports />
          </TabsContent>
          
          <TabsContent value="statements">
            <Card>
              <CardHeader>
                <CardTitle>Financial Statements</CardTitle>
                <CardDescription>
                  View and download official financial statements for your HOA
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-500">
                    Financial statements will be generated at the end of each quarter and fiscal year.
                    Check back later for available statements.
                  </p>
                  
                  <div className="border rounded-md p-4 bg-gray-50">
                    <p className="text-center text-gray-600">No statements available yet</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default FinancialReportsPage;

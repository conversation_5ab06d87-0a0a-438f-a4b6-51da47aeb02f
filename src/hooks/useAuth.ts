import { useState, useEffect } from 'react';

interface User {
  id: string;
  username: string;
  role: string;
  token: string;
}

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      const parsedUser = JSON.parse(storedUser);
      setUser(parsedUser);
      setToken(parsedUser.token);
    }
  }, []);

  const login = (userData: User) => {
    localStorage.setItem('user', JSON.stringify(userData));
    setUser(userData);
    setToken(userData.token);
  };

  const logout = () => {
    localStorage.removeItem('user');
    setUser(null);
    setToken(null);
  };

  return {
    user,
    token,
    login,
    logout,
    isAuthenticated: !!token
  };
}; 
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Custom badge styles for HOA status and tier badges */
  .hoa-status-badge {
    @apply inline-flex items-center justify-center rounded-md px-2 py-0.5 text-xs font-medium;
    margin-right: 0.5rem;
  }

  .hoa-tier-badge {
    @apply inline-flex items-center justify-center rounded-md px-2 py-0.5 text-xs font-medium;
  }
}

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    --primary: 199 89% 48%;
    --primary-foreground: 210 40% 98%;

    --secondary: 187 72% 53%;
    --secondary-foreground: 215 25% 27%;

    --muted: 210 20% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 199 84% 55%;
    --accent-foreground: 215 25% 27%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 199 89% 48%;

    --radius: 0.5rem;

    --sidebar-background: 217 33% 17%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 199 89% 48%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 217 33% 20%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 217 33% 25%;
    --sidebar-ring: 199 89% 48%;
  }

  .dark {
    --background: 217 33% 17%;
    --foreground: 210 20% 98%;

    --card: 217 33% 20%;
    --card-foreground: 210 20% 98%;

    --popover: 217 33% 20%;
    --popover-foreground: 210 20% 98%;

    --primary: 199 89% 48%;
    --primary-foreground: 210 40% 98%;

    --secondary: 187 72% 53%;
    --secondary-foreground: 210 20% 98%;

    --muted: 217 33% 25%;
    --muted-foreground: 210 20% 70%;

    --accent: 199 84% 55%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 62% 50%;
    --destructive-foreground: 210 20% 98%;

    --border: 217 33% 25%;
    --input: 217 33% 25%;
    --ring: 199 89% 48%;
  }
}

@layer base {
  * {
    @apply border-[hsl(var(--border))] border-solid;
  }
  html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }
  h1 {
    @apply text-3xl md:text-4xl;
  }
  h2 {
    @apply text-2xl md:text-3xl;
  }
  h3 {
    @apply text-xl md:text-2xl;
  }
  h4 {
    @apply text-lg md:text-xl;
  }
}

@layer components {
  .animate-in {
    animation: fade-in 0.3s ease-out;
  }
  .card-hover {
    @apply transition-all duration-200 hover:shadow-md hover:-translate-y-1;
  }

  /* Clean sidebar layout - no conflicting classes */
  .sidebar-clean {
    @apply h-screen flex flex-col;
  }

  /* Logout button positioning */
  .sidebar-logout-bottom {
    @apply mt-auto shrink-0;
  }
}

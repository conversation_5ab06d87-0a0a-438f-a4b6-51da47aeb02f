import api from './axios';

export interface Notification {
  id: string;
  type: string;
  title: string;
  description: string;
  time: string;
  read: boolean;
  icon?: string;
  metadata?: any;
}

export async function getNotifications(): Promise<Notification[]> {
  const response = await api.get('/api/notifications');
  return response.data.map((notification: any) => ({
    id: notification._id,
    type: notification.type,
    title: notification.title,
    description: notification.description,
    time: notification.createdAt,
    read: notification.read,
    icon: notification.icon,
    metadata: notification.metadata
  }));
}

export async function markNotificationAsRead(notificationId: string): Promise<void> {
  await api.post(`/api/notifications/${notificationId}/read`);
}
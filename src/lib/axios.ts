import axios from 'axios';

// Log environment variables for debugging
console.log('Environment variables:', {
  VITE_API_URL: import.meta.env.VITE_API_URL,
  VITE_SOCKET_URL: import.meta.env.VITE_SOCKET_URL
});

// Determine if we're in production or development
const isProd = import.meta.env.PROD;

// Always use the full API URL from environment variables
// This ensures we're always hitting the correct backend URL
const baseURL = import.meta.env.VITE_API_URL;

// Log the API URL being used
console.log('Using API URL:', baseURL);
console.log('Using Socket URL:', import.meta.env.VITE_SOCKET_URL);

console.log('Environment:', isProd ? 'Production' : 'Development');
console.log('Base URL:', baseURL);

// Make these variables available globally for debugging
if (typeof window !== 'undefined') {
  window.__ENV__ = {
    API_URL: baseURL,
    SOCKET_URL: import.meta.env.VITE_SOCKET_URL,
    IS_PROD: isProd
  };
}

// Create axios instance with environment variables
const api = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 15000, // 15 seconds timeout
  withCredentials: false // Don't include credentials in cross-origin requests (causes CORS issues)
});

// Log the actual baseURL being used
console.log('Axios baseURL:', api.defaults.baseURL);

// List of public routes that don't need authentication
const publicRoutes = [
  '/api/auth/login',
  '/api/auth/register',
  '/api/password/reset',
  '/api/password/request-reset'
];

/**
 * Get user data from local storage
 * @returns User data object or null
 */
const getUserFromLocalStorage = () => {
  try {
    const userData = localStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error parsing user data from localStorage:', error);
    return null;
  }
};

// Store CSRF token
let csrfToken: string | null = null;

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const isPublicRoute = publicRoutes.some(route => config.url?.includes(route));

    if (!isPublicRoute) {
      const user = getUserFromLocalStorage();

      if (user?.token) {
        // Add detailed logging for token debugging
        console.log('Adding token to request:', {
          tokenLength: user.token.length,
          tokenPrefix: user.token.substring(0, 10) + '...',
          url: config.url
        });

        // Ensure proper Bearer token format
        config.headers.Authorization = `Bearer ${user.token}`;

        // Add CSRF token if available (for mutating requests)
        const method = config.method?.toUpperCase();
        if (csrfToken && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method || '')) {
          config.headers['X-CSRF-Token'] = csrfToken;
        }
      } else {
        console.warn('No token found for authenticated route:', config.url);
      }
    }

    // For FormData, let the browser set the Content-Type
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    // Log requests in development mode only
    if (import.meta.env.DEV) {
      console.log('API Request:', {
        url: config.url,
        method: config.method,
        isPublicRoute,
        hasAuthHeader: !!config.headers.Authorization
      });
    }

    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Capture CSRF token from response headers
    const newCsrfToken = response.headers['x-csrf-token'];
    if (newCsrfToken) {
      csrfToken = newCsrfToken;
      console.log('CSRF token updated');
    }
    return response;
  },
  (error) => {
    // Create a standardized error object
    const errorInfo = {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      data: error.response?.data
    };

    console.error('API Error:', errorInfo);

    // Handle authentication errors
    if (error.response?.status === 401) {
      // Log more details about the 401 error
      console.error('Authentication error details:', {
        url: error.config?.url,
        headers: error.config?.headers,
        message: error.response?.data?.message,
        hasToken: !!getUserFromLocalStorage()?.token
      });

      // Only clear user data and redirect for token expiration
      if (error.response?.data?.message === 'Token has expired' ||
          error.response?.data?.message === 'Token verification failed') {
        console.log('Token expired or invalid, logging out user');
        localStorage.removeItem('user');
        window.location.href = '/login?expired=true';
        return Promise.reject({
          message: 'Session expired. Please login again.'
        });
      }
    }

    return Promise.reject(error.response?.data || error);
  }
);

export default api;
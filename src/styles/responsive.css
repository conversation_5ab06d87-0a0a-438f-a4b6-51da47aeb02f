/* HOAFLO Responsive Design Enhancements */

/* Very narrow screens (mobile portrait) */
@media (max-width: 374px) {
  .document-card-narrow {
    padding: 0.75rem;
  }

  .document-card-narrow .document-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .document-card-narrow .document-actions button {
    width: 100%;
    justify-content: center;
  }
}

/* Custom breakpoint for extra small screens */
@media (min-width: 475px) {
  .xs\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xs\:flex-row {
    flex-direction: row;
  }

  .xs\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .xs\:inline {
    display: inline;
  }
}

/* Touch-friendly enhancements */
@media (max-width: 768px) {
  /* Ensure minimum touch target size */
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  /* Document card responsive improvements */
  .document-actions {
    flex-wrap: wrap;
    gap: 0.5rem;
    width: 100%;
  }

  .document-actions button {
    flex: 1;
    min-width: 0;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Ensure buttons don't overflow container */
  .document-card-container {
    min-width: 0;
    overflow: hidden;
  }

  /* Document card mobile layout */
  .document-card {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
  }

  .document-header {
    width: 100%;
  }

  .document-title {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.4;
    margin: 0;
  }

  .document-content-stack {
    width: 100%;
  }

  .document-actions {
    width: 100%;
    flex-direction: column;
    gap: 0.5rem;
  }

  .document-action-btn {
    width: 100%;
    justify-content: center;
    min-height: 44px;
    font-size: 0.875rem;
  }

  /* Metadata badges responsive */
  .document-metadata {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  /* Description responsive */
  .document-description {
    line-height: 1.5;
  }
  
  /* Improve form field spacing */
  input, 
  textarea, 
  select {
    min-height: 44px;
    padding: 12px 16px;
  }
  
  /* Better spacing for mobile cards */
  .card-mobile {
    padding: 16px;
    margin-bottom: 12px;
  }
  
  /* Mobile-optimized text sizes */
  .text-mobile-sm {
    font-size: 14px;
    line-height: 1.4;
  }
  
  .text-mobile-base {
    font-size: 16px;
    line-height: 1.5;
  }
  
  .text-mobile-lg {
    font-size: 18px;
    line-height: 1.4;
  }
}

/* Small screens (640px+) - Horizontal buttons with wrapping */
@media (min-width: 640px) {
  .document-actions {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .document-action-btn {
    flex: 0 1 auto;
    min-width: 100px;
    width: auto;
  }
}

/* Medium screens (768px+) - Mixed layout */
@media (min-width: 768px) {
  .document-card {
    padding: 1.25rem;
    gap: 1rem;
  }

  .document-header {
    margin-bottom: 0.5rem;
  }

  .document-title {
    font-size: 1.125rem;
    line-height: 1.5;
  }

  .document-content-stack {
    gap: 0.75rem;
  }

  .document-actions {
    gap: 0.75rem;
  }

  .document-action-btn {
    min-width: 120px;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Large screens (1024px+) - Optimized layout */
@media (min-width: 1024px) {
  .document-card {
    padding: 1.5rem;
  }

  .document-title {
    font-size: 1.25rem;
  }

  .document-actions {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  .document-action-btn {
    flex: 0 0 auto;
    min-width: 140px;
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Optimize grid layouts for tablet */
  .tablet-grid-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .tablet-grid-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  /* Better spacing for tablet */
  .tablet-spacing {
    padding: 20px;
    gap: 20px;
  }
}

/* Wide screens (1280px+) - Horizontal layout */
@media (min-width: 1280px) {
  .document-card {
    flex-direction: row;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .document-header {
    flex: 1;
    min-width: 0;
    margin-bottom: 0;
  }

  .document-content-stack {
    flex: 1;
    min-width: 0;
  }

  .document-actions {
    flex: 0 0 auto;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 0.75rem;
    margin-top: 0;
    align-self: flex-start;
  }

  .document-action-btn {
    flex: 0 0 auto;
    min-width: 100px;
    white-space: nowrap;
  }
}

/* Extra wide screens (1536px+) - Enhanced horizontal layout */
@media (min-width: 1536px) {
  .document-card {
    padding: 2rem;
    gap: 2rem;
  }

  .document-actions {
    gap: 1rem;
  }

  .document-action-btn {
    min-width: 120px;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  /* Multi-column layouts */
  .desktop-grid-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .desktop-grid-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  /* Hover states for desktop */
  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease-in-out;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid;
  }
  
  button {
    border: 2px solid;
  }
}

/* Focus improvements for keyboard navigation */
*:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Responsive text utilities */
.responsive-text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

@media (min-width: 640px) {
  .responsive-text-xs {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

.responsive-text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

@media (min-width: 640px) {
  .responsive-text-sm {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.responsive-text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

@media (min-width: 640px) {
  .responsive-text-base {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

/* Mobile-first container */
.mobile-container {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .mobile-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Responsive spacing utilities */
.responsive-spacing-sm {
  padding: 0.75rem;
}

@media (min-width: 640px) {
  .responsive-spacing-sm {
    padding: 1rem;
  }
}

@media (min-width: 1024px) {
  .responsive-spacing-sm {
    padding: 1.5rem;
  }
}

.responsive-spacing-md {
  padding: 1rem;
}

@media (min-width: 640px) {
  .responsive-spacing-md {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-spacing-md {
    padding: 2rem;
  }
}

/* Safe area insets for mobile devices */
@supports (padding: max(0px)) {
  .safe-area-inset {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* Responsive grid gap utilities */
.responsive-gap {
  gap: 0.75rem;
}

@media (min-width: 640px) {
  .responsive-gap {
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .responsive-gap {
    gap: 1.5rem;
  }
}

/* Mobile navigation improvements */
.mobile-nav-item {
  display: flex;
  align-items: center;
  padding: 1rem 0.75rem;
  min-height: 44px;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease-in-out;
}

.mobile-nav-item:hover,
.mobile-nav-item:focus {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Responsive card layouts */
.responsive-card {
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

/* Document card specific responsive behavior */
.document-card {
  overflow: hidden;
  position: relative;
}

.document-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Ensure proper spacing for all elements */
.document-title-section {
  width: 100%;
  min-width: 0;
}

.document-icon {
  flex-shrink: 0;
  align-self: flex-start;
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .document-title {
    font-size: 1rem;
    line-height: 1.4;
  }

  .document-description {
    font-size: 0.875rem;
  }
}

/* Ensure buttons never overflow */
.document-actions {
  max-width: 100%;
  overflow: hidden;
}

.document-action-btn {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}

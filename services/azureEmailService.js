/**
 * Azure OAuth2 Email Service for HOAFLO
 * Handles email sending using Azure OAuth2 authentication with Microsoft Graph API
 */

const { Client } = require('@microsoft/microsoft-graph-client');
const { ConfidentialClientApplication } = require('@azure/msal-node');
const axios = require('axios');

class AzureAuthProvider {
  constructor() {
    // Debug Azure configuration
    console.log('🔍 Azure Configuration Debug:');
    console.log('AZURE_CLIENT_ID:', process.env.AZURE_CLIENT_ID ? 'Present' : 'Missing');
    console.log('AZURE_CLIENT_SECRET:', process.env.AZURE_CLIENT_SECRET ? 'Present' : 'Missing');
    console.log('AZURE_TENANT_ID:', process.env.AZURE_TENANT_ID || 'Missing');
    console.log('AZURE_TENANT_ID (raw value):', JSON.stringify(process.env.AZURE_TENANT_ID));
    console.log('Authority URL:', `https://login.microsoftonline.com/${process.env.AZURE_TENANT_ID}`);
    console.log('All Azure env vars:', {
      AZURE_CLIENT_ID: process.env.AZURE_CLIENT_ID,
      AZURE_CLIENT_SECRET: process.env.AZURE_CLIENT_SECRET ? '[REDACTED]' : undefined,
      AZURE_TENANT_ID: process.env.AZURE_TENANT_ID,
      AZURE_CLIENT_SECRET_ID: process.env.AZURE_CLIENT_SECRET_ID
    });

    this.clientApp = new ConfidentialClientApplication({
      auth: {
        clientId: process.env.AZURE_CLIENT_ID,
        clientSecret: process.env.AZURE_CLIENT_SECRET,
        authority: `https://login.microsoftonline.com/${process.env.AZURE_TENANT_ID}`
      }
    });
  }

  async getAccessToken() {
    try {
      const clientCredentialRequest = {
        scopes: ['https://graph.microsoft.com/.default'],
      };

      const response = await this.clientApp.acquireTokenByClientCredential(clientCredentialRequest);
      return response.accessToken;
    } catch (error) {
      console.error('Error acquiring access token:', error);
      throw error;
    }
  }

  async getAuthenticationHeaderValue() {
    const token = await this.getAccessToken();
    return `Bearer ${token}`;
  }
}



// Send email using direct Microsoft Graph REST API
const sendEmailWithGraphAPI = async (accessToken, to, subject, htmlContent, attachments = []) => {
  try {
    const emailData = {
      message: {
        subject: subject,
        body: {
          contentType: 'HTML',
          content: htmlContent
        },
        toRecipients: [
          {
            emailAddress: {
              address: to
            }
          }
        ],
        from: {
          emailAddress: {
            address: process.env.OUTLOOK_EMAIL_USER || '<EMAIL>',
            name: process.env.EMAIL_FROM_NAME || 'HOAFLO Support'
          }
        }
      },
      saveToSentItems: true
    };

    // Add attachments if provided
    if (attachments && attachments.length > 0) {
      emailData.message.attachments = attachments.map(attachment => ({
        '@odata.type': '#microsoft.graph.fileAttachment',
        name: attachment.filename,
        contentBytes: attachment.content, // Base64 encoded content
        contentType: attachment.contentType
      }));
    }

    const response = await axios.post(
      `https://graph.microsoft.com/v1.0/users/${process.env.OUTLOOK_EMAIL_USER || '<EMAIL>'}/sendMail`,
      emailData,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return { success: true, response: response.status };
  } catch (error) {
    console.error('Microsoft Graph API Error Details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.response?.headers,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers
      }
    });

    // Handle 403 permission errors gracefully
    if (error.response?.status === 403) {
      return {
        success: false,
        error: 'Permission denied',
        status: 403,
        details: error.response?.data
      };
    }

    throw error;
  }
};

/**
 * Send email using Microsoft Graph API
 * @param {string} to - Recipient email address
 * @param {string} subject - Email subject
 * @param {string} htmlContent - HTML content of the email
 * @param {Array} attachments - Optional attachments
 * @returns {Object} Result object with success status
 */
const sendEmailWithGraph = async (to, subject, htmlContent, attachments = []) => {
  try {
    console.log('Azure Email Service - Sending email via Microsoft Graph API');
    console.log('To:', to);
    console.log('Subject:', subject);
    console.log('From:', process.env.OUTLOOK_EMAIL_USER);

    // Check if Azure credentials are configured
    if (!process.env.AZURE_CLIENT_ID || !process.env.AZURE_CLIENT_SECRET || !process.env.AZURE_TENANT_ID) {
      console.log('Azure credentials not configured, falling back to simulation');
      return { 
        success: true, 
        simulated: true, 
        message: 'Azure credentials not configured - email simulated' 
      };
    }

    const authProvider = new AzureAuthProvider();
    const accessToken = await authProvider.getAccessToken();

    // Send the email using direct Graph API
    const result = await sendEmailWithGraphAPI(accessToken, to, subject, htmlContent, attachments);

    // Handle permission errors
    if (!result.success && result.status === 403) {
      console.log('Azure Email Service - Permission denied, simulating email send...');
      console.log('📧 SIMULATED EMAIL SENT:');
      console.log('From: HOAFLO Support <<EMAIL>>');
      console.log('To:', to);
      console.log('Subject:', subject);
      console.log('Content:', htmlContent.substring(0, 200) + '...');

      return {
        success: true,
        simulated: true,
        provider: 'microsoft-graph-simulated',
        message: 'Email simulated due to Azure permissions - would have been sent successfully'
      };
    }

    if (!result.success) {
      throw new Error(result.error);
    }

    console.log('Email sent successfully via Microsoft Graph API to:', to);
    return { 
      success: true, 
      provider: 'microsoft-graph',
      messageId: `graph-${Date.now()}` 
    };

  } catch (error) {
    console.error('Azure Email Service Error:', error);

    // If Graph API fails due to permissions, simulate the email for now
    if (error.response?.status === 403) {
      console.log('Azure Email Service - Permission denied, simulating email send...');
      console.log('📧 SIMULATED EMAIL SENT:');
      console.log('From: HOAFLO Support <<EMAIL>>');
      console.log('To:', to);
      console.log('Subject:', subject);
      console.log('Content:', htmlContent.substring(0, 200) + '...');

      return {
        success: true,
        simulated: true,
        provider: 'microsoft-graph-simulated',
        message: 'Email simulated due to Azure permissions - would have been sent successfully'
      };
    }

    // If Graph API fails, return error but don't crash
    return {
      success: false,
      error: error.message,
      provider: 'microsoft-graph',
      details: error.stack
    };
  }
};

/**
 * Test Azure email configuration
 * @returns {Object} Configuration test results
 */
const testAzureEmailConfig = async () => {
  console.log('Testing Azure Email Configuration...');
  
  const config = {
    clientId: !!process.env.AZURE_CLIENT_ID,
    clientSecret: !!process.env.AZURE_CLIENT_SECRET,
    tenantId: !!process.env.AZURE_TENANT_ID,
    emailUser: !!process.env.OUTLOOK_EMAIL_USER
  };

  console.log('Azure Configuration:', config);

  if (!config.clientId || !config.clientSecret || !config.tenantId) {
    return {
      success: false,
      error: 'Missing Azure credentials',
      config
    };
  }

  try {
    const authProvider = new AzureAuthProvider();
    const token = await authProvider.getAccessToken();
    
    return {
      success: true,
      message: 'Azure authentication successful',
      tokenLength: token ? token.length : 0,
      config
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      config
    };
  }
};

module.exports = {
  sendEmailWithGraph,
  testAzureEmailConfig,
  AzureAuthProvider
};

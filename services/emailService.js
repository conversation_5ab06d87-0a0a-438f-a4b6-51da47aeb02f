const nodemailer = require('nodemailer');
const { sendEmailWithGraph, testAzureEmailConfig } = require('./azureEmailService');

// Create company admin transporter (Outlook Business)
const createCompanyAdminTransporter = () => {
  return nodemailer.createTransport({
    service: 'hotmail', // Outlook/Hotmail service for Outlook Business
    auth: {
      user: '<EMAIL>',
      pass: process.env.OUTLOOK_EMAIL_PASSWORD || process.env.OUTLOOK_APP_PASSWORD
    }
  });
};

// Removed Gmail fallback - using Outlook only for cleaner email handling

// Primary transporter - Outlook only
const transporter = createCompanyAdminTransporter();

// Common email template layout
const createEmailLayout = (content, title) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          background-color: #f9f9f9;
          margin: 0; padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }
        .header {
          background-color: #2563EB;
          background-image: linear-gradient(135deg, #2563EB, #1E40AF);
          padding: 25px 20px;
          text-align: center;
        }
        .content {
          padding: 30px;
        }
        .footer {
          background-color: #f5f5f5;
          padding: 20px;
          text-align: center;
          font-size: 12px;
          color: #666;
        }
        h1, h2 {
          color: #2563EB;
        }
        .button {
          display: inline-block;
          padding: 12px 24px;
          background-color: #2563EB;
          color: white;
          text-decoration: none;
          border-radius: 6px;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2 style="color: white; margin: 0;">HOA MANAGEMENT APP</h2>
          <p style="color: white; margin: 5px 0 0;">by hoaflo.com</p>
        </div>
        <div class="content">
          ${content}
        </div>
        <div class="footer">
          <p>© ${new Date().getFullYear()} hoaflo.com LLC. All rights reserved.</p>
          <p>Questions? Contact <EMAIL></p>
        </div>
      </div>
    </body>
    </html>
  `;
};

// Email Templates
const emailTemplates = {
  passwordReset: (email, data) => ({
    subject: 'Password Reset Request - HOA MANAGEMENT APP',
    html: createEmailLayout(`
      <h1>Password Reset</h1>
      <p>Click the button below to reset your password:</p>
      <a href="${data.resetLink}" class="button">Reset Password</a>
      <p>This link will expire in 1 hour.</p>
    `, 'Password Reset')
  }),

  registrationEmail: (email, data) => ({
    subject: 'Registration Received - HOA MANAGEMENT APP',
    html: createEmailLayout(`
      <h1>Thank You for Registering</h1>
      <p>Hello ${data.username}, your registration is pending approval.</p>
    `, 'Registration Received')
  }),

  welcomeEmail: (username) => ({
    subject: 'Welcome to HOA MANAGEMENT APP',
    html: createEmailLayout(`
      <h1>Welcome ${username}</h1>
      <p>You can now manage HOA payments, requests, and more.</p>
    `, 'Welcome')
  }),

  accountApproved: (email, data) => ({
    subject: 'Account Approved - HOA MANAGEMENT APP',
    html: createEmailLayout(`
      <h1>Account Approved</h1>
      <p>Hello ${data.username}, you can now log in:</p>
      <a href="${data.loginUrl || 'https://hoa-front.vercel.app/login'}" class="button">Log In</a>
    `, 'Account Approved')
  }),

  accountDenied: (email, data) => ({
    subject: 'Account Application Status - HOA MANAGEMENT APP',
    html: createEmailLayout(`
      <h1>Application Denied</h1>
      <p>Hello ${data.username}, your application was denied. Contact support for questions.</p>
    `, 'Account Denied')
  }),

  passwordChanged: (username) => ({
    subject: 'Password Changed - HOA MANAGEMENT APP',
    html: createEmailLayout(`
      <h1>Password Changed</h1>
      <p>Hello ${username}, your password was successfully changed.</p>
    `, 'Password Changed')
  }),

  taskNotification: (username, taskTitle) => ({
    subject: 'Task Update - HOA MANAGEMENT APP',
    html: createEmailLayout(`
      <h1>Task Update</h1>
      <p>Hello ${username}, the task <strong>${taskTitle}</strong> has been updated.</p>
    `, 'Task Update')
  }),

  adminAnnouncement: (username, data) => ({
    subject: `${data.subject || 'Announcement'} - HOA MANAGEMENT APP`,
    html: createEmailLayout(`
      <h1>${data.subject || 'Announcement'}</h1>
      <p>Hello ${username},</p>
      <div style="margin: 20px 0; padding: 15px; border-left: 4px solid #4F46E5; background-color: #f8f9fa;">
        ${data.message.replace(/\n/g, '<br>')}
      </div>
      ${data.actionLink ? `<p><a href="${data.actionLink}" class="button">${data.actionText || 'View Details'}</a></p>` : ''}
      <p>Best regards,<br>HOA MANAGEMENT APP Team<br>Pelican App Solutions</p>
    `, data.subject || 'Important Announcement')
  }),

  streetAnnouncement: (username, data) => {
    // Generate attachments section if attachments exist
    let attachmentsHtml = '';
    if (data.attachments && data.attachments.length > 0) {
      attachmentsHtml = `
        <div style="margin: 20px 0; padding: 15px; background-color: #f8fafc; border-radius: 6px; border: 1px solid #e2e8f0;">
          <h3 style="margin: 0 0 10px 0; color: #374151; font-size: 16px;">📎 Attachments (${data.attachments.length})</h3>
          <ul style="margin: 0; padding-left: 20px;">
            ${data.attachments.map(attachment => `
              <li style="margin: 5px 0;">
                <a href="${attachment.signedUrl}" style="color: #3B82F6; text-decoration: none;">
                  ${attachment.filename}
                </a>
                <span style="color: #6B7280; font-size: 12px;"> (${Math.round(attachment.size / 1024)} KB)</span>
              </li>
            `).join('')}
          </ul>
          <p style="margin: 10px 0 0 0; font-size: 12px; color: #6B7280;">
            💡 Click on attachment names to download. Links expire in 24 hours.
          </p>
        </div>
      `;
    }

    const content = `
      <h1>📢 ${data.subject}</h1>
      <p>Hello ${username},</p>
      <p>You have received a new announcement for <strong>${data.streetName}</strong>:</p>
      <div style="margin: 20px 0; padding: 20px; border-left: 4px solid #10B981; background-color: #f0fdf4; border-radius: 6px;">
        ${data.message.replace(/\n/g, '<br>')}
      </div>
      ${attachmentsHtml}
      <p style="color: #6B7280; font-size: 14px;">
        <strong>From:</strong> ${data.senderName}<br>
        <strong>Street:</strong> ${data.streetName}
      </p>
      ${data.actionLink ? `<p><a href="${data.actionLink}" class="button">View in App</a></p>` : ''}
      <p>Best regards,<br>Your HOA Administration<br>HOA MANAGEMENT APP</p>
    `;
    return {
      subject: `📢 ${data.subject} - ${data.streetName}`,
      html: createEmailLayout(content, data.subject)
    };
  },

  hoaAnnouncement: (username, data) => {
    // Generate attachments section if attachments exist
    let attachmentsHtml = '';
    if (data.attachments && data.attachments.length > 0) {
      attachmentsHtml = `
        <div style="margin: 20px 0; padding: 15px; background-color: #f8fafc; border-radius: 6px; border: 1px solid #e2e8f0;">
          <h3 style="margin: 0 0 10px 0; color: #374151; font-size: 16px;">📎 Attachments (${data.attachments.length})</h3>
          <ul style="margin: 0; padding-left: 20px;">
            ${data.attachments.map(attachment => `
              <li style="margin: 5px 0;">
                <a href="${attachment.signedUrl}" style="color: #3B82F6; text-decoration: none;">
                  ${attachment.filename}
                </a>
                <span style="color: #6B7280; font-size: 12px;"> (${Math.round(attachment.size / 1024)} KB)</span>
              </li>
            `).join('')}
          </ul>
          <p style="margin: 10px 0 0 0; font-size: 12px; color: #6B7280;">
            💡 Click on attachment names to download. Links expire in 24 hours.
          </p>
        </div>
      `;
    }

    const content = `
      <h1>📢 ${data.subject}</h1>
      <p>Hello ${username},</p>
      <p>You have received a new announcement from <strong>${data.hoaName}</strong>:</p>
      <div style="margin: 20px 0; padding: 20px; border-left: 4px solid #3B82F6; background-color: #eff6ff; border-radius: 6px;">
        ${data.message.replace(/\n/g, '<br>')}
      </div>
      ${attachmentsHtml}
      <p style="color: #6B7280; font-size: 14px;">
        <strong>From:</strong> ${data.senderName}<br>
        <strong>HOA:</strong> ${data.hoaName}
      </p>
      ${data.actionLink ? `<p><a href="${data.actionLink}" class="button">View in App</a></p>` : ''}
      <p>Best regards,<br>Your HOA Management Team<br>HOA MANAGEMENT APP</p>
    `;
    return {
      subject: `📢 ${data.subject} - ${data.hoaName}`,
      html: createEmailLayout(content, data.subject)
    };
  }
};

// Send Raw Email
const sendEmail = async (to, subject, htmlContent) => {
  try {
    console.log('Email Service - Attempting to send direct email:');
    console.log('To:', to);
    console.log('Subject:', subject);
    const outlookConfigured = !!(process.env.OUTLOOK_EMAIL_PASSWORD || process.env.OUTLOOK_APP_PASSWORD);
    console.log('Email Provider: Outlook Business');
    console.log('Sender Email: <EMAIL>');
    console.log('Email Password configured:', !!outlookConfigured);

    // Use Outlook only
    const senderEmail = '<EMAIL>';
    const senderName = 'HOAFLO Support';

    // Force Azure OAuth2 for testing - skip SMTP
    console.log('🔧 Forcing Azure OAuth2 for email testing...');
    try {
      const azureResult = await sendEmailWithGraph(to, subject, createEmailLayout(htmlContent, subject));
      if (azureResult.success) {
        console.log('Email Service - Successfully sent via Azure OAuth2');
        return azureResult;
      } else {
        console.log('Email Service - Azure OAuth2 failed:', azureResult.error);
      }
    } catch (azureError) {
      console.error('Email Service - Azure OAuth2 error:', azureError);
    }

    // If Outlook configuration is not set up, just log the email
    if (!outlookConfigured) {
      console.log('Email Service - Email not configured, would have sent:');
      console.log('From: HOAFLO Support <<EMAIL>>');
      console.log('To:', to);
      console.log('Subject:', subject);
      console.log('Content:', htmlContent);
      return { success: true, simulated: true };
    }

    const mailOptions = {
      from: `${senderName} <${senderEmail}>`,
      to,
      subject,
      html: createEmailLayout(htmlContent, subject),
      headers: {
        'X-Priority': '1',
        'Importance': 'High'
      }
    };

    console.log('Email Service - Sending mail with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject
    });

    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully to', to, 'Message ID:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Email sending error:', error.message);

    // Simulate email sending for development/testing
    console.log('Email Service - SMTP failed, simulating email send...');
    console.log('📧 SIMULATED EMAIL SENT:');
    console.log('From: HOAFLO Support <<EMAIL>>');
    console.log('To:', to);
    console.log('Subject:', subject);
    console.log('Content:', htmlContent.substring(0, 200) + '...');

    return {
      success: true,
      simulated: true,
      provider: 'simulated',
      message: 'Email simulated for development - would have been sent successfully'
    };

    return { success: false, error: error.message, details: error.stack };
  }
};

// Send email using template with HOA-specific email support
const sendTemplateEmail = async (to, template, data = {}, options = {}) => {
  try {
    console.log('Email Service - Attempting to send template email:');
    console.log('To:', to);
    console.log('Template:', template);
    console.log('Data:', JSON.stringify(data, null, 2));
    const outlookConfigured = !!(process.env.OUTLOOK_EMAIL_PASSWORD || process.env.OUTLOOK_APP_PASSWORD);
    console.log('Email Provider: Outlook Business');
    console.log('Sender Email: <EMAIL>');
    console.log('Email Password configured:', !!outlookConfigured);

    // Validate template exists
    if (!emailTemplates[template]) {
      console.error(`Email Service - Template '${template}' not found`);
      return { success: false, error: 'Email template not found' };
    }

    const emailContent = emailTemplates[template](to, data);

    // Determine sender email and name based on options
    let senderEmail, senderName, selectedTransporter;

    if (options.senderType === 'hoa' && options.hoaEmail && options.hoaId) {
      // Use HOA-specific email
      senderEmail = options.hoaEmail;
      senderName = options.hoaName || 'HOA Administration';

      // For now, HOA emails will use the same transporter as company admin
      // In the future, this could be enhanced to use HOA-specific SMTP settings
      selectedTransporter = createCompanyAdminTransporter();

      console.log(`📧 Using HOA email: ${senderName} <${senderEmail}>`);
    } else {
      // Use company admin email (default)
      senderEmail = '<EMAIL>';
      senderName = 'HOAFLO Support';
      selectedTransporter = transporter;

      console.log(`📧 Using company admin email: ${senderName} <${senderEmail}>`);
    }

    // Force Azure OAuth2 for testing - skip SMTP
    console.log('🔧 Forcing Azure OAuth2 for template email testing...');
    try {
      const azureResult = await sendEmailWithGraph(to, emailContent.subject, emailContent.html);
      if (azureResult.success) {
        console.log('Email Service - Successfully sent template email via Azure OAuth2');
        return azureResult;
      } else {
        console.log('Email Service - Azure OAuth2 failed for template email:', azureResult.error);
      }
    } catch (azureError) {
      console.error('Email Service - Azure OAuth2 error for template email:', azureError);
    }

    // If Outlook configuration is not set up, just log the email
    if (!outlookConfigured) {
      console.log('Email Service - Email not configured, would have sent:');
      console.log('From: HOAFLO Support <<EMAIL>>');
      console.log('To:', to);
      console.log('Subject:', emailContent.subject);
      console.log('Content:', emailContent.html);
      return { success: true, simulated: true };
    }

    const mailOptions = {
      from: `${senderName} <${senderEmail}>`,
      to,
      subject: emailContent.subject,
      html: emailContent.html,
      // Add headers to reduce chance of being marked as spam
      headers: {
        'X-Priority': '1',
        'X-MSMail-Priority': 'High',
        'Importance': 'High'
      },
      // Add text version as fallback
      text: emailContent.html.replace(/<[^>]*>/g, '')
    };

    console.log('Email Service - Sending mail with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject
    });

    // Verify transporter before sending
    const verifyResult = await selectedTransporter.verify();
    console.log('Email Service - Transporter verification:', verifyResult);

    const info = await selectedTransporter.sendMail(mailOptions);
    console.log('Email Service - Email sent successfully:', info.messageId);
    console.log('Email Service - Full response:', JSON.stringify(info, null, 2));

    // Log preview URL if available (for testing)
    if (info.messageId && getTestMessageUrl) {
      try {
        const previewUrl = getTestMessageUrl(info);
        if (previewUrl) {
          console.log('Email Service - Preview URL:', previewUrl);
        }
      } catch (e) {
        console.log('Email Service - Preview URL not available');
      }
    }

    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Email Service - Error sending email:', error);
    console.error('Email Service - Error details:', error.stack);

    // Simulate email sending for development/testing
    console.log('Email Service - SMTP failed, simulating template email send...');
    try {
      // Generate email content for simulation
      const { subject, html } = emailTemplates[template](to, data);
      console.log('📧 SIMULATED TEMPLATE EMAIL SENT:');
      console.log('Template:', template);
      console.log('From: HOAFLO Support <<EMAIL>>');
      console.log('To:', to);
      console.log('Subject:', subject);
      console.log('Content:', html.substring(0, 200) + '...');

      return {
        success: true,
        simulated: true,
        provider: 'simulated-template',
        template: template,
        message: 'Template email simulated for development - would have been sent successfully'
      };
    } catch (templateError) {
      console.error('Email Service - Template generation error:', templateError);
    }

    // Return error instead of throwing to prevent crashes
    return {
      success: false,
      error: error.message,
      details: error.stack
    };
  }

  const { subject, html } = emailTemplates[template](to, data);
  return await sendEmail(to, subject, html);
};

// Test email configuration
const testEmailConfig = async () => {
  console.log('🔍 Testing Email Configuration...');

  // Test environment variables
  const config = {
    outlookUser: !!process.env.OUTLOOK_EMAIL_USER,
    outlookPassword: !!process.env.OUTLOOK_EMAIL_PASSWORD,
    azureClientId: !!process.env.AZURE_CLIENT_ID,
    azureClientSecret: !!process.env.AZURE_CLIENT_SECRET,
    azureTenantId: !!process.env.AZURE_TENANT_ID,
    emailTesting: process.env.ENABLE_EMAIL_TESTING
  };

  console.log('📧 Email Configuration Status:', config);

  // Test Azure authentication
  try {
    const azureResult = await testAzureEmailConfig();
    console.log('🔍 Azure Test Result:', azureResult);
    return { config, azureTest: azureResult };
  } catch (error) {
    console.error('🔍 Azure Test Error:', error);
    return { config, azureTest: { success: false, error: error.message } };
  }
};

module.exports = {
  sendEmail,
  sendTemplateEmail,
  testAzureEmailConfig,
  testEmailConfig
};

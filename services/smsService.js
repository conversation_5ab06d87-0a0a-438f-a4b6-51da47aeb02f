/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

const nodemailer = require('nodemailer');
const { generateVerificationCode } = require('../utils/authUtils');

/**
 * In a production environment, this would use a real SMS service like Twilio.
 * For development, we'll simulate SMS by sending an email instead.
 */
const sendSMS = async (phoneNumber, message) => {
  try {
    // For development, we'll use email instead of actual SMS
    // Extract the user's email from the request context or database
    const user = await getUserByPhoneNumber(phoneNumber);
    
    if (!user || !user.email) {
      console.error(`No user found with phone number: ${phoneNumber}`);
      return false;
    }
    
    // Use the email service to send the verification code
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_APP_PASSWORD
      }
    });
    
    const mailOptions = {
      from: process.env.GMAIL_USER,
      to: user.email,
      subject: 'Street Harmony HOA - Verification Code',
      text: message,
      html: `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4a6ee0;">Street Harmony HOA</h2>
        <p>Your verification code is:</p>
        <div style="background-color: #f5f5f5; padding: 15px; font-size: 24px; text-align: center; letter-spacing: 5px; font-weight: bold;">
          ${message.replace('Your verification code is: ', '')}
        </div>
        <p style="margin-top: 20px; font-size: 14px; color: #666;">
          This code will expire in 10 minutes. If you didn't request this code, please ignore this message.
        </p>
      </div>`
    };
    
    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('Error sending SMS (email):', error);
    return false;
  }
};

/**
 * Helper function to get user by phone number
 */
const getUserByPhoneNumber = async (phoneNumber) => {
  try {
    const User = require('../models/user');
    return await User.findOne({ phoneNumber });
  } catch (error) {
    console.error('Error finding user by phone number:', error);
    return null;
  }
};

/**
 * Send verification code via SMS (simulated with email)
 */
const sendVerificationCode = async (phoneNumber) => {
  try {
    // Generate a 6-digit verification code
    const verificationCode = generateVerificationCode();
    
    // Store the verification code in the database with an expiration time
    const User = require('../models/user');
    const user = await User.findOneAndUpdate(
      { phoneNumber },
      { 
        'mfa.verificationCode': verificationCode,
        'mfa.verificationCodeExpires': Date.now() + 10 * 60 * 1000, // 10 minutes
        'mfa.enabled': true
      },
      { new: true }
    );
    
    if (!user) {
      console.error(`No user found with phone number: ${phoneNumber}`);
      return false;
    }
    
    // Send the verification code via SMS (simulated with email)
    const message = `Your verification code is: ${verificationCode}`;
    return await sendSMS(phoneNumber, message);
  } catch (error) {
    console.error('Error sending verification code:', error);
    return false;
  }
};

/**
 * Verify the code provided by the user
 */
const verifyCode = async (phoneNumber, code) => {
  try {
    const User = require('../models/user');
    const user = await User.findOne({ 
      phoneNumber,
      'mfa.verificationCode': code,
      'mfa.verificationCodeExpires': { $gt: Date.now() }
    });
    
    if (!user) {
      return false;
    }
    
    // Clear the verification code after successful verification
    await User.findByIdAndUpdate(user._id, {
      'mfa.verificationCode': null,
      'mfa.verificationCodeExpires': null,
      'mfa.lastVerified': Date.now()
    });
    
    return true;
  } catch (error) {
    console.error('Error verifying code:', error);
    return false;
  }
};

module.exports = {
  sendVerificationCode,
  verifyCode
};

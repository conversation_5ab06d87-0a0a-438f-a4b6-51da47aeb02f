/**
 * Operational Admin Middleware
 * 
 * This middleware ensures that only regular 'admin' users can access operational routes.
 * Company admins are denied access to maintain their watchdog-only role.
 * This prevents company admins from interfering with individual HOA operations.
 */

const operationalAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else if (req.user && req.user.role === 'company_admin') {
    res.status(403).json({ 
      error: 'Access denied. Operational admin privileges required.',
      message: 'Company administrators have oversight access only. This operation requires HOA admin privileges.'
    });
  } else {
    res.status(403).json({ 
      error: 'Access denied. Admin privileges required.',
      message: 'This action can only be performed by HOA administrators.'
    });
  }
};

module.exports = operationalAdmin;

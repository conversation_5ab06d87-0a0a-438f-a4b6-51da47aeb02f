/**
 * Company Admin Middleware
 * 
 * This middleware ensures that only company_admin users can access certain routes.
 * Regular admins and members are denied access.
 */

const companyAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'company_admin') {
    next();
  } else {
    res.status(403).json({ 
      error: 'Access denied. Company admin privileges required.',
      message: 'This action can only be performed by company administrators.'
    });
  }
};

module.exports = companyAdmin;

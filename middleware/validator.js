/**
 * Request validation middleware using Joi
 */
const Joi = require('joi');

/**
 * Validate request data against a Joi schema
 * @param {Object} schema - Joi schema for validation
 * @param {String} property - Request property to validate (body, params, query)
 * @returns {Function} Express middleware function
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error } = schema.validate(req[property], { abortEarly: false });
    
    if (!error) {
      return next();
    }
    
    const errors = error.details.map(detail => detail.message);
    
    return res.status(400).json({
      success: false,
      message: 'Validation Error',
      statusCode: 400,
      errors
    });
  };
};

// Common validation schemas
const schemas = {
  // Auth schemas
  login: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'string.empty': 'Email is required',
      'any.required': 'Email is required'
    }),
    password: Joi.string().required().messages({
      'string.empty': 'Password is required',
      'any.required': 'Password is required'
    })
  }),
  
  register: Joi.object({
    username: Joi.string().min(3).max(30).required().messages({
      'string.min': 'Username must be at least 3 characters',
      'string.max': 'Username cannot exceed 30 characters',
      'string.empty': 'Username is required',
      'any.required': 'Username is required'
    }),
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'string.empty': 'Email is required',
      'any.required': 'Email is required'
    }),
    password: Joi.string().min(6).required().messages({
      'string.min': 'Password must be at least 6 characters',
      'string.empty': 'Password is required',
      'any.required': 'Password is required'
    }),
    fullName: Joi.string().required().messages({
      'string.empty': 'Full name is required',
      'any.required': 'Full name is required'
    }),
    propertyAddress: Joi.string().required().messages({
      'string.empty': 'Property address is required',
      'any.required': 'Property address is required'
    })
  }),
  
  // Task schemas
  createTask: Joi.object({
    title: Joi.string().required().messages({
      'string.empty': 'Task title is required',
      'any.required': 'Task title is required'
    }),
    description: Joi.string().required().messages({
      'string.empty': 'Task description is required',
      'any.required': 'Task description is required'
    }),
    priority: Joi.string().valid('Low', 'Medium', 'High').required().messages({
      'any.only': 'Priority must be Low, Medium, or High',
      'any.required': 'Priority is required'
    }),
    dueDate: Joi.date().iso().required().messages({
      'date.base': 'Due date must be a valid date',
      'date.format': 'Due date must be in ISO format',
      'any.required': 'Due date is required'
    }),
    budget: Joi.number().min(0).optional().messages({
      'number.base': 'Budget must be a number',
      'number.min': 'Budget cannot be negative'
    }),
    assignee: Joi.object({
      userId: Joi.string().optional(),
      username: Joi.string().optional(),
      email: Joi.string().email().optional()
    }).optional()
  }),
  
  // Finance schemas
  createFinance: Joi.object({
    type: Joi.string().valid('income', 'expense').required().messages({
      'any.only': 'Type must be income or expense',
      'any.required': 'Type is required'
    }),
    category: Joi.string().required().messages({
      'string.empty': 'Category is required',
      'any.required': 'Category is required'
    }),
    amount: Joi.number().positive().required().messages({
      'number.base': 'Amount must be a number',
      'number.positive': 'Amount must be positive',
      'any.required': 'Amount is required'
    }),
    note: Joi.string().optional()
  }),
  
  // Event schemas
  createEvent: Joi.object({
    title: Joi.string().required().messages({
      'string.empty': 'Event title is required',
      'any.required': 'Event title is required'
    }),
    date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required().messages({
      'string.pattern.base': 'Date must be in YYYY-MM-DD format',
      'any.required': 'Date is required'
    })
  }),
  
  // ID parameter schema
  idParam: Joi.object({
    id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required().messages({
      'string.pattern.base': 'Invalid ID format',
      'any.required': 'ID is required'
    })
  })
};

module.exports = {
  validate,
  schemas
};

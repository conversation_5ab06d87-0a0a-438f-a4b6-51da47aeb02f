// middleware/socketMiddleware.js
const { getIo } = require('../socket');

/**
 * Middleware to attach the socket.io instance to the request object
 * This allows controllers to access the socket instance via req.io
 */
const socketMiddleware = (req, res, next) => {
  try {
    // Get the socket.io instance
    const io = getIo();

    // Attach it to the request object
    req.io = io;

    // Log socket middleware usage for debugging
    if (req.path.includes('/messages')) {
      console.log('Socket middleware attached to messages request:', {
        path: req.path,
        method: req.method,
        hasIo: !!io,
        userId: req.user?._id?.toString()
      });
    }

    // Continue to the next middleware
    next();
  } catch (error) {
    console.error('Socket middleware error:', error);
    // Continue even if there's an error with the socket
    // This ensures the API still works even if real-time updates don't
    next();
  }
};

module.exports = socketMiddleware;

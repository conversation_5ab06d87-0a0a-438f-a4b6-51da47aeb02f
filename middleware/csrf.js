/**
 * CSRF Protection Middleware
 */
const crypto = require('crypto');

// In-memory token store (should be replaced with Redis in production)
const csrfTokens = new Map();

// Clean up expired tokens every hour
setInterval(() => {
  const now = Date.now();
  csrfTokens.forEach((data, token) => {
    if (now > data.expires) {
      csrfTokens.delete(token);
    }
  });
}, 60 * 60 * 1000);

/**
 * Generate a new CSRF token
 * @param {string} userId - User ID to associate with the token
 * @param {number} expiryMinutes - Token expiry time in minutes
 * @returns {string} - CSRF token
 */
const generateToken = (userId, expiryMinutes = 60) => {
  const token = crypto.randomBytes(32).toString('hex');
  const expires = Date.now() + (expiryMinutes * 60 * 1000);

  csrfTokens.set(token, {
    userId,
    expires
  });

  return token;
};

/**
 * Verify a CSRF token
 * @param {string} token - CSRF token to verify
 * @param {string} userId - User ID to check against
 * @returns {boolean} - Whether the token is valid
 */
const verifyToken = (token, userId) => {
  if (!token || !csrfTokens.has(token)) {
    return false;
  }

  const data = csrfTokens.get(token);

  // Check if token is expired
  if (Date.now() > data.expires) {
    csrfTokens.delete(token);
    return false;
  }

  // Check if token belongs to the user
  if (data.userId !== userId) {
    return false;
  }

  return true;
};

// Enable a grace period for CSRF protection to ensure smooth transition
// After the grace period, this should be set to true for full protection
const ENFORCE_CSRF = false; // Set to true after initial deployment period

/**
 * Middleware to check CSRF token
 * Only applies to mutating methods (POST, PUT, DELETE, PATCH)
 * Has a grace period where missing tokens are logged but not blocked
 */
const csrfProtection = (req, res, next) => {
  // Skip for non-mutating methods
  const mutatingMethods = ['POST', 'PUT', 'DELETE', 'PATCH'];
  if (!mutatingMethods.includes(req.method)) {
    return next();
  }

  // Skip for certain routes that don't need CSRF protection
  const exemptRoutes = [
    '/api/auth/login',
    '/api/auth/register',
    '/api/password/request-reset',
    '/api/password/reset',
    '/api/documents/download',
    '/api/test/upload',
    '/api/test/fs-check'
  ];

  if (exemptRoutes.some(route => req.path.startsWith(route))) {
    return next();
  }

  // Check for token
  const token = req.headers['x-csrf-token'] ||
                (req.body && req.body._csrf) ||
                (req.query && req.query._csrf);

  if (!req.user || !req.user._id) {
    // During grace period, just log and continue
    if (!ENFORCE_CSRF) {
      console.warn('CSRF validation warning: No authenticated user', {
        path: req.path,
        method: req.method
      });
      return next();
    }

    return res.status(403).json({
      message: 'CSRF validation failed: No authenticated user'
    });
  }

  if (!token) {
    // During grace period, just log and continue
    if (!ENFORCE_CSRF) {
      console.warn('CSRF token missing', {
        path: req.path,
        method: req.method,
        userId: req.user._id
      });
      return next();
    }

    return res.status(403).json({
      message: 'CSRF token missing'
    });
  }

  // Verify token
  if (!verifyToken(token, req.user._id.toString())) {
    // During grace period, just log and continue
    if (!ENFORCE_CSRF) {
      console.warn('Invalid or expired CSRF token', {
        path: req.path,
        method: req.method,
        userId: req.user._id
      });
      return next();
    }

    return res.status(403).json({
      message: 'Invalid or expired CSRF token'
    });
  }

  // Token is valid, proceed
  next();
};

/**
 * Middleware to provide a CSRF token
 * Adds a token to the response headers for authenticated users
 */
const provideCsrfToken = (req, res, next) => {
  // Only provide token for authenticated users
  if (req.user && req.user._id) {
    const token = generateToken(req.user._id.toString());
    res.setHeader('X-CSRF-Token', token);
  }

  next();
};

module.exports = {
  csrfProtection,
  provideCsrfToken,
  generateToken,
  verifyToken
};

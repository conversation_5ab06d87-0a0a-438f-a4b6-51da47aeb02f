const jwt = require('jsonwebtoken');

const auth = (req, res, next) => {
  try {
    console.log('Auth middleware - Headers:', req.headers);
    
    // Get token from header
    let token = req.header('Authorization');
    
    // Handle different token formats
    if (token) {
      if (token.startsWith('Bearer ')) {
        token = token.slice(7);
      }
    } else {
      // Try getting token from query or body as fallback
      token = req.query.token || req.body.token;
    }

    console.log('Auth middleware - Extracted token:', token ? 'Present' : 'Missing');

    if (!token) {
      console.log('Auth middleware - No token provided');
      return res.status(401).json({ message: 'No authentication token, access denied' });
    }

    if (!process.env.JWT_SECRET) {
      console.log('Auth middleware - JWT_SECRET is missing');
      return res.status(500).json({ message: 'Server configuration error' });
    }

    // Verify token
    try {
      const verified = jwt.verify(token, process.env.JWT_SECRET);
      console.log('Auth middleware - Token verified:', verified);
      
      if (!verified.user || !verified.user._id) {
        throw new Error('Invalid token structure');
      }

      // Extract user data from the verified token
      req.user = verified.user;
      console.log('Auth middleware - User set:', req.user);
      next();
    } catch (verifyError) {
      console.log('Auth middleware - Token verification failed:', verifyError.message);
      if (verifyError.name === 'TokenExpiredError') {
        return res.status(401).json({ message: 'Token has expired' });
      }
      return res.status(401).json({ message: 'Token verification failed' });
    }
  } catch (error) {
    console.error('Auth middleware - Unexpected error:', error);
    res.status(500).json({ message: 'Server error in auth middleware' });
  }
};

module.exports = auth; 
/**
 * Middleware to check if user is either an admin or company_admin
 * Used for endpoints that should be accessible to both HOA admins and company admins
 */

const adminOrCompanyAdmin = (req, res, next) => {
  try {
    // Check if user exists and has the required role
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userRole = req.user.role;
    
    // Allow both 'admin' (HOA admin) and 'company_admin' roles
    if (userRole === 'admin' || userRole === 'company_admin') {
      console.log(`✅ Access granted for ${userRole}: ${req.user.email}`);
      return next();
    }

    // Deny access for other roles
    console.log(`❌ Access denied for role '${userRole}': ${req.user.email}`);
    return res.status(403).json({
      success: false,
      message: 'Access denied. Admin or Company Admin privileges required.'
    });

  } catch (error) {
    console.error('Error in adminOrCompanyAdmin middleware:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = adminOrCompanyAdmin;

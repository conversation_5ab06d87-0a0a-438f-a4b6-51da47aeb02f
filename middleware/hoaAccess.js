/**
 * HOA Access Middleware
 *
 * This middleware ensures that:
 * 1. Regular admins can only access data for their specific HOA
 * 2. Company admins can access data for all HOAs
 * 3. Members can only access data for their own HOA
 */

const mongoose = require('mongoose');

/**
 * Middleware to check if a user has access to a specific HOA
 * @param {Object} options - Configuration options
 * @param {boolean} options.allowSameHoaAdmins - Whether to allow admins of the same HOA to access
 * @param {boolean} options.allowCompanyAdmin - Whether to allow company_admin to access (default: true)
 * @param {boolean} options.allowMembers - Whether to allow members of the same HOA to access
 */
const hoaAccess = (options = {}) => {
  // Default options
  const {
    allowSameHoaAdmins = true,
    allowCompanyAdmin = true,
    allowMembers = false
  } = options;

  return async (req, res, next) => {
    try {
      // If no user is authenticated, deny access
      if (!req.user) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      // Company admins always have access if allowed by options
      if (allowCompanyAdmin && req.user.role === 'company_admin') {
        console.log('Company admin access granted');
        return next();
      }

      // Regular admins should have access to all HOAs too (similar to company_admin)
      // This ensures that admins without a specific HOA can still manage properties
      if (allowCompanyAdmin && req.user.role === 'admin' && !req.user.hoaId && !req.user.hoaCommunityCode) {
        console.log('Regular admin access granted (global admin)');
        return next();
      }

      // Get the HOA ID(s) from the request parameters, query, or body
      let hoaIds = [];
      let rawHoaId = req.params.hoaId || req.query.hoaId || (req.body && req.body.hoaId);

      // Handle multiple HOA IDs (comma-separated)
      if (rawHoaId) {
        console.log('Raw HOA ID received:', rawHoaId);
        if (rawHoaId.includes(',')) {
          // Split comma-separated HOA IDs
          hoaIds = rawHoaId.split(',').map(id => id.trim()).filter(id => id.length > 0);
          console.log('Split HOA IDs:', hoaIds);
        } else {
          hoaIds = [rawHoaId];
          console.log('Single HOA ID:', hoaIds);
        }
      }

      // If no HOA ID is provided, try to get it from the HOA code
      const hoaCode = req.params.hoaCode || req.query.hoaCode || (req.body && req.body.hoaCode);

      if (hoaIds.length === 0 && hoaCode) {
        const HOA = require('../models/hoa');
        const hoa = await HOA.findOne({ hoaCommunityCode: hoaCode.toUpperCase() });
        if (hoa) {
          hoaIds = [hoa._id.toString()];
        }
      }

      // If still no HOA ID, check if the request is for a user's HOA
      const userId = req.params.userId || req.query.userId || (req.body && req.body.userId);

      if (hoaIds.length === 0 && userId) {
        const User = require('../models/user');
        const user = await User.findById(userId);
        if (user && user.hoaId) {
          const userHoaIds = Array.isArray(user.hoaId) ? user.hoaId : [user.hoaId];
          hoaIds = userHoaIds.map(id => id.toString());
        }
      }

      // If no HOA ID could be determined, use the user's own HOAs
      if (hoaIds.length === 0 && req.user.hoaId) {
        const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
        hoaIds = userHoaIds.map(id => id.toString());
      } else if (hoaIds.length === 0 && req.user.hoaCommunityCode) {
        // Try to find HOAs by community codes
        const codes = Array.isArray(req.user.hoaCommunityCode) ? req.user.hoaCommunityCode : [req.user.hoaCommunityCode];
        const HOA = require('../models/hoa');

        for (const code of codes) {
          const hoa = await HOA.findOne({ hoaCommunityCode: code });
          if (hoa) {
            hoaIds.push(hoa._id.toString());
          }
        }
      }

      // If still no HOA IDs, deny access
      if (hoaIds.length === 0) {
        console.log('HOA access denied: No HOA ID could be determined');
        return res.status(403).json({ error: 'Access denied. HOA not specified.' });
      }

      // Validate all HOA IDs are valid ObjectIds
      const mongoose = require('mongoose');
      const invalidIds = hoaIds.filter(id => !mongoose.Types.ObjectId.isValid(id));
      if (invalidIds.length > 0) {
        console.log('Invalid HOA IDs found:', invalidIds);
        return res.status(400).json({ error: 'Invalid HOA ID format', invalidIds });
      }

      console.log('Checking access for HOA IDs:', hoaIds);

      // Check if user has access to the requested HOAs
      let hasAccess = false;

      if (req.user.role === 'admin' && allowSameHoaAdmins) {
        // Check if admin has access to any of the requested HOAs
        if (req.user.hoaId) {
          const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
          const userHoaIdStrings = userHoaIds.map(id => id.toString());

          // Check if any of the requested HOAs match the user's HOAs
          hasAccess = hoaIds.some(hoaId => userHoaIdStrings.includes(hoaId.toString()));

          if (hasAccess) {
            console.log('Admin access granted to own HOAs');
            return next();
          }
        }

        // If admin has HOA codes but not HOA IDs, check by codes
        if (!hasAccess && req.user.hoaCommunityCode) {
          const codes = Array.isArray(req.user.hoaCommunityCode) ? req.user.hoaCommunityCode : [req.user.hoaCommunityCode];
          const HOA = require('../models/hoa');
          const Community = require('../models/community');

          // Check each requested HOA against user's community codes
          for (const hoaId of hoaIds) {
            // First check if it's an HOA code
            const hoa = await HOA.findById(hoaId);
            if (hoa && codes.includes(hoa.hoaCommunityCode)) {
              hasAccess = true;
              break;
            }

            // Also check if any of the user's codes match communities under this HOA
            for (const code of codes) {
              const community = await Community.findOne({ communityCode: code }).populate('hoaId');
              if (community && community.hoaId && community.hoaId._id.toString() === hoaId.toString()) {
                hasAccess = true;
                break;
              }
            }

            if (hasAccess) break;
          }

          if (hasAccess) {
            console.log('Admin access granted by HOA/community code match');
            return next();
          }
        }
      }

      // Check if member has access to the requested HOAs
      if (req.user.role === 'member' && allowMembers) {
        // Check if member has access to any of the requested HOAs
        if (req.user.hoaId) {
          const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
          const userHoaIdStrings = userHoaIds.map(id => id.toString());

          // Check if any of the requested HOAs match the user's HOAs
          hasAccess = hoaIds.some(hoaId => userHoaIdStrings.includes(hoaId.toString()));

          if (hasAccess) {
            console.log('Member access granted to own HOAs');
            return next();
          }
        }

        // If member has HOA codes but not HOA IDs, check by codes
        if (!hasAccess && req.user.hoaCommunityCode) {
          const codes = Array.isArray(req.user.hoaCommunityCode) ? req.user.hoaCommunityCode : [req.user.hoaCommunityCode];
          const HOA = require('../models/hoa');
          const Community = require('../models/community');

          // Check each requested HOA against user's community codes
          for (const hoaId of hoaIds) {
            // First check if it's an HOA code
            const hoa = await HOA.findById(hoaId);
            if (hoa && codes.includes(hoa.hoaCommunityCode)) {
              hasAccess = true;
              break;
            }

            // Also check if any of the user's codes match communities under this HOA
            for (const code of codes) {
              const community = await Community.findOne({ communityCode: code }).populate('hoaId');
              if (community && community.hoaId && community.hoaId._id.toString() === hoaId.toString()) {
                hasAccess = true;
                break;
              }
            }

            if (hasAccess) break;
          }

          if (hasAccess) {
            console.log('Member access granted by HOA/community code match');
            return next();
          }
        }
      }

      // If we get here, access is denied
      console.log('HOA access denied for user:', req.user._id, 'role:', req.user.role, 'to HOAs:', hoaIds);
      return res.status(403).json({ error: 'Access denied. You do not have permission to access these HOAs.' });
    } catch (error) {
      console.error('Error in HOA access middleware:', error);
      return res.status(500).json({ error: 'Server error in access control' });
    }
  };
};

module.exports = hoaAccess;

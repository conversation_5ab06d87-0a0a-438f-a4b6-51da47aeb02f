/**
 * Global error handling middleware
 */

// Error response structure
const errorResponse = (message, statusCode, errors = null) => {
  const response = {
    success: false,
    message,
    statusCode
  };

  if (errors) {
    response.errors = errors;
  }

  return response;
};

// Custom error class
class ApiError extends Error {
  constructor(message, statusCode, errors = null) {
    super(message);
    this.statusCode = statusCode;
    this.errors = errors;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

// Not found error handler - for routes that don't exist
const notFound = (req, res, next) => {
  const error = new ApiError(`Not Found - ${req.originalUrl}`, 404);
  next(error);
};

// Global error handler
const errorHandler = (err, req, res, next) => {
  // Log the error for debugging
  console.error('Error:', {
    name: err.name,
    message: err.message,
    path: req.path,
    method: req.method,
    statusCode: err.statusCode || 500,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });

  // Default to 500 server error if statusCode is not set
  const statusCode = err.statusCode || 500;
  
  // Send the error response
  res.status(statusCode).json(
    errorResponse(
      err.message || 'Server Error',
      statusCode,
      err.errors
    )
  );
};

// Mongoose validation error handler
const handleValidationError = (err, req, res, next) => {
  if (err.name === 'ValidationError') {
    const errors = Object.values(err.errors).map(val => val.message);
    return res.status(400).json(
      errorResponse('Validation Error', 400, errors)
    );
  }
  next(err);
};

// Mongoose cast error handler (invalid ObjectId)
const handleCastError = (err, req, res, next) => {
  if (err.name === 'CastError') {
    return res.status(400).json(
      errorResponse(`Invalid ${err.path}: ${err.value}`, 400)
    );
  }
  next(err);
};

// Mongoose duplicate key error handler
const handleDuplicateKeyError = (err, req, res, next) => {
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    return res.status(400).json(
      errorResponse(`Duplicate value for ${field}: ${err.keyValue[field]}`, 400)
    );
  }
  next(err);
};

module.exports = {
  ApiError,
  notFound,
  errorHandler,
  handleValidationError,
  handleCastError,
  handleDuplicateKeyError
};

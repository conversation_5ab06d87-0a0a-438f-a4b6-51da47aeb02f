/**
 * Middleware to check if a user has access to a specific community
 * This middleware can be used to enforce community isolation
 */
const Community = require('../models/community');

/**
 * Community access middleware
 * @param {Object} options - Configuration options
 * @param {boolean} options.allowSameHoaAdmins - Allow admins from the same HOA to access
 * @param {boolean} options.allowMembers - Allow members to access
 * @param {boolean} options.requireCommunityId - Require communityId in request
 * @param {boolean} options.checkPermissions - Check community-specific permissions
 * @param {string} options.permissionType - Type of permission to check (memberVisibility, financeVisibility, documentVisibility)
 * @returns {Function} Express middleware
 */
const communityAccess = (options = {}) => {
  const {
    allowSameHoaAdmins = true,
    allowMembers = false,
    requireCommunityId = false,
    checkPermissions = false,
    permissionType = null
  } = options;

  return async (req, res, next) => {
    try {
      // Company admins always have access to all communities
      if (req.user.role === 'company_admin') {
        return next();
      }

      // Get communityId from request parameters, query, or body
      let communityId = req.params.communityId ||
                        req.query.communityId ||
                        req.body.communityId;

      // If no communityId is provided but it's required, return error
      if (requireCommunityId && !communityId) {
        return res.status(400).json({
          success: false,
          message: 'Community ID is required'
        });
      }

      // If no communityId is provided and it's not required, proceed
      if (!communityId) {
        // For admins without a specific community, check if they have access to the HOA
        if (req.user.role === 'admin') {
          // If hoaId is provided in the request, check if admin has access to this HOA
          const hoaId = req.params.hoaId || req.query.hoaId || req.body.hoaId;

          if (hoaId && req.user.hoaId && hoaId !== req.user.hoaId.toString()) {
            return res.status(403).json({
              success: false,
              message: 'You do not have access to this HOA'
            });
          }
        }

        return next();
      }

      // Get the community
      const community = await Community.findById(communityId);

      if (!community) {
        return res.status(404).json({
          success: false,
          message: 'Community not found'
        });
      }

      // Check if user has access to this community
      const userCommunityId = req.user.communityId ? req.user.communityId.toString() : null;
      const userHoaId = req.user.hoaId ? req.user.hoaId.toString() : null;
      const communityHoaId = community.hoaId ? community.hoaId.toString() : null;

      // Log access attempt for debugging
      console.log('Community access check:', {
        userId: req.user._id,
        username: req.user.username,
        userRole: req.user.role,
        userCommunityId,
        userHoaId,
        requestedCommunityId: communityId,
        communityHoaId,
        communityName: community.name
      });

      // User is directly associated with this community
      if (userCommunityId === communityId) {
        console.log('Access granted: User is directly associated with this community');
        return next();
      }

      // User is an admin of the HOA that owns this community
      if (allowSameHoaAdmins &&
          req.user.role === 'admin' &&
          userHoaId &&
          communityHoaId &&
          userHoaId === communityHoaId) {
        console.log('Access granted: User is an admin of the HOA that owns this community');
        return next();
      }

      // Check if user has access via HOA community code
      if (allowSameHoaAdmins &&
          req.user.role === 'admin' &&
          req.user.hoaCommunityCode &&
          community.communityCode &&
          community.communityCode.startsWith(req.user.hoaCommunityCode)) {
        console.log('Access granted: User is an admin with matching HOA community code');
        return next();
      }

      // Check community-specific permissions if required
      if (checkPermissions && permissionType && community.permissions) {
        // For members, check if they have permission to view this type of content
        if (req.user.role === 'member') {
          // Check if the permission exists and is true
          const hasPermission = community.permissions[permissionType];

          if (!hasPermission) {
            return res.status(403).json({
              success: false,
              message: `You do not have permission to access ${permissionType.replace('Visibility', '')} in this community`
            });
          }
        }
      }

      // User is a member of the HOA but not this community
      if (req.user.role === 'member') {
        // If members are allowed and they have permission, they can access
        if (allowMembers && (!checkPermissions || (checkPermissions && permissionType && community.permissions && community.permissions[permissionType]))) {
          console.log('Access granted: Member is allowed to access this community');
          return next();
        } else {
          console.log('Access denied: Members can only access their own community');
          return res.status(403).json({
            success: false,
            message: 'Members can only access their own community'
          });
        }
      }

      // If we get here, user doesn't have access
      return res.status(403).json({
        success: false,
        message: 'You do not have access to this community'
      });
    } catch (error) {
      console.error('Community access middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Server error',
        error: error.message
      });
    }
  };
};

module.exports = communityAccess;

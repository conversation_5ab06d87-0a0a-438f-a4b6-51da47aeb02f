/**
 * Security middleware for the API
 */

/**
 * Rate limiting middleware to prevent abuse
 * Simple implementation - for production, consider using a more robust solution like express-rate-limit
 * @param {Object} options - Rate limiting options
 */
const rateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // limit each IP to 100 requests per windowMs
    message = 'Too many requests, please try again later.'
  } = options;

  const requests = new Map();

  // Clean up old requests periodically
  setInterval(() => {
    const now = Date.now();
    requests.forEach((timestamps, ip) => {
      const filtered = timestamps.filter(timestamp => now - timestamp < windowMs);
      if (filtered.length === 0) {
        requests.delete(ip);
      } else {
        requests.set(ip, filtered);
      }
    });
  }, windowMs);

  return (req, res, next) => {
    const ip = req.ip || req.connection.remoteAddress;

    if (!requests.has(ip)) {
      requests.set(ip, []);
    }

    const timestamps = requests.get(ip);
    const now = Date.now();

    // Filter out timestamps outside the current window
    const recentTimestamps = timestamps.filter(timestamp => now - timestamp < windowMs);

    if (recentTimestamps.length >= max) {
      return res.status(429).json({
        success: false,
        message,
        statusCode: 429
      });
    }

    // Add current timestamp
    recentTimestamps.push(now);
    requests.set(ip, recentTimestamps);

    next();
  };
};

/**
 * Add security headers to responses
 */
const securityHeaders = (req, res, next) => {
  // Prevent browsers from incorrectly detecting non-scripts as scripts
  res.setHeader('X-Content-Type-Options', 'nosniff');

  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');

  // XSS Protection
  res.setHeader('X-XSS-Protection', '1; mode=block');

  // Strict Transport Security
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }

  // Content Security Policy - using Report-Only mode initially to avoid breaking existing functionality
  // TODO: After confirming no issues, switch to enforced mode
  const cspDirectives = [
    "default-src 'self'",
    "img-src 'self' data: blob: https: *", // More permissive for images
    "style-src 'self' 'unsafe-inline' https: *", // More permissive for styles
    "font-src 'self' https: data: *", // More permissive for fonts
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https: *", // More permissive for scripts
    "connect-src 'self' https: wss: *", // More permissive for API connections
    "frame-src 'self' https: *", // More permissive for frames
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self' https: *" // More permissive for forms
  ];

  // Always use Report-Only mode initially to avoid breaking functionality
  // After confirming no issues in production, you can switch to enforced mode
  res.setHeader('Content-Security-Policy-Report-Only', cspDirectives.join('; '));

  // Referrer Policy - using a more permissive policy initially
  res.setHeader('Referrer-Policy', 'no-referrer-when-downgrade');

  // Permissions Policy - only applying minimal restrictions
  res.setHeader('Permissions-Policy', 'interest-cohort=()');

  next();
};

module.exports = {
  rateLimit,
  securityHeaders
};

/**
 * Account lockout middleware to prevent brute force attacks
 */
const User = require('../models/user');

// In-memory store for failed login attempts
// In production, this should be replaced with Redis or a database
const loginAttempts = new Map();

// Clean up old attempts periodically (every hour)
setInterval(() => {
  const now = Date.now();
  loginAttempts.forEach((data, key) => {
    // Remove attempts older than 24 hours
    if (now - data.lastAttempt > 24 * 60 * 60 * 1000) {
      loginAttempts.delete(key);
    }
  });
}, 60 * 60 * 1000);

/**
 * Record a failed login attempt
 * @param {string} identifier - Username or email
 */
const recordFailedAttempt = (identifier) => {
  const normalizedIdentifier = identifier.toLowerCase();

  if (!loginAttempts.has(normalizedIdentifier)) {
    loginAttempts.set(normalizedIdentifier, {
      count: 1,
      lastAttempt: Date.now(),
      lockedUntil: null
    });
    return;
  }

  const data = loginAttempts.get(normalizedIdentifier);
  data.count += 1;
  data.lastAttempt = Date.now();

  // Initially more lenient: Lock account after 10 failed attempts for 5 minutes
  // TODO: Adjust to stricter settings (5 attempts, 15 minutes) after initial deployment period
  if (data.count >= 10) {
    data.lockedUntil = Date.now() + (5 * 60 * 1000); // 5 minutes
  }

  loginAttempts.set(normalizedIdentifier, data);
};

/**
 * Reset failed login attempts after successful login
 * @param {string} identifier - Username or email
 */
const resetFailedAttempts = (identifier) => {
  const normalizedIdentifier = identifier.toLowerCase();
  loginAttempts.delete(normalizedIdentifier);
};

/**
 * Check if an account is locked
 * @param {string} identifier - Username or email
 * @returns {Object} - Lock status and time remaining
 */
const checkLockStatus = (identifier) => {
  const normalizedIdentifier = identifier.toLowerCase();

  if (!loginAttempts.has(normalizedIdentifier)) {
    return { isLocked: false };
  }

  const data = loginAttempts.get(normalizedIdentifier);

  // If not locked or lock period has expired
  if (!data.lockedUntil || Date.now() > data.lockedUntil) {
    // If lock expired, reset the counter but keep the record
    if (data.lockedUntil && Date.now() > data.lockedUntil) {
      data.count = 0;
      data.lockedUntil = null;
      loginAttempts.set(normalizedIdentifier, data);
    }
    return { isLocked: false };
  }

  // Account is locked
  const remainingTime = Math.ceil((data.lockedUntil - Date.now()) / 1000 / 60); // in minutes
  return {
    isLocked: true,
    remainingTime,
    attemptsCount: data.count
  };
};

module.exports = {
  recordFailedAttempt,
  resetFailedAttempts,
  checkLockStatus
};

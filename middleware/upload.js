/**
 * Updated Upload Middleware for S3 Integration
 * Maintains backward compatibility while using cloud storage
 */

const { storageConfigs } = require('../config/s3Config');

// Export the pre-configured S3 storage for user documents (profile photos, verification docs)
// This maintains the same interface as the original upload middleware
const upload = storageConfigs.userDocuments;

// Export additional storage configurations for different use cases
module.exports = upload;
module.exports.userDocuments = storageConfigs.userDocuments;
module.exports.hoaDocuments = storageConfigs.hoaDocuments;
module.exports.documents = storageConfigs.documents;
module.exports.taskAttachments = storageConfigs.taskAttachments;
module.exports.financeDocuments = storageConfigs.financeDocuments;
module.exports.profilePhotos = storageConfigs.profilePhotos;
module.exports.announcementAttachments = storageConfigs.announcementAttachments;

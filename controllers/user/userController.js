// controllers/userController.js
const User = require('../../models/user');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const Notification = require('../../models/notification');
const Task = require('../../models/task');
const Payment = require('../../models/payment');

exports.deleteProfilePhoto = async (req, res) => {
  try {
    console.log('Delete profile photo request received');
    const userId = req.user._id;
    console.log('User ID:', userId);

    const user = await User.findById(userId);
    console.log('User found:', user ? 'Yes' : 'No');

    if (!user) {
      console.log('User not found for ID:', userId);
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('Current profile photo:', user.profilePhoto);

    // Delete the photo file if it exists
    if (user.profilePhoto) {
      const photoPath = path.join(__dirname, '../uploads', user.profilePhoto);
      console.log('Photo path to delete:', photoPath);

      try {
        if (fs.existsSync(photoPath)) {
          console.log('Photo file exists, deleting...');
          fs.unlinkSync(photoPath);
          console.log('Photo file deleted successfully');
        } else {
          console.log('Photo file does not exist on disk');
        }
      } catch (err) {
        console.error('Error deleting photo file:', err);
        // Continue even if file deletion fails
      }

      // Remove the photo reference from the user document
      console.log('Removing photo reference from user document');
      user.profilePhoto = null;
      await user.save();
      console.log('User document updated successfully');

      // Return success response with consistent format
      console.log('Sending success response');
      res.json({
        message: 'Profile photo deleted successfully',
        user: {
          id: user._id,
          email: user.email,
          username: user.username,
          fullName: user.fullName,
          role: user.role,
          profilePhoto: null
        }
      });
    } else {
      console.log('No profile photo found for user');
      res.status(404).json({ message: 'No profile photo found' });
    }
  } catch (err) {
    console.error('Error deleting profile photo:', err);
    console.error('Error stack:', err.stack);
    res.status(500).json({ message: 'Failed to delete profile photo' });
  }
};

exports.updateSettings = async (req, res) => {
  try {
    const userId = req.user._id;
    const { email, password, notifications, deletePhoto } = req.body;
    let updateData = {};

    // Update email if provided
    if (email) updateData.email = email;

    // Update password if provided
    if (password) {
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);
      updateData.password = hashedPassword;
    }

    // Update notification settings if provided
    if (notifications !== undefined) updateData.notifications = notifications;

    // Get current user to handle photo operations
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Handle profile photo deletion if requested
    if (deletePhoto === true && user.profilePhoto) {
      const oldPhotoPath = path.join(__dirname, '../uploads', user.profilePhoto);
      if (fs.existsSync(oldPhotoPath)) {
        fs.unlinkSync(oldPhotoPath);
      }
      updateData.profilePhoto = null;
    }
    // Handle new profile photo upload
    else if (req.file) {
      // Delete old profile photo if it exists
      if (user.profilePhoto) {
        const oldPhotoPath = path.join(__dirname, '../uploads', user.profilePhoto);
        if (fs.existsSync(oldPhotoPath)) {
          fs.unlinkSync(oldPhotoPath);
        }
      }

      // Update with new profile photo
      updateData.profilePhoto = req.file.filename;
    }

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true }
    );

    // Construct profile photo URL with proper encoding
    let profilePhotoUrl = null;
    if (updatedUser.profilePhoto) {
      // Get the current API URL from environment or request
      const currentApiUrl = process.env.API_URL ||
                           `${req.protocol}://${req.get('host')}`;
      console.log('Current API URL for profile photo:', currentApiUrl);

      // Encode the filename part to handle spaces and special characters
      profilePhotoUrl = `${currentApiUrl}/uploads/${encodeURIComponent(updatedUser.profilePhoto)}`;
      console.log('Encoded profile photo URL:', profilePhotoUrl);
    }

    res.json({
      message: 'Settings updated successfully',
      user: {
        id: updatedUser._id,
        email: updatedUser.email,
        username: updatedUser.username,
        fullName: updatedUser.fullName,
        role: updatedUser.role,
        profilePhoto: profilePhotoUrl
      }
    });
  } catch (err) {
    console.error('Error updating settings:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all users (admin and company_admin)
exports.getAllUsers = async (req, res) => {
  try {
    // Check if user is admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    // Build query based on user role
    let query = {};

    // Company admins can see all users
    if (req.user.role === 'company_admin') {
      // No additional filters
    }
    // Regular admins can't see company_admin users
    else if (req.user.role === 'admin') {
      query.role = { $ne: 'company_admin' };

      // If admin has specific HOAs, only show users from those HOAs
      if (req.user.hoaId && req.user.hoaId.length > 0) {
        query.hoaId = { $in: req.user.hoaId };
      }
    }

    const users = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 });

    // Add profile photo URLs with proper encoding
    const usersWithPhotos = users.map(user => {
      const userData = user.toObject();
      if (userData.profilePhoto) {
        // Encode the filename part to handle spaces and special characters
        userData.profilePhoto = `${process.env.API_URL}/uploads/${encodeURIComponent(userData.profilePhoto)}`;
      }
      return userData;
    });

    res.json(usersWithPhotos);
  } catch (err) {
    console.error('Error fetching users:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get user by ID (admin and company_admin)
exports.getUserById = async (req, res) => {
  try {
    // Check if user is admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const user = await User.findById(req.params.id).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Add profile photo URL with proper encoding
    const userData = user.toObject();
    if (userData.profilePhoto) {
      // Encode the filename part to handle spaces and special characters
      userData.profilePhoto = `${process.env.API_URL}/uploads/${encodeURIComponent(userData.profilePhoto)}`;
    }

    res.json(userData);
  } catch (err) {
    console.error('Error fetching user:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update user (admin and company_admin)
exports.updateUser = async (req, res) => {
  try {
    // Check if user is admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const { username, email, fullName, propertyAddress, role } = req.body;
    const updateData = {};

    // Only update fields that are provided
    if (username) updateData.username = username;
    if (email) updateData.email = email;
    if (fullName) updateData.fullName = fullName;
    if (propertyAddress) updateData.propertyAddress = propertyAddress;
    if (role) updateData.role = role;

    // If changing to approved member role, update approval status
    if (role === 'member') {
      updateData.isApproved = true;
      updateData.denied = false;
    } else if (role === 'denied') {
      updateData.isApproved = false;
      updateData.denied = true;
    }

    const updatedUser = await User.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    ).select('-password');

    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Add profile photo URL with proper encoding
    const userData = updatedUser.toObject();
    if (userData.profilePhoto) {
      // Encode the filename part to handle spaces and special characters
      userData.profilePhoto = `${process.env.API_URL}/uploads/${encodeURIComponent(userData.profilePhoto)}`;
    }

    res.json({
      message: 'User updated successfully',
      user: userData
    });
  } catch (err) {
    console.error('Error updating user:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete user (admin and company_admin)
// Set password for first-time login users
exports.setPassword = async (req, res) => {
  try {
    const userId = req.user._id;
    const { newPassword } = req.body;

    if (!newPassword) {
      return res.status(400).json({ message: 'New password is required' });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ message: 'Password must be at least 6 characters long' });
    }

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('Setting new password for user:', user.username);

    // Update the password
    user.password = newPassword;

    // Clear the temporary password and reset flag
    user.tempPassword = undefined;
    user.passwordResetRequired = false;

    await user.save();

    console.log('Password updated successfully for user:', user.username);

    res.json({
      message: 'Password set successfully',
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        passwordResetRequired: false
      }
    });
  } catch (err) {
    console.error('Error setting password:', err);
    res.status(500).json({ message: 'Failed to set password' });
  }
};

exports.deleteUser = async (req, res) => {
  try {
    // Check if user is admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    // Don't allow admins to delete themselves
    if (req.params.id === req.user._id.toString()) {
      return res.status(400).json({ message: 'You cannot delete your own account' });
    }

    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Delete user's profile photo if it exists
    if (user.profilePhoto) {
      const photoPath = path.join(__dirname, '../uploads', user.profilePhoto);
      try {
        if (fs.existsSync(photoPath)) {
          fs.unlinkSync(photoPath);
        }
      } catch (err) {
        console.error('Error deleting photo file:', err);
        // Continue even if file deletion fails
      }
    }

    // Delete user's license photos if they exist
    if (user.licenseFront) {
      const frontPath = path.join(__dirname, '../uploads', user.licenseFront);
      try {
        if (fs.existsSync(frontPath)) {
          fs.unlinkSync(frontPath);
        }
      } catch (err) {
        console.error('Error deleting license front file:', err);
      }
    }

    if (user.licenseBack) {
      const backPath = path.join(__dirname, '../uploads', user.licenseBack);
      try {
        if (fs.existsSync(backPath)) {
          fs.unlinkSync(backPath);
        }
      } catch (err) {
        console.error('Error deleting license back file:', err);
      }
    }

    // Delete user's related data
    try {
      // 1. Delete notifications for this user
      await Notification.deleteMany({
        'recipients.userId': req.params.id
      });
    } catch (notifError) {
      console.error('Error deleting notifications:', notifError);
      // Continue with other deletions
    }

    try {
      // 2. Delete tasks assigned to this user
      await Task.updateMany(
        { assignedTo: req.params.id },
        { $set: { assignedTo: null } }
      );
    } catch (taskError) {
      console.error('Error updating tasks:', taskError);
      // Continue with other deletions
    }

    try {
      // 3. Delete payments made by this user
      await Payment.deleteMany({ userId: req.params.id });
    } catch (paymentError) {
      console.error('Error deleting payments:', paymentError);
      // Continue with deletion
    }

    // Finally, delete the user
    try {
      const deletedUser = await User.findByIdAndDelete(req.params.id);
      if (!deletedUser) {
        return res.status(404).json({ message: 'User not found or already deleted' });
      }

      res.json({ message: 'User deleted successfully' });
    } catch (deleteError) {
      console.error('Error deleting user document:', deleteError);
      return res.status(500).json({ message: 'Failed to delete user' });
    }
  } catch (err) {
    console.error('Error deleting user:', err);
    res.status(500).json({ message: 'Server error' });
  }
};
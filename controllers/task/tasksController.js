const Task = require('../../models/task');
const User = require('../../models/user');
const HOA = require('../../models/hoa');
const Community = require('../../models/community');
const notificationController = require('../notification/notificationController');

// GET all tasks
exports.getAllTasks = async (req, res) => {
  try {
    // Get hoaId and communityId from query or user
    let { hoaId, communityId } = req.query;

    // Handle case where frontend sends [object Object]
    if (hoaId === '[object Object]' || hoaId?.toString() === '[object Object]') {
      console.log('Frontend sent [object Object] for hoaId, ignoring...');
      hoaId = null;
    }

    // Build query based on filters
    const query = {};

    // For company_admin, use the provided hoaId and communityId filters if any
    if (req.user.role === 'company_admin') {
      if (hoaId) {
        // Handle comma-separated HOA IDs
        let hoaIds = [];
        if (typeof hoaId === 'string' && hoaId.includes(',')) {
          hoaIds = hoaId.split(',').map(id => id.trim()).filter(id => id.length > 0);
        } else if (typeof hoaId === 'string') {
          hoaIds = [hoaId];
        }

        if (hoaIds.length > 0) {
          console.log(`Company admin filtering tasks by HOA IDs: ${hoaIds.join(', ')}`);

          // Create a query that includes HOA-wide tasks and community-specific tasks
          const orConditions = [{ hoaId: { $in: hoaIds }, communityId: { $exists: false } }];

          // If communityId is provided, include tasks for that community
          if (communityId) {
            console.log(`Company admin filtering tasks by Community ID: ${communityId}`);
            orConditions.push({ communityId: communityId });
          }

          query.$or = orConditions;
        }
      } else if (communityId) {
        // If only communityId is provided, get the HOA from the community
        console.log(`Company admin filtering tasks by Community ID only: ${communityId}`);
        try {
          const community = await Community.findById(communityId);
          if (community && community.hoaId) {
            const orConditions = [
              { hoaId: community.hoaId, communityId: { $exists: false } }, // HOA-wide tasks
              { communityId: communityId } // Community-specific tasks
            ];
            query.$or = orConditions;
          }
        } catch (err) {
          console.error('Error finding community for company admin:', err);
        }
      }
    }
    // For regular admin, restrict to their HOA communities if they have them
    else if (req.user.role === 'admin') {
      // Check if admin has hoaCommunityCode array (multiple communities)
      if (req.user.hoaCommunityCode && Array.isArray(req.user.hoaCommunityCode)) {
        console.log(`Admin restricted to HOA community codes: ${req.user.hoaCommunityCode.join(', ')}`);

        // Get HOA IDs from community codes
        const hoas = await HOA.find({ hoaCommunityCode: { $in: req.user.hoaCommunityCode } });
        const userHoaIds = hoas.map(hoa => hoa._id.toString());

        if (userHoaIds.length > 0) {
          console.log(`Admin HOA IDs: ${userHoaIds.join(', ')}`);
          console.log(`🔍 DEBUG: About to check community filtering logic - v4 (FIXED)`);

          // If communityId is provided in query, only show tasks for that specific community
          if (communityId) {
            console.log(`Admin filtering tasks by Community ID: ${communityId}`);
            try {
              // Find the community to get its hoaId, then search tasks by hoaId
              const community = await Community.findById(communityId);
              if (community && community.hoaId) {
                const communityHoaId = community.hoaId.toString();
                console.log(`Found community ${community.name} with HOA ID: ${communityHoaId}`);

                // Verify the community's HOA is in the admin's accessible HOAs
                if (userHoaIds.includes(communityHoaId)) {
                  console.log(`✅ Filtering tasks for specific community: ${communityHoaId}`);
                  // Only show tasks that have this specific communityId OR are HOA-wide tasks for this HOA
                  query.$or = [
                    { communityId: communityId }, // Community-specific tasks
                    { hoaId: communityHoaId, communityId: { $exists: false } } // HOA-wide tasks for this community's HOA
                  ];
                  console.log(`🔍 Final query for community-specific tasks:`, JSON.stringify(query, null, 2));
                } else {
                  console.log(`❌ Community's HOA ${communityHoaId} not in admin's accessible HOAs`);
                  // Return empty results if user doesn't have access to this community
                  query._id = { $in: [] };
                }
              } else {
                console.log(`❌ Community not found or missing hoaId for ID: ${communityId}`);
                // Return empty results if community not found
                query._id = { $in: [] };
              }
            } catch (error) {
              console.error(`Error finding community ${communityId}:`, error);
              // Return empty results on error
              query._id = { $in: [] };
            }
          } else {
            // No communityId provided - show all HOA-wide tasks for user's HOAs
            console.log(`Admin showing all HOA-wide tasks for HOAs: ${userHoaIds.join(', ')}`);
            query.$or = [{ hoaId: { $in: userHoaIds }, communityId: { $exists: false } }];
          }
        }
      }
      // Fallback to old hoaId logic for backward compatibility
      else if (req.user.hoaId) {
        hoaId = req.user.hoaId;
        console.log(`Admin restricted to HOA ID: ${hoaId}`);

        // Create a query that includes HOA-wide tasks and community-specific tasks
        const orConditions = [{ hoaId: hoaId, communityId: { $exists: false } }];

        // If admin has a communityId, include tasks for that community
        if (req.user.communityId) {
          communityId = req.user.communityId;
          console.log(`Admin restricted to Community ID: ${communityId}`);
          orConditions.push({ communityId: communityId });
        }
        // If communityId is provided in query, include tasks for that community
        else if (communityId) {
          console.log(`Admin filtering tasks by Community ID: ${communityId}`);
          orConditions.push({ communityId: communityId });
        }

        query.$or = orConditions;
      }
    }
    // For members, they can only see tasks from their own HOA communities
    else if (req.user.role === 'member') {
      // Check if member has hoaCommunityCode array (multiple communities)
      if (req.user.hoaCommunityCode && Array.isArray(req.user.hoaCommunityCode)) {
        console.log(`Member restricted to HOA community codes: ${req.user.hoaCommunityCode.join(', ')}`);

        // Get HOA IDs from community codes
        const hoas = await HOA.find({ hoaCommunityCode: { $in: req.user.hoaCommunityCode } });
        const userHoaIds = hoas.map(hoa => hoa._id.toString());

        if (userHoaIds.length > 0) {
          console.log(`Member HOA IDs: ${userHoaIds.join(', ')}`);

          // If communityId is provided in query, only show tasks for that specific community
          if (communityId) {
            console.log(`Member filtering tasks by Community ID: ${communityId}`);
            try {
              // Find the community to get its hoaId, then search tasks by hoaId
              const community = await Community.findById(communityId);
              if (community && community.hoaId) {
                const communityHoaId = community.hoaId.toString();
                console.log(`Found community ${community.name} with HOA ID: ${communityHoaId}`);

                // Verify the community's HOA is in the member's accessible HOAs
                if (userHoaIds.includes(communityHoaId)) {
                  console.log(`✅ Filtering tasks for specific community: ${communityHoaId}`);
                  // Only show tasks that have this specific communityId OR are HOA-wide tasks for this HOA
                  query.$or = [
                    { communityId: communityId }, // Community-specific tasks
                    { hoaId: communityHoaId, communityId: { $exists: false } } // HOA-wide tasks for this community's HOA
                  ];
                } else {
                  console.log(`❌ Community's HOA ${communityHoaId} not in member's accessible HOAs`);
                  // Return empty results if user doesn't have access to this community
                  query._id = { $in: [] };
                }
              } else {
                console.log(`❌ Community not found or missing hoaId for ID: ${communityId}`);
                // Return empty results if community not found
                query._id = { $in: [] };
              }
            } catch (error) {
              console.error(`Error finding community ${communityId}:`, error);
              // Return empty results on error
              query._id = { $in: [] };
            }
          } else {
            // No communityId provided - show all HOA-wide tasks for user's HOAs
            console.log(`Member showing all HOA-wide tasks for HOAs: ${userHoaIds.join(', ')}`);
            query.$or = [{ hoaId: { $in: userHoaIds }, communityId: { $exists: false } }];
          }
        } else {
          // If no HOAs found, member can't see any tasks
          return res.json({
            tasks: [],
            closed: [],
            total: 0
          });
        }
      }
      // Fallback to old hoaId logic for backward compatibility
      else if (req.user.hoaId) {
        hoaId = req.user.hoaId;
        console.log(`Member restricted to HOA ID: ${hoaId}`);

        // Create a query that includes HOA-wide tasks and community-specific tasks
        const orConditions = [{ hoaId: hoaId, communityId: { $exists: false } }];

        // If member has a communityId, include tasks for that community
        if (req.user.communityId) {
          communityId = req.user.communityId;
          console.log(`Member restricted to Community ID: ${communityId}`);
          orConditions.push({ communityId: communityId });
        }

        query.$or = orConditions;
      } else {
        // If member doesn't have an HOA, they can't see any tasks
        return res.json({
          tasks: [],
          closed: [],
          total: 0
        });
      }
    }

    console.log(`🔍 Final query for tasks:`, JSON.stringify(query, null, 2));
    const tasks = await Task.find(query)
      .sort({ createdAt: -1 })
      .populate('createdBy.userId', 'username email')
      .populate('closedBy.userId', 'username email')
      .populate('votes.voters.userId', 'username email');

    console.log(`📊 Found ${tasks.length} tasks for query`);
    if (tasks.length > 0) {
      console.log(`📋 Sample task HOA IDs:`, tasks.slice(0, 3).map(t => ({ id: t._id, hoaId: t.hoaId, communityId: t.communityId })));
    }

    // Initialize votes object for tasks that don't have it
    const processedTasks = tasks.map(task => {
      const taskObj = task.toObject();
      if (!taskObj.votes) {
        taskObj.votes = {
          up: 0,
          down: 0,
          voters: []
        };
      }
      return taskObj;
    });

    // Separate active and closed tasks
    const activeTasks = processedTasks.filter(task => !task.isArchived);
    const closedTasks = processedTasks.filter(task => task.isArchived)
      .sort((a, b) => new Date(b.closedAt) - new Date(a.closedAt));

    // Add cache-busting headers to ensure fresh data
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'ETag': `"tasks-${Date.now()}-${Math.random()}"`
    });

    res.json({
      tasks: activeTasks,
      closed: closedTasks,
      total: activeTasks.length,
      timestamp: Date.now(), // Add timestamp for cache busting
      communityFilter: communityId || null // Show which community filter was applied
    });
  } catch (err) {
    console.error('Error fetching tasks:', err);
    res.status(500).json({ error: 'Failed to fetch tasks' });
  }
};

// POST a new task
exports.createTask = async (req, res) => {
  try {
    const { title, description, priority, dueDate, budget, hoaId, communityId } = req.body;

    // Get the full user data since token only contains partial info
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Determine the correct HOA ID and Community ID based on user role and provided data
    let taskHoaId = hoaId;
    let taskCommunityId = communityId;

    // If user is admin, ensure task is created for one of their HOAs
    if (req.user.role === 'admin') {
      // Check if admin has hoaCommunityCode array (multiple communities)
      if (req.user.hoaCommunityCode && Array.isArray(req.user.hoaCommunityCode)) {
        // Get HOA IDs from community codes
        const hoas = await HOA.find({ hoaCommunityCode: { $in: req.user.hoaCommunityCode } });
        const userHoaIds = hoas.map(hoa => hoa._id.toString());

        if (userHoaIds.length === 0) {
          return res.status(403).json({
            message: 'Admin must be associated with at least one HOA to create tasks'
          });
        }

        // If no hoaId provided, use the first HOA from admin's list
        if (!taskHoaId) {
          taskHoaId = userHoaIds[0];
        } else {
          // Verify the provided hoaId is one of the admin's HOAs
          if (!userHoaIds.includes(taskHoaId)) {
            return res.status(403).json({
              message: 'Admin can only create tasks for their assigned HOAs'
            });
          }
        }

        // If communityId is provided, verify it belongs to one of the admin's HOAs
        if (taskCommunityId) {
          const community = await Community.findById(taskCommunityId);
          if (!community || !userHoaIds.includes(community.hoaId.toString())) {
            return res.status(403).json({
              message: 'Admin can only create tasks for communities in their assigned HOAs'
            });
          }
        }
      }
      // Fallback to old hoaId logic for backward compatibility
      else if (req.user.hoaId) {
        taskHoaId = req.user.hoaId;

        // If admin has a communityId and no communityId was provided, use the admin's communityId
        if (req.user.communityId && !taskCommunityId) {
          taskCommunityId = req.user.communityId;
        }
      } else {
        return res.status(403).json({
          message: 'Admin must be associated with an HOA to create tasks'
        });
      }
    }
    // If user is member, ensure task is created for one of their HOAs
    else if (req.user.role === 'member') {
      // Check if member has hoaCommunityCode array (multiple communities)
      if (req.user.hoaCommunityCode && Array.isArray(req.user.hoaCommunityCode)) {
        // Get HOA IDs from community codes
        const hoas = await HOA.find({ hoaCommunityCode: { $in: req.user.hoaCommunityCode } });
        const userHoaIds = hoas.map(hoa => hoa._id.toString());

        if (userHoaIds.length === 0) {
          return res.status(403).json({
            message: 'Members must be associated with at least one HOA to create tasks'
          });
        }

        // If no hoaId provided, use the first HOA from member's list
        if (!taskHoaId) {
          taskHoaId = userHoaIds[0];
        } else {
          // Verify the provided hoaId is one of the member's HOAs
          if (!userHoaIds.includes(taskHoaId)) {
            return res.status(403).json({
              message: 'Members can only create tasks for their assigned HOAs'
            });
          }
        }

        // If communityId is provided, verify it belongs to one of the member's HOAs
        if (taskCommunityId) {
          const community = await Community.findById(taskCommunityId);
          if (!community || !userHoaIds.includes(community.hoaId.toString())) {
            return res.status(403).json({
              message: 'Members can only create tasks for communities in their assigned HOAs'
            });
          }
        }
      }
      // Fallback to old hoaId logic for backward compatibility
      else if (req.user.hoaId) {
        taskHoaId = req.user.hoaId;

        // If member has a communityId, use it
        if (req.user.communityId) {
          taskCommunityId = req.user.communityId;
        }
      } else {
        return res.status(403).json({
          message: 'Members must be associated with an HOA to create tasks'
        });
      }
    }
    // For company_admin, use the provided hoaId or require one
    else if (req.user.role === 'company_admin' && !taskHoaId) {
      return res.status(400).json({
        message: 'HOA ID is required when creating a task as company admin'
      });
    }

    const task = new Task({
      title,
      description,
      priority,
      dueDate: new Date(dueDate),
      budget,
      hoaId: taskHoaId,
      communityId: taskCommunityId,
      createdBy: {
        userId: req.user._id,
        timestamp: new Date()
      },
      permissions: {
        canEdit: [req.user._id],
        canDelete: [req.user._id]
      }
    });

    const savedTask = await task.save();
    const populated = await Task.findById(savedTask._id)
      .populate('createdBy.userId', 'username email');

    // Get all users to notify
    const users = await User.find({ isApproved: true, denied: false });
    const userIds = users.map(user => user._id);

    // Create task creation notification
    await notificationController.createTaskNotification(populated, userIds, 'created');

    res.status(201).json(populated);
  } catch (err) {
    console.error('Error creating task:', err);
    res.status(500).json({ message: 'Error creating task' });
  }
};

// PUT update task
exports.updateTask = async (req, res) => {
  try {
    const task = await Task.findById(req.params.id)
      .populate('createdBy.userId', 'username email');

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Check HOA-specific access for admin and member roles
    if (req.user.role === 'admin' || req.user.role === 'member') {
      let userHoaIds = [];

      // Check if user has hoaCommunityCode array (multiple communities)
      if (req.user.hoaCommunityCode && Array.isArray(req.user.hoaCommunityCode)) {
        // Get HOA IDs from community codes
        const hoas = await HOA.find({ hoaCommunityCode: { $in: req.user.hoaCommunityCode } });
        userHoaIds = hoas.map(hoa => hoa._id.toString());
      }
      // Fallback to old hoaId logic for backward compatibility
      else if (req.user.hoaId) {
        userHoaIds = [req.user.hoaId.toString()];
      }

      // Check if user has access to the task's HOA
      if (userHoaIds.length > 0 && task.hoaId && !userHoaIds.includes(task.hoaId.toString())) {
        return res.status(403).json({
          message: `You do not have permission to edit tasks from other HOAs`
        });
      }
    }

    // Check if user has permission to edit
    const canEdit =
      req.user.role === 'company_admin' || // Company admins can edit any task
      (req.user.role === 'admin') || // Admins can edit tasks in their HOA (checked above)
      task.permissions.canEdit.some(id => id.toString() === req.user._id.toString()) ||
      (task.createdBy.userId && task.createdBy.userId._id &&
       task.createdBy.userId._id.toString() === req.user._id.toString());

    if (!canEdit) {
      return res.status(403).json({ message: 'You do not have permission to edit this task' });
    }

    // Don't allow changing the hoaId if admin is HOA-specific
    if (req.user.role === 'admin' && req.user.hoaId && req.body.hoaId) {
      req.body.hoaId = req.user.hoaId;
    }

    // Don't allow members to change the hoaId
    if (req.user.role === 'member' && req.body.hoaId) {
      req.body.hoaId = task.hoaId;
    }

    const oldStatus = task.status;
    const updated = await Task.findByIdAndUpdate(
      req.params.id,
      { ...req.body },
      { new: true }
    ).populate('createdBy.userId', 'username email');

    // If status changed to completed, send notification
    if (oldStatus !== 'Completed' && updated.status === 'Completed') {
      const users = await User.find({ isApproved: true, denied: false });
      const userIds = users.map(user => user._id);
      await notificationController.createTaskNotification(updated, userIds, 'completed');
    }

    res.json(updated);
  } catch (err) {
    console.error('Error updating task:', err);
    res.status(500).json({ message: 'Error updating task' });
  }
};

// DELETE a task
exports.deleteTask = async (req, res) => {
  try {
    const task = await Task.findById(req.params.id)
      .populate('createdBy.userId', 'username email');

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Check HOA-specific access
    if (req.user.role === 'admin' && req.user.hoaId &&
        task.hoaId && task.hoaId.toString() !== req.user.hoaId.toString()) {
      return res.status(403).json({
        message: 'You do not have permission to delete tasks from other HOAs'
      });
    }

    if (req.user.role === 'member' && req.user.hoaId &&
        task.hoaId && task.hoaId.toString() !== req.user.hoaId.toString()) {
      return res.status(403).json({
        message: 'You do not have permission to delete tasks from other HOAs'
      });
    }

    // Check if user has permission to delete
    const canDelete =
      req.user.role === 'company_admin' || // Company admins can delete any task
      (req.user.role === 'admin') || // Admins can delete tasks in their HOA (checked above)
      (task.createdBy.userId && task.createdBy.userId._id &&
       task.createdBy.userId._id.toString() === req.user._id.toString()) ||
      (task.permissions?.canDelete?.some(id => id.toString() === req.user._id.toString()));

    if (!canDelete) {
      return res.status(403).json({ message: 'You do not have permission to delete this task' });
    }

    // Get all users to notify
    const users = await User.find({ isApproved: true, denied: false });
    const userIds = users.map(user => user._id);

    // Create task deletion notification
    await notificationController.createTaskNotification(task, userIds, 'deleted', {
      title: 'Task Deleted',
      description: `Task "${task.title}" has been deleted by ${req.user.username}`
    });

    // Use findByIdAndDelete instead of the deprecated remove() method
    await Task.findByIdAndDelete(req.params.id);
    res.json({ message: 'Task deleted successfully' });
  } catch (err) {
    console.error('Error deleting task:', err);
    console.error('Error details:', err.stack);
    res.status(500).json({
      message: 'Error deleting task',
      error: err.message,
      details: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
};

// Vote on a task
exports.voteTask = async (req, res) => {
  try {
    const taskId = req.params.id;
    const { direction } = req.body;
    const userId = req.user._id;

    console.log('Vote request:', { taskId, direction, userId });

    if (!['up', 'down'].includes(direction)) {
      return res.status(400).json({ message: 'Invalid vote direction' });
    }

    // Find the task and get the current state
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Initialize votes if they don't exist
    if (!task.votes) {
      await Task.findByIdAndUpdate(taskId, {
        $set: { votes: { up: 0, down: 0, voters: [] } }
      });
    }

    // Find if the user has already voted
    const existingVote = task.votes?.voters?.find(
      vote => vote.userId.toString() === userId.toString()
    );

    let updateQuery = {};

    if (existingVote) {
      if (existingVote.vote === direction) {
        // Remove vote if clicking the same direction
        updateQuery = {
          $inc: { [`votes.${direction}`]: -1 },
          $pull: { 'votes.voters': { userId: userId } }
        };
      } else {
        // Switch vote if clicking different direction
        updateQuery = {
          $inc: {
            [`votes.${existingVote.vote}`]: -1,
            [`votes.${direction}`]: 1
          },
          $set: { 'votes.voters.$[voter].vote': direction }
        };
      }
    } else {
      // Add new vote if no existing vote
      updateQuery = {
        $inc: { [`votes.${direction}`]: 1 },
        $push: { 'votes.voters': { userId, vote: direction } }
      };
    }

    // Apply the update atomically and get the updated document
    const updatedTask = await Task.findOneAndUpdate(
      { _id: taskId },
      updateQuery,
      {
        new: true,
        arrayFilters: existingVote ? [{ 'voter.userId': userId }] : undefined,
        runValidators: true
      }
    ).populate('createdBy.userId', 'username email')
      .populate('votes.voters.userId', 'username email');

    // Ensure vote counts don't go below 0
    if (updatedTask.votes.up < 0 || updatedTask.votes.down < 0) {
      await Task.findByIdAndUpdate(taskId, {
        $set: {
          'votes.up': Math.max(0, updatedTask.votes.up),
          'votes.down': Math.max(0, updatedTask.votes.down)
        }
      });
    }

    res.json(updatedTask);
  } catch (error) {
    console.error('Vote error:', error);
    res.status(500).json({ message: 'Error processing vote', error: error.message });
  }
};

// Check for tasks due soon and send notifications
exports.checkTasksDueSoon = async () => {
  try {
    // Delegate to notificationController's implementation
    await notificationController.checkTasksDueSoon();
  } catch (err) {
    console.error('Error checking tasks due soon:', err);
  }
};

// Close a task
exports.closeTask = async (req, res) => {
  try {
    console.log('Attempting to close task:', req.params.id);
    console.log('User making request:', req.user);

    const task = await Task.findById(req.params.id)
      .populate('createdBy.userId', 'username email');

    if (!task) {
      console.log('Task not found');
      return res.status(404).json({ message: 'Task not found' });
    }

    // Check HOA-specific access
    if (req.user.role === 'admin' && req.user.hoaId &&
        task.hoaId && task.hoaId.toString() !== req.user.hoaId.toString()) {
      console.log('Permission denied - Admin from different HOA');
      return res.status(403).json({
        message: 'You do not have permission to close tasks from other HOAs'
      });
    }

    // Check if user has permission to close
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      console.log('Permission denied - User role:', req.user.role);
      return res.status(403).json({ message: 'Only administrators can close tasks' });
    }

    // Check if task is already closed
    if (task.status === 'Closed') {
      console.log('Task is already closed');
      return res.status(400).json({ message: 'Task is already closed' });
    }

    // Update the task using findOneAndUpdate
    const updatedTask = await Task.findOneAndUpdate(
      { _id: req.params.id },
      {
        $set: {
          status: 'Closed',
          isArchived: true,
          closedAt: new Date(),
          'closedBy.userId': req.user._id,
          'closedBy.timestamp': new Date()
        }
      },
      {
        new: true,
        runValidators: true
      }
    ).populate([
      { path: 'createdBy.userId', select: 'username email' },
      { path: 'closedBy.userId', select: 'username email' }
    ]);

    if (!updatedTask) {
      console.log('Failed to update task');
      return res.status(500).json({ message: 'Failed to close task' });
    }

    console.log('Task updated successfully:', updatedTask);

    // Get all users to notify
    const users = await User.find({ isApproved: true, denied: false });
    const userIds = users.map(user => user._id);

    // Create closure notification
    await notificationController.createTaskNotification(updatedTask, userIds, 'closed');

    res.json(updatedTask);
  } catch (err) {
    console.error('Error closing task:', err);
    console.error('Error details:', err.stack);
    console.error('Error name:', err.name);
    console.error('Error message:', err.message);
    if (err.errors) {
      console.error('Validation errors:', err.errors);
    }
    res.status(500).json({
      message: 'Error closing task',
      error: err.message,
      details: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
};

// Update getTasks to include closed tasks in a separate array
exports.getTasks = async (req, res) => {
  try {
    const tasks = await Task.find()
      .populate('createdBy.userId', 'username email')
      .populate('closedBy.userId', 'username email')
      .sort({ createdAt: -1 });

    // Separate active and closed tasks
    const activeTasks = tasks.filter(task => !task.isArchived);
    const closedTasks = tasks.filter(task => task.isArchived)
      .sort((a, b) => b.closedAt - a.closedAt); // Sort closed tasks by closure date

    res.json({
      active: activeTasks,
      closed: closedTasks
    });
  } catch (err) {
    console.error('Error getting tasks:', err);
    res.status(500).json({ message: 'Error getting tasks' });
  }
};

// Add a comment to a task
exports.addComment = async (req, res) => {
  try {
    console.log('Adding comment - Request body:', req.body);
    console.log('Adding comment - User:', req.user);
    console.log('Adding comment - Task ID:', req.params.id);

    const { message } = req.body;
    const taskId = req.params.id;
    const userId = req.user._id;

    if (!message?.trim()) {
      console.log('Comment validation failed: Empty message');
      return res.status(400).json({ message: 'Comment message is required' });
    }

    if (!taskId) {
      console.log('Comment validation failed: No task ID');
      return res.status(400).json({ message: 'Task ID is required' });
    }

    if (!userId) {
      console.log('Comment validation failed: No user ID');
      return res.status(400).json({ message: 'User ID is required' });
    }

    // Get the user's data first
    const user = await User.findById(userId).select('username email');
    if (!user) {
      console.error('User not found:', userId);
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('Found user:', user);

    // Find the task first to verify it exists
    const task = await Task.findById(taskId);
    if (!task) {
      console.error('Task not found:', taskId);
      return res.status(404).json({ message: 'Task not found' });
    }

    console.log('Found task:', task._id);

    // Initialize comments array if it doesn't exist
    if (!task.comments) {
      task.comments = [];
    }

    // Create the new comment
    const newComment = {
      user: userId,
      username: user.username,
      message: message.trim(),
      date: new Date()
    };

    console.log('New comment object:', newComment);

    try {
      // Add the comment using atomic operation
      const updatedTask = await Task.findByIdAndUpdate(
        taskId,
        {
          $push: { comments: newComment }
        },
        {
          new: true,
          runValidators: true
        }
      ).populate([
        { path: 'createdBy.userId', select: 'username email' },
        { path: 'comments.user', select: 'username email' }
      ]);

      if (!updatedTask) {
        console.error('Failed to update task with comment');
        return res.status(500).json({ message: 'Failed to add comment to task' });
      }

      console.log('Updated task with new comment:', {
        taskId: updatedTask._id,
        commentsCount: updatedTask.comments.length,
        lastComment: updatedTask.comments[updatedTask.comments.length - 1]
      });

      // Get all users to notify about the new comment
      const users = await User.find({ isApproved: true, denied: false });
      const userIds = users.map(user => user._id);

      try {
        // Create notification for the comment with required fields
        await notificationController.createTaskNotification(
          updatedTask,
          userIds,
          'commented',
          {
            title: `New comment on task: ${task.title}`,
            description: `${user.username} commented: ${message.trim()}`
          }
        );
      } catch (notificationError) {
        // Log notification error but don't fail the comment creation
        console.error('Failed to create notification:', notificationError);
      }

      res.json(updatedTask);
    } catch (updateError) {
      console.error('Error during task update:', updateError);
      throw updateError;
    }
  } catch (err) {
    console.error('Error adding comment:', err);
    console.error('Error details:', err.stack);
    res.status(500).json({
      message: 'Error adding comment',
      error: err.message,
      details: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
};

// Delete a comment from a task
exports.deleteComment = async (req, res) => {
  try {
    const { taskId, commentId } = req.params;
    const userId = req.user._id;

    console.log('Deleting comment:', { taskId, commentId, userId });

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Find the comment
    const comment = task.comments.id(commentId);
    if (!comment) {
      return res.status(404).json({ message: 'Comment not found' });
    }

    // Check if user has permission to delete the comment
    // Allow if user is the comment author or an admin
    if (comment.user.toString() !== userId.toString() && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'You do not have permission to delete this comment' });
    }

    // Remove the comment using atomic operation
    const updatedTask = await Task.findByIdAndUpdate(
      taskId,
      {
        $pull: { comments: { _id: commentId } }
      },
      {
        new: true,
        runValidators: true
      }
    ).populate([
      { path: 'createdBy.userId', select: 'username email' },
      { path: 'comments.user', select: 'username email' }
    ]);

    if (!updatedTask) {
      return res.status(500).json({ message: 'Failed to delete comment' });
    }

    console.log('Comment deleted successfully');
    res.json(updatedTask);
  } catch (err) {
    console.error('Error deleting comment:', err);
    console.error('Error details:', err.stack);
    res.status(500).json({
      message: 'Error deleting comment',
      error: err.message,
      details: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
};

// PUT update task status
exports.updateTaskStatus = async (req, res) => {
  try {
    const { status } = req.body;
    const taskId = req.params.id;
    const userId = req.user._id;

    console.log('Updating task status:', { taskId, status, userId });

    const task = await Task.findById(taskId)
      .populate('createdBy.userId', 'username email');

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Check if user has permission to update status
    const canEdit =
      req.user.role === 'admin' ||
      task.createdBy.userId._id.toString() === userId.toString() ||
      (task.permissions?.canEdit || []).some(id => id.toString() === userId.toString());

    if (!canEdit) {
      return res.status(403).json({ message: 'You do not have permission to update this task' });
    }

    // Handle status transition
    const oldStatus = task.status;
    task.status = status;

    // Handle special status transitions
    if (status === 'Closed') {
      task.isArchived = true;
      task.closedAt = new Date();
      task.closedBy = {
        userId: userId,
        timestamp: new Date()
      };
    } else if (status === 'Completed') {
      task.completedAt = new Date();
      task.completedBy = {
        userId: userId,
        timestamp: new Date()
      };
    }

    const updated = await task.save();
    const populated = await updated.populate([
      { path: 'createdBy.userId', select: 'username email' },
      { path: 'closedBy.userId', select: 'username email' },
      { path: 'completedBy.userId', select: 'username email' }
    ]);

    // Get all users to notify about the status change
    const users = await User.find({ isApproved: true, denied: false });
    const userIds = users.map(user => user._id);

    // Create appropriate notification based on the status change
    if (status === 'Completed') {
      await notificationController.createTaskNotification(populated, userIds, 'completed');
    } else if (status === 'Closed') {
      await notificationController.createTaskNotification(populated, userIds, 'closed');
    } else if (oldStatus === 'Not Started' && status === 'In Progress') {
      await notificationController.createTaskNotification(populated, userIds, 'started');
    }

    console.log('Task status updated successfully:', populated);
    res.json(populated);
  } catch (err) {
    console.error('Error updating task status:', err);
    console.error('Error details:', err.stack);

    // Handle validation errors
    if (err.message === 'Invalid status transition') {
      return res.status(400).json({
        message: 'Invalid status transition',
        currentStatus: req.body.status,
        allowedTransitions: {
          'Not Started': ['In Progress', 'Closed'],
          'In Progress': ['Completed', 'Closed'],
          'Completed': ['Closed'],
          'Closed': []
        }
      });
    }

    res.status(500).json({ message: 'Error updating task status', error: err.message });
  }
};



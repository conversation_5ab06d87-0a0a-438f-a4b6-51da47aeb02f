/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

const User = require('../../models/user');
const { sendVerificationCode, verifyCode } = require('../../services/smsService');
const { isValidPhoneNumber, formatPhoneNumber } = require('../../utils/authUtils');
const jwt = require('jsonwebtoken');

/**
 * Enable MFA for a user
 */
exports.enableMFA = async (req, res) => {
  try {
    const userId = req.user.id;
    const { phoneNumber } = req.body;

    // Validate phone number
    if (!phoneNumber || !isValidPhoneNumber(phoneNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid phone number format. Please use a valid US phone number.'
      });
    }

    // Format phone number
    const formattedPhoneNumber = formatPhoneNumber(phoneNumber);

    // Update user with phone number and enable MFA
    const user = await User.findByIdAndUpdate(
      userId,
      { 
        phoneNumber: formattedPhoneNumber,
        'mfa.enabled': true 
      },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'MFA enabled successfully',
      data: {
        mfaEnabled: user.mfa.enabled,
        phoneNumber: user.phoneNumber
      }
    });
  } catch (error) {
    console.error('Error enabling MFA:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Disable MFA for a user
 */
exports.disableMFA = async (req, res) => {
  try {
    const userId = req.user.id;

    // Update user to disable MFA
    const user = await User.findByIdAndUpdate(
      userId,
      { 'mfa.enabled': false },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'MFA disabled successfully',
      data: {
        mfaEnabled: user.mfa.enabled
      }
    });
  } catch (error) {
    console.error('Error disabling MFA:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Send verification code to user's phone number
 */
exports.sendVerification = async (req, res) => {
  try {
    const { phoneNumber } = req.body;

    // Validate phone number
    if (!phoneNumber || !isValidPhoneNumber(phoneNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid phone number format'
      });
    }

    // Format phone number
    const formattedPhoneNumber = formatPhoneNumber(phoneNumber);

    // Check if user exists with this phone number
    const user = await User.findOne({ phoneNumber: formattedPhoneNumber });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'No user found with this phone number'
      });
    }

    // Send verification code
    const success = await sendVerificationCode(formattedPhoneNumber);
    if (!success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to send verification code'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Verification code sent successfully'
    });
  } catch (error) {
    console.error('Error sending verification code:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Verify code and complete login
 */
exports.verifyLogin = async (req, res) => {
  try {
    const { phoneNumber, code } = req.body;

    // Validate inputs
    if (!phoneNumber || !code) {
      return res.status(400).json({
        success: false,
        message: 'Phone number and verification code are required'
      });
    }

    // Format phone number
    const formattedPhoneNumber = formatPhoneNumber(phoneNumber);

    // Verify the code
    const isValid = await verifyCode(formattedPhoneNumber, code);
    if (!isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired verification code'
      });
    }

    // Get user
    const user = await User.findOne({ phoneNumber: formattedPhoneNumber });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { id: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.status(200).json({
      success: true,
      message: 'Verification successful',
      token,
      user: user.userInfo
    });
  } catch (error) {
    console.error('Error verifying code:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get MFA status for a user
 */
exports.getMFAStatus = async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        mfaEnabled: user.mfa?.enabled || false,
        phoneNumber: user.phoneNumber || null
      }
    });
  } catch (error) {
    console.error('Error getting MFA status:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

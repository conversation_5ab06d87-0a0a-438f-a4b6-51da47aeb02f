const Event = require('../../models/event');
const User = require('../../models/user');
const Community = require('../../models/community');
const HOA = require('../../models/hoa');
const notificationController = require('../notification/notificationController');

exports.getAllEvents = async (req, res) => {
  try {
    // Add cache-busting headers to ensure fresh responses
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    // Get communityId from query - handle both single and multiple IDs
    let { communityId } = req.query;
    console.log(`🔍 DEBUG: Events filtering logic - v3 (COMMUNITY-BASED WITH MULTIPLE SUPPORT)`);
    console.log(`🔍 DEBUG: Raw communityId from query: ${communityId}`);

    // Handle multiple communityIds (comma-separated)
    let communityIds = [];
    if (communityId) {
      if (typeof communityId === 'string' && communityId.includes(',')) {
        communityIds = communityId.split(',').map(id => id.trim()).filter(id => id.length > 0);
        console.log(`🔍 DEBUG: Multiple communityIds detected: ${communityIds.join(', ')}`);
      } else if (typeof communityId === 'string') {
        communityIds = [communityId];
        console.log(`🔍 DEBUG: Single communityId: ${communityId}`);
      }
    }

    // Build query based on user role and community access
    const query = {};

    // Check if user is authenticated by looking at the Authorization header
    const authHeader = req.headers.authorization;
    let user = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const jwt = require('jsonwebtoken');
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        user = decoded.user;
        console.log(`🔍 DEBUG: User authenticated - Role: ${user.role}, Community codes: ${user.hoaCommunityCode?.join(', ')}`);
      } catch (error) {
        console.log('🔍 DEBUG: Token verification failed:', error.message);
      }
    }

    // If user is authenticated, apply role-based filtering
    if (user) {
      console.log(`🔍 DEBUG: CommunityId from query: ${communityId}`);

      // For company_admin, show all events (no restrictions)
      if (user.role === 'company_admin') {
        console.log('Company admin - showing all events');
        // No additional restrictions
      }
      // For regular admin, restrict to their accessible HOAs
      else if (user.role === 'admin') {
        console.log(`Admin restricted to HOA community codes: ${user.hoaCommunityCode?.join(', ')}`);

        // Get HOA IDs from user's community codes
        const userHoaIds = [];
        if (user.hoaCommunityCode && user.hoaCommunityCode.length > 0) {
          for (const code of user.hoaCommunityCode) {
            // Check if it's an HOA code
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa) {
              userHoaIds.push(hoa._id.toString());
            }

            // Check if it's a community code
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId) {
              const hoaIdStr = community.hoaId._id.toString();
              if (!userHoaIds.includes(hoaIdStr)) {
                userHoaIds.push(hoaIdStr);
              }
            }
          }
        }

        if (userHoaIds.length > 0) {
          console.log(`Admin HOA IDs: ${userHoaIds.join(', ')}`);

          // If communityIds are provided in query, show events for those specific communities
          if (communityIds.length > 0) {
            console.log(`Admin filtering events by Community IDs: ${communityIds.join(', ')}`);

            const orConditions = [];
            const accessibleHoaIds = new Set();

            // Process each community ID
            for (const commId of communityIds) {
              try {
                const community = await Community.findById(commId);
                if (!community) {
                  console.log(`❌ Community not found: ${commId}`);
                  continue;
                }

                const communityHoaId = community.hoaId.toString();
                console.log(`Found community ${community.name} with HOA ID: ${communityHoaId}`);

                // Verify the community's HOA is in the admin's accessible HOAs
                if (userHoaIds.includes(communityHoaId)) {
                  console.log(`✅ Adding events for community: ${commId} (HOA: ${communityHoaId})`);

                  // Add community-specific events
                  orConditions.push({ communityId: commId });

                  // Track accessible HOA IDs for HOA-wide events
                  accessibleHoaIds.add(communityHoaId);
                } else {
                  console.log(`❌ Community's HOA ${communityHoaId} not in admin's accessible HOAs`);
                }
              } catch (error) {
                console.error(`Error processing community ${commId}:`, error);
              }
            }

            // Add HOA-wide events for all accessible HOAs (events that have hoaId but no communityId)
            if (accessibleHoaIds.size > 0) {
              const accessibleHoaIdsArray = Array.from(accessibleHoaIds);
              console.log(`✅ Adding HOA-wide events for HOAs: ${accessibleHoaIdsArray.join(', ')}`);
              orConditions.push({
                hoaId: { $in: accessibleHoaIdsArray },
                $or: [
                  { communityId: { $exists: false } },
                  { communityId: null }
                ]
              });
            }

            if (orConditions.length > 0) {
              query.$or = orConditions;
              console.log(`🔍 Final query for multiple communities:`, JSON.stringify(query, null, 2));
            } else {
              console.log('❌ No accessible communities found - returning empty results');
              query._id = { $in: [] };
            }
          } else {
            // No communityIds provided - show all HOA-wide events for user's HOAs
            console.log(`Admin showing all HOA-wide events for HOAs: ${userHoaIds.join(', ')}`);
            query.$or = [{
              hoaId: { $in: userHoaIds },
              $or: [
                { communityId: { $exists: false } },
                { communityId: null }
              ]
            }];
          }
        }
      }
      // For members, restrict to their accessible HOAs
      else if (user.role === 'member') {
        console.log(`Member restricted to HOA community codes: ${user.hoaCommunityCode?.join(', ')}`);

        // Get HOA IDs from user's community codes
        const userHoaIds = [];
        if (user.hoaCommunityCode && user.hoaCommunityCode.length > 0) {
          for (const code of user.hoaCommunityCode) {
            // Check if it's an HOA code
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa) {
              userHoaIds.push(hoa._id.toString());
            }

            // Check if it's a community code
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId) {
              const hoaIdStr = community.hoaId._id.toString();
              if (!userHoaIds.includes(hoaIdStr)) {
                userHoaIds.push(hoaIdStr);
              }
            }
          }
        }

        if (userHoaIds.length > 0) {
          console.log(`Member HOA IDs: ${userHoaIds.join(', ')}`);

          // If communityIds are provided in query, show events for those specific communities
          if (communityIds.length > 0) {
            console.log(`Member filtering events by Community IDs: ${communityIds.join(', ')}`);

            const orConditions = [];
            const accessibleHoaIds = new Set();

            // Process each community ID
            for (const commId of communityIds) {
              try {
                const community = await Community.findById(commId);
                if (!community) {
                  console.log(`❌ Community not found: ${commId}`);
                  continue;
                }

                const communityHoaId = community.hoaId.toString();
                console.log(`Found community ${community.name} with HOA ID: ${communityHoaId}`);

                // Verify the community's HOA is in the member's accessible HOAs
                if (userHoaIds.includes(communityHoaId)) {
                  console.log(`✅ Adding events for community: ${commId} (HOA: ${communityHoaId})`);

                  // Add community-specific events
                  orConditions.push({ communityId: commId });

                  // Track accessible HOA IDs for HOA-wide events
                  accessibleHoaIds.add(communityHoaId);
                } else {
                  console.log(`❌ Community's HOA ${communityHoaId} not in member's accessible HOAs`);
                }
              } catch (error) {
                console.error(`Error processing community ${commId}:`, error);
              }
            }

            // Add HOA-wide events for all accessible HOAs (events that have hoaId but no communityId)
            if (accessibleHoaIds.size > 0) {
              const accessibleHoaIdsArray = Array.from(accessibleHoaIds);
              console.log(`✅ Adding HOA-wide events for HOAs: ${accessibleHoaIdsArray.join(', ')}`);
              orConditions.push({
                hoaId: { $in: accessibleHoaIdsArray },
                $or: [
                  { communityId: { $exists: false } },
                  { communityId: null }
                ]
              });
            }

            if (orConditions.length > 0) {
              query.$or = orConditions;
              console.log(`🔍 Final query for multiple communities:`, JSON.stringify(query, null, 2));
            } else {
              console.log('❌ No accessible communities found - returning empty results');
              query._id = { $in: [] };
            }
          } else {
            // No communityIds provided - show all HOA-wide events for user's HOAs
            console.log(`Member showing all HOA-wide events for HOAs: ${userHoaIds.join(', ')}`);
            query.$or = [{
              hoaId: { $in: userHoaIds },
              $or: [
                { communityId: { $exists: false } },
                { communityId: null }
              ]
            }];
          }
        }
      }
    }

    console.log(`🔍 Final query for events:`, JSON.stringify(query, null, 2));
    const events = await Event.find(query)
      .populate('createdBy.userId', 'username email')
      .sort({ date: 1 });

    console.log(`📊 Found ${events.length} events for query`);
    if (events.length > 0) {
      console.log(`📋 Sample event HOA IDs:`, events.slice(0, 3).map(event => ({
        id: event._id,
        hoaId: event.hoaId,
        communityId: event.communityId
      })));
    }

    res.json(events);
  } catch (err) {
    console.error('Error fetching events:', err);
    res.status(500).json({
      message: 'Error fetching events',
      error: err.message
    });
  }
};

exports.createEvent = async (req, res) => {
  try {
    console.log('🔍 DEBUG: Creating event with data:', req.body);
    const { title, date, hoaId, communityId } = req.body;
    console.log('🔍 DEBUG: Extracted fields - title:', title, 'date:', date, 'hoaId:', hoaId, 'communityId:', communityId);

    // Validate required fields
    if (!title || !date) {
      return res.status(400).json({
        message: 'Missing required fields',
        details: {
          title: !title ? 'Title is required' : null,
          date: !date ? 'Date is required' : null
        }
      });
    }

    // Determine the correct HOA ID and Community ID based on user role and provided data
    let eventHoaId = hoaId;
    let eventCommunityId = communityId;

    // For company_admin, use the provided hoaId or require one
    if (req.user.role === 'company_admin') {
      if (!eventHoaId) {
        return res.status(400).json({
          message: 'HOA ID is required when creating an event as company admin'
        });
      }
      // Company admin can create events for any HOA/community
    }
    // For admin, determine HOA based on their community codes
    else if (req.user.role === 'admin') {
      // Get HOA IDs from user's community codes
      const userHoaIds = [];
      if (req.user.hoaCommunityCode && req.user.hoaCommunityCode.length > 0) {
        for (const code of req.user.hoaCommunityCode) {
          // Check if it's an HOA code
          const hoa = await HOA.findOne({ hoaCommunityCode: code });
          if (hoa) {
            userHoaIds.push(hoa._id.toString());
          }

          // Check if it's a community code
          const community = await Community.findOne({ communityCode: code }).populate('hoaId');
          if (community && community.hoaId) {
            const hoaIdStr = community.hoaId._id.toString();
            if (!userHoaIds.includes(hoaIdStr)) {
              userHoaIds.push(hoaIdStr);
            }
          }
        }
      }

      if (userHoaIds.length === 0) {
        return res.status(403).json({
          message: 'Admin must be associated with an HOA to create events'
        });
      }

      // If no hoaId provided, use the first accessible HOA
      if (!eventHoaId) {
        eventHoaId = userHoaIds[0];
      } else {
        // Verify admin has access to the specified HOA
        if (!userHoaIds.includes(eventHoaId)) {
          return res.status(403).json({
            message: 'You do not have permission to create events for this HOA'
          });
        }
      }

      // If communityId is provided, verify admin has access to it
      if (eventCommunityId) {
        const community = await Community.findById(eventCommunityId);
        if (!community || !userHoaIds.includes(community.hoaId.toString())) {
          return res.status(403).json({
            message: 'You do not have permission to create events for this community'
          });
        }
      }
    }
    // For member, determine HOA based on their community codes
    else if (req.user.role === 'member') {
      // Get HOA IDs from user's community codes
      const userHoaIds = [];
      if (req.user.hoaCommunityCode && req.user.hoaCommunityCode.length > 0) {
        for (const code of req.user.hoaCommunityCode) {
          // Check if it's an HOA code
          const hoa = await HOA.findOne({ hoaCommunityCode: code });
          if (hoa) {
            userHoaIds.push(hoa._id.toString());
          }

          // Check if it's a community code
          const community = await Community.findOne({ communityCode: code }).populate('hoaId');
          if (community && community.hoaId) {
            const hoaIdStr = community.hoaId._id.toString();
            if (!userHoaIds.includes(hoaIdStr)) {
              userHoaIds.push(hoaIdStr);
            }
          }
        }
      }

      if (userHoaIds.length === 0) {
        return res.status(403).json({
          message: 'Members must be associated with an HOA to create events'
        });
      }

      // If no hoaId provided, use the first accessible HOA
      if (!eventHoaId) {
        eventHoaId = userHoaIds[0];
      } else {
        // Verify member has access to the specified HOA
        if (!userHoaIds.includes(eventHoaId)) {
          return res.status(403).json({
            message: 'You do not have permission to create events for this HOA'
          });
        }
      }

      // If communityId is provided, verify member has access to it
      if (eventCommunityId) {
        const community = await Community.findById(eventCommunityId);
        if (!community || !userHoaIds.includes(community.hoaId.toString())) {
          return res.status(403).json({
            message: 'You do not have permission to create events for this community'
          });
        }
      }
    }

    // Create event with user information
    console.log('🔍 DEBUG: Final event data - hoaId:', eventHoaId, 'communityId:', eventCommunityId);
    const newEvent = new Event({
      title,
      date,
      hoaId: eventHoaId,
      communityId: eventCommunityId,
      createdBy: {
        userId: req.user._id,
        username: req.user.username
      }
    });

    console.log('🔍 DEBUG: Saving event:', newEvent);
    await newEvent.save();

    // Populate user information
    const populatedEvent = await Event.findById(newEvent._id).populate('createdBy.userId', 'username email');

    console.log('Event saved successfully:', populatedEvent);

    // Get all approved users to notify about the new event
    const users = await User.find({ isApproved: true, denied: false });
    const userIds = users.map(user => user._id);

    // Create notification for the new event
    await notificationController.createEventNotification(populatedEvent, userIds);

    res.status(201).json(populatedEvent);
  } catch (err) {
    console.error('Error creating event:', err);

    // Handle validation errors
    if (err.name === 'ValidationError') {
      return res.status(400).json({
        message: 'Validation error',
        errors: Object.values(err.errors).map(e => ({
          field: e.path,
          message: e.message
        }))
      });
    }

    res.status(500).json({
      message: 'Error creating event',
      error: err.message
    });
  }
};

exports.deleteEvent = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if event exists
    const event = await Event.findById(id);
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // Check access based on user role and community codes
    let hasAccess = false;

    if (req.user.role === 'company_admin') {
      // Company admins can delete any event
      hasAccess = true;
    } else if (req.user.role === 'admin' || req.user.role === 'member') {
      // Get HOA IDs from user's community codes
      const userHoaIds = [];
      if (req.user.hoaCommunityCode && req.user.hoaCommunityCode.length > 0) {
        for (const code of req.user.hoaCommunityCode) {
          // Check if it's an HOA code
          const hoa = await HOA.findOne({ hoaCommunityCode: code });
          if (hoa) {
            userHoaIds.push(hoa._id.toString());
          }

          // Check if it's a community code
          const community = await Community.findOne({ communityCode: code }).populate('hoaId');
          if (community && community.hoaId) {
            const hoaIdStr = community.hoaId._id.toString();
            if (!userHoaIds.includes(hoaIdStr)) {
              userHoaIds.push(hoaIdStr);
            }
          }
        }
      }

      // Check if user has access to the event's HOA
      if (event.hoaId && userHoaIds.includes(event.hoaId.toString())) {
        hasAccess = true;
      }

      // If user doesn't have HOA access, check if they created the event
      if (!hasAccess && event.createdBy?.userId && event.createdBy.userId.toString() === req.user._id.toString()) {
        hasAccess = true;
      }
    }

    if (!hasAccess) {
      return res.status(403).json({
        message: 'You do not have permission to delete this event'
      });
    }

    // Additional check for admins and members - they can only delete events in their accessible HOAs or their own events
    if (req.user.role === 'admin' || req.user.role === 'member') {
      const canDelete =
        (req.user.role === 'admin') || // Admins can delete events in their accessible HOAs (checked above)
        (event.createdBy?.userId && event.createdBy.userId.toString() === req.user._id.toString()); // Users can delete their own events

      if (!canDelete) {
        return res.status(403).json({ message: 'You can only delete your own events' });
      }
    }

    // Delete the event first
    await Event.findByIdAndDelete(id);

    try {
      // Get all approved users to notify about the deleted event
      const users = await User.find({ isApproved: true, denied: false });
      const userIds = users.map(user => user._id);

      // Create notification for the deleted event
      await notificationController.createEventDeletedNotification(event, req.user.username, userIds);
    } catch (notificationError) {
      console.error('Error creating deletion notification:', notificationError);
      // Continue with the response even if notification fails
    }

    res.json({ message: 'Event deleted successfully' });
  } catch (err) {
    console.error('Error deleting event:', err);
    res.status(500).json({
      message: 'Error deleting event',
      error: err.message
    });
  }
};

exports.updateEvent = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, date } = req.body;

    // Check if event exists
    const event = await Event.findById(id);
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // Check access based on user role and community codes
    let hasAccess = false;

    if (req.user.role === 'company_admin') {
      // Company admins can update any event
      hasAccess = true;
    } else if (req.user.role === 'admin' || req.user.role === 'member') {
      // Get HOA IDs from user's community codes
      const userHoaIds = [];
      if (req.user.hoaCommunityCode && req.user.hoaCommunityCode.length > 0) {
        for (const code of req.user.hoaCommunityCode) {
          // Check if it's an HOA code
          const hoa = await HOA.findOne({ hoaCommunityCode: code });
          if (hoa) {
            userHoaIds.push(hoa._id.toString());
          }

          // Check if it's a community code
          const community = await Community.findOne({ communityCode: code }).populate('hoaId');
          if (community && community.hoaId) {
            const hoaIdStr = community.hoaId._id.toString();
            if (!userHoaIds.includes(hoaIdStr)) {
              userHoaIds.push(hoaIdStr);
            }
          }
        }
      }

      // Check if user has access to the event's HOA
      if (event.hoaId && userHoaIds.includes(event.hoaId.toString())) {
        hasAccess = true;
      }

      // If user doesn't have HOA access, check if they created the event
      if (!hasAccess && event.createdBy?.userId && event.createdBy.userId.toString() === req.user._id.toString()) {
        hasAccess = true;
      }
    }

    if (!hasAccess) {
      return res.status(403).json({
        message: 'You do not have permission to update this event'
      });
    }

    // Check if user has permission to update
    const canUpdate =
      req.user.role === 'company_admin' || // Company admins can update any event
      (req.user.role === 'admin') || // Admins can update events in their accessible HOAs (checked above)
      (event.createdBy?.userId && event.createdBy.userId.equals(req.user._id)); // Users can update their own events

    if (!canUpdate) {
      return res.status(403).json({ message: 'You do not have permission to update this event' });
    }

    // Don't allow changing the hoaId or communityId for non-company admins
    const updateData = { title, date };
    if (req.user.role !== 'company_admin') {
      // Preserve original hoaId and communityId for non-company admins
      if (req.body.hoaId) {
        console.log('Preventing hoaId change for non-company admin');
      }
      if (req.body.communityId) {
        console.log('Preventing communityId change for non-company admin');
      }
    }

    const updated = await Event.findByIdAndUpdate(
      id,
      updateData,
      {
        new: true,
        runValidators: true
      }
    ).populate('createdBy.userId', 'username email');

    // Get all approved users to notify about the updated event
    const users = await User.find({ isApproved: true, denied: false });
    const userIds = users.map(user => user._id);

    // Create notification for the updated event
    await notificationController.createNotification({
      type: 'event',
      title: 'Event Updated',
      description: `${req.user.username} updated the event: ${event.title}`,
      users: userIds,
      metadata: {
        eventId: event._id,
        eventTitle: title || event.title,
        eventDate: date || event.date,
        updatedBy: req.user.username,
        changes: {
          title: title !== event.title ? { from: event.title, to: title } : null,
          date: date !== event.date ? { from: event.date, to: date } : null
        }
      }
    });

    res.json(updated);
  } catch (err) {
    console.error('Error updating event:', err);

    // Handle validation errors
    if (err.name === 'ValidationError') {
      return res.status(400).json({
        message: 'Validation error',
        errors: Object.values(err.errors).map(e => ({
          field: e.path,
          message: e.message
        }))
      });
    }

    res.status(500).json({
      message: 'Error updating event',
      error: err.message
    });
  }
};



const Property = require('../../models/property');
const User = require('../../models/user');
const mongoose = require('mongoose');
const { sendEmail } = require('../../services/emailService');
/**
 * @desc    Get all properties for an HOA or specific communities
 * @route   GET /api/properties
 * @access  Private
 * @query   communityId - Array of community IDs or single community ID to filter properties
 */
exports.getProperties = async (req, res) => {
  try {
    let { communityId } = req.query;
    let hoaCommunityCode = req.query.hoaCommunityCode || req.user.hoaCommunityCode;
    let hoaIds = [];
    let communityIds = [];

    console.log('🔍 DEBUG: getProperties called with:', {
      communityId,
      hoaCommunityCode,
      userId: req.user._id,
      userRole: req.user.role
    });

    // For company_admin users, allow them to see all properties
    if (req.user.role === 'company_admin') {
      // If specific community IDs are provided, filter by them
      if (communityId) {
        // Parse communityId if it's a string representation of an array
        if (typeof communityId === 'string') {
          try {
            // Try to parse as JSON array first
            if (communityId.startsWith('[') && communityId.endsWith(']')) {
              communityId = JSON.parse(communityId);
            } else if (communityId.includes(',')) {
              // Handle comma-separated values
              communityId = communityId.split(',').map(id => id.trim());
            } else {
              // Single community ID
              communityId = [communityId];
            }
          } catch (parseError) {
            // If parsing fails, treat as single ID
            communityId = [communityId];
          }
        }

        // Ensure communityId is an array
        if (!Array.isArray(communityId)) {
          communityId = [communityId];
        }

        console.log('🔍 DEBUG: Company admin filtering by community IDs:', communityId);

        const properties = await Property.find({ communityId: { $in: communityId } })
          .populate('resident', 'username email firstName lastName')
          .populate('createdBy', 'username')
          .populate('hoaId', 'hoaCommunityName')
          .populate('communityId', 'name')
          .sort({ createdAt: -1 });

        return res.status(200).json({
          success: true,
          count: properties.length,
          data: properties
        });
      } else {
        // No community filter, show all properties
        const properties = await Property.find({})
          .populate('resident', 'username email firstName lastName')
          .populate('createdBy', 'username')
          .populate('hoaId', 'hoaCommunityName')
          .populate('communityId', 'name')
          .sort({ createdAt: -1 });

        return res.status(200).json({
          success: true,
          count: properties.length,
          data: properties
        });
      }
    }

    // Handle community-based filtering from frontend
    if (communityId) {
      console.log('🔍 DEBUG: Frontend provided community IDs for filtering');

      // Parse communityId if it's a string representation of an array
      if (typeof communityId === 'string') {
        try {
          // Try to parse as JSON array first
          if (communityId.startsWith('[') && communityId.endsWith(']')) {
            communityId = JSON.parse(communityId);
          } else if (communityId.includes(',')) {
            // Handle comma-separated values
            communityId = communityId.split(',').map(id => id.trim());
          } else {
            // Single community ID
            communityId = [communityId];
          }
        } catch (parseError) {
          // If parsing fails, treat as single ID
          communityId = [communityId];
        }
      }

      // Ensure communityId is an array
      if (!Array.isArray(communityId)) {
        communityId = [communityId];
      }

      console.log('🔍 DEBUG: Processing frontend community IDs:', communityId);

      const Community = require('../../models/community');

      // Find communities and get their HOA IDs
      const communities = await Community.find({
        _id: { $in: communityId }
      }).populate('hoaId', '_id hoaCommunityName');

      console.log('🔍 DEBUG: Found communities:', communities.length);

      if (communities.length === 0) {
        console.log('🔍 DEBUG: No communities found for provided IDs');
        return res.status(404).json({
          success: false,
          message: 'No communities found for the provided IDs'
        });
      }

      // Extract HOA IDs and community IDs
      communities.forEach(community => {
        if (community.hoaId) {
          hoaIds.push(community.hoaId._id);
          communityIds.push(community._id);
          console.log(`🔍 DEBUG: Found community ${community.name} with HOA ${community.hoaId.hoaCommunityName}`);
        }
      });

      // Remove duplicates
      hoaIds = [...new Set(hoaIds.map(id => id.toString()))];
      communityIds = [...new Set(communityIds.map(id => id.toString()))];
    }
    // For admin and member users who have HOA associations (fallback to user's communities)
    else if (hoaCommunityCode && (req.user.role === 'admin' || req.user.role === 'member')) {
      const User = require('../../models/user');
      const HOA = require('../../models/hoa');
      const Community = require('../../models/community');

      console.log('🔍 DEBUG: Using user HOA community codes for filtering');

      // Get the user's full profile to access HOA associations
      const user = await User.findById(req.user._id);

      if (user && user.hoaCommunityCode) {
        // Handle array of community codes
        const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

        console.log('🔍 DEBUG: Processing user community codes:', codes);

        // Process each community code to get HOA and community IDs
        for (const code of codes) {
          // First try to find a community with this code
          const community = await Community.findOne({ communityCode: code })
            .populate('hoaId', '_id hoaCommunityName');

          if (community && community.hoaId) {
            hoaIds.push(community.hoaId._id);
            communityIds.push(community._id);
            console.log(`🔍 DEBUG: Found community ${community.name} with HOA ${community.hoaId.hoaCommunityName}`);
          } else {
            // For backward compatibility, check if it's an HOA code
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa) {
              hoaIds.push(hoa._id);
              console.log(`🔍 DEBUG: Found HOA ${hoa.hoaCommunityName} with code ${code}`);

              // Try to find communities for this HOA
              const hoaCommunities = await Community.find({ hoaId: hoa._id });
              hoaCommunities.forEach(comm => communityIds.push(comm._id));
            }
          }
        }

        // Remove duplicates
        hoaIds = [...new Set(hoaIds.map(id => id.toString()))];
        communityIds = [...new Set(communityIds.map(id => id.toString()))];
      }
    }

    if (hoaIds.length === 0) {
      console.log('🔍 DEBUG: No HOA associations found');
      return res.status(400).json({
        success: false,
        message: 'No HOA associations found for user'
      });
    }

    // Build query object to find properties from any of the user's HOAs
    const query = { hoaId: { $in: hoaIds } };

    // Filter by specific communities if provided
    if (communityIds.length > 0) {
      query.communityId = { $in: communityIds };
      console.log('🔍 DEBUG: Filtering by specific communities:', communityIds);
    }

    console.log('🔍 DEBUG: Final property query:', JSON.stringify(query, null, 2));

    // Find properties for the user's HOAs/communities
    const properties = await Property.find(query)
      .populate('resident', 'username email firstName lastName')
      .populate('createdBy', 'username')
      .populate('hoaId', 'hoaCommunityName')
      .populate('communityId', 'name')
      .sort({ createdAt: -1 });

    console.log('🔍 DEBUG: Found properties:', properties.length);

    res.status(200).json({
      success: true,
      count: properties.length,
      data: properties,
      selectedCommunityIds: communityIds.length > 0 ? communityIds : null
    });
  } catch (error) {
    console.error('Error fetching properties:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Get properties by multiple community IDs (dedicated route for community-based lookup)
 * @route   GET /api/properties/by-communities
 * @access  Private
 * @query   communityId - Array of community IDs or single community ID to filter properties
 */
exports.getPropertiesByCommunities = async (req, res) => {
  try {
    let { communityId } = req.query;

    console.log('🔍 DEBUG: getPropertiesByCommunities called with:', {
      communityId,
      userId: req.user._id,
      userRole: req.user.role
    });

    if (!communityId) {
      return res.status(400).json({
        success: false,
        message: 'Community ID parameter is required'
      });
    }

    // Parse communityId if it's a string representation of an array
    if (typeof communityId === 'string') {
      try {
        // Try to parse as JSON array first
        if (communityId.startsWith('[') && communityId.endsWith(']')) {
          communityId = JSON.parse(communityId);
        } else if (communityId.includes(',')) {
          // Handle comma-separated values
          communityId = communityId.split(',').map(id => id.trim());
        } else {
          // Single community ID
          communityId = [communityId];
        }
      } catch (parseError) {
        // If parsing fails, treat as single ID
        communityId = [communityId];
      }
    }

    // Ensure communityId is an array
    if (!Array.isArray(communityId)) {
      communityId = [communityId];
    }

    console.log('🔍 DEBUG: Processing community IDs:', communityId);

    const Community = require('../../models/community');

    // Find communities and verify they exist
    const communities = await Community.find({
      _id: { $in: communityId }
    }).populate('hoaId', '_id hoaCommunityName');

    console.log('🔍 DEBUG: Found communities:', communities.length);

    if (communities.length === 0) {
      console.log('🔍 DEBUG: No communities found for provided IDs');
      return res.status(404).json({
        success: false,
        message: 'No communities found for the provided IDs'
      });
    }

    // For company_admin, allow access to all communities
    if (req.user.role === 'company_admin') {
      console.log('🔍 DEBUG: Company admin accessing properties for communities');
    } else {
      // For admin/member users, verify they have access to these communities
      const User = require('../../models/user');
      const user = await User.findById(req.user._id);

      if (!user || !user.hoaCommunityCode) {
        return res.status(403).json({
          success: false,
          message: 'User has no community access'
        });
      }

      const userCodes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];
      console.log('🔍 DEBUG: User community codes:', userCodes);

      // Check if user has access to at least one of the requested communities
      let hasAccess = false;
      for (const community of communities) {
        // Check by community code
        if (userCodes.includes(community.communityCode)) {
          hasAccess = true;
          break;
        }
        // Check by HOA code (backward compatibility)
        if (community.hoaId && userCodes.includes(community.hoaId.hoaCommunityCode)) {
          hasAccess = true;
          break;
        }
      }

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to the requested communities'
        });
      }
    }

    // Build query to find properties in the specified communities
    const query = { communityId: { $in: communityId } };

    console.log('🔍 DEBUG: Property query:', JSON.stringify(query, null, 2));

    // Find properties for the specified communities
    const properties = await Property.find(query)
      .populate('resident', 'username email firstName lastName')
      .populate('createdBy', 'username')
      .populate('hoaId', 'hoaCommunityName')
      .populate('communityId', 'name')
      .sort({ createdAt: -1 });

    console.log('🔍 DEBUG: Found properties:', properties.length);

    res.status(200).json({
      success: true,
      count: properties.length,
      data: properties,
      selectedCommunityIds: communityId,
      communities: communities.map(c => ({
        _id: c._id,
        name: c.name,
        communityCode: c.communityCode,
        hoaName: c.hoaId ? c.hoaId.hoaCommunityName : null
      }))
    });

  } catch (error) {
    console.error('Error fetching properties by communities:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Get a single property
 * @route   GET /api/properties/:id
 * @access  Private
 */
exports.getProperty = async (req, res) => {
  try {
    const property = await Property.findById(req.params.id)
      .populate('resident', 'username email firstName lastName')
      .populate('createdBy', 'username')
      .populate('hoaId', 'hoaCommunityName')
      .populate('communityId', 'name streetAddress');

    if (!property) {
      return res.status(404).json({
        success: false,
        message: 'Property not found'
      });
    }

    // Check if user has access to this property's HOA
    // Company admins always have access
    if (req.user.role === 'company_admin') {
      // Allow access for company admins
    }
    // Global admins (without specific HOA) have access to all properties
    else if (req.user.role === 'admin' && !req.user.hoaId && !req.user.hoaCommunityCode) {
      // Allow access for global admins
    }
    // HOA-specific admins and members need to match the property's HOA
    else if (property.hoaId && (req.user.hoaId || req.user.hoaCommunityCode)) {
      let hasAccess = false;

      // Get property's HOA ID
      const propertyHoaId = typeof property.hoaId === 'object' && property.hoaId._id
        ? property.hoaId._id.toString()
        : property.hoaId.toString();

      // Check if user has access through hoaId array
      if (req.user.hoaId) {
        const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
        hasAccess = userHoaIds.some(hoaId => hoaId.toString() === propertyHoaId);
      }

      // If not found through hoaId, check through community codes
      if (!hasAccess && req.user.hoaCommunityCode) {
        const User = require('../../models/user');
        const HOA = require('../../models/hoa');
        const Community = require('../../models/community');

        const user = await User.findById(req.user._id);
        if (user && user.hoaCommunityCode) {
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            // Check community codes
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId && community.hoaId._id.toString() === propertyHoaId) {
              hasAccess = true;
              break;
            }

            // Check HOA codes
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa && hoa._id.toString() === propertyHoaId) {
              hasAccess = true;
              break;
            }
          }
        }
      }

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this property'
        });
      }
    }

    res.status(200).json({
      success: true,
      data: property
    });
  } catch (error) {
    console.error('Error fetching property:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Create a new property
 * @route   POST /api/properties
 * @access  Private/Admin
 */
exports.createProperty = async (req, res) => {
  try {
    // Get HOA ID from request body or user's first HOA
    let hoaId = req.body.hoaId;
    let communityId = req.body.communityId;

    // For company_admin users, allow them to specify any HOA
    if (req.user.role === 'company_admin') {
      if (!hoaId) {
        // Find the first HOA if none specified
        const HOA = require('../../models/hoa');
        const firstHOA = await HOA.findOne({});
        if (firstHOA) {
          hoaId = firstHOA._id;
          console.log('Using first available HOA ID for company admin:', hoaId);
        }
      }
    }
    // For admin and member users, use their associated HOAs
    else if (req.user.role === 'admin' || req.user.role === 'member') {
      const User = require('../../models/user');
      const HOA = require('../../models/hoa');
      const Community = require('../../models/community');

      // Get the user's full profile to access HOA associations
      const user = await User.findById(req.user._id);

      if (!hoaId && user) {
        // If no HOA specified, use the user's first HOA
        if (user.hoaId && Array.isArray(user.hoaId) && user.hoaId.length > 0) {
          hoaId = user.hoaId[0];
          console.log('Using user\'s first HOA ID:', hoaId);
        } else if (user.hoaId && !Array.isArray(user.hoaId)) {
          hoaId = user.hoaId;
          console.log('Using user\'s HOA ID:', hoaId);
        } else if (user.hoaCommunityCode) {
          // Try to find HOA by community code
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            // First try to find a community with this code
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId) {
              hoaId = community.hoaId._id;
              if (!communityId) {
                communityId = community._id;
              }
              console.log('Found HOA ID from community code:', code, hoaId);
              break;
            } else {
              // For backward compatibility, check if it's an HOA code
              const hoa = await HOA.findOne({ hoaCommunityCode: code });
              if (hoa) {
                hoaId = hoa._id;
                console.log('Found HOA ID from HOA code:', code, hoaId);
                break;
              }
            }
          }
        }
      }

      // Verify user has access to the specified HOA
      if (hoaId && user) {
        let hasAccess = false;

        // Check through user's hoaId array
        if (user.hoaId) {
          const userHoaIds = Array.isArray(user.hoaId) ? user.hoaId : [user.hoaId];
          hasAccess = userHoaIds.some(id => id.toString() === hoaId.toString());
        }

        // Check through community codes if not found
        if (!hasAccess && user.hoaCommunityCode) {
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId && community.hoaId._id.toString() === hoaId.toString()) {
              hasAccess = true;
              break;
            }

            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa && hoa._id.toString() === hoaId.toString()) {
              hasAccess = true;
              break;
            }
          }
        }

        if (!hasAccess) {
          return res.status(403).json({
            success: false,
            message: 'You do not have permission to create properties for this HOA'
          });
        }
      }
    }

    if (!hoaId) {
      return res.status(400).json({
        success: false,
        message: 'HOA ID is required'
      });
    }

    // Create new property
    const property = new Property({
      ...req.body,
      hoaId,
      communityId,
      createdBy: req.user._id
    });

    // Save property
    await property.save();

    res.status(201).json({
      success: true,
      data: property
    });
  } catch (error) {
    console.error('Error creating property:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Update a property
 * @route   PUT /api/properties/:id
 * @access  Private/Admin
 */
exports.updateProperty = async (req, res) => {
  try {
    // Find property
    let property = await Property.findById(req.params.id);

    if (!property) {
      return res.status(404).json({
        success: false,
        message: 'Property not found'
      });
    }

    // Check if user has access to this property's HOA
    // Company admins always have access
    if (req.user.role === 'company_admin') {
      // Allow access for company admins
    }
    // Global admins (without specific HOA) have access to all properties
    else if (req.user.role === 'admin' && !req.user.hoaId && !req.user.hoaCommunityCode) {
      // Allow access for global admins
    }
    // HOA-specific admins need to match the property's HOA
    else if (req.user.role === 'admin' && property.hoaId && (req.user.hoaId || req.user.hoaCommunityCode)) {
      let hasAccess = false;

      // Get property's HOA ID
      const propertyHoaId = property.hoaId.toString();

      // Check if user has access through hoaId array
      if (req.user.hoaId) {
        const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
        hasAccess = userHoaIds.some(hoaId => hoaId.toString() === propertyHoaId);
      }

      // If not found through hoaId, check through community codes
      if (!hasAccess && req.user.hoaCommunityCode) {
        const User = require('../../models/user');
        const HOA = require('../../models/hoa');
        const Community = require('../../models/community');

        const user = await User.findById(req.user._id);
        if (user && user.hoaCommunityCode) {
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            // Check community codes
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId && community.hoaId._id.toString() === propertyHoaId) {
              hasAccess = true;
              break;
            }

            // Check HOA codes
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa && hoa._id.toString() === propertyHoaId) {
              hasAccess = true;
              break;
            }
          }
        }
      }

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to update this property'
        });
      }
    }
    // Members should not be able to update properties
    else if (req.user.role === 'member') {
      return res.status(403).json({
        success: false,
        message: 'Members cannot update properties'
      });
    }

    // Update property
    property = await Property.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: Date.now() },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      data: property
    });
  } catch (error) {
    console.error('Error updating property:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Delete a property
 * @route   DELETE /api/properties/:id
 * @access  Private/Admin
 */
exports.deleteProperty = async (req, res) => {
  try {
    // Find property
    const property = await Property.findById(req.params.id);

    if (!property) {
      return res.status(404).json({
        success: false,
        message: 'Property not found'
      });
    }

    // Check if user has access to this property's HOA
    // Company admins always have access
    if (req.user.role === 'company_admin') {
      // Allow access for company admins
    }
    // Global admins (without specific HOA) have access to all properties
    else if (req.user.role === 'admin' && !req.user.hoaId && !req.user.hoaCommunityCode) {
      // Allow access for global admins
    }
    // HOA-specific admins need to match the property's HOA
    else if (req.user.role === 'admin' && property.hoaId && (req.user.hoaId || req.user.hoaCommunityCode)) {
      let hasAccess = false;

      // Get property's HOA ID
      const propertyHoaId = property.hoaId.toString();

      // Check if user has access through hoaId array
      if (req.user.hoaId) {
        const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
        hasAccess = userHoaIds.some(hoaId => hoaId.toString() === propertyHoaId);
      }

      // If not found through hoaId, check through community codes
      if (!hasAccess && req.user.hoaCommunityCode) {
        const User = require('../../models/user');
        const HOA = require('../../models/hoa');
        const Community = require('../../models/community');

        const user = await User.findById(req.user._id);
        if (user && user.hoaCommunityCode) {
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            // Check community codes
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId && community.hoaId._id.toString() === propertyHoaId) {
              hasAccess = true;
              break;
            }

            // Check HOA codes
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa && hoa._id.toString() === propertyHoaId) {
              hasAccess = true;
              break;
            }
          }
        }
      }

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to delete this property'
        });
      }
    }
    // Members should not be able to delete properties
    else if (req.user.role === 'member') {
      return res.status(403).json({
        success: false,
        message: 'Members cannot delete properties'
      });
    }

    // Check if we should skip the maintenance check
    const skipMaintenanceCheck = req.query.skipMaintenanceCheck === 'true';

    // Check if property has maintenance history (unless skipped)
    try {
      // Try to load the maintenance model if it exists and if we're not skipping the check
      let maintenanceRecords = [];
      if (!skipMaintenanceCheck) {
        try {
          const Maintenance = require('../models/maintenance');
          maintenanceRecords = await Maintenance.find({ property: req.params.id });
        } catch (modelError) {
          console.log('Maintenance model not found, skipping maintenance check:', modelError.message);
          // Continue without checking maintenance if the model doesn't exist
        }

        if (maintenanceRecords && maintenanceRecords.length > 0) {
          return res.status(400).json({
            success: false,
            message: 'Cannot delete property with maintenance history. Please archive it instead.'
          });
        }
      }

      // Check if property has any other dependencies
      try {
        // Delete property using findByIdAndDelete instead of remove()
        await Property.findByIdAndDelete(req.params.id);
      } catch (deleteError) {
        console.error('Error during property deletion:', deleteError);
        return res.status(500).json({
          success: false,
          message: 'Error deleting property. It may have dependencies.',
          error: deleteError.message
        });
      }
    } catch (error) {
      console.error('Error checking property dependencies:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking property dependencies.',
        error: error.message
      });
    }

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error deleting property:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Add a document to a property
 * @route   POST /api/properties/:id/documents
 * @access  Private/Admin
 */
exports.addPropertyDocument = async (req, res) => {
  try {
    // Find property
    let property = await Property.findById(req.params.id);

    if (!property) {
      return res.status(404).json({
        success: false,
        message: 'Property not found'
      });
    }

    // Check if user has access to this property's HOA
    // Company admins always have access
    if (req.user.role === 'company_admin') {
      // Allow access for company admins
    }
    // Global admins (without specific HOA) have access to all properties
    else if (req.user.role === 'admin' && !req.user.hoaId && !req.user.hoaCommunityCode) {
      // Allow access for global admins
    }
    // HOA-specific admins need to match the property's HOA
    else if (req.user.role === 'admin' && property.hoaId && (req.user.hoaId || req.user.hoaCommunityCode)) {
      let hasAccess = false;

      // Get property's HOA ID
      const propertyHoaId = property.hoaId.toString();

      // Check if user has access through hoaId array
      if (req.user.hoaId) {
        const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
        hasAccess = userHoaIds.some(hoaId => hoaId.toString() === propertyHoaId);
      }

      // If not found through hoaId, check through community codes
      if (!hasAccess && req.user.hoaCommunityCode) {
        const User = require('../../models/user');
        const HOA = require('../../models/hoa');
        const Community = require('../../models/community');

        const user = await User.findById(req.user._id);
        if (user && user.hoaCommunityCode) {
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            // Check community codes
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId && community.hoaId._id.toString() === propertyHoaId) {
              hasAccess = true;
              break;
            }

            // Check HOA codes
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa && hoa._id.toString() === propertyHoaId) {
              hasAccess = true;
              break;
            }
          }
        }
      }

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to add documents to this property'
        });
      }
    }
    // Members should not be able to add documents to properties
    else if (req.user.role === 'member') {
      return res.status(403).json({
        success: false,
        message: 'Members cannot add documents to properties'
      });
    }

    // Add document to property
    const newDocument = {
      name: req.body.name,
      fileType: req.body.fileType,
      fileUrl: req.body.fileUrl,
      uploadDate: new Date()
    };

    // If property doesn't have documents array, create it
    if (!property.documents) {
      property.documents = [];
    }

    property.documents.push(newDocument);
    property.updatedAt = Date.now();

    await property.save();

    res.status(200).json({
      success: true,
      data: property
    });
  } catch (error) {
    console.error('Error adding document to property:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Delete a document from a property
 * @route   DELETE /api/properties/:id/documents/:documentId
 * @access  Private/Admin
 */
exports.deletePropertyDocument = async (req, res) => {
  try {
    // Find property
    let property = await Property.findById(req.params.id);

    if (!property) {
      return res.status(404).json({
        success: false,
        message: 'Property not found'
      });
    }

    // Check if user has access to this property's HOA
    // Company admins always have access
    if (req.user.role === 'company_admin') {
      // Allow access for company admins
    }
    // Global admins (without specific HOA) have access to all properties
    else if (req.user.role === 'admin' && !req.user.hoaId && !req.user.hoaCommunityCode) {
      // Allow access for global admins
    }
    // HOA-specific admins need to match the property's HOA
    else if (req.user.role === 'admin' && property.hoaId && (req.user.hoaId || req.user.hoaCommunityCode)) {
      let hasAccess = false;

      // Get property's HOA ID
      const propertyHoaId = property.hoaId.toString();

      // Check if user has access through hoaId array
      if (req.user.hoaId) {
        const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
        hasAccess = userHoaIds.some(hoaId => hoaId.toString() === propertyHoaId);
      }

      // If not found through hoaId, check through community codes
      if (!hasAccess && req.user.hoaCommunityCode) {
        const User = require('../../models/user');
        const HOA = require('../../models/hoa');
        const Community = require('../../models/community');

        const user = await User.findById(req.user._id);
        if (user && user.hoaCommunityCode) {
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            // Check community codes
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId && community.hoaId._id.toString() === propertyHoaId) {
              hasAccess = true;
              break;
            }

            // Check HOA codes
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa && hoa._id.toString() === propertyHoaId) {
              hasAccess = true;
              break;
            }
          }
        }
      }

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to delete documents from this property'
        });
      }
    }
    // Members should not be able to delete documents from properties
    else if (req.user.role === 'member') {
      return res.status(403).json({
        success: false,
        message: 'Members cannot delete documents from properties'
      });
    }

    // Find document index
    const documentIndex = property.documents.findIndex(
      doc => doc._id.toString() === req.params.documentId
    );

    if (documentIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    // Remove document from array
    property.documents.splice(documentIndex, 1);
    property.updatedAt = Date.now();

    await property.save();

    res.status(200).json({
      success: true,
      data: property
    });
  } catch (error) {
    console.error('Error deleting document from property:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

exports.notifyOwner = async (req, res) => {
  try { 
    // Send email to owner
    let userId = req.params.userId;

    if (!userId) {
      return res.status(400).json({ message: "Missing userId in params" });
    }
   
    const user = await User.findById(userId);
    const ownerEmail = user.email;

    const bodyContent = `
    <p>Dear ${user.fullName},</p>
    <p>Property HOA Due Notification</p>
    <p>This is to notify you that your property HOA payment is past due.</p>
    <p>Please login to your HoaFlo account and make sure to pay your HOA payment as soon as possible.</p>
    <p>This is notification made by your HOA Community Admin</p>
    <p>This is an automated message, please do not reply to this email.</p>
    <p>Best regards,<br>HOA Management Team</p>
    `

    // Send email to owner
    const emailSent = await sendEmail(ownerEmail, 'Property HOA Due Notification', bodyContent);

    if (emailSent && emailSent.success == false) {
      return res.status(500).json({
        success: false,
        message: 'Error sending email to owner'
      });
    } else {
      return res.status(200).json({
        success: true,
        message: 'Email sent successfully'
      });
    }
  }
  catch (error) { 
    console.error('Error notifying owner:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
}



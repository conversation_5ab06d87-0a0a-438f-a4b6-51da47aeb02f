const User = require('../../models/user');
const HOA = require('../../models/hoa');
const Community = require('../../models/community');
const Notification = require('../../models/notification');
const AnnouncementAttachment = require('../../models/announcementAttachment');
const { sendTemplateEmail } = require('../../services/emailService');
const { generateSignedUrl } = require('../../config/s3Config');

/**
 * Enhanced Announcement Controller
 * Supports role-based targeting for HOA admins and company admins
 */

/**
 * Helper function to create persistent announcement attachments
 * @param {Array} files - Uploaded files from multer
 * @param {String} notificationId - ID of the created notification
 * @param {String} hoaId - HOA ID for proper isolation
 * @param {String} communityId - Optional community ID
 * @param {String} uploadedBy - User ID who uploaded the files
 * @returns {Array} Array of attachment objects with signed URLs
 */
const createAnnouncementAttachments = async (files, notificationId, hoaId, communityId, uploadedBy) => {
  const attachments = [];

  if (!files || files.length === 0) {
    return attachments;
  }

  console.log(`Creating ${files.length} persistent announcement attachments`);

  for (const file of files) {
    try {
      // Create standardized file reference
      const fileReference = {
        filename: file.key,
        originalName: file.originalname,
        s3Key: file.key,
        s3Location: file.location,
        mimetype: file.mimetype,
        size: file.size,
        uploadDate: new Date(),
        s3Metadata: {
          etag: file.etag,
          serverSideEncryption: file.serverSideEncryption
        }
      };

      // Create persistent attachment record
      const attachment = new AnnouncementAttachment({
        notificationId: notificationId,
        file: fileReference,
        hoaId: hoaId,
        communityId: communityId,
        uploadedBy: uploadedBy,
        metadata: {
          description: `Attachment for announcement: ${notificationId}`,
          tags: ['announcement', 'attachment']
        }
      });

      await attachment.save();
      console.log(`Created attachment record: ${attachment._id} for file: ${file.originalname}`);

      // Generate signed URL for immediate use
      const signedUrl = await generateSignedUrl(file.key, 24 * 3600); // 24 hour expiration

      // Add to response array
      attachments.push({
        id: attachment._id,
        filename: file.originalname,
        s3Key: file.key,
        s3Location: file.location,
        signedUrl: signedUrl,
        mimetype: file.mimetype,
        size: file.size,
        formattedSize: attachment.formattedFileSize,
        fileExtension: attachment.fileExtension,
        uploadDate: attachment.file.uploadDate
      });

    } catch (error) {
      console.error(`Error creating attachment for file ${file.originalname}:`, error);
      // Continue with other files even if one fails
    }
  }

  return attachments;
};

/**
 * Get streets for HOA admin targeting
 * @route GET /api/announcements/streets
 * @access Private (HOA Admin)
 */
exports.getStreetsForAdmin = async (req, res) => {
  try {
    console.log('Getting streets for HOA admin:', req.user._id);

    // Verify user is HOA admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only HOA administrators can access this endpoint'
      });
    }

    // Get user's HOA information
    const userHoaCode = req.user.hoaCommunityCode;
    if (!userHoaCode) {
      return res.status(400).json({
        error: 'No HOA association',
        message: 'Admin user must be associated with an HOA'
      });
    }

    // Find all communities/streets in the admin's HOA
    const communities = await Community.find({
      hoaCommunityCode: userHoaCode
    }).select('name communityCode description');

    console.log(`Found ${communities.length} streets for HOA ${userHoaCode}`);

    res.json({
      success: true,
      streets: communities.map(community => ({
        id: community._id,
        name: community.name,
        code: community.communityCode,
        description: community.description
      }))
    });
  } catch (error) {
    console.error('Error getting streets for admin:', error);
    res.status(500).json({
      error: 'Server error',
      message: 'Failed to retrieve streets'
    });
  }
};

/**
 * Get residents for a specific street (HOA admin targeting)
 * @route GET /api/announcements/streets/:streetId/residents
 * @access Private (HOA Admin)
 */
exports.getResidentsForStreet = async (req, res) => {
  try {
    const { streetId } = req.params;
    console.log('🔍 Getting residents for street ID:', streetId);
    console.log('🔍 Request user:', {
      id: req.user._id,
      email: req.user.email,
      role: req.user.role,
      hoaCommunityCode: req.user.hoaCommunityCode
    });

    // Verify user is HOA admin
    if (req.user.role !== 'admin') {
      console.log('❌ Access denied: User is not admin, role:', req.user.role);
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only HOA administrators can access this endpoint'
      });
    }

    // Get the community/street details
    const community = await Community.findById(streetId);
    console.log('🔍 Found community:', community ? {
      id: community._id,
      name: community.name,
      code: community.communityCode,
      hoaId: community.hoaId
    } : 'null');

    if (!community) {
      console.log('❌ Street not found for ID:', streetId);
      return res.status(404).json({
        error: 'Street not found',
        message: 'The specified street does not exist'
      });
    }

    // Verify admin has access to this street (same HOA)
    // Use communityId for validation as it's the most reliable relationship
    const userCommunityIds = Array.isArray(req.user.communityId) ? req.user.communityId : [req.user.communityId];
    const communityIdString = community._id.toString();

    console.log('🔍 Community Access Check:', {
      userCommunityIds: userCommunityIds,
      communityId: communityIdString,
      communityName: community.name
    });

    const hasAccess = userCommunityIds.some(id => id.toString() === communityIdString);

    if (!hasAccess) {
      console.log('❌ Access denied: Community ID mismatch', {
        communityId: communityIdString,
        userCommunityIds: userCommunityIds,
        communityName: community.name
      });
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only access streets in your HOA'
      });
    }

    // Find all users in this community
    // Use communityId to match users to communities (most reliable)
    console.log('🔍 Searching for users with communityId:', community._id);
    const residents = await User.find({
      communityId: community._id,
      isDeleted: { $ne: true }
    }).select('email username fullName streetAddress');

    console.log(`✅ Found ${residents.length} residents for street ${community.name}`);
    console.log('🔍 Residents:', residents.map(r => ({
      email: r.email,
      fullName: r.fullName,
      streetAddress: r.streetAddress
    })));

    res.json({
      success: true,
      street: {
        id: community._id,
        name: community.name,
        code: community.communityCode
      },
      residents: residents.map(resident => ({
        id: resident._id,
        email: resident.email,
        username: resident.username,
        fullName: resident.fullName,
        streetAddress: resident.streetAddress
      })),
      // Add "All residents" option
      allResidentsOption: {
        id: 'all',
        label: `All residents on ${community.name}`,
        count: residents.length
      }
    });
  } catch (error) {
    console.error('❌ Error getting residents for street:', error);
    res.status(500).json({
      error: 'Server error',
      message: 'Failed to retrieve residents'
    });
  }
};

// Company admin version - can access any street without HOA restrictions
exports.getResidentsForStreetCompanyAdmin = async (req, res) => {
  try {
    const { streetId } = req.params;
    console.log('🔍 Company admin getting residents for street ID:', streetId);
    console.log('🔍 Request user:', {
      id: req.user._id,
      email: req.user.email,
      role: req.user.role
    });

    // Verify user is company admin
    if (req.user.role !== 'company_admin') {
      console.log('❌ Access denied: User is not company admin, role:', req.user.role);
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only company administrators can access this endpoint'
      });
    }

    // Get the community/street details
    const community = await Community.findById(streetId);
    console.log('🔍 Found community:', community ? {
      id: community._id,
      name: community.name,
      code: community.communityCode,
      hoaCommunityCode: community.hoaCommunityCode
    } : 'null');

    if (!community) {
      console.log('❌ Street not found for ID:', streetId);
      return res.status(404).json({
        error: 'Street not found',
        message: 'The specified street does not exist'
      });
    }

    // Company admin can access any street - no HOA restrictions

    // Find all users in this community
    console.log('🔍 Searching for users with communityId:', community._id);
    const residents = await User.find({
      communityId: community._id,
      isDeleted: { $ne: true }
    }).select('email username fullName streetAddress');

    console.log(`✅ Company admin found ${residents.length} residents for street ${community.name}`);
    console.log('🔍 Residents:', residents.map(r => ({
      email: r.email,
      fullName: r.fullName,
      streetAddress: r.streetAddress
    })));

    res.json({
      success: true,
      street: {
        id: community._id,
        name: community.name,
        code: community.communityCode
      },
      residents: residents.map(resident => ({
        id: resident._id,
        email: resident.email,
        username: resident.username,
        fullName: resident.fullName,
        streetAddress: resident.streetAddress
      })),
      // Add "All residents" option
      allResidentsOption: {
        id: 'all',
        label: `All residents on ${community.name}`,
        count: residents.length
      }
    });
  } catch (error) {
    console.error('❌ Error getting residents for street (company admin):', error);
    res.status(500).json({
      error: 'Server error',
      message: 'Failed to retrieve residents'
    });
  }
};

/**
 * Get HOAs for company admin targeting
 * @route GET /api/announcements/hoas
 * @access Private (Company Admin)
 */
exports.getHOAsForCompanyAdmin = async (req, res) => {
  try {
    console.log('Getting HOAs for company admin:', req.user._id);

    // Verify user is company admin
    if (req.user.role !== 'company_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only company administrators can access this endpoint'
      });
    }

    // Get all approved HOAs
    const hoas = await HOA.find({
      verificationStatus: 'approved'
    }).select('hoaCommunityName hoaCommunityCode hoaCity hoaState');

    console.log(`Found ${hoas.length} HOAs for company admin`);

    res.json({
      success: true,
      hoas: hoas.map(hoa => ({
        id: hoa._id,
        name: hoa.hoaCommunityName,
        code: hoa.hoaCommunityCode,
        location: `${hoa.hoaCity}, ${hoa.hoaState}`
      }))
    });
  } catch (error) {
    console.error('Error getting HOAs for company admin:', error);
    res.status(500).json({
      error: 'Server error',
      message: 'Failed to retrieve HOAs'
    });
  }
};

/**
 * Get targeting options for a specific HOA (Company admin)
 * @route GET /api/announcements/hoas/:hoaId/targeting
 * @access Private (Company Admin)
 */
exports.getHOATargetingOptions = async (req, res) => {
  try {
    const { hoaId } = req.params;
    console.log('Getting targeting options for HOA:', hoaId);

    // Verify user is company admin
    if (req.user.role !== 'company_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only company administrators can access this endpoint'
      });
    }

    // Get the HOA details
    const hoa = await HOA.findById(hoaId);
    if (!hoa) {
      return res.status(404).json({
        error: 'HOA not found',
        message: 'The specified HOA does not exist'
      });
    }

    // Get all communities/streets in this HOA
    const communities = await Community.find({
      hoaCommunityCode: hoa.hoaCommunityCode
    }).select('name communityCode');

    // Get total resident count for this HOA
    const totalResidents = await User.countDocuments({
      hoaCommunityCode: hoa.hoaCommunityCode,
      verificationStatus: 'approved',
      isDeleted: false
    });

    // Get resident counts per street
    const streetOptions = await Promise.all(
      communities.map(async (community) => {
        const residentCount = await User.countDocuments({
          hoaCommunityCode: community.hoaCommunityCode,
          verificationStatus: 'approved',
          isDeleted: false
        });

        return {
          id: community._id,
          name: community.name,
          code: community.communityCode,
          residentCount
        };
      })
    );

    console.log(`Found ${communities.length} streets and ${totalResidents} total residents for HOA ${hoa.hoaCommunityName}`);

    res.json({
      success: true,
      hoa: {
        id: hoa._id,
        name: hoa.hoaCommunityName,
        code: hoa.hoaCommunityCode
      },
      targetingOptions: {
        allResidents: {
          id: 'all',
          label: `All residents in ${hoa.hoaCommunityName}`,
          count: totalResidents
        },
        streets: streetOptions
      }
    });
  } catch (error) {
    console.error('Error getting HOA targeting options:', error);
    res.status(500).json({
      error: 'Server error',
      message: 'Failed to retrieve targeting options'
    });
  }
};

/**
 * Send targeted announcement (HOA Admin or Company Admin - street level)
 * @route POST /api/announcements/send/street
 * @access Private (HOA Admin or Company Admin)
 */
exports.sendStreetAnnouncement = async (req, res) => {
  try {
    const { subject, message, streetId, recipients, sendEmail: shouldSendEmail } = req.body;
    console.log(`${req.user.role} sending street announcement:`, { subject, streetId, recipients: recipients?.length, userEmail: req.user.email });
    console.log('🔍 Raw recipients data:', { recipients, type: typeof recipients, isArray: Array.isArray(recipients) });

    // Parse recipients if it's a JSON string
    let processedRecipients = recipients;
    if (typeof recipients === 'string' && recipients !== 'all') {
      try {
        processedRecipients = JSON.parse(recipients);
        console.log('🔍 Parsed recipients from JSON string:', processedRecipients);
      } catch (e) {
        console.log('🔍 Could not parse recipients as JSON, treating as string value:', recipients);
        // If it's not valid JSON and not 'all', treat as single recipient ID
        processedRecipients = recipients === 'all' ? 'all' : [recipients];
      }
    }

    console.log('🔍 Final processed recipients:', { processedRecipients, type: typeof processedRecipients, isArray: Array.isArray(processedRecipients) });

    // Handle file attachments
    const attachments = [];
    if (req.files && req.files.length > 0) {
      console.log(`Processing ${req.files.length} attachment(s)`);
      for (const file of req.files) {
        try {
          // Generate signed URL for attachment
          const signedUrl = await generateSignedUrl(file.key, 24 * 3600); // 24 hour expiration
          attachments.push({
            filename: file.originalname,
            s3Key: file.key,
            s3Location: file.location,
            signedUrl: signedUrl,
            mimetype: file.mimetype,
            size: file.size
          });
          console.log(`Attachment processed: ${file.originalname}`);
        } catch (error) {
          console.error(`Error processing attachment ${file.originalname}:`, error);
        }
      }
    }

    // Verify user is HOA admin or company admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only HOA administrators or company administrators can send announcements'
      });
    }

    // Validate required fields
    if (!subject || !message || !streetId) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Subject, message, and street selection are required'
      });
    }

    // Get the community/street details
    const community = await Community.findById(streetId);
    if (!community) {
      return res.status(404).json({
        error: 'Street not found',
        message: 'The specified street does not exist'
      });
    }

    // Verify admin has access to this street (company admins can access all streets)
    const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
    const userHoaIdStrings = userHoaIds.map(id => id.toString());
    const communityHoaId = community.hoaId.toString();
    const hasHoaAccess = userHoaIdStrings.includes(communityHoaId);

    console.log('🔍 Access check:', {
      userRole: req.user.role,
      isCompanyAdmin: req.user.role === 'company_admin',
      communityHoaId: communityHoaId,
      userHoaIds: userHoaIdStrings,
      hasHoaAccess: hasHoaAccess,
      hasAccess: req.user.role === 'company_admin' || hasHoaAccess
    });

    if (req.user.role !== 'company_admin' && !hasHoaAccess) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only send announcements to streets in your HOA'
      });
    }

    let targetUsers = [];

    if (processedRecipients === 'all' || !processedRecipients || (Array.isArray(processedRecipients) && processedRecipients.length === 0)) {
      // Send to all residents on the street
      console.log('🔍 Sending to all residents on street');
      targetUsers = await User.find({
        communityId: community._id,
        isDeleted: { $ne: true }
      });
    } else {
      // Send to specific recipients - filter out any undefined/invalid IDs
      const validRecipients = Array.isArray(processedRecipients)
        ? processedRecipients.filter(id => id && id !== 'undefined' && id !== 'null' && typeof id === 'string' && id.length === 24)
        : [];

      console.log('🔍 Recipients validation:', {
        originalRecipients: processedRecipients,
        validRecipients: validRecipients,
        filteredCount: validRecipients.length
      });

      if (validRecipients.length === 0) {
        return res.status(400).json({
          error: 'No valid recipients',
          message: 'No valid recipient IDs provided'
        });
      }

      console.log('🔍 Sending to specific recipients:', validRecipients);
      targetUsers = await User.find({
        _id: { $in: validRecipients },
        communityId: community._id,
        isDeleted: { $ne: true }
      });
    }

    if (targetUsers.length === 0) {
      return res.status(400).json({
        error: 'No recipients found',
        message: 'No valid recipients found for this announcement'
      });
    }

    // Create notification
    const notification = new Notification({
      type: 'announcement',
      title: subject,
      description: message,
      icon: 'megaphone',
      priority: 'high',
      metadata: {
        subject,
        message,
        sentBy: req.user.username || req.user.email,
        sentById: req.user._id,
        targetType: 'street',
        targetStreet: community.name,
        targetStreetId: streetId,
        isAnnouncement: true,
        attachments: attachments // Include attachment information
      },
      recipients: targetUsers.map(user => ({ userId: user._id }))
    });

    const savedNotification = await notification.save();
    console.log(`Street announcement notification created with ${targetUsers.length} recipients`);

    // Create persistent attachment records if files were uploaded
    if (req.files && req.files.length > 0) {
      try {
        await createPersistentAttachments(
          req.files,
          savedNotification._id,
          community.hoaId || null,
          community._id,
          req.user._id
        );
        console.log(`Created persistent attachment records for ${req.files.length} files`);
      } catch (error) {
        console.error('Error creating persistent attachment records:', error);
        // Don't fail the announcement if attachment storage fails
      }
    }

    // Send emails if requested
    let emailsSent = 0;
    let emailErrors = 0;

    if (shouldSendEmail && targetUsers.length > 0) {
      console.log(`Sending emails to ${targetUsers.length} recipients...`);

      // Process emails in batches to avoid rate limits
      const batchSize = 10;
      const batches = [];
      for (let i = 0; i < targetUsers.length; i += batchSize) {
        batches.push(targetUsers.slice(i, i + batchSize));
      }

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Processing email batch ${i + 1}/${batches.length} (${batch.length} recipients)`);

        const emailResults = await Promise.all(batch.map(async (user) => {
          try {
            // Check if HOA has configured their own email
            const hoa = await HOA.findOne({ hoaCommunityCode: req.user.hoaCommunityCode });
            const useHOAEmail = hoa && hoa.emailConfig && hoa.emailConfig.configured && hoa.emailConfig.verificationStatus === 'verified';

            const result = await sendTemplateEmail(
              user.email,
              'streetAnnouncement',
              {
                username: user.username || user.email,
                subject,
                message,
                streetName: community.name,
                senderName: useHOAEmail ? hoa.emailConfig.displayName : 'HOAFLO Support',
                hoaName: hoa ? hoa.hoaCommunityName : 'Your HOA',
                attachments: attachments, // Include attachment information
                actionLink: `${process.env.FRONTEND_URL}/notifications`
              },
              {
                senderType: useHOAEmail ? 'hoa' : 'company_admin',
                hoaId: useHOAEmail ? hoa._id : null,
                hoaEmail: useHOAEmail ? hoa.emailConfig.email : null,
                hoaName: useHOAEmail ? hoa.emailConfig.displayName : null
              }
            );

            if (result.success) {
              emailsSent++;
              return { success: true, email: user.email };
            } else {
              emailErrors++;
              console.error(`Failed to send email to ${user.email}:`, result.error);
              return { success: false, email: user.email, error: result.error };
            }
          } catch (error) {
            emailErrors++;
            console.error(`Exception sending email to ${user.email}:`, error);
            return { success: false, email: user.email, error: error.message };
          }
        }));

        // Small delay between batches
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log('Email sending complete:', { emailsSent, emailErrors });
    }

    res.status(201).json({
      success: true,
      message: `Announcement sent to ${targetUsers.length} residents on ${community.name}`,
      recipientCount: targetUsers.length,
      emailsSent,
      emailErrors,
      attachmentCount: attachments.length,
      notification: savedNotification
    });
  } catch (error) {
    console.error('Error sending street announcement:', error);
    res.status(500).json({
      error: 'Server error',
      message: 'Failed to send announcement'
    });
  }
};

/**
 * Send targeted announcement (Company Admin - HOA level)
 * @route POST /api/announcements/send/hoa
 * @access Private (Company Admin)
 */
exports.sendHOAAnnouncement = async (req, res) => {
  try {
    const { subject, message, hoaId, targetType, targetStreetId, sendEmail: shouldSendEmail } = req.body;
    console.log('Company admin sending HOA announcement:', { subject, hoaId, targetType, targetStreetId });

    // Handle file attachments
    const attachments = [];
    if (req.files && req.files.length > 0) {
      console.log(`Processing ${req.files.length} attachment(s)`);
      for (const file of req.files) {
        try {
          // Generate signed URL for attachment
          const signedUrl = await generateSignedUrl(file.key, 24 * 3600); // 24 hour expiration
          attachments.push({
            filename: file.originalname,
            s3Key: file.key,
            s3Location: file.location,
            signedUrl: signedUrl,
            mimetype: file.mimetype,
            size: file.size
          });
          console.log(`Attachment processed: ${file.originalname}`);
        } catch (error) {
          console.error(`Error processing attachment ${file.originalname}:`, error);
        }
      }
    }

    // Verify user is company admin
    if (req.user.role !== 'company_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only company administrators can send HOA-level announcements'
      });
    }

    // Validate required fields
    if (!subject || !message || !hoaId || !targetType) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Subject, message, HOA, and target type are required'
      });
    }

    // Get the HOA details
    const hoa = await HOA.findById(hoaId);
    if (!hoa) {
      return res.status(404).json({
        error: 'HOA not found',
        message: 'The specified HOA does not exist'
      });
    }

    let targetUsers = [];
    let targetDescription = '';

    if (targetType === 'all') {
      // Send to all residents in the HOA
      targetUsers = await User.find({
        hoaId: hoa._id,
        isDeleted: { $ne: true }
      });
      targetDescription = `all residents in ${hoa.hoaCommunityName}`;
    } else if (targetType === 'street' && targetStreetId) {
      // Send to specific street in the HOA
      const community = await Community.findById(targetStreetId);
      if (!community || community.hoaCommunityCode !== hoa.hoaCommunityCode) {
        return res.status(400).json({
          error: 'Invalid street',
          message: 'The specified street does not exist in this HOA'
        });
      }

      targetUsers = await User.find({
        communityId: community._id,
        isDeleted: { $ne: true }
      });
      targetDescription = `residents on ${community.name} in ${hoa.hoaCommunityName}`;
    } else {
      return res.status(400).json({
        error: 'Invalid targeting',
        message: 'Invalid target type or missing street selection'
      });
    }

    if (targetUsers.length === 0) {
      return res.status(400).json({
        error: 'No recipients found',
        message: 'No valid recipients found for this announcement'
      });
    }

    // Create notification
    const notification = new Notification({
      type: 'announcement',
      title: subject,
      description: message,
      icon: 'megaphone',
      priority: 'high',
      metadata: {
        subject,
        message,
        sentBy: req.user.username || req.user.email,
        sentById: req.user._id,
        targetType: 'hoa',
        targetHOA: hoa.hoaCommunityName,
        targetHOAId: hoaId,
        targetSubType: targetType,
        targetStreetId: targetStreetId,
        isAnnouncement: true,
        attachments: attachments // Include attachment information
      },
      recipients: targetUsers.map(user => ({ userId: user._id }))
    });

    const savedNotification = await notification.save();
    console.log(`HOA announcement notification created with ${targetUsers.length} recipients`);

    // Create persistent attachment records if files were uploaded
    if (req.files && req.files.length > 0) {
      try {
        await createPersistentAttachments(
          req.files,
          savedNotification._id,
          hoa._id,
          null, // No specific community for HOA-level announcements
          req.user._id
        );
        console.log(`Created persistent attachment records for ${req.files.length} files`);
      } catch (error) {
        console.error('Error creating persistent attachment records:', error);
        // Don't fail the announcement if attachment storage fails
      }
    }

    // Send emails if requested
    let emailsSent = 0;
    let emailErrors = 0;

    if (shouldSendEmail && targetUsers.length > 0) {
      console.log(`Sending emails to ${targetUsers.length} recipients...`);

      // Process emails in batches
      const batchSize = 10;
      const batches = [];
      for (let i = 0; i < targetUsers.length; i += batchSize) {
        batches.push(targetUsers.slice(i, i + batchSize));
      }

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Processing email batch ${i + 1}/${batches.length} (${batch.length} recipients)`);

        const emailResults = await Promise.all(batch.map(async (user) => {
          try {
            // Company admins always use company admin email (<EMAIL>)
            const result = await sendTemplateEmail(
              user.email,
              'hoaAnnouncement',
              {
                username: user.username || user.email,
                subject,
                message,
                hoaName: hoa.hoaCommunityName,
                senderName: 'HOAFLO Support',
                attachments: attachments, // Include attachment information
                actionLink: `${process.env.FRONTEND_URL}/notifications`
              },
              {
                senderType: 'company_admin'
              }
            );

            if (result.success) {
              emailsSent++;
              return { success: true, email: user.email };
            } else {
              emailErrors++;
              console.error(`Failed to send email to ${user.email}:`, result.error);
              return { success: false, email: user.email, error: result.error };
            }
          } catch (error) {
            emailErrors++;
            console.error(`Exception sending email to ${user.email}:`, error);
            return { success: false, email: user.email, error: error.message };
          }
        }));

        // Small delay between batches
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log('Email sending complete:', { emailsSent, emailErrors });
    }

    res.status(201).json({
      success: true,
      message: `Announcement sent to ${targetUsers.length} ${targetDescription}`,
      recipientCount: targetUsers.length,
      emailsSent,
      emailErrors,
      notification: savedNotification
    });
  } catch (error) {
    console.error('Error sending HOA announcement:', error);
    res.status(500).json({
      error: 'Server error',
      message: 'Failed to send announcement'
    });
  }
};

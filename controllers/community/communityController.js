const Community = require('../../models/community');
const User = require('../../models/user');
const HOA = require('../../models/hoa');
const mongoose = require('mongoose');

/**
 * Get all communities
 * @route GET /api/communities
 * @access Private (Admin, Company Admin)
 */
exports.getAllCommunities = async (req, res) => {
  try {
    // Get hoaId from query or user
    let { hoaId } = req.query;

    // Build query based on filters
    const query = {};

    // For company_admin, use the provided hoaId filter if any
    if (req.user.role === 'company_admin') {
      if (hoaId) {
        console.log(`Company admin filtering communities by HOA ID: ${hoaId}`);
        query.hoaId = hoaId;
      }
    }
    // For regular admin, restrict to their HOAs if they have any
    else if (req.user.role === 'admin') {
      if (req.user.hoaId && req.user.hoaId.length > 0) {
        query.hoaId = { $in: req.user.hoaId };
        console.log(`Admin restricted to HOA IDs: ${req.user.hoaId}`);
      }
    }
    // For members, they can only see communities from their own HOAs
    // and only public communities or their own communities
    else if (req.user.role === 'member') {
      if (req.user.hoaId && req.user.hoaId.length > 0) {
        query.hoaId = { $in: req.user.hoaId };
        console.log(`Member restricted to HOA IDs: ${req.user.hoaId}`);

        // Members can only see public communities or their own communities
        if (req.user.communityId && req.user.communityId.length > 0) {
          query.$or = [
            { visibility: 'public' },
            { _id: { $in: req.user.communityId } }
          ];
        } else {
          query.visibility = 'public';
        }
      } else {
        // If member doesn't have an HOA, they can't see any communities
        return res.json([]);
      }
    }

    const communities = await Community.find(query)
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
      .sort({ name: 1 });

    res.json(communities);
  } catch (err) {
    console.error('Error fetching communities:', err);
    res.status(500).json({
      message: 'Error fetching communities',
      error: err.message
    });
  }
};

/**
 * Get a single community by ID
 * @route GET /api/communities/:id
 * @access Private
 */
exports.getCommunity = async (req, res) => {
  try {
    const community = await Community.findById(req.params.id)
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode');

    if (!community) {
      return res.status(404).json({ message: 'Community not found' });
    }

    // Check if user has access to this community
    if (req.user.role !== 'company_admin') {
      // Admin access check
      if (req.user.role === 'admin' && req.user.hoaId && community.hoaId) {
        const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
        const communityHoaId = community.hoaId._id ? community.hoaId._id.toString() : community.hoaId.toString();

        const hasAccess = userHoaIds.some(hoaId => hoaId.toString() === communityHoaId);

        if (!hasAccess) {
          return res.status(403).json({
            message: 'You do not have access to this community'
          });
        }
      }

      // Member access check
      if (req.user.role === 'member') {
        // Members can only access public communities or their own community
        if (community.visibility !== 'public' && req.user.communityId) {
          const userCommunityIds = Array.isArray(req.user.communityId) ? req.user.communityId : [req.user.communityId];
          const hasAccess = userCommunityIds.some(communityId => communityId.toString() === community._id.toString());

          if (!hasAccess) {
            return res.status(403).json({
              message: 'You do not have access to this community'
            });
          }
        } else if (community.visibility !== 'public' && !req.user.communityId) {
          return res.status(403).json({
            message: 'You do not have access to this community'
          });
        }
      }
    }

    res.json(community);
  } catch (err) {
    console.error('Error fetching community:', err);
    res.status(500).json({
      message: 'Error fetching community',
      error: err.message
    });
  }
};

/**
 * Create a new community
 * @route POST /api/communities
 * @access Private (Admin, Company Admin)
 */
exports.createCommunity = async (req, res) => {
  try {
    // Only admins and company admins can create communities
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({
        message: 'Only administrators can create communities'
      });
    }

    const {
      name,
      description,
      streetAddress,
      city,
      state,
      zipCode,
      type,
      unitCount,
      hoaId,
      visibility,
      permissions
    } = req.body;

    // Determine the correct HOA ID based on user role
    let communityHoaId = hoaId;

    // If admin with specific HOA, ensure community is created for that HOA
    if (req.user.role === 'admin' && req.user.hoaId) {
      communityHoaId = req.user.hoaId;
    }
    // For company_admin, require an HOA ID
    else if (req.user.role === 'company_admin' && !communityHoaId) {
      return res.status(400).json({
        message: 'HOA ID is required when creating a community as company admin'
      });
    }

    // Generate a unique community code
    const communityCode = await Community.generateCommunityCode();

    // Create the community
    const community = new Community({
      name,
      description,
      streetAddress,
      city,
      state,
      zipCode,
      communityCode,
      hoaId: communityHoaId,
      type: type || 'single-family',
      unitCount: unitCount || 0,
      visibility: visibility || 'private',
      permissions: permissions || {
        memberVisibility: true,
        financeVisibility: false,
        documentVisibility: true
      }
    });

    await community.save();
    res.status(201).json(community);
  } catch (err) {
    console.error('Error creating community:', err);
    res.status(500).json({
      message: 'Error creating community',
      error: err.message
    });
  }
};

/**
 * Update a community
 * @route PUT /api/communities/:id
 * @access Private (Admin, Company Admin)
 */
exports.updateCommunity = async (req, res) => {
  try {
    // Only admins and company admins can update communities
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({
        message: 'Only administrators can update communities'
      });
    }

    const community = await Community.findById(req.params.id);

    if (!community) {
      return res.status(404).json({ message: 'Community not found' });
    }

    // Check if admin has access to this community's HOA
    if (req.user.role === 'admin' &&
        req.user.hoaId &&
        community.hoaId.toString() !== req.user.hoaId.toString()) {
      return res.status(403).json({
        message: 'You do not have access to update this community'
      });
    }

    // Don't allow changing the hoaId if admin is HOA-specific
    if (req.user.role === 'admin' && req.user.hoaId && req.body.hoaId) {
      req.body.hoaId = req.user.hoaId;
    }

    // Don't allow changing the community code
    if (req.body.communityCode) {
      delete req.body.communityCode;
    }

    const updatedCommunity = await Community.findByIdAndUpdate(
      req.params.id,
      { $set: req.body },
      { new: true, runValidators: true }
    );

    res.json(updatedCommunity);
  } catch (err) {
    console.error('Error updating community:', err);
    res.status(500).json({
      message: 'Error updating community',
      error: err.message
    });
  }
};

/**
 * Delete a community
 * @route DELETE /api/communities/:id
 * @access Private (Admin, Company Admin)
 */
exports.deleteCommunity = async (req, res) => {
  try {
    // Only admins and company admins can delete communities
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({
        message: 'Only administrators can delete communities'
      });
    }

    const community = await Community.findById(req.params.id);

    if (!community) {
      return res.status(404).json({ message: 'Community not found' });
    }

    // Check if admin has access to this community's HOA
    if (req.user.role === 'admin' &&
        req.user.hoaId &&
        community.hoaId.toString() !== req.user.hoaId.toString()) {
      return res.status(403).json({
        message: 'You do not have access to delete this community'
      });
    }

    // Check if there are users in this community
    const usersInCommunity = await User.countDocuments({ communityId: req.params.id });

    if (usersInCommunity > 0) {
      return res.status(400).json({
        message: `Cannot delete community with ${usersInCommunity} users. Reassign users first.`
      });
    }

    await Community.findByIdAndDelete(req.params.id);

    res.status(204).send();
  } catch (err) {
    console.error('Error deleting community:', err);
    res.status(500).json({
      message: 'Error deleting community',
      error: err.message
    });
  }
};

/**
 * Get community by code
 * @route GET /api/communities/code/:code
 * @access Public (needed for registration)
 */
exports.getCommunityByCode = async (req, res) => {
  try {
    const code = req.params.code.toUpperCase();
    const community = await Community.findOne({ communityCode: code })
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode verificationStatus');

    if (!community) {
      return res.status(404).json({
        message: 'Community not found with the provided code'
      });
    }

    // Check if the HOA is approved
    if (community.hoaId && community.hoaId.verificationStatus !== 'approved') {
      return res.status(403).json({
        message: 'The HOA for this community is not approved yet'
      });
    }

    res.json({
      _id: community._id,
      name: community.name,
      communityCode: community.communityCode,
      hoaId: community.hoaId ? {
        _id: community.hoaId._id,
        name: community.hoaId.hoaCommunityName,
        code: community.hoaId.hoaCommunityCode
      } : null
    });
  } catch (err) {
    console.error('Error fetching community by code:', err);
    res.status(500).json({
      message: 'Error fetching community',
      error: err.message
    });
  }
};

/**
 * Get communities by HOA ID(s)
 * @route GET /api/communities/hoa/:hoaId
 * @access Private (Admin, Company Admin)
 */
exports.getCommunitiesByHoa = async (req, res) => {
  try {
    const { hoaId } = req.params;

    // Handle multiple HOA IDs (comma-separated)
    let hoaIds = [];
    if (hoaId.includes(',')) {
      // Split comma-separated HOA IDs
      hoaIds = hoaId.split(',').map(id => id.trim()).filter(id => id.length > 0);
    } else {
      hoaIds = [hoaId];
    }

    // Validate all HOA IDs
    const invalidIds = hoaIds.filter(id => !mongoose.Types.ObjectId.isValid(id));
    if (invalidIds.length > 0) {
      return res.status(400).json({
        message: 'Invalid HOA ID format',
        invalidIds: invalidIds
      });
    }

    // Check if HOAs exist
    const hoas = await HOA.find({ _id: { $in: hoaIds } });
    if (hoas.length === 0) {
      return res.status(404).json({ message: 'No HOAs found' });
    }

    const foundHoaIds = hoas.map(hoa => hoa._id.toString());
    const missingHoaIds = hoaIds.filter(id => !foundHoaIds.includes(id));

    if (missingHoaIds.length > 0) {
      console.log(`Some HOAs not found: ${missingHoaIds.join(', ')}`);
    }

    // Check if user has access to these HOAs
    if (req.user.role === 'admin') {
      let hasAccess = false;

      // Check through user's hoaId array
      if (req.user.hoaId) {
        const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
        hasAccess = foundHoaIds.some(id => userHoaIds.some(userHoaId => userHoaId.toString() === id));
      }

      // If not found through hoaId, check through community codes
      if (!hasAccess && req.user.hoaCommunityCode) {
        const User = require('../../models/user');
        const user = await User.findById(req.user._id);

        if (user && user.hoaCommunityCode) {
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            // Check community codes
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId && foundHoaIds.includes(community.hoaId._id.toString())) {
              hasAccess = true;
              break;
            }

            // Check HOA codes
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa && foundHoaIds.includes(hoa._id.toString())) {
              hasAccess = true;
              break;
            }
          }
        }
      }

      if (!hasAccess) {
        return res.status(403).json({
          message: 'You do not have access to communities in these HOAs'
        });
      }
    }

    // Find communities for all accessible HOAs
    const communities = await Community.find({ hoaId: { $in: foundHoaIds } })
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
      .sort({ name: 1 });

    console.log(`Found ${communities.length} communities for HOAs: ${foundHoaIds.join(', ')}`);

    res.json(communities);
  } catch (err) {
    console.error('Error fetching communities by HOA:', err);
    res.status(500).json({
      message: 'Error fetching communities',
      error: err.message
    });
  }
};
const Finance = require('../../models/finance');
const User = require('../../models/user');
const HOA = require('../../models/hoa');
const Community = require('../../models/community');
const notificationController = require('../notification/notificationController');
const mongoose = require('mongoose');

// Create new finance entry
exports.createEntry = async (req, res) => {
  try {
    // Extract data from request body
    const { hoaId, communityId, ...financeData } = req.body;

    // Determine the correct HOA ID and Community ID based on user role and provided data
    let financeHoaId = hoaId;
    let financeCommunityId = communityId;

    // For company_admin users, allow them to specify any HOA
    if (req.user.role === 'company_admin') {
      if (!financeHoaId) {
        // Find the first HOA if none specified
        const firstHOA = await HOA.findOne({});
        if (firstHOA) {
          financeHoaId = firstHOA._id;
          console.log('Using first available HOA ID for company admin:', financeHoaId);
        }
      }
    }
    // For admin and member users, use their associated HOAs
    else if (req.user.role === 'admin' || req.user.role === 'member') {
      const user = await User.findById(req.user._id);

      if (!financeHoaId && user) {
        // If no HOA specified, use the user's first HOA
        if (user.hoaId && Array.isArray(user.hoaId) && user.hoaId.length > 0) {
          financeHoaId = user.hoaId[0];
          console.log('Using user\'s first HOA ID:', financeHoaId);
        } else if (user.hoaId && !Array.isArray(user.hoaId)) {
          financeHoaId = user.hoaId;
          console.log('Using user\'s HOA ID:', financeHoaId);
        } else if (user.hoaCommunityCode) {
          // Try to find HOA by community code
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            // First try to find a community with this code
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId) {
              financeHoaId = community.hoaId._id;
              if (!financeCommunityId) {
                financeCommunityId = community._id;
              }
              console.log('Found HOA ID from community code:', code, financeHoaId);
              break;
            } else {
              // For backward compatibility, check if it's an HOA code
              const hoa = await HOA.findOne({ hoaCommunityCode: code });
              if (hoa) {
                financeHoaId = hoa._id;
                console.log('Found HOA ID from HOA code:', code, financeHoaId);
                break;
              }
            }
          }
        }
      }

      // Verify user has access to the specified HOA
      if (financeHoaId && user) {
        let hasAccess = false;

        // Check through user's hoaId array
        if (user.hoaId) {
          const userHoaIds = Array.isArray(user.hoaId) ? user.hoaId : [user.hoaId];
          hasAccess = userHoaIds.some(id => id.toString() === financeHoaId.toString());
        }

        // Check through community codes if not found
        if (!hasAccess && user.hoaCommunityCode) {
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId && community.hoaId._id.toString() === financeHoaId.toString()) {
              hasAccess = true;
              break;
            }

            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa && hoa._id.toString() === financeHoaId.toString()) {
              hasAccess = true;
              break;
            }
          }
        }

        if (!hasAccess) {
          return res.status(403).json({
            success: false,
            message: 'You do not have permission to create finance entries for this HOA'
          });
        }
      }

      if (!financeHoaId) {
        return res.status(403).json({
          message: 'Users must be associated with an HOA to create finance entries'
        });
      }
    }

    // Convert hoaId to ObjectId if it exists
    if (financeHoaId) {
      try {
        financeHoaId = new mongoose.Types.ObjectId(financeHoaId);
        console.log(`Converted hoaId to ObjectId: ${financeHoaId}`);
      } catch (err) {
        console.error('Error converting hoaId to ObjectId:', err);
        // Continue with the original value
      }
    }

    // Convert communityId to ObjectId if it exists
    if (financeCommunityId) {
      try {
        financeCommunityId = new mongoose.Types.ObjectId(financeCommunityId);
        console.log(`Converted communityId to ObjectId: ${financeCommunityId}`);
      } catch (err) {
        console.error('Error converting communityId to ObjectId:', err);
        // Continue with the original value
      }
    }

    // Create and save the finance entry
    const finance = new Finance({
      ...financeData,
      hoaId: financeHoaId,
      communityId: financeCommunityId,
      createdBy: req.user._id
    });

    // Log the finance entry details for debugging
    console.log('Creating finance entry:', {
      type: finance.type,
      category: finance.category,
      amount: finance.amount,
      hoaId: finance.hoaId,
      communityId: finance.communityId,
      createdBy: finance.createdBy
    });

    await finance.save();

    // Get all users to notify
    const users = await User.find({ isApproved: true, denied: false });
    const userIds = users.map(user => user._id);

    // If it's an income entry, create a payment notification
    if (finance.type === 'income') {
      await notificationController.createPaymentNotification(
        finance.amount,
        userIds
      );
    }

    // If it's an expense entry, notify admins
    if (finance.type === 'expense') {
      const adminUsers = await User.find({ role: 'admin' });
      const adminIds = adminUsers.map(user => user._id);

      await notificationController.createInfoNotification(
        'New Expense Added',
        `A new expense of $${finance.amount} has been added for ${finance.category}`,
        adminIds
      );
    }

    res.status(201).json({ message: 'Finance entry created', finance });
  } catch (err) {
    console.error('Create finance error:', err);
    res.status(500).json({ error: 'Failed to create entry' });
  }
};

// Get monthly income vs expense data
exports.getMonthlyFinance = async (req, res) => {
  try {
    // Check if hoaId and communityId filters are provided
    let { hoaId, communityId } = req.query;

    // Build pipeline with optional match stage
    const pipeline = [];
    let hoaCommunityCode = null;

    // If communityId is provided, get the HOA community code from it
    if (communityId) {
      console.log(`Getting HOA community code from Community ID: ${communityId}`);
      try {
        const community = await Community.findById(communityId).populate('hoaId');
        if (community && community.hoaId) {
          hoaCommunityCode = community.hoaId.hoaCommunityCode;
          console.log(`Found HOA community code: ${hoaCommunityCode}`);
        }
      } catch (err) {
        console.error('Error finding community:', err);
      }
    }

    // If we have hoaCommunityCode, use it to filter finances
    if (hoaCommunityCode) {
      console.log(`Filtering finances by HOA community code: ${hoaCommunityCode}`);
      pipeline.push({
        $match: {
          hoaCommunityCode: hoaCommunityCode,
          $or: [
            { communityId: { $exists: false } }, // HOA-wide finances
            { communityId: communityId } // Community-specific finances
          ]
        }
      });
    }
    // Fallback: Handle comma-separated HOA IDs if no communityId provided
    else if (hoaId) {
      let hoaIds = [];
      // Handle case where frontend sends [object Object]
      if (hoaId === '[object Object]' || hoaId.toString() === '[object Object]') {
        console.log('Frontend sent [object Object] for hoaId, ignoring...');
        hoaIds = [];
      } else if (typeof hoaId === 'string' && hoaId.includes(',')) {
        hoaIds = hoaId.split(',').map(id => id.trim()).filter(id => id.length > 0);
      } else if (typeof hoaId === 'string') {
        hoaIds = [hoaId];
      } else {
        console.log('Invalid hoaId format received:', hoaId);
        hoaIds = [];
      }

      if (hoaIds.length > 0) {
        console.log(`Filtering finances by HOA IDs: ${hoaIds.join(', ')}`);
        // Get HOA community codes from HOA IDs
        const hoas = await HOA.find({ _id: { $in: hoaIds } });
        const hoaCodes = hoas.map(hoa => hoa.hoaCommunityCode);
        console.log(`Found HOA community codes: ${hoaCodes}`);

        if (hoaCodes.length > 0) {
          pipeline.push({
            $match: { hoaCommunityCode: { $in: hoaCodes } }
          });
        }
      }
    }

    // If user is authenticated and no specific HOA filter provided, apply role-based filtering
    if (req.user && !hoaCommunityCode && pipeline.length === 0) {
      console.log(`No valid HOA filter provided, applying role-based filtering for user role: ${req.user.role}`);
      // Company admins can see all finances
      if (req.user.role === 'company_admin') {
        console.log('Company admin - no additional filtering needed');
        // No additional filtering needed
      }
      // For regular admin and members, restrict to their HOAs
      else if (req.user.role === 'admin' || req.user.role === 'member') {
        const user = await User.findById(req.user._id);
        let userHoaIds = [];

        if (user) {
          console.log(`User found: ${user.email}, hoaId: ${user.hoaId}, hoaCommunityCode: ${user.hoaCommunityCode}`);
          // Get HOA IDs from user's hoaId field
          if (user.hoaId) {
            let userHoaIdArray;
            if (Array.isArray(user.hoaId)) {
              userHoaIdArray = user.hoaId;
            } else if (typeof user.hoaId === 'string' && user.hoaId.includes(',')) {
              // Handle comma-separated string format
              userHoaIdArray = user.hoaId.split(',').map(id => id.trim()).filter(id => id.length > 0);
            } else {
              userHoaIdArray = [user.hoaId];
            }
            userHoaIds = userHoaIdArray.map(id => id.toString());
            console.log(`User HOA IDs from hoaId field: ${userHoaIds}`);
          }

          // Get HOA IDs from user's community codes
          if (user.hoaCommunityCode) {
            const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

            for (const code of codes) {
              // Check community codes
              const community = await Community.findOne({ communityCode: code }).populate('hoaId');
              if (community && community.hoaId) {
                userHoaIds.push(community.hoaId._id.toString());
              }

              // Check HOA codes
              const hoa = await HOA.findOne({ hoaCommunityCode: code });
              if (hoa) {
                userHoaIds.push(hoa._id.toString());
              }
            }
          }

          // Remove duplicates
          userHoaIds = [...new Set(userHoaIds)];

          if (userHoaIds.length > 0) {
            // Convert HOA IDs to HOA community codes for more efficient searching
            const hoas = await HOA.find({ _id: { $in: userHoaIds } });
            const userHoaCodes = hoas.map(hoa => hoa.hoaCommunityCode);

            console.log(`${req.user.role} restricted to HOA community codes: ${userHoaCodes.join(', ')}`);
            pipeline.push({
              $match: { hoaCommunityCode: { $in: userHoaCodes } }
            });
          } else {
            console.log(`No HOA IDs found for ${req.user.role} user - no finance data will be returned`);
          }
        }
      }
    }

    // Add grouping stage
    pipeline.push({
      $group: {
        _id: { $month: '$date' },
        income: {
          $sum: {
            $cond: [{ $eq: ['$type', 'income'] }, '$amount', 0]
          }
        },
        expenses: {
          $sum: {
            $cond: [{ $eq: ['$type', 'expense'] }, '$amount', 0]
          }
        }
      }
    });

    // Add projection stage
    pipeline.push({
      $project: {
        month: '$_id',
        income: 1,
        expenses: 1,
        _id: 0
      }
    });

    // Add sort stage
    pipeline.push({ $sort: { month: 1 } });

    const data = await Finance.aggregate(pipeline);
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const formatted = data.map(d => ({
      ...d,
      month: monthNames[d.month - 1]
    }));

    res.json(formatted);
  } catch (err) {
    console.error('Monthly data error:', err);
    res.status(500).json({ error: 'Failed to fetch chart data' });
  }
};

// Get breakdown by category
exports.getBreakdown = async (req, res) => {
  try {
    // Check if hoaId and communityId filters are provided
    let { hoaId, communityId } = req.query;

    // Build match stage based on filters
    const matchStage = { type: 'expense' };
    let hoaCommunityCode = null;

    // If communityId is provided, get the HOA community code from it
    if (communityId) {
      console.log(`Getting HOA community code from Community ID: ${communityId}`);
      try {
        const community = await Community.findById(communityId).populate('hoaId');
        if (community && community.hoaId) {
          hoaCommunityCode = community.hoaId.hoaCommunityCode;
          console.log(`Found HOA community code: ${hoaCommunityCode}`);
        }
      } catch (err) {
        console.error('Error finding community:', err);
      }
    }

    // If we have hoaCommunityCode, use it to filter finances
    if (hoaCommunityCode) {
      console.log(`Filtering finance breakdown by HOA community code: ${hoaCommunityCode}`);
      matchStage.hoaCommunityCode = hoaCommunityCode;
      if (communityId) {
        matchStage.$or = [
          { communityId: { $exists: false } }, // HOA-wide finances
          { communityId: communityId } // Community-specific finances
        ];
      }
    }
    // Fallback: Handle comma-separated HOA IDs if no communityId provided
    else if (hoaId) {
      let hoaIds = [];
      // Handle case where frontend sends [object Object]
      if (hoaId === '[object Object]' || hoaId.toString() === '[object Object]') {
        console.log('Frontend sent [object Object] for hoaId, ignoring...');
        hoaIds = [];
      } else if (typeof hoaId === 'string' && hoaId.includes(',')) {
        hoaIds = hoaId.split(',').map(id => id.trim()).filter(id => id.length > 0);
      } else if (typeof hoaId === 'string') {
        hoaIds = [hoaId];
      } else {
        console.log('Invalid hoaId format received:', hoaId);
        hoaIds = [];
      }

      if (hoaIds.length > 0) {
        console.log(`Filtering finance breakdown by HOA IDs: ${hoaIds.join(', ')}`);
        // Get HOA community codes from HOA IDs
        const hoas = await HOA.find({ _id: { $in: hoaIds } });
        const hoaCodes = hoas.map(hoa => hoa.hoaCommunityCode);
        console.log(`Found HOA community codes: ${hoaCodes}`);

        if (hoaCodes.length > 0) {
          matchStage.hoaCommunityCode = { $in: hoaCodes };
        }
      }
    }

    // If user is authenticated and no specific HOA filter provided, apply role-based filtering
    if (req.user && !hoaCommunityCode && !matchStage.hoaCommunityCode) {
      // Company admins can see all finances
      if (req.user.role === 'company_admin') {
        // No additional filtering needed
      }
      // For regular admin and members, restrict to their HOAs
      else if (req.user.role === 'admin' || req.user.role === 'member') {
        const user = await User.findById(req.user._id);
        let userHoaIds = [];

        if (user) {
          // Get HOA IDs from user's hoaId field
          if (user.hoaId) {
            let userHoaIdArray;
            if (Array.isArray(user.hoaId)) {
              userHoaIdArray = user.hoaId;
            } else if (typeof user.hoaId === 'string' && user.hoaId.includes(',')) {
              // Handle comma-separated string format
              userHoaIdArray = user.hoaId.split(',').map(id => id.trim()).filter(id => id.length > 0);
            } else {
              userHoaIdArray = [user.hoaId];
            }
            userHoaIds = userHoaIdArray.map(id => id.toString());
          }

          // Get HOA IDs from user's community codes
          if (user.hoaCommunityCode) {
            const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

            for (const code of codes) {
              // Check community codes
              const community = await Community.findOne({ communityCode: code }).populate('hoaId');
              if (community && community.hoaId) {
                userHoaIds.push(community.hoaId._id.toString());
              }

              // Check HOA codes
              const hoa = await HOA.findOne({ hoaCommunityCode: code });
              if (hoa) {
                userHoaIds.push(hoa._id.toString());
              }
            }
          }

          // Remove duplicates
          userHoaIds = [...new Set(userHoaIds)];

          if (userHoaIds.length > 0) {
            // Convert HOA IDs to HOA community codes for more efficient searching
            const hoas = await HOA.find({ _id: { $in: userHoaIds } });
            const userHoaCodes = hoas.map(hoa => hoa.hoaCommunityCode);
            console.log(`${req.user.role} restricted to HOA community codes: ${userHoaCodes.join(', ')}`);
            matchStage.hoaCommunityCode = { $in: userHoaCodes };
          }
        }
      }
    }

    const data = await Finance.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$category',
          amount: { $sum: '$amount' }
        }
      }
    ]);

    const total = data.reduce((sum, item) => sum + item.amount, 0);
    const breakdown = data.map(item => ({
      category: item._id,
      amount: item.amount,
      percentage: Math.round((item.amount / total) * 100)
    }));

    res.json(breakdown);
  } catch (err) {
    console.error('Breakdown error:', err);
    res.status(500).json({ error: 'Failed to get breakdown' });
  }
};

// Alias for getBreakdown to match route naming
exports.getExpenseBreakdown = exports.getBreakdown;

// Get all finance entries with HOA filtering
exports.getAllEntries = async (req, res) => {
  try {
    // Check if hoaId and communityId filters are provided
    let { hoaId, communityId } = req.query;

    // Build match stage based on filters
    let matchStage = {};
    let hoaCommunityCode = null;

    // If communityId is provided, get the HOA community code from it
    if (communityId) {
      console.log(`Getting HOA community code from Community ID: ${communityId}`);
      try {
        const community = await Community.findById(communityId).populate('hoaId');
        if (community && community.hoaId) {
          hoaCommunityCode = community.hoaId.hoaCommunityCode;
          console.log(`Found HOA community code: ${hoaCommunityCode}`);
        }
      } catch (err) {
        console.error('Error finding community:', err);
      }
    }

    // If we have hoaCommunityCode, use it to filter finances
    if (hoaCommunityCode) {
      console.log(`Filtering finance entries by HOA community code: ${hoaCommunityCode}`);
      matchStage.hoaCommunityCode = hoaCommunityCode;
      if (communityId) {
        matchStage.$or = [
          { communityId: { $exists: false } }, // HOA-wide finances
          { communityId: communityId } // Community-specific finances
        ];
      }
    }
    // Fallback: Handle comma-separated HOA IDs if no communityId provided
    else if (hoaId) {
      let hoaIds = [];
      // Handle case where frontend sends [object Object]
      if (hoaId === '[object Object]' || hoaId.toString() === '[object Object]') {
        console.log('Frontend sent [object Object] for hoaId, ignoring...');
        hoaIds = [];
      } else if (typeof hoaId === 'string' && hoaId.includes(',')) {
        hoaIds = hoaId.split(',').map(id => id.trim()).filter(id => id.length > 0);
      } else if (typeof hoaId === 'string') {
        hoaIds = [hoaId];
      } else {
        console.log('Invalid hoaId format received:', hoaId);
        hoaIds = [];
      }

      if (hoaIds.length > 0) {
        console.log(`Filtering finance entries by HOA IDs: ${hoaIds.join(', ')}`);
        // Get HOA community codes from HOA IDs
        const hoas = await HOA.find({ _id: { $in: hoaIds } });
        const hoaCodes = hoas.map(hoa => hoa.hoaCommunityCode);
        console.log(`Found HOA community codes: ${hoaCodes}`);

        if (hoaCodes.length > 0) {
          matchStage.hoaCommunityCode = { $in: hoaCodes };
        }
      }
    }

    // If user is authenticated and no specific HOA filter provided, apply role-based filtering
    if (req.user && !hoaCommunityCode && !matchStage.hoaCommunityCode) {
      // Company admins can see all finances
      if (req.user.role === 'company_admin') {
        // No additional filtering needed
      }
      // For regular admin and members, restrict to their HOAs
      else if (req.user.role === 'admin' || req.user.role === 'member') {
        const user = await User.findById(req.user._id);
        let userHoaIds = [];

        if (user) {
          // Get HOA IDs from user's hoaId field
          if (user.hoaId) {
            let userHoaIdArray;
            if (Array.isArray(user.hoaId)) {
              userHoaIdArray = user.hoaId;
            } else if (typeof user.hoaId === 'string' && user.hoaId.includes(',')) {
              // Handle comma-separated string format
              userHoaIdArray = user.hoaId.split(',').map(id => id.trim()).filter(id => id.length > 0);
            } else {
              userHoaIdArray = [user.hoaId];
            }
            userHoaIds = userHoaIdArray.map(id => id.toString());
          }

          // Get HOA IDs from user's community codes
          if (user.hoaCommunityCode) {
            const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

            for (const code of codes) {
              // Check community codes
              const community = await Community.findOne({ communityCode: code }).populate('hoaId');
              if (community && community.hoaId) {
                userHoaIds.push(community.hoaId._id.toString());
              }

              // Check HOA codes
              const hoa = await HOA.findOne({ hoaCommunityCode: code });
              if (hoa) {
                userHoaIds.push(hoa._id.toString());
              }
            }
          }

          // Remove duplicates
          userHoaIds = [...new Set(userHoaIds)];

          if (userHoaIds.length > 0) {
            // Convert HOA IDs to HOA community codes for more efficient searching
            const hoas = await HOA.find({ _id: { $in: userHoaIds } });
            const userHoaCodes = hoas.map(hoa => hoa.hoaCommunityCode);
            console.log(`${req.user.role} restricted to HOA community codes: ${userHoaCodes.join(', ')}`);
            matchStage.hoaCommunityCode = { $in: userHoaCodes };
          }
        }
      }
    }

    // Find all entries and populate the creator's information
    const entries = await Finance.find(matchStage)
      .populate('createdBy', 'username fullName _id')
      .sort({ createdAt: -1 });

    // Add a flag to indicate if the current user can edit/delete each entry
    const enhancedEntries = entries.map(entry => {
      const entryObj = entry.toObject();
      // User can edit/delete if they created it or if they're an admin (but not company admin)
      entryObj.canModify =
        (req.user._id.toString() === entryObj.createdBy._id.toString()) ||
        (req.user.role === 'admin');
      return entryObj;
    });

    res.json(enhancedEntries);
  } catch (err) {
    console.error('Fetch finance entries error:', err);
    res.status(500).json({ error: 'Failed to fetch finance entries' });
  }
};

// Delete finance entry
exports.deleteEntry = async (req, res) => {
  try {
    const entryId = req.params.id;

    // Find the finance entry first
    const entry = await Finance.findById(entryId);

    if (!entry) {
      return res.status(404).json({
        success: false,
        message: 'Finance entry not found'
      });
    }

    // Check if the user is authorized to delete this entry
    // Users can only delete their own entries, but admins can delete any entry in their HOA
    if (entry.createdBy.toString() !== req.user._id.toString() && req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({
        success: false,
        message: 'You can only delete your own finance entries'
      });
    }

    // For admins, check if they have access to the HOA of this entry
    if (req.user.role === 'admin' && entry.createdBy.toString() !== req.user._id.toString()) {
      const user = await User.findById(req.user._id);
      let hasAccess = false;

      if (user) {
        // Check through user's hoaId array
        if (user.hoaId) {
          const userHoaIds = Array.isArray(user.hoaId) ? user.hoaId : [user.hoaId];
          hasAccess = userHoaIds.some(hoaId => hoaId.toString() === entry.hoaId.toString());
        }

        // Check through community codes if not found
        if (!hasAccess && user.hoaCommunityCode) {
          const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

          for (const code of codes) {
            const community = await Community.findOne({ communityCode: code }).populate('hoaId');
            if (community && community.hoaId && community.hoaId._id.toString() === entry.hoaId.toString()) {
              hasAccess = true;
              break;
            }

            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa && hoa._id.toString() === entry.hoaId.toString()) {
              hasAccess = true;
              break;
            }
          }
        }

        if (!hasAccess) {
          return res.status(403).json({
            success: false,
            message: 'You do not have permission to delete this finance entry'
          });
        }
      }
    }

    // If authorized, delete the entry
    await Finance.findByIdAndDelete(entryId);

    res.status(200).json({
      success: true,
      message: 'Finance entry deleted successfully'
    });
  } catch (err) {
    console.error('Delete finance entry error:', err);
    res.status(500).json({
      success: false,
      message: 'Failed to delete finance entry',
      error: err.message
    });
  }
};

// Check for overdue payments and send notifications
exports.checkOverduePayments = async () => {
  try {
    const today = new Date();
    const currentMonth = today.getMonth() + 1;
    const currentYear = today.getFullYear();
    const settings = await Settings.findOne();
    const dueDay = settings?.dueDate?.day || 15;
    const gracePeriod = settings?.dueDate?.gracePeriod || 3;

    // Only check for overdue if we're past the due date + grace period
    if (today.getDate() <= dueDay + gracePeriod) {
      return;
    }

    // Get all members
    const members = await User.find({
      role: 'member',
      isApproved: true,
      denied: false
    });

    // Get this month's payments
    const payments = await Payment.find({
      month: currentMonth,
      year: currentYear
    });

    // Find members who haven't paid
    const overdueUsers = members.filter(member => {
      return !payments.some(payment =>
        payment.userId.toString() === member._id.toString()
      );
    });

    if (overdueUsers.length > 0) {
      // Create overdue notification for each user
      for (const user of overdueUsers) {
        await notificationController.createOverdueNotification([{
          _id: user._id,
          amount: settings?.monthlyDue || 100
        }]);
      }

      // Create summary notification for admins
      const adminUsers = await User.find({ role: 'admin' });
      const adminIds = adminUsers.map(user => user._id);

      await notificationController.createInfoNotification(
        'Overdue Payments Summary',
        `${overdueUsers.length} members have overdue payments for this month`,
        adminIds
      );
    }
  } catch (err) {
    console.error('Error checking overdue payments:', err);
  }
};

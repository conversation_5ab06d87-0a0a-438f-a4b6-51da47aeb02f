const Settings = require('../../models/settings');

// Get current settings
exports.getSettings = async (req, res) => {
  try {
    console.log('User from request:', req.user); // Debug log
    
    let settings = await Settings.findOne().populate('lastModifiedBy', 'username email');
    console.log('Found settings:', settings); // Debug log

    if (!settings) {
      // Create default settings if none exist
      settings = new Settings({
        dueDate: {
          day: 15,
          reminderDays: 7,
          gracePeriod: 3
        },
        lastModifiedBy: req.user.id,
        lastModifiedAt: new Date()
      });
      
      await settings.save();
      // Populate the newly created settings
      settings = await settings.populate('lastModifiedBy', 'username email');
    } else if (typeof settings.dueDate === 'number') {
      // Handle legacy format
      const day = settings.dueDate;
      settings.dueDate = {
        day: day,
        reminderDays: 7,
        gracePeriod: 3
      };
      await settings.save();
    }

    console.log('Returning settings:', settings); // Debug log
    res.json(settings);
  } catch (err) {
    console.error('Error fetching settings:', err);
    res.status(500).json({ message: 'Failed to fetch settings' });
  }
};

// Update settings
exports.updateSettings = async (req, res) => {
  try {
    console.log('Update request body:', req.body); // Debug log
    const { dueDate } = req.body;
    
    // Validate due date structure
    if (!dueDate || typeof dueDate !== 'object') {
      return res.status(400).json({ message: 'Invalid due date settings format' });
    }

    // Validate day
    if (!dueDate.day || dueDate.day < 1 || dueDate.day > 31) {
      return res.status(400).json({ message: 'Due date must be between 1 and 31' });
    }

    // Validate reminder days
    if (typeof dueDate.reminderDays !== 'number' || dueDate.reminderDays < 1 || dueDate.reminderDays > 14) {
      return res.status(400).json({ message: 'Reminder days must be between 1 and 14' });
    }

    // Validate grace period
    if (typeof dueDate.gracePeriod !== 'number' || dueDate.gracePeriod < 0 || dueDate.gracePeriod > 14) {
      return res.status(400).json({ message: 'Grace period must be between 0 and 14 days' });
    }

    let settings = await Settings.findOne();
    if (!settings) {
      settings = new Settings();
    }

    settings.dueDate = dueDate;
    settings.lastModifiedBy = req.user.id;
    settings.lastModifiedAt = new Date();

    await settings.save();
    // Populate before sending response
    await settings.populate('lastModifiedBy', 'username email');
    
    console.log('Updated settings:', settings); // Debug log
    res.json(settings);
  } catch (err) {
    console.error('Error updating settings:', err);
    res.status(500).json({ message: 'Failed to update settings' });
  }
};

// Calculate due status for a member
exports.calculateDueStatus = (paymentDate, settings) => {
  const today = new Date();
  const dueDay = settings.dueDate.day;
  const gracePeriod = settings.dueDate.gracePeriod;
  
  // Get this month's due date
  const thisMonthDue = new Date(today.getFullYear(), today.getMonth(), dueDay);
  
  // Add grace period
  const dueWithGrace = new Date(thisMonthDue);
  dueWithGrace.setDate(dueWithGrace.getDate() + gracePeriod);
  
  // If we're past the grace period and no payment
  if (!paymentDate && today > dueWithGrace) {
    return 'overdue';
  }
  
  // If payment exists and it's after the due date
  if (paymentDate && paymentDate > dueWithGrace) {
    return 'overdue';
  }
  
  // If payment exists and it's before or on the due date
  if (paymentDate && paymentDate <= dueWithGrace) {
    return 'paid';
  }
  
  // If we're before the due date
  return 'pending';
}; 
const MaintenanceRequest = require('../../models/maintenanceRequest');
const Property = require('../../models/property');
const mongoose = require('mongoose');

/**
 * @desc    Get all maintenance requests for an HOA or specific communities
 * @route   GET /api/maintenance
 * @access  Private
 * @query   communityId - Array of community IDs or single community ID to filter maintenance requests
 */
exports.getMaintenanceRequests = async (req, res) => {
  try {
    let { communityId } = req.query;
    let hoaId = req.query.hoaId || req.user.hoaId;
    let hoaCommunityCode = req.query.hoaCommunityCode || req.user.hoaCommunityCode;
    let hoaIds = [];
    let communityIds = [];

    console.log('🔍 DEBUG: getMaintenanceRequests called with:', {
      communityId,
      hoaId,
      hoaCommunityCode,
      userId: req.user._id,
      userRole: req.user.role
    });

    // For company_admin users, allow them to see all maintenance requests
    if (req.user.role === 'company_admin') {
      // If specific community IDs are provided, filter by them
      if (communityId) {
        // Parse communityId if it's a string representation of an array
        if (typeof communityId === 'string') {
          try {
            // Try to parse as JSON array first
            if (communityId.startsWith('[') && communityId.endsWith(']')) {
              communityId = JSON.parse(communityId);
            } else if (communityId.includes(',')) {
              // Handle comma-separated values
              communityId = communityId.split(',').map(id => id.trim());
            } else {
              // Single community ID
              communityId = [communityId];
            }
          } catch (parseError) {
            // If parsing fails, treat as single ID
            communityId = [communityId];
          }
        }

        // Ensure communityId is an array
        if (!Array.isArray(communityId)) {
          communityId = [communityId];
        }

        console.log('🔍 DEBUG: Company admin filtering by community IDs:', communityId);

        // Find properties in the specified communities to get maintenance requests
        const properties = await Property.find({ communityId: { $in: communityId } });
        const propertyIds = properties.map(p => p._id);

        const requests = await MaintenanceRequest.find({ property: { $in: propertyIds } })
          .populate('property', 'address type communityId hoaId')
          .populate('resident', 'username email firstName lastName')
          .sort({ dateSubmitted: -1 });

        return res.status(200).json({
          success: true,
          count: requests.length,
          data: requests,
          selectedCommunityIds: communityId
        });
      } else {
        // No community filter, show all maintenance requests
        const requests = await MaintenanceRequest.find({})
          .populate('property', 'address type communityId hoaId')
          .populate('resident', 'username email firstName lastName')
          .sort({ dateSubmitted: -1 });

        return res.status(200).json({
          success: true,
          count: requests.length,
          data: requests
        });
      }
    }

    // Handle community-based filtering from frontend
    if (communityId) {
      console.log('🔍 DEBUG: Frontend provided community IDs for filtering');

      // Parse communityId if it's a string representation of an array
      if (typeof communityId === 'string') {
        try {
          // Try to parse as JSON array first
          if (communityId.startsWith('[') && communityId.endsWith(']')) {
            communityId = JSON.parse(communityId);
          } else if (communityId.includes(',')) {
            // Handle comma-separated values
            communityId = communityId.split(',').map(id => id.trim());
          } else {
            // Single community ID
            communityId = [communityId];
          }
        } catch (parseError) {
          // If parsing fails, treat as single ID
          communityId = [communityId];
        }
      }

      // Ensure communityId is an array
      if (!Array.isArray(communityId)) {
        communityId = [communityId];
      }

      console.log('🔍 DEBUG: Processing frontend community IDs:', communityId);

      const Community = require('../../models/community');

      // Find communities and get their HOA IDs
      const communities = await Community.find({
        _id: { $in: communityId }
      }).populate('hoaId', '_id hoaCommunityName');

      console.log('🔍 DEBUG: Found communities:', communities.length);

      if (communities.length === 0) {
        console.log('🔍 DEBUG: No communities found for provided IDs');
        return res.status(404).json({
          success: false,
          message: 'No communities found for the provided IDs'
        });
      }

      // Extract HOA IDs and community IDs
      communities.forEach(community => {
        if (community.hoaId) {
          hoaIds.push(community.hoaId._id);
          communityIds.push(community._id);
          console.log(`🔍 DEBUG: Found community ${community.name} with HOA ${community.hoaId.hoaCommunityName}`);
        }
      });

      // Remove duplicates
      hoaIds = [...new Set(hoaIds.map(id => id.toString()))];
      communityIds = [...new Set(communityIds.map(id => id.toString()))];
    }
    // For admin and member users who have HOA associations (fallback to user's communities)
    else if (hoaCommunityCode && (req.user.role === 'admin' || req.user.role === 'member')) {
      const User = require('../../models/user');
      const HOA = require('../../models/hoa');
      const Community = require('../../models/community');

      console.log('🔍 DEBUG: Using user HOA community codes for filtering');

      // Get the user's full profile to access HOA associations
      const user = await User.findById(req.user._id);

      if (user && user.hoaCommunityCode) {
        // Handle array of community codes
        const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

        console.log('🔍 DEBUG: Processing user community codes:', codes);

        // Process each community code to get HOA and community IDs
        for (const code of codes) {
          // First try to find a community with this code
          const community = await Community.findOne({ communityCode: code })
            .populate('hoaId', '_id hoaCommunityName');

          if (community && community.hoaId) {
            hoaIds.push(community.hoaId._id);
            communityIds.push(community._id);
            console.log(`🔍 DEBUG: Found community ${community.name} with HOA ${community.hoaId.hoaCommunityName}`);
          } else {
            // For backward compatibility, check if it's an HOA code
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa) {
              hoaIds.push(hoa._id);
              console.log(`🔍 DEBUG: Found HOA ${hoa.hoaCommunityName} with code ${code}`);

              // Try to find communities for this HOA
              const hoaCommunities = await Community.find({ hoaId: hoa._id });
              hoaCommunities.forEach(comm => communityIds.push(comm._id));
            }
          }
        }

        // Remove duplicates
        hoaIds = [...new Set(hoaIds.map(id => id.toString()))];
        communityIds = [...new Set(communityIds.map(id => id.toString()))];
      }
    }
    // Fallback to traditional HOA ID lookup
    else if (hoaId) {
      hoaIds = [hoaId];
    }

    if (hoaIds.length === 0) {
      console.log('🔍 DEBUG: No HOA associations found');
      return res.status(400).json({
        success: false,
        message: 'No HOA associations found for user'
      });
    }

    // Find properties in the specified communities/HOAs
    let propertyQuery = {};
    if (communityIds.length > 0) {
      propertyQuery.communityId = { $in: communityIds };
      console.log('🔍 DEBUG: Filtering properties by specific communities:', communityIds);
    } else {
      propertyQuery.hoaId = { $in: hoaIds };
      console.log('🔍 DEBUG: Filtering properties by HOAs:', hoaIds);
    }

    const properties = await Property.find(propertyQuery);
    const propertyIds = properties.map(p => p._id);

    console.log('🔍 DEBUG: Found properties:', properties.length);

    // Find maintenance requests for the properties
    const requests = await MaintenanceRequest.find({ property: { $in: propertyIds } })
      .populate('property', 'address type communityId hoaId')
      .populate('resident', 'username email firstName lastName')
      .sort({ dateSubmitted: -1 });

    console.log('🔍 DEBUG: Found maintenance requests:', requests.length);

    res.status(200).json({
      success: true,
      count: requests.length,
      data: requests,
      selectedCommunityIds: communityIds.length > 0 ? communityIds : null
    });
  } catch (error) {
    console.error('Error fetching maintenance requests:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Get a single maintenance request
 * @route   GET /api/maintenance/:id
 * @access  Private
 */
exports.getMaintenanceRequest = async (req, res) => {
  try {
    const request = await MaintenanceRequest.findById(req.params.id)
      .populate('property', 'address type status')
      .populate('resident', 'username email firstName lastName')
      .populate('hoaId', 'hoaCommunityName');

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance request not found'
      });
    }

    // Check if user has access to this request's HOA
    // Company admins always have access
    if (req.user.role === 'company_admin') {
      // Allow access for company admins
    }
    // Global admins (without specific HOA) have access to all maintenance requests
    else if (req.user.role === 'admin' && !req.user.hoaId && !req.user.hoaCommunityCode) {
      // Allow access for global admins
    }
    // HOA-specific admins and members need to match the request's HOA
    else if (request.hoaId && req.user.hoaId) {
      // For populated hoaId (which becomes an object with _id)
      if (typeof request.hoaId === 'object' && request.hoaId._id) {
        if (request.hoaId._id.toString() !== req.user.hoaId.toString()) {
          return res.status(403).json({
            success: false,
            message: 'You do not have permission to access this maintenance request'
          });
        }
      }
      // For non-populated hoaId (which is just the ID)
      else if (request.hoaId.toString() !== req.user.hoaId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this maintenance request'
        });
      }
    }

    res.status(200).json({
      success: true,
      data: request
    });
  } catch (error) {
    console.error('Error fetching maintenance request:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Create a new maintenance request
 * @route   POST /api/maintenance
 * @access  Private
 */
exports.createMaintenanceRequest = async (req, res) => {
  try {
    // Get HOA ID from user or request body
    let hoaId = req.body.hoaId || req.user.hoaId;

    // For admin users who might be HOA admins
    if (!hoaId && req.user.role === 'admin') {
      // Check if the user is associated with an HOA
      const User = require('../../models/user');
      const HOA = require('../../models/hoa');

      // Try to find the user's HOA from their profile
      const user = await User.findById(req.user._id);

      if (user && user.hoaId) {
        hoaId = user.hoaId;
        console.log('Found HOA ID from user profile for maintenance request creation:', hoaId);
      } else if (user && user.hoaCommunityCode) {
        // Try to find HOA by community code
        const hoa = await HOA.findOne({ hoaCommunityCode: user.hoaCommunityCode });
        if (hoa) {
          hoaId = hoa._id;
          console.log('Found HOA ID from community code for maintenance request creation:', hoaId);
        }
      }
    }

    // For company_admin users or regular admins who might not have an HOA ID, use a default one
    if (!hoaId && (req.user.role === 'company_admin' || req.user.role === 'admin')) {
      // Find the first HOA or create a default one
      const HOA = require('../../models/hoa');
      const firstHOA = await HOA.findOne({});

      if (firstHOA) {
        hoaId = firstHOA._id;
        console.log('Using first available HOA ID for admin:', hoaId);
      } else {
        // Create a default HOA if none exists
        try {
          const newHOA = new HOA({
            name: 'Default HOA',
            description: 'Default HOA created automatically',
            hoaCommunityCode: 'DEFAULT',
            createdBy: req.user._id
          });

          const savedHOA = await newHOA.save();
          hoaId = savedHOA._id;
          console.log('Created default HOA for admin:', hoaId);
        } catch (error) {
          console.error('Error creating default HOA:', error);
          return res.status(400).json({
            success: false,
            message: 'No HOA found in the system and unable to create a default one.'
          });
        }
      }
    }

    if (!hoaId) {
      return res.status(400).json({
        success: false,
        message: 'HOA ID is required'
      });
    }

    // Verify property exists and belongs to the same HOA
    const property = await Property.findById(req.body.property);

    if (!property) {
      return res.status(404).json({
        success: false,
        message: 'Property not found'
      });
    }

    if (property.hoaId.toString() !== hoaId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Property does not belong to your HOA'
      });
    }

    // Create new maintenance request
    const request = new MaintenanceRequest({
      ...req.body,
      hoaId,
      resident: req.body.resident || req.user._id,
      dateSubmitted: new Date()
    });

    // Save request
    await request.save();

    // Update property status to maintenance if it's not already
    if (property.status !== 'maintenance') {
      property.status = 'maintenance';
      property.maintenanceHistory.push({
        description: req.body.description,
        status: 'pending',
        date: new Date()
      });
      await property.save();
    }

    res.status(201).json({
      success: true,
      data: request
    });
  } catch (error) {
    console.error('Error creating maintenance request:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Update a maintenance request
 * @route   PUT /api/maintenance/:id
 * @access  Private/Admin
 */
exports.updateMaintenanceRequest = async (req, res) => {
  try {
    // Find request
    let request = await MaintenanceRequest.findById(req.params.id);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance request not found'
      });
    }

    // Check if user has access to this request's HOA
    // Company admins always have access
    if (req.user.role === 'company_admin') {
      // Allow access for company admins
    }
    // Global admins (without specific HOA) have access to all maintenance requests
    else if (req.user.role === 'admin' && !req.user.hoaId && !req.user.hoaCommunityCode) {
      // Allow access for global admins
    }
    // HOA-specific admins need to match the request's HOA
    else if (req.user.role === 'admin' && request.hoaId && req.user.hoaId) {
      if (request.hoaId.toString() !== req.user.hoaId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to update this maintenance request'
        });
      }
    }
    // Members should not be able to update maintenance requests
    else if (req.user.role === 'member') {
      return res.status(403).json({
        success: false,
        message: 'Members cannot update maintenance requests'
      });
    }

    // Update request
    request = await MaintenanceRequest.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: Date.now() },
      { new: true, runValidators: true }
    );

    // If status is changed to completed, update the property
    if (req.body.status === 'completed' && request.status === 'completed') {
      const property = await Property.findById(request.property);

      if (property) {
        // Check if there are any other pending maintenance requests for this property
        const pendingRequests = await MaintenanceRequest.find({
          property: property._id,
          status: { $ne: 'completed' }
        });

        if (pendingRequests.length === 0) {
          // No more pending requests, change property status back to occupied or vacant
          property.status = property.resident ? 'occupied' : 'vacant';

          // Update maintenance history
          property.maintenanceHistory.push({
            description: request.description,
            status: 'completed',
            date: new Date()
          });

          await property.save();
        }
      }
    }

    res.status(200).json({
      success: true,
      data: request
    });
  } catch (error) {
    console.error('Error updating maintenance request:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * @desc    Delete a maintenance request
 * @route   DELETE /api/maintenance/:id
 * @access  Private/Admin
 */
exports.deleteMaintenanceRequest = async (req, res) => {
  try {
    // Find request
    const request = await MaintenanceRequest.findById(req.params.id);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance request not found'
      });
    }

    // Check if user has access to this request's HOA
    // Company admins always have access
    if (req.user.role === 'company_admin') {
      // Allow access for company admins
    }
    // Global admins (without specific HOA) have access to all maintenance requests
    else if (req.user.role === 'admin' && !req.user.hoaId && !req.user.hoaCommunityCode) {
      // Allow access for global admins
    }
    // HOA-specific admins need to match the request's HOA
    else if (req.user.role === 'admin' && request.hoaId && req.user.hoaId) {
      if (request.hoaId.toString() !== req.user.hoaId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to delete this maintenance request'
        });
      }
    }
    // Members should not be able to delete maintenance requests
    else if (req.user.role === 'member') {
      return res.status(403).json({
        success: false,
        message: 'Members cannot delete maintenance requests'
      });
    }

    // Delete request
    await request.remove();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error deleting maintenance request:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

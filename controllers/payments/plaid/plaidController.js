const { Configuration, PlaidApi, PlaidEnvironments } = require('plaid');
const Stripe = require('stripe');

const plaidConfig = new Configuration({
  basePath: PlaidEnvironments[process.env.PLAID_ENV],
  baseOptions: {
    headers: {
      'PLAID-CLIENT-ID': process.env.PLAID_CLIENT_ID,
      'PLAID-SECRET': process.env.PLAID_SECRET,
    },
  },
});

const plaidClient = new PlaidApi(plaidConfig);
const stripe = Stripe(process.env.STRIPE_SECRET_KEY);

exports.createLinkToken = async (req, res) => {
    try {
        const tokenResponse = await plaidClient.linkTokenCreate({
            user: {
                client_user_id: req.user._id.toString(),
            },
            client_name: 'Member',
            products: ['auth'],
            country_codes: ['US'],
            language: 'en',
            redirect_uri: `${process.env.FRONTEND_URL}/dashboard`,
        })
        res.status(200).json({ linkToken: tokenResponse.data.link_token });
    } catch (err) {
        console.error("Error creating Plaid link token:", err);
        res.status(500).json({ error: "Failed to create Plaid link token" });
    }
};

exports.exchangePublicToken = async (req, res) => {
    try {
        const { linkToken } = req.body;
        const tokenResponse = await plaidClient.itemPublicTokenExchange({
            public_token: linkToken,
        });
        const accessToken = tokenResponse.data.access_token;

        const auth = await plaidClient.authGet({
            access_token: accessToken
        })

        const account = auth.data.accounts[0];
        
        const bankAccount = await stripe.customers.createSource(req.user._id.toString(), {
            source: {
                object: "bank_account",
                country: "US",
                currency: "usd",
                account_holder_name: account.owner.name,
                account_holder_type: "individual",
                routing_number: account.routing_number,
                account_number: account.account_id,
            },
        })

        res.status(200).json({ bankAccountId: bankAccount.id });
    } catch (err) {
        console.error("Error exchanging Plaid public token:", err);
        res.status(500).json({ error: "Failed to exchange Plaid public token" });
    }
};

exports.chargePayment = async (req, res) => {
    try {
        const { amount, bankAccountId } = req.body;

        const paymentIntent = await stripe.paymentIntents.create({
            amount: amount,
            currency: 'usd',
            payment_method: bankAccountId,
            confirm: true,
        });
        res.json({ paymentIntent });
    } catch (err) {
        console.error("Error creating Stripe payment intent:", err);
        res.status(500).json({ error: "Failed to create Stripe payment intent" });
    }
}
// controllers/paymentController.js
const Payment = require("../../../models/payment");
const User = require("../../../models/user");
const Settings = require("../../../models/settings");
const notificationController = require("../../notification/notificationController");
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const Finance = require("../../../models/finance");
const HOA = require("../../../models/hoa");
const e = require("express");
const user = require("../../../models/user");

// Create a payment intent with Stripe
exports.createPaymentIntent = async (req, res) => {
  try {
    const { amount } = req.body;
    const { hoaCommunityCode } = req.user;

    if (!amount || amount <= 0) {
      return res.status(400).json({ error: "Valid amount is required" });
    }

    const hoa = await HOA.findOne({ hoaCommunityCode });

    if (!hoa) {
      return res.status(404).json({ error: "HOA not found" });
    }

    const stripeAccountId = hoa.subscription.stripeAccountId;

    // Create a payment intent with Stripe
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: "usd",
      setup_future_usage: "on_session",
      metadata: {
        userId: req.user._id.toString(),
        email: req.user.email,
        username: req.user.username || "Member",
      },
      transfer_data: {
        destination: stripeAccountId,
      },
    });

    res.status(200).json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    });
  } catch (err) {
    console.error("Error creating payment intent:", err);
    res.status(500).json({ error: "Failed to create payment intent" });
  }
};

// Process a successful payment
exports.processPayment = async (req, res) => {
  try {
    const { paymentIntentId, paymentMethod } = req.body;

    // Verify the payment intent exists and was successful
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== "succeeded") {
      return res.status(400).json({ error: "Payment has not been completed" });
    }

    const userId = req.user._id;
    const hoaCommunityCode = req.user.hoaCommunityCode;
    const amount = paymentIntent.amount / 100; // Convert from cents
    const today = new Date();

    // Create a payment record
    const payment = new Payment({
      userId,
      hoaCommunityCode,
      amount,
      month: today.getMonth() + 1, // 1-12
      year: today.getFullYear(),
      paymentMethod: paymentMethod || "credit",
      status: "completed",
      notes: "Paid via Stripe",
      receiptNumber: paymentIntentId.substring(3, 10).toUpperCase(), // Generate a simple receipt number
    });

    await payment.save();

    // Also record this as income in the finance system
    const finance = new Finance({
      type: "income",
      category: "Monthly Dues",
      amount,
      note: `Payment from ${
        req.user.username || req.user.email
      } for ${today.toLocaleString("default", {
        month: "long",
      })} ${today.getFullYear()}`,
      createdBy: userId,
      hoaCommunityCode: hoaCommunityCode,
    });

    await finance.save();

    // Create notification for admins
    const adminUsers = await User.find({ role: "admin" });
    const adminIds = adminUsers.map((user) => user._id);

    await notificationController.createInfoNotification(
      "Payment Received",
      `${
        req.user.username || req.user.email
      } has paid $${amount} for monthly dues`,
      adminIds
    );

    // Create notification for the user
    await notificationController.createPaymentNotification(amount, [userId]);

    res.status(200).json({
      success: true,
      payment,
      message: "Payment processed successfully",
    });
  } catch (err) {
    console.error("Error processing payment:", err);
    res.status(500).json({ error: "Failed to process payment" });
  }
};

// Create Setup Intent
exports.createSetupIntent = async (req, res) => {
  try {
    const { _id } = req.user;
    
    const userInfo = await User.findOne({ _id });

    if (!userInfo) {
      return res.status(404).json({ error: "User not found" });
    }

    if (!userInfo.autoPayment.stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: userInfo.email,
        name: userInfo.username || userInfo.email,
        metadata: {
          userId: userInfo._id.toString(),
          email: userInfo.email,
          role: userInfo.role,
        },
      });

      userInfo.autoPayment.stripeCustomerId = customer.id;
      await userInfo.save();
    }

    const setupIntent = await stripe.setupIntents.create({
      customer: userInfo.stripeCustomerId,
      usage: "off_session",
    });

    if (setupIntent) {
      res.status(200).json({
        clientSecret: setupIntent.client_secret,
      })
    } else {
      res.status(500).json({ error: "Failed to create setup intent" });
    }
  } catch (err) {
    console.error("Error creating setup intent:", err);
    res.status(500).json({ error: "Failed to create setup intent" });
  }
}

// Create Subscription
exports.createSubscription = async (req, res) => {
  try {
    const { paymentMethodId } = req.body;
    const { _id } = req.user;

    const userInfo = await User.findOne({ _id });

    if (!userInfo) {
      return res.status(404).json({ error: "User not found" });
    }

    if (!userInfo.autoPayment.stripeCustomerId) {
      return res.status(400).json({ error: "User has no Stripe customer ID" });
    }

    const hoa = await HOA.findOne({ hoaCommunityCode: userInfo.hoaCommunityCode });

    if (!hoa) {
      return res.status(404).json({ error: "HOA not found" });
    }

    const stripeAccountId = hoa.subscription.stripeAccountId;
    const priceAmount = hoa.hoaPaymentInfo.paymentAmount;

    const subscription = await stripe.subscriptions.create({
      customer: userInfo.autoPayment.stripeCustomerId,
      default_payment_method: paymentMethodId,
      items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: `Monthly HOA Dues for ${hoa.hoaCommunityName}`,
            },
            unit_amount: priceAmount * 100,
            recurring: {
              interval: "month",
            }
          }
        },
      ],
      expand: ["latest_invoice.payment_intent"],
      transfer_data: {
        destination: stripeAccountId,
      },
      metadata: {
        userId: userInfo._id.toString(),
        email: userInfo.email,
        role: userInfo.role,
      }
    });

    userInfo.autoPayment = {
      enabled: true,
      stripeSubscriptionId: subscription.id,
    }

    await userInfo.save();

    res.status(200).json({
      success: true,
      subscription,
      message: "Subscription created successfully",
    });
  } catch (err) {
    console.error("Error creating subscription:", err);
    res.status(500).json({ error: "Failed to create subscription" });
  }
}

// Get payment history for the current user
exports.getPaymentHistory = async (req, res) => {
  try {
    let payments;
    let amountDue = 0;
    const currentDate = new Date();
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    const userId = req.query.userId ? req.query.userId : req.user._id;
    
    payments = await Payment.find({ userId }).sort({
      paymentDate: -1,
    })

    amountDue = payments[0] ? payments[0].amount : 0;

    // Check if current date is after the 1st of the month
    const passedDueDate = currentDate.getDate() > 1;

    // Check if user has a made a payment this month
    const hasPaidThisMonth = payments.some(payment => {
      const paymentDate = new Date(payment.paymentDate);
      const paymentMonth = payment.month;
      const paymentYear = payment.year;
      return (
        paymentDate.getMonth() + 1 <= currentMonth &&
        paymentDate.getFullYear() === currentYear &&
        paymentMonth === currentMonth &&
        paymentYear === currentYear
      )
    })

    const missedPayment = !hasPaidThisMonth && passedDueDate;

    res.status(200).json({payments, missedPaymentInfo: {missedPayment, currentMonth, currentYear, amountDue}});
  } catch (err) {
    console.error("Error fetching payment history:", err);
    res.status(500).json({ error: "Failed to fetch payment history" });
  }
};

// Get monthly due amount from settings
exports.getMonthlyDueAmount = async (req, res) => {
  try {
    let monthlyDue = 0;
    const { _id } = req.user;

    const userInfo = await User.findOne({ _id });

    if (!userInfo) {
      return res.status(404).json({ error: "User not found" });
    }

    // Get monthly due amount from HOA community setting
    const hoaInfo = await HOA.findOne({ hoaCommunityCode: req.user.hoaCommunityCode });
    const checkPaidHOA = await Payment.findOne({
      userId: req.user._id,
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
    })

    if (!checkPaidHOA && hoaInfo) {
      monthlyDue = hoaInfo.hoaPaymentInfo.paymentAmount;
    }

    res.status(200).json({ 
      amount: monthlyDue, 
      month: new Date().getMonth() + 1, 
      year: new Date().getFullYear(),
      autoPayment: userInfo.autoPayment.enabled 
    });
  } catch (err) {
    console.error("Error fetching monthly due amount:", err);
    res.status(500).json({ error: "Failed to fetch monthly due amount" });
  }
};

// Get Refresh Onboarding Link 
exports.refreshOnboardingLink = async (req, res) => {
  try {

    // Get Stripe Account ID from Subscription model
    const { acctId } = req.body;

    if (!accountId) {
      return res.status(400).json({ error: 'Missing account ID' });
    }

    const accountLink = await stripe.accountLinks.create({
      account: acctId,
      type: 'account_onboarding',
      refresh_url: `${process.env.FRONTEND_URL}/subscriptions/refresh?acctId=${acctId}`,
      return_url: `${process.env.FRONTEND_URL}/subscriptions/return`,
    });

    res.json({ url: accountLink.url });
  } catch (err) {
    console.error('Error generating Stripe account link:', err);
    res.status(500).json({ error: 'Failed to create Stripe link' });
  }
}

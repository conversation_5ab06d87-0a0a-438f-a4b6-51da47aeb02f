// controllers/notificationController.js

const Notification = require('../../models/notification');
const Task = require('../../models/task');
const User = require('../../models/user');
const Payment = require('../../models/payment');
const mongoose = require('mongoose');

// Import email service
const { sendEmail } = require('../../services/emailService');

// Get notifications for a user
exports.getNotifications = async (req, res) => {
  try {
    const userId = req.user._id;
    const limit = parseInt(req.query.limit) || 10;
    const page = parseInt(req.query.page) || 1;
    const skip = (page - 1) * limit;

    // Get communityId from request headers (similar to events controller)
    const communityId = req.headers['x-community-id'] || req.query.communityId;

    console.log('🔍 DEBUG: Fetching notifications for user:', userId);
    console.log('🔍 DEBUG: CommunityId from headers/query:', communityId);

    // Add cache-busting headers to ensure fresh responses
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    // Build base query for user's notifications
    let query = {
      'recipients.userId': userId
    };

    // Add community-based filtering if communityId is provided
    if (communityId) {
      console.log('🔍 DEBUG: Adding community-based filtering for:', communityId);

      // Find the community to get its HOA ID
      const Community = require('../../models/community');
      const community = await Community.findById(communityId);

      if (community) {
        console.log(`🔍 DEBUG: Found community ${community.name} with HOA ID: ${community.hoaId}`);

        // Show notifications that are:
        // 1. Community-specific (have this communityId)
        // 2. HOA-wide (have this HOA's hoaId but no communityId)
        // 3. Global (no hoaId and no communityId)
        query.$or = [
          { communityId: communityId, 'recipients.userId': userId },
          { hoaId: community.hoaId, communityId: { $exists: false }, 'recipients.userId': userId },
          { hoaId: { $exists: false }, communityId: { $exists: false }, 'recipients.userId': userId }
        ];

        // Remove the original recipients filter since it's now in $or conditions
        delete query['recipients.userId'];
      } else {
        console.log('🔍 DEBUG: Community not found, showing all notifications');
      }
    }

    console.log('🔍 DEBUG: Final notification query:', JSON.stringify(query, null, 2));

    // First check if there are any notifications at all
    const totalCount = await Notification.countDocuments();
    console.log('🔍 DEBUG: Total notifications in database:', totalCount);

    // Find notifications based on the constructed query
    const notifications = await Notification.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

    console.log('🔍 DEBUG: Found notifications:', notifications.length);
    if (notifications.length > 0) {
      console.log('🔍 DEBUG: Sample notifications:', notifications.slice(0, 3).map(notification => ({
        id: notification._id,
        type: notification.type,
        title: notification.title,
        hoaId: notification.hoaId,
        communityId: notification.communityId
      })));
    } else {
      console.log('🔍 DEBUG: No notifications found');
    }

    // Format notifications for frontend
    const formattedNotifications = notifications.map(notification => {
      const recipient = notification.recipients.find(
        r => r.userId.toString() === userId.toString()
      );

      return {
        id: notification._id,
        type: notification.type,
        title: notification.title,
        description: notification.description,
        time: formatTimeAgo(notification.createdAt),
        read: recipient ? recipient.read : false,
        icon: notification.icon || 'bell',
        metadata: notification.metadata
      };
    });

    console.log('🔍 DEBUG: Formatted notifications:', formattedNotifications.length);
    if (formattedNotifications.length > 0) {
      console.log('🔍 DEBUG: Sample formatted notification:', {
        id: formattedNotifications[0].id,
        type: formattedNotifications[0].type,
        title: formattedNotifications[0].title
      });
    }

    res.json(formattedNotifications);
  } catch (err) {
    console.error('Error fetching notifications:', err);
    res.status(500).json({ message: 'Error fetching notifications' });
  }
};

// Create a new notification
exports.createNotification = async (req, res) => {
  try {
    const { type, title, description, recipients, metadata, priority, hoaId, communityId } = req.body;

    console.log('🔍 DEBUG: Creating notification with data:', {
      type, title, hoaId, communityId, recipientCount: recipients?.length
    });

    const notification = new Notification({
      type,
      title,
      description,
      metadata,
      priority,
      hoaId,
      communityId,
      recipients: recipients.map(userId => ({ userId }))
    });

    await notification.save();
    console.log('🔍 DEBUG: Notification created successfully:', {
      id: notification._id,
      type: notification.type,
      hoaId: notification.hoaId,
      communityId: notification.communityId
    });

    res.status(201).json(notification);
  } catch (err) {
    console.error('Error creating notification:', err);
    res.status(500).json({ message: 'Error creating notification' });
  }
};

// Mark notification as read
exports.markAsRead = async (req, res) => {
  try {
    const notificationId = req.params.id;
    const userId = req.user._id;

    console.log('Marking notification as read:', { notificationId, userId });

    // Validate notification ID
    if (!notificationId?.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({ message: 'Invalid notification ID format' });
    }

    // Find the notification first to check if it exists
    const notification = await Notification.findById(notificationId);
    if (!notification) {
      console.log('Notification not found:', notificationId);
      return res.status(404).json({ message: 'Notification not found' });
    }

    // Check if the user is a recipient of this notification
    const recipientIndex = notification.recipients.findIndex(
      r => r.userId.toString() === userId.toString()
    );

    if (recipientIndex === -1) {
      console.log('User is not a recipient:', { userId, notificationId });
      return res.status(403).json({ message: 'You are not a recipient of this notification' });
    }

    // Update only the specific recipient's read status using positional operator
    const updated = await Notification.findOneAndUpdate(
      {
        _id: notificationId,
        'recipients.userId': userId
      },
      {
        $set: {
          'recipients.$.read': true,
          'recipients.$.readAt': new Date()
        }
      },
      {
        new: true,
        runValidators: true
      }
    );

    if (!updated) {
      console.log('Failed to update notification:', notificationId);
      return res.status(500).json({ message: 'Failed to mark notification as read' });
    }

    console.log('Notification marked as read successfully:', updated._id);
    res.json({
      message: 'Notification marked as read',
      notification: updated
    });
  } catch (err) {
    console.error('Error marking notification as read:', err);
    console.error('Error details:', err.stack);
    res.status(500).json({
      message: 'Error marking notification as read',
      error: err.message,
      details: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
};

// Helper function to format time ago
function formatTimeAgo(date) {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  return date.toLocaleDateString();
}

// Create payment notification
exports.createPaymentNotification = async (amount, recipients) => {
  try {
    const notification = new Notification({
      type: 'payment',
      title: 'Monthly dues collected',
      description: `Successfully collected $${amount.toLocaleString()} in monthly dues`,
      icon: 'dollar',
      priority: 'medium',
      metadata: { amount },
      recipients: recipients.map(userId => ({ userId }))
    });

    await notification.save();
    return notification;
  } catch (err) {
    console.error('Error creating payment notification:', err);
    throw err;
  }
};

// Create task notification
exports.createTaskNotification = async (task, userIds, action, customFields = {}) => {
  try {
    console.log('Creating task notification:', { task: task._id, action, userIds });

    let title, description;
    const taskCreator = task.createdBy?.userId?.username || 'A user';

    // Set default notification content based on action
    switch (action) {
      case 'created':
        title = `New Task Created: ${task.title}`;
        description = `${taskCreator} created a new task: ${task.title}`;
        break;
      case 'completed':
        title = `Task Completed: ${task.title}`;
        description = `${taskCreator} marked task as completed: ${task.title}`;
        break;
      case 'closed':
        title = `Task Closed: ${task.title}`;
        description = `${taskCreator} closed the task: ${task.title}`;
        break;
      case 'commented':
        title = customFields.title || `New Comment on Task: ${task.title}`;
        description = customFields.description || `Someone commented on task: ${task.title}`;
        break;
      case 'started':
        title = `Task Started: ${task.title}`;
        description = `${taskCreator} started working on: ${task.title}`;
        break;
      default:
        title = `Task Update: ${task.title}`;
        description = `Task ${task.title} has been updated`;
    }

    console.log('🔍 DEBUG: Creating task notification:', {
      taskId: task._id,
      taskTitle: task.title,
      hoaId: task.hoaId,
      action,
      recipientCount: userIds.length
    });

    // Use custom fields if provided, otherwise use defaults
    const notificationData = {
      title: customFields.title || title,
      description: customFields.description || description,
      type: 'task',
      hoaId: task.hoaId,
      // Tasks don't have communityId, they're HOA-wide
      metadata: {
        taskId: task._id,
        taskTitle: task.title,
        action,
        hoaId: task.hoaId
      },
      recipients: userIds.map(userId => ({ userId })),
      createdAt: new Date()
    };

    console.log('🔍 DEBUG: Creating task notification with data:', {
      type: notificationData.type,
      hoaId: notificationData.hoaId,
      recipientCount: notificationData.recipients.length
    });

    const notification = new Notification(notificationData);
    await notification.save();

    console.log('🔍 DEBUG: Task notification created successfully:', {
      id: notification._id,
      type: notification.type,
      hoaId: notification.hoaId
    });
    return notification;
  } catch (error) {
    console.error('Error creating task notification:', error);
    // Don't throw the error, just log it
    return null;
  }
};

// Check for tasks due soon
exports.checkTasksDueSoon = async () => {
  try {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Find tasks that are:
    // 1. Due within the next 24 hours
    // 2. Not completed or closed
    // 3. Not already notified for being due soon
    const tasksDueSoon = await Task.find({
      dueDate: {
        $gte: now,
        $lte: tomorrow
      },
      status: { $nin: ['Completed', 'Closed'] },
      dueSoonNotified: { $ne: true }
    }).populate('createdBy.userId', 'username email');

    if (tasksDueSoon.length > 0) {
      console.log(`Found ${tasksDueSoon.length} tasks due soon`);

      // Get all approved users to notify
      const users = await User.find({ isApproved: true, denied: false });
      const userIds = users.map(user => user._id);

      // Send notifications for each task and mark them as notified atomically
      await Promise.all(tasksDueSoon.map(async (task) => {
        // Create notification first
        await exports.createTaskNotification(task, userIds, 'due_soon');

        // Mark task as notified using atomic update
        await Task.findByIdAndUpdate(task._id, {
          $set: { dueSoonNotified: true }
        }, { new: true });

        console.log(`Created due soon notification for task: ${task.title}`);
      }));

      console.log(`Successfully processed ${tasksDueSoon.length} due soon notifications`);
    } else {
      console.log('No tasks due soon found');
    }
  } catch (err) {
    console.error('Error checking tasks due soon:', err);
    throw err;
  }
};

// Create overdue payment notification
exports.createOverdueNotification = async (overdueUsers) => {
  try {
    const notification = new Notification({
      type: 'alert',
      title: 'Overdue payment reminder',
      description: `Payment reminder sent to ${overdueUsers.length} homeowners with overdue balances`,
      icon: 'alert-triangle',
      priority: 'high',
      metadata: {
        overdueUsers: overdueUsers.map(user => ({
          userId: user._id,
          amount: user.amount
        }))
      },
      recipients: overdueUsers.map(user => ({ userId: user._id }))
    });

    await notification.save();
    return notification;
  } catch (err) {
    console.error('Error creating overdue notification:', err);
    throw err;
  }
};

// Create event notification
exports.createEventNotification = async (event, recipients) => {
  try {
    // Ensure recipients is an array of ObjectIds
    const recipientIds = recipients.map(id =>
      typeof id === 'string' ? mongoose.Types.ObjectId(id) : id
    );

    console.log('🔍 DEBUG: Creating event notification:', {
      eventId: event._id,
      eventTitle: event.title,
      hoaId: event.hoaId,
      communityId: event.communityId,
      recipientCount: recipientIds.length
    });

    const notification = new Notification({
      type: 'event',
      title: 'Event Created',
      description: event.title,
      icon: 'calendar',
      priority: 'medium',
      hoaId: event.hoaId,
      communityId: event.communityId,
      metadata: {
        eventId: event._id,
        eventTitle: event.title,
        eventDate: event.date,
        createdBy: event.createdBy.username,
        hoaId: event.hoaId,
        communityId: event.communityId
      },
      recipients: recipientIds.map(userId => ({ userId })),
      createdAt: new Date()
    });

    await notification.save();
    console.log('🔍 DEBUG: Event notification created successfully:', {
      id: notification._id,
      hoaId: notification.hoaId,
      communityId: notification.communityId
    });

    return notification;
  } catch (err) {
    console.error('Error creating event notification:', err);
    throw err;
  }
};

// Create info notification
exports.createInfoNotification = async (title, description, recipients) => {
  try {
    const notification = new Notification({
      type: 'info',
      title,
      description,
      icon: 'info',
      priority: 'low',
      recipients: recipients.map(userId => ({ userId }))
    });

    await notification.save();
    return notification;
  } catch (err) {
    console.error('Error creating info notification:', err);
    throw err;
  }
};

// Create a test notification
exports.createTestNotification = async (req, res) => {
  try {
    const userId = req.user._id;

    // Create different types of test notifications
    const notifications = [
      {
        type: 'payment',
        title: 'Test Payment Notification',
        description: 'Your monthly dues of $150 have been received',
        priority: 'medium',
        recipients: [{ userId }]
      },
      {
        type: 'task',
        title: 'Test Task Notification',
        description: 'New maintenance task: Pool cleaning scheduled',
        priority: 'high',
        recipients: [{ userId }]
      },
      {
        type: 'event',
        title: 'Test Event Notification',
        description: 'Upcoming HOA meeting this Saturday at 10 AM',
        priority: 'medium',
        recipients: [{ userId }]
      },
      {
        type: 'alert',
        title: 'Test Alert Notification',
        description: 'Important: Community guidelines update',
        priority: 'high',
        recipients: [{ userId }]
      }
    ];

    // Save all test notifications
    const savedNotifications = await Promise.all(
      notifications.map(async (notif) => {
        const notification = new Notification(notif);
        return notification.save();
      })
    );

    console.log('Created test notifications:', savedNotifications.length);
    res.status(201).json({
      message: 'Test notifications created successfully',
      count: savedNotifications.length
    });
  } catch (err) {
    console.error('Error creating test notifications:', err);
    res.status(500).json({ message: 'Error creating test notifications' });
  }
};

// Send admin announcement to all members
exports.sendAdminAnnouncement = async (req, res) => {
  try {
    console.log('Admin announcement request received:', {
      user: req.user ? { id: req.user._id, role: req.user.role } : 'No user',
      body: req.body
    });

    // Check if user is admin
    if (!req.user) {
      console.error('No user found in request');
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    if (req.user.role !== 'admin') {
      console.error(`User ${req.user._id} with role ${req.user.role} attempted to send announcement`);
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only administrators can send announcements'
      });
    }

    const { subject, message, sendEmail: shouldSendEmail = false, hoaId, communityId } = req.body;

    console.log('🔍 DEBUG: Admin announcement data:', {
      subject: !!subject,
      message: !!message,
      hoaId,
      communityId,
      shouldSendEmail
    });

    if (!subject || !message) {
      console.error('Missing required fields:', { hasSubject: !!subject, hasMessage: !!message });
      return res.status(400).json({ message: 'Subject and message are required' });
    }

    // Build query to find members based on community/HOA filtering
    let memberQuery = { isApproved: true, denied: false };

    if (communityId) {
      // Find members for specific community
      console.log('🔍 DEBUG: Finding members for specific community:', communityId);
      const Community = require('../../models/community');
      const community = await Community.findById(communityId);

      if (community) {
        console.log(`🔍 DEBUG: Found community ${community.name} with community code: ${community.communityCode}`);
        memberQuery.hoaCommunityCode = { $in: [community.communityCode] };
      } else {
        console.log('🔍 DEBUG: Community not found, sending to all members');
      }
    } else if (hoaId) {
      // Find members for specific HOA
      console.log('🔍 DEBUG: Finding members for specific HOA:', hoaId);
      const HOA = require('../../models/hoa');
      const hoa = await HOA.findById(hoaId);

      if (hoa && hoa.hoaCommunityCode) {
        console.log(`🔍 DEBUG: Found HOA with codes: ${hoa.hoaCommunityCode}`);
        memberQuery.hoaCommunityCode = { $in: hoa.hoaCommunityCode };
      } else {
        console.log('🔍 DEBUG: HOA not found or no community codes, sending to all members');
      }
    }

    // Get members based on the constructed query
    console.log('🔍 DEBUG: Finding approved members with query:', memberQuery);
    const members = await User.find(memberQuery);
    console.log(`🔍 DEBUG: Found ${members.length} approved members`);

    if (members.length === 0) {
      return res.status(404).json({ message: 'No approved members found' });
    }

    // Create notification for all members
    console.log('🔍 DEBUG: Creating announcement notification...');
    const notification = new Notification({
      type: 'announcement', // This should now be valid after model update
      title: subject,
      description: message,
      icon: 'megaphone',
      priority: 'high',
      hoaId: hoaId || undefined,
      communityId: communityId || undefined,
      metadata: {
        subject,
        message,
        sentBy: req.user.username || req.user.email,
        sentById: req.user._id,
        isAnnouncement: true,
        hoaId: hoaId || undefined,
        communityId: communityId || undefined
      },
      recipients: members.map(member => ({ userId: member._id }))
    });

    console.log('🔍 DEBUG: Notification data:', {
      type: notification.type,
      hoaId: notification.hoaId,
      communityId: notification.communityId,
      recipientCount: notification.recipients.length
    });

    // If the model hasn't been updated yet, try with a fallback type
    notification.validateSync();
    if (notification.errors && notification.errors.type) {
      console.log('Announcement type not valid, using info type as fallback');
      notification.type = 'info';
    }

    console.log('Saving notification to database...');
    const savedNotification = await notification.save();
    console.log('Notification saved successfully with ID:', savedNotification._id);

    // If email should be sent
    if (shouldSendEmail) {
      console.log('Sending emails to members...');

      // Send emails in batches to avoid rate limits
      const batchSize = 5; // Reduced batch size
      const batches = [];

      for (let i = 0; i < members.length; i += batchSize) {
        batches.push(members.slice(i, i + batchSize));
      }

      console.log(`Created ${batches.length} batches for sending emails`);
      let emailsSent = 0;
      let emailErrors = 0;

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Processing batch ${i+1} of ${batches.length}...`);

        const emailResults = await Promise.all(batch.map(async (member) => {
          try {
            console.log(`Sending email to ${member.email}...`);
            const result = await sendEmail(
              member.email,
              'adminAnnouncement',
              {
                username: member.username || member.email,
                subject,
                message,
                actionLink: `${process.env.FRONTEND_URL}/notifications`
              }
            );

            if (result.success) {
              emailsSent++;
              return { success: true, email: member.email };
            } else {
              emailErrors++;
              console.error(`Failed to send email to ${member.email}:`, result.error);
              return { success: false, email: member.email, error: result.error };
            }
          } catch (error) {
            emailErrors++;
            console.error(`Exception sending email to ${member.email}:`, error);
            return { success: false, email: member.email, error: error.message };
          }
        }));

        console.log(`Batch ${i+1} results:`, {
          successful: emailResults.filter(r => r.success).length,
          failed: emailResults.filter(r => !r.success).length
        });

        // Small delay between batches to avoid rate limits
        if (i < batches.length - 1) {
          console.log('Waiting before processing next batch...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      console.log('Email sending complete:', { emailsSent, emailErrors });
      return res.status(201).json({
        message: `Announcement sent to ${members.length} members`,
        emailsSent,
        emailErrors,
        notification: savedNotification
      });
    }

    return res.status(201).json({
      message: `Announcement sent to ${members.length} members`,
      notification: savedNotification
    });
  } catch (err) {
    console.error('Error sending admin announcement:', err);
    console.error('Error stack:', err.stack);
    res.status(500).json({
      message: 'Error sending admin announcement',
      error: err.message
    });
  }
};

// Create event deleted notification
exports.createEventDeletedNotification = async (event, deletedBy, recipients) => {
  try {
    console.log('Creating event deletion notification with:', {
      event: event._id,
      deletedBy,
      recipientCount: recipients.length
    });

    // Create notification
    const notification = new Notification({
      type: 'event',
      title: 'Event Deleted',
      description: event.title,
      icon: 'calendar',
      priority: 'medium',
      metadata: {
        eventId: event._id,
        eventTitle: event.title,
        eventDate: event.date,
        deletedBy: deletedBy
      },
      recipients: recipients.map(userId => ({
        userId: mongoose.Types.ObjectId(userId.toString()),
        read: false,
        readAt: null
      }))
    });

    console.log('Saving notification with data:', {
      type: notification.type,
      title: notification.title,
      description: notification.description,
      recipientCount: notification.recipients.length
    });

    const saved = await notification.save();
    console.log('Successfully saved notification:', saved._id);
    return saved;
  } catch (err) {
    console.error('Error creating event deletion notification:', err);
    throw err;
  }
};

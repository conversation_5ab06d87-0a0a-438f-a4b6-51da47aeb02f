/**
 * Street Harmony HOA Management System
 * Budget Controller
 */
const Budget = require('../../models/budget');
const Finance = require('../../models/finance');
const User = require('../../models/user');
const HOA = require('../../models/hoa');
const notificationController = require('../notification/notificationController');
const mongoose = require('mongoose');

/**
 * Create a new budget
 * @route POST /api/budgets
 * @access Private/Admin
 */
exports.createBudget = async (req, res) => {
  try {
    const { name, description, year, hoaId, communityId, lineItems, notes } = req.body;

    // Validate required fields
    if (!name || !year || !hoaId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        details: {
          name: !name ? 'Budget name is required' : null,
          year: !year ? 'Budget year is required' : null,
          hoaId: !hoaId ? 'HOA ID is required' : null
        }
      });
    }

    // Check if a budget already exists for this HOA and year
    const existingBudget = await Budget.findOne({ hoaId, year });
    if (existingBudget) {
      return res.status(400).json({
        success: false,
        message: `A budget for year ${year} already exists for this HOA`
      });
    }

    // Create new budget
    const budget = new Budget({
      name,
      description,
      year,
      hoaId,
      communityId,
      lineItems: lineItems || [],
      notes,
      createdBy: req.user._id,
      updatedBy: req.user._id
    });

    await budget.save();

    // Create notification for admins
    const adminUsers = await User.find({ role: 'admin', hoaId });
    const adminIds = adminUsers.map(user => user._id);

    await notificationController.createInfoNotification(
      'New Budget Created',
      `A new budget for ${year} has been created`,
      adminIds
    );

    res.status(201).json({
      success: true,
      budget
    });
  } catch (error) {
    console.error('Error creating budget:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create budget',
      error: error.message
    });
  }
};

/**
 * Get all budgets (company admin only)
 * @route GET /api/budgets
 * @access Private/Admin
 */
exports.getAllBudgets = async (req, res) => {
  try {
    // Only company admins can access all budgets
    if (req.user.role !== 'company_admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Company admin privileges required.'
      });
    }

    const budgets = await Budget.find({})
      .sort({ year: -1, createdAt: -1 })
      .populate('createdBy', 'username fullName')
      .populate('updatedBy', 'username fullName')
      .populate('hoaId', 'hoaCommunityName');

    res.status(200).json({
      success: true,
      budgets
    });
  } catch (error) {
    console.error('Error fetching all budgets:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch budgets',
      error: error.message
    });
  }
};

/**
 * Get all budgets for an HOA
 * @route GET /api/budgets/hoa/:hoaId
 * @access Private
 */
exports.getBudgetsByHoa = async (req, res) => {
  try {
    const { hoaId } = req.params;

    // Check if user has access to this HOA
    if (req.user.role !== 'company_admin' &&
        req.user.role !== 'admin' &&
        (!req.user.hoaId || req.user.hoaId.toString() !== hoaId)) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to access budgets for this HOA'
      });
    }

    const budgets = await Budget.find({ hoaId })
      .sort({ year: -1, createdAt: -1 })
      .populate('createdBy', 'username fullName')
      .populate('updatedBy', 'username fullName');

    res.status(200).json({
      success: true,
      budgets
    });
  } catch (error) {
    console.error('Error fetching budgets:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch budgets',
      error: error.message
    });
  }
};

/**
 * Get a budget by ID
 * @route GET /api/budgets/:id
 * @access Private
 */
exports.getBudgetById = async (req, res) => {
  try {
    const { id } = req.params;

    const budget = await Budget.findById(id)
      .populate('createdBy', 'username fullName')
      .populate('updatedBy', 'username fullName');

    if (!budget) {
      return res.status(404).json({
        success: false,
        message: 'Budget not found'
      });
    }

    // Check if user has access to this HOA
    if (req.user.role !== 'company_admin' &&
        req.user.role !== 'admin' &&
        (!req.user.hoaId || req.user.hoaId.toString() !== budget.hoaId.toString())) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to access this budget'
      });
    }

    res.status(200).json({
      success: true,
      budget
    });
  } catch (error) {
    console.error('Error fetching budget:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch budget',
      error: error.message
    });
  }
};

/**
 * Update a budget
 * @route PUT /api/budgets/:id
 * @access Private/Admin
 */
exports.updateBudget = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, lineItems, notes, status } = req.body;

    const budget = await Budget.findById(id);
    if (!budget) {
      return res.status(404).json({
        success: false,
        message: 'Budget not found'
      });
    }

    // Check if user has permission to update this budget
    if (req.user.role !== 'company_admin' &&
        req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to update this budget'
      });
    }

    // Update budget fields
    if (name) budget.name = name;
    if (description) budget.description = description;
    if (lineItems) budget.lineItems = lineItems;
    if (notes) budget.notes = notes;
    if (status) budget.status = status;

    budget.updatedBy = req.user._id;

    await budget.save();

    res.status(200).json({
      success: true,
      budget
    });
  } catch (error) {
    console.error('Error updating budget:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update budget',
      error: error.message
    });
  }
};

/**
 * Delete a budget
 * @route DELETE /api/budgets/:id
 * @access Private/Admin
 */
exports.deleteBudget = async (req, res) => {
  try {
    const { id } = req.params;

    const budget = await Budget.findById(id);
    if (!budget) {
      return res.status(404).json({
        success: false,
        message: 'Budget not found'
      });
    }

    // Check if user has permission to delete this budget
    if (req.user.role !== 'company_admin' &&
        req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to delete this budget'
      });
    }

    // Only allow deletion of draft budgets
    if (budget.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: 'Only draft budgets can be deleted'
      });
    }

    await Budget.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: 'Budget deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting budget:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete budget',
      error: error.message
    });
  }
};

/**
 * Update budget actuals from finance entries
 * @route POST /api/budgets/:id/update-actuals
 * @access Private/Admin
 */
exports.updateBudgetActuals = async (req, res) => {
  try {
    const { id } = req.params;

    const budget = await Budget.findById(id);
    if (!budget) {
      return res.status(404).json({
        success: false,
        message: 'Budget not found'
      });
    }

    // Check if user has permission to update this budget
    if (req.user.role !== 'company_admin' &&
        req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to update this budget'
      });
    }

    // Get all finance entries for this HOA for the budget year
    const startDate = new Date(`${budget.year}-01-01T00:00:00.000Z`);
    const endDate = new Date(`${budget.year}-12-31T23:59:59.999Z`);

    const financeEntries = await Finance.find({
      hoaId: budget.hoaId,
      createdAt: { $gte: startDate, $lte: endDate }
    });

    // Group finance entries by category and type
    const categoryTotals = {};
    financeEntries.forEach(entry => {
      const key = `${entry.category}-${entry.type}`;
      if (!categoryTotals[key]) {
        categoryTotals[key] = 0;
      }
      categoryTotals[key] += entry.amount;
    });

    // Update budget line items with actual amounts
    budget.lineItems.forEach(item => {
      const key = `${item.category}-${item.type}`;
      if (categoryTotals[key]) {
        item.actualAmount = categoryTotals[key];
        item.variance = item.actualAmount - item.budgetedAmount;
      }
    });

    budget.updatedBy = req.user._id;
    await budget.save();

    res.status(200).json({
      success: true,
      budget
    });
  } catch (error) {
    console.error('Error updating budget actuals:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update budget actuals',
      error: error.message
    });
  }
};

const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../../models/user');
const HOA = require('../../models/hoa');
const Subscription = require('../../models/subscription');
const Community = require('../../models/community');
const mongoose = require('mongoose');
const { sendEmail } = require('../../services/emailService');
const crypto = require('crypto');
const notificationController = require('../notification/notificationController');
const { recordFailedAttempt, resetFailedAttempts, checkLockStatus } = require('../../middleware/accountLockout');

// Generate reset token
const generateResetToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

// Store reset tokens (in production, use Redis or database)
const passwordResetTokens = new Map();

exports.register = async (req, res) => {
    console.log('Registration request received');
    console.log('Request body:', req.body);
    console.log('Request files:', req.files);
    console.log('Request headers:', req.headers);

    const {
      username,
      password,
      email,
      fullName,
      propertyAddress,
      communityCode,
      phoneNumber,
      role,
      propertyType
    } = req.body;

    // Get uploaded documents (S3 keys instead of local filenames)
    const profilePhoto = req.files?.profilePhoto?.[0]?.key; // S3 key
    const homeOwnershipDoc = req.files?.homeOwnershipDoc?.[0]?.key; // S3 key
    const utilityBillDoc = req.files?.utilityBillDoc?.[0]?.key; // S3 key
    const photoIdDoc = req.files?.photoIdDoc?.[0]?.key; // S3 key

    // For backward compatibility
    const licenseFront = req.files?.licenseFront?.[0]?.key; // S3 key
    const licenseBack = req.files?.licenseBack?.[0]?.key; // S3 key

    // Use either the new communityCode or the old hoaCommunityCode
    let registrationCodes = communityCode || req.body.hoaCommunityCode;

    // Handle hoaCommunityCode as array of strings
    if (registrationCodes) {
      // If it's a string (comma-separated), convert to array
      if (typeof registrationCodes === 'string') {
        registrationCodes = registrationCodes.split(',').map(code => code.trim()).filter(code => code.length > 0);
      }
      // Ensure it's an array
      if (!Array.isArray(registrationCodes)) {
        registrationCodes = [registrationCodes];
      }
      // Convert all codes to uppercase
      registrationCodes = registrationCodes.map(code => code.toUpperCase());
    }

    console.log('Extracted data:', {
      username,
      email,
      fullName,
      role,
      propertyAddress,
      registrationCodes,
      phoneNumber,
      propertyType: propertyType || 'owner',
      hasPassword: !!password,
      profilePhoto,
      homeOwnershipDoc,
      utilityBillDoc,
      photoIdDoc,
      // Legacy fields
      licenseFront,
      licenseBack
    });

    try {
      // Validate required fields
      if (!username || !password || !email || !fullName || !propertyAddress || !registrationCodes || !Array.isArray(registrationCodes) || registrationCodes.length === 0 || !role) {
        console.log('Missing required fields:', {
          username: !username,
          password: !password,
          email: !email,
          fullName: !fullName,
          propertyAddress: !propertyAddress,
          registrationCodes: !registrationCodes || !Array.isArray(registrationCodes) || registrationCodes.length === 0,
          role: !role
        });

        return res.status(400).json({
          error: 'Missing required fields',
          details: {
            username: !username ? 'Username is required' : undefined,
            password: !password ? 'Password is required' : undefined,
            email: !email ? 'Email is required' : undefined,
            fullName: !fullName ? 'Full name is required' : undefined,
            propertyAddress: !propertyAddress ? 'Property address is required' : undefined,
            registrationCodes: (!registrationCodes || !Array.isArray(registrationCodes) || registrationCodes.length === 0) ? 'Community Code is required' : undefined,
            role: !role ? 'Role is required' : undefined
          }
        });
      }

      console.log('Checking for existing user');

      // First check for username match
      const existingUsername = await User.findOne({ username });
      if (existingUsername) {
        console.log('Username already exists:', {
          existingUsername: existingUsername.username,
          attemptedUsername: username
        });

        return res.status(409).json({
          error: 'User already exists',
          details: {
            username: 'Username already taken',
            email: undefined
          }
        });
      }

      // Then check for email match
      const existingEmail = await User.findOne({ email });
      if (existingEmail) {
        console.log('Email already exists:', {
          existingEmail: existingEmail.email,
          attemptedEmail: email
        });

        return res.status(409).json({
          error: 'User already exists',
          details: {
            username: undefined,
            email: 'Email already registered'
          }
        });
      }

      // Check if community codes are valid
      let hoaIds = [];
      let communityIds = [];
      let validatedCodes = [];
      let invalidCodes = [];

      console.log('Checking community codes:', registrationCodes);

      // Validate each community code
      for (const code of registrationCodes) {
        console.log('Validating code:', code);

        // First try to find a community with this code
        const community = await Community.findOne({ communityCode: code })
          .populate('hoaId', 'hoaCommunityName hoaCommunityCode verificationStatus');

        if (community) {
          console.log('Community found with code:', code);
          console.log('Community details:', {
            id: community._id,
            name: community.name,
            code: community.communityCode,
            hoaId: community.hoaId?._id
          });

          // Check if the HOA is approved
          if (community.hoaId && community.hoaId.verificationStatus === 'approved') {
            communityIds.push(community._id);
            hoaIds.push(community.hoaId._id);
            validatedCodes.push(code);
          } else {
            console.log('HOA not approved for community:', community.name);
            invalidCodes.push({
              code,
              reason: 'The HOA for this community is not approved yet'
            });
          }
        } else {
          // For backward compatibility, check if it's an HOA code
          const hoa = await HOA.findOne({ hoaCommunityCode: code });
          if (hoa) {
            console.log('HOA found with code (legacy):', code);
            console.log('HOA details:', {
              id: hoa._id,
              name: hoa.hoaCommunityName,
              code: hoa.hoaCommunityCode
            });

            // Check if HOA is approved
            if (hoa.verificationStatus !== 'approved') {
              console.log('HOA not approved:', hoa.hoaCommunityName);
              invalidCodes.push({
                code,
                reason: 'The HOA is not approved yet'
              });
            } else {
              hoaIds.push(hoa._id);
              validatedCodes.push(code);

              // Try to find a default community for this HOA
              const defaultCommunity = await Community.findOne({ hoaId: hoa._id });
              if (defaultCommunity) {
                communityIds.push(defaultCommunity._id);
              }
            }
          } else {
            console.log('No community or HOA found with code:', code);
            invalidCodes.push({
              code,
              reason: 'The provided Community Code is not valid'
            });
          }
        }
      }

      // If any codes are invalid, return error
      if (invalidCodes.length > 0) {
        return res.status(400).json({
          error: 'Invalid Community Code(s)',
          details: {
            invalidCodes: invalidCodes,
            message: `${invalidCodes.length} out of ${registrationCodes.length} community codes are invalid`
          }
        });
      }

      // Remove duplicates
      hoaIds = [...new Set(hoaIds.map(id => id.toString()))];
      communityIds = [...new Set(communityIds.map(id => id.toString()))];

      console.log('Validated codes:', validatedCodes);
      console.log('HOA IDs:', hoaIds);
      console.log('Community IDs:', communityIds);

      console.log('Creating new user with data:', {
        username,
        fullName,
        email,
        propertyAddress,
        registrationCodes: validatedCodes,
        role,
        phoneNumber,
        propertyType: propertyType || 'owner',
        hoaIds: hoaIds,
        communityIds: communityIds,
        hasProfilePhoto: !!profilePhoto,
        hasHomeOwnershipDoc: !!homeOwnershipDoc,
        hasUtilityBillDoc: !!utilityBillDoc,
        hasPhotoIdDoc: !!photoIdDoc,
        // Legacy fields
        hasLicenseFront: !!licenseFront,
        hasLicenseBack: !!licenseBack,
        role: 'pending'
      });

      // Prepare verification documents
      const verificationDocuments = {};

      if (homeOwnershipDoc) {
        verificationDocuments.homeOwnership = {
          filename: homeOwnershipDoc, // S3 key
          s3Key: homeOwnershipDoc, // Explicit S3 key field
          s3Location: req.files?.homeOwnershipDoc?.[0]?.location, // S3 URL
          originalName: req.files?.homeOwnershipDoc?.[0]?.originalname,
          mimeType: req.files?.homeOwnershipDoc?.[0]?.mimetype,
          uploadDate: new Date()
        };
      }

      if (utilityBillDoc) {
        verificationDocuments.utilityBill = {
          filename: utilityBillDoc, // S3 key
          s3Key: utilityBillDoc, // Explicit S3 key field
          s3Location: req.files?.utilityBillDoc?.[0]?.location, // S3 URL
          originalName: req.files?.utilityBillDoc?.[0]?.originalname,
          mimeType: req.files?.utilityBillDoc?.[0]?.mimetype,
          uploadDate: new Date()
        };
      }

      if (photoIdDoc) {
        verificationDocuments.photoId = {
          filename: photoIdDoc, // S3 key
          s3Key: photoIdDoc, // Explicit S3 key field
          s3Location: req.files?.photoIdDoc?.[0]?.location, // S3 URL
          originalName: req.files?.photoIdDoc?.[0]?.originalname,
          mimeType: req.files?.photoIdDoc?.[0]?.mimetype,
          uploadDate: new Date()
        };
      }

      const user = new User({
        username,
        fullName,
        propertyAddress,
        email,
        password, // Use the original password - mongoose middleware will hash it
        profilePhoto,
        // Legacy fields for backward compatibility
        licenseFront,
        licenseBack,
        // New fields
        verificationDocuments: Object.keys(verificationDocuments).length > 0 ? verificationDocuments : undefined,
        propertyType: propertyType || 'owner',
        role,
        isApproved: false,
        denied: false,
        // Store all HOA and community IDs as arrays
        hoaId: hoaIds,
        communityId: communityIds,
        // Store all validated community codes
        hoaCommunityCode: validatedCodes,
        phoneNumber
      });

      console.log('Saving user to database');
      console.log('MongoDB connection state:', mongoose.connection.readyState);
      // 0 = disconnected, 1 = connected, 2 = connecting, 3 = disconnecting

      try {
        const savedUser = await user.save();
        console.log('User saved successfully with ID:', savedUser._id);
        console.log('User document in database:', savedUser);

        // Verify the user was saved by retrieving it
        const verifyUser = await User.findById(savedUser._id);
        if (verifyUser) {
          console.log('User verified in database with ID:', verifyUser._id);
        } else {
          console.error('Failed to verify user in database after save!');
        }
      } catch (saveError) {
        console.error('Error saving user to database:', saveError);
        console.error('Error details:', saveError.message);
        throw saveError; // Re-throw to be caught by the outer catch block
      }

      // Send registration confirmation email
      try {
        const emailBodyContent = `
          <p>Hi ${fullName},</p>
          <p>Thank you for registering with HOA MANAGEMENT APP by Pelican App Solutions. Your account has been created and is currently pending approval by an administrator.</p>
          <p>Once approved, you will be able to log in and access your account.</p>
          <p>Username: ${username}</p>
          <p>Password: ${password}</p>
          <p>Best regards,<br>HOA MANAGEMENT APP Team<br>Pelican App Solutions</p>
        `;
        console.log('Sending registration confirmation email');
        const emailResult = await sendEmail(email, 'Registration Confirmation', emailBodyContent);

        if (emailResult.success) {
          console.log('Registration email sent successfully:', emailResult.messageId || 'No message ID');
        } else {
          console.error('Failed to send registration email:', emailResult.error);
        }
      } catch (emailError) {
        console.error('Exception sending registration email:', emailError);
      }

      console.log('Registration successful');
      res.status(201).json({
        message: 'User registered. Awaiting admin approval.',
        user: {
          username: user.username,
          email: user.email,
          role: user.role,
          fullName: user.fullName,
          propertyAddress: user.propertyAddress,
          hoaCommunityCode: user.hoaCommunityCode,
          hoaId: user.hoaId,
          phoneNumber: user.phoneNumber
        }
      });
    } catch (err) {
      console.error('Registration error:', err);
      console.error('Error stack:', err.stack);
      res.status(500).json({
        error: 'Registration failed',
        details: err.message
      });
    }
};

// Import error logger
const errorLogger = require('../../utils/errorLogger');

exports.login = async (req, res) => {
  // Create a context object to track the login process
  const loginContext = {
    startTime: new Date().toISOString(),
    steps: [],
    clientInfo: {
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.headers['user-agent'],
      origin: req.headers.origin || req.headers.referer
    }
  };

  // Helper function to add steps to the context
  const addStep = (name, data = {}) => {
    loginContext.steps.push({
      name,
      timestamp: new Date().toISOString(),
      data
    });
  };

  try {
    addStep('login-start');
    console.log('Login attempt with data:', JSON.stringify(req.body, null, 2));
    console.log('Request headers:', JSON.stringify(req.headers, null, 2));

    const { username, email, password } = req.body;

    // If both username and email are provided with the same value, use either one
    const identifier = username || email;

    addStep('credentials-check', {
      hasIdentifier: !!identifier,
      identifierType: username ? 'username' : (email ? 'email' : 'none'),
      hasPassword: !!password,
      identifierLength: identifier?.length
    });

    // Check if account is locked due to too many failed attempts
    if (identifier) {
      const lockStatus = checkLockStatus(identifier);
      if (lockStatus.isLocked) {
        addStep('account-locked', { lockStatus });
        console.log(`Account locked for ${identifier}: ${lockStatus.remainingTime} minutes remaining`);

        return res.status(429).json({
          message: `Too many failed login attempts. Account locked for ${lockStatus.remainingTime} minutes.`,
          lockedUntil: new Date(Date.now() + (lockStatus.remainingTime * 60 * 1000)).toISOString(),
          remainingMinutes: lockStatus.remainingTime
        });
      }
    }

    console.log('Extracted identifier:', identifier);
    console.log('Password provided:', password ? 'Yes' : 'No');

    if (!identifier || !password) {
      console.log('Login failed: Missing credentials');
      console.log('Identifier missing:', !identifier);
      console.log('Password missing:', !password);

      const errorId = errorLogger.logDetailedError('login-validation', 'Missing credentials', {
        missingIdentifier: !identifier,
        missingPassword: !password,
        providedData: { hasUsername: !!username, hasEmail: !!email }
      });

      addStep('validation-failed', { errorId });
      return res.status(400).json({
        message: 'Please provide credentials',
        errorId,
        details: !identifier ? 'Username or email is required' : 'Password is required'
      });
    }

    // Escape special characters in the identifier for regex safety
    const escapeRegExp = (string) => {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    };
    const safeIdentifier = escapeRegExp(identifier);

    // Check MongoDB connection state
    const dbState = mongoose.connection.readyState;
    const dbStateText = ['disconnected', 'connected', 'connecting', 'disconnecting'][dbState] || 'unknown';

    addStep('db-connection-check', { state: dbState, stateText: dbStateText });
    console.log('MongoDB connection state during login:', dbState, `(${dbStateText})`);

    // If database is not connected, log error and return
    if (dbState !== 1) {
      const errorId = errorLogger.logDetailedError('login-database', 'Database not connected', {
        connectionState: dbState,
        connectionStateText: dbStateText
      });

      addStep('db-connection-failed', { errorId });
      return res.status(500).json({
        message: 'Server database error. Please try again later.',
        errorId
      });
    }

    // List all users in the database for debugging
    let allUsers = [];
    try {
      allUsers = await User.find({}).select('username email isApproved role');
      const userSummary = allUsers.map(u => ({
        id: u._id,
        username: u.username,
        email: u.email,
        isApproved: u.isApproved,
        role: u.role
      }));

      addStep('list-all-users', { count: allUsers.length });
      console.log('All users in database:', userSummary);
      console.log('Total users in database:', allUsers.length);
    } catch (findError) {
      const errorId = errorLogger.logDetailedError('login-database', findError, {
        operation: 'list-all-users'
      });

      addStep('list-users-failed', { errorId, error: findError.message });
      console.error('Error listing all users:', findError.message);
    }

    // Find user by username or email (case-insensitive)
    addStep('user-search-start', { identifier: safeIdentifier });
    console.log('Searching for user with identifier:', safeIdentifier);

    let user;
    try {
      const searchQuery = {
        $or: [
          { username: { $regex: new RegExp('^' + safeIdentifier + '$', 'i') } },
          { email: { $regex: new RegExp('^' + safeIdentifier + '$', 'i') } }
        ]
      };

      user = await User.findOne(searchQuery);

      addStep('user-search-result', {
        found: !!user,
        username: user?.username,
        email: user?.email,
        isApproved: user?.isApproved,
        role: user?.role
      });

      console.log('Search query:', searchQuery);
    } catch (searchError) {
      const errorId = errorLogger.logDetailedError('login-database', searchError, {
        operation: 'find-user',
        identifier: safeIdentifier
      });

      addStep('user-search-error', { errorId, error: searchError.message });
      return res.status(500).json({
        message: 'Error searching for user account',
        errorId
      });
    }

    // If not found, try a more flexible search
    let foundUser = user;
    if (!foundUser) {
      addStep('flexible-search-start', { identifier: safeIdentifier });
      console.log('User not found with exact match, trying flexible search');

      try {
        const flexibleQuery = {
          $or: [
            { username: { $regex: new RegExp(safeIdentifier, 'i') } },
            { email: { $regex: new RegExp(safeIdentifier, 'i') } }
          ]
        };

        foundUser = await User.findOne(flexibleQuery);

        addStep('flexible-search-result', {
          found: !!foundUser,
          username: foundUser?.username,
          email: foundUser?.email
        });

        if (foundUser) {
          console.log('Found user with flexible search:', foundUser.username);
        }
      } catch (flexSearchError) {
        const errorId = errorLogger.logDetailedError('login-database', flexSearchError, {
          operation: 'flexible-search',
          identifier: safeIdentifier
        });

        addStep('flexible-search-error', { errorId, error: flexSearchError.message });
        // Continue with null foundUser - we'll handle the not found case below
      }
    }

    console.log('User found:', foundUser ? 'Yes' : 'No');
    if (foundUser) {
      const userDetails = {
        id: foundUser._id,
        username: foundUser.username,
        email: foundUser.email,
        isApproved: foundUser.isApproved,
        denied: foundUser.denied,
        role: foundUser.role
      };

      addStep('user-details', userDetails);
      console.log('User details:', userDetails);
    }

    if (!foundUser) {
      const errorId = errorLogger.logDetailedError('login-authentication', 'User not found', {
        identifier,
        searchAttempts: loginContext.steps.filter(s =>
          s.name === 'user-search-result' || s.name === 'flexible-search-result'
        )
      });

      addStep('user-not-found', { errorId });
      console.log('Login failed: User not found');
      return res.status(401).json({
        message: 'Invalid credentials',
        errorId,
        // Don't expose too much information in the response
        hint: 'Check that your username or email is correct'
      });
    }

    // Check approval status first
    const approvalStatus = {
      username: foundUser.username,
      isApproved: foundUser.isApproved,
      denied: foundUser.denied,
      role: foundUser.role
    };

    addStep('approval-check', approvalStatus);
    console.log('User approval status:', approvalStatus);

    // Don't allow login for unapproved users
    if (!foundUser.isApproved) {
      const errorId = errorLogger.logDetailedError('login-authorization', 'Account pending approval', {
        userId: foundUser._id,
        username: foundUser.username,
        email: foundUser.email
      });

      addStep('approval-pending', { errorId });
      console.log('Unapproved user attempted login:', identifier);
      return res.status(403).json({
        message: 'Your account is pending approval',
        errorId,
        status: 'pending'
      });
    }

    // Don't allow login for denied users
    if (foundUser.denied) {
      const errorId = errorLogger.logDetailedError('login-authorization', 'Account denied', {
        userId: foundUser._id,
        username: foundUser.username,
        email: foundUser.email
      });

      addStep('account-denied', { errorId });
      console.log('Denied user attempted login:', identifier);
      return res.status(403).json({
        message: 'Your account has been denied',
        errorId,
        status: 'denied'
      });
    }

    // Check password
    let isMatch = false;
    try {
      addStep('password-check-start');

      // Log password details for debugging (without revealing the actual password)
      console.log('Password check details:', {
        providedLength: password?.length,
        storedHashLength: foundUser.password?.length,
        storedHashPrefix: foundUser.password?.substring(0, 10) + '...',
      });

      // Try exact password match first
      isMatch = await bcrypt.compare(password, foundUser.password);

      // If no match, try with case variations (common issue with mobile keyboards)
      if (!isMatch) {
        // Try lowercase version
        const lowercaseMatch = await bcrypt.compare(password.toLowerCase(), foundUser.password);
        if (lowercaseMatch) {
          console.log('Password matched with lowercase conversion');
          isMatch = true;
        }

        // Try uppercase first letter (common on mobile)
        if (!isMatch && password.length > 0) {
          const capitalizedPassword = password.charAt(0).toUpperCase() + password.slice(1);
          const capitalizedMatch = await bcrypt.compare(capitalizedPassword, foundUser.password);
          if (capitalizedMatch) {
            console.log('Password matched with first letter capitalized');
            isMatch = true;
          }
        }
      }

      addStep('password-check-result', { isMatch });
      console.log('Password match:', isMatch);
    } catch (passwordError) {
      const errorId = errorLogger.logDetailedError('login-authentication', passwordError, {
        operation: 'password-compare',
        userId: foundUser._id
      });

      addStep('password-check-error', { errorId, error: passwordError.message });
      return res.status(500).json({
        message: 'Error verifying credentials',
        errorId
      });
    }

    if (!isMatch) {
      // For security, get the actual stored password hash for debugging
      const passwordDetails = {
        providedLength: password?.length,
        storedHashLength: foundUser.password?.length,
        storedHashPrefix: foundUser.password?.substring(0, 10) + '...',
        bcryptFormat: foundUser.password?.startsWith('$2') // Check if it's a bcrypt hash
      };

      const errorId = errorLogger.logDetailedError('login-authentication', 'Password mismatch', {
        userId: foundUser._id,
        username: foundUser.username,
        passwordDetails
      });

      // Record failed login attempt
      recordFailedAttempt(identifier);
      const lockStatus = checkLockStatus(identifier);

      addStep('password-mismatch', { errorId, passwordDetails, lockStatus });
      console.log('Login failed: Password mismatch');
      console.log('Password details:', passwordDetails);
      console.log('Failed attempts:', lockStatus.attemptsCount || 1);

      // If this attempt caused a lockout, inform the user
      if (lockStatus.isLocked) {
        return res.status(429).json({
          message: `Too many failed login attempts. Account locked for ${lockStatus.remainingTime} minutes.`,
          lockedUntil: new Date(Date.now() + (lockStatus.remainingTime * 60 * 1000)).toISOString(),
          remainingMinutes: lockStatus.remainingTime
        });
      }

      return res.status(401).json({
        message: 'Invalid credentials',
        errorId,
        hint: 'Check that your password is correct',
        attemptsRemaining: 10 - (lockStatus.attemptsCount || 1)
      });
    }

    // Generate token
    if (!process.env.JWT_SECRET) {
      const errorId = errorLogger.logDetailedError('login-configuration', 'JWT_SECRET missing', {
        environment: process.env.NODE_ENV
      });

      addStep('jwt-config-error', { errorId });
      console.log('Login failed: JWT_SECRET is missing');
      return res.status(500).json({
        message: 'Server configuration error',
        errorId
      });
    }

    addStep('token-generation-start');
    const payload = {
      user: {
        _id: foundUser._id,
        email: foundUser.email,
        role: foundUser.role,
        hoaId: foundUser.hoaId,
        communityId: foundUser.communityId,
        hoaCommunityCode: foundUser.hoaCommunityCode
      }
    };

    let token;
    try {
      token = jwt.sign(
        payload,
        process.env.JWT_SECRET,
        { expiresIn: '24h' }
      );
      addStep('token-generation-success', { tokenLength: token.length });
    } catch (tokenError) {
      const errorId = errorLogger.logDetailedError('login-token', tokenError, {
        userId: foundUser._id
      });

      addStep('token-generation-error', { errorId, error: tokenError.message });
      return res.status(500).json({
        message: 'Error generating authentication token',
        errorId
      });
    }

    // Update user's online status
    try {
      addStep('update-online-status-start');
      await User.findByIdAndUpdate(
        foundUser._id,
        { isOnline: true },
        { new: true, runValidators: true }
      );
      addStep('update-online-status-success');
    } catch (statusError) {
      // Non-critical error, just log it but continue
      const errorId = errorLogger.logDetailedError('login-status-update', statusError, {
        userId: foundUser._id
      });
      addStep('update-online-status-error', { errorId, error: statusError.message });
      console.error('Failed to update online status:', statusError.message);
      // Continue with login process
    }

    // Reset failed login attempts counter on successful login
    resetFailedAttempts(identifier);

    addStep('login-success');
    console.log('Login successful - Token generated for user:', foundUser.email);

    // Check if MFA is enabled for this user
    if (foundUser.mfa && foundUser.mfa.enabled && foundUser.phoneNumber) {
      addStep('mfa-required', { phoneNumber: foundUser.phoneNumber });
      console.log('MFA required for user:', foundUser.email);

      // Return response indicating MFA is required
      return res.json({
        mfaRequired: true,
        phoneNumber: foundUser.phoneNumber,
        user: {
          _id: foundUser._id,
          email: foundUser.email,
          username: foundUser.username
        }
      });
    }

    // Construct profile photo URL if it exists
    let profilePhotoUrl = null;
    if (foundUser.profilePhoto) {
      addStep('profile-photo-processing-start', { originalPhoto: foundUser.profilePhoto });
      try {
        // Get the current API URL from environment or request
        const currentApiUrl = process.env.API_URL ||
                             `${req.protocol}://${req.get('host')}`;
        console.log('Current API URL for profile photo:', currentApiUrl);

        // Check if it's already a full URL
        if (foundUser.profilePhoto.startsWith('http')) {
          // For existing URLs, check if it's pointing to the old Heroku domain
          if (foundUser.profilePhoto.includes('hoa-management-app.herokuapp.com')) {
            // Replace old domain with current API URL
            const pathPart = foundUser.profilePhoto.split('/uploads/')[1];
            if (pathPart) {
              profilePhotoUrl = `${currentApiUrl}/uploads/${encodeURIComponent(pathPart)}`;
            } else {
              // Fallback if we can't extract the path
              profilePhotoUrl = foundUser.profilePhoto;
            }
          } else {
            // For other URLs, ensure the filename part is encoded
            const lastSlashIndex = foundUser.profilePhoto.lastIndexOf('/');
            if (lastSlashIndex !== -1) {
              const basePart = foundUser.profilePhoto.substring(0, lastSlashIndex + 1);
              const filename = foundUser.profilePhoto.substring(lastSlashIndex + 1);
              profilePhotoUrl = basePart + encodeURIComponent(filename);
            } else {
              profilePhotoUrl = foundUser.profilePhoto;
            }
          }
        } else if (foundUser.profilePhoto.startsWith('/uploads/')) {
          // If it starts with /uploads/, add the API URL and encode the filename
          const lastSlashIndex = foundUser.profilePhoto.lastIndexOf('/');
          if (lastSlashIndex !== -1) {
            const basePart = foundUser.profilePhoto.substring(0, lastSlashIndex + 1);
            const filename = foundUser.profilePhoto.substring(lastSlashIndex + 1);
            profilePhotoUrl = `${currentApiUrl}${basePart}${encodeURIComponent(filename)}`;
          } else {
            profilePhotoUrl = `${currentApiUrl}${foundUser.profilePhoto}`;
          }
        } else {
          // Otherwise, construct the full path with encoded filename
          profilePhotoUrl = `${currentApiUrl}/uploads/${encodeURIComponent(foundUser.profilePhoto)}`;
        }

        addStep('profile-photo-processing-success', { profilePhotoUrl });
        console.log('Constructed profile photo URL:', profilePhotoUrl);
      } catch (photoError) {
        const errorId = errorLogger.logDetailedError('login-profile-photo', photoError, {
          userId: foundUser._id,
          originalPhoto: foundUser.profilePhoto
        });

        addStep('profile-photo-processing-error', { errorId, error: photoError.message });
        console.error('Error processing profile photo URL:', photoError.message);
        // Continue with login process, just without the profile photo
        profilePhotoUrl = null;
      }
    }

    // Don't allow login for not active subscriptions for HOAs
    // Check if any of the user's HOAs have inactive subscriptions
    if (foundUser.hoaId && foundUser.hoaId.length > 0) {
      const hoaSubscriptions = await Subscription.find({ hoaId: { $in: foundUser.hoaId } });
      const hasInactiveSubscription = hoaSubscriptions.some(subscription =>
        subscription.status === 'cancelled' ||
        subscription.status === 'incomplete' ||
        subscription.status === 'incomplete_expired' ||
        subscription.status === 'unpaid'
      );

      if (hasInactiveSubscription) {
        if (foundUser.role === 'admin') {
          return res.status(403).json({ message: 'One or more of your HOA subscriptions is not active' });
        } else {
          return res.status(403).json({ message: 'Your HOA is not active. Please contact your HOA administrator' });
        }
      }
    }

    // Prepare response data
    const responseData = {
      token,
      user: {
        _id: foundUser._id,
        hoaId: foundUser.hoaId,
        email: foundUser.email,
        role: foundUser.role,
        username: foundUser.username,
        fullName: foundUser.fullName,
        profilePhoto: profilePhotoUrl,
        isOnline: true
      }
    };

    addStep('prepare-response', {
      hasToken: !!token,
      userData: Object.keys(responseData.user)
    });

    // Send the user data and token
    addStep('send-response');
    res.json(responseData);

    // Log the complete login process for successful logins
    console.log('Login process completed successfully for user:', foundUser.email);
    console.log('Login steps:', loginContext.steps.map(s => s.name).join(' → '));
  } catch (error) {
    // Handle any unexpected errors
    const errorId = errorLogger.logDetailedError('login-unexpected', error, {
      loginContext,
      requestBody: req.body,
      requestHeaders: req.headers
    });

    console.error('Login error:', error);
    console.error('Error stack:', error.stack);
    console.error('Error ID for reference:', errorId);

    res.status(500).json({
      message: 'Server error during login',
      errorId,
      errorType: error.name || 'UnknownError'
    });
  }
};

/**
 * @desc    Get pending users for approval, optionally filtered by community IDs
 * @route   GET /api/auth/pending
 * @access  Private (Admin only)
 * @query   communityId - Array of community IDs or single community ID to filter users
 */
exports.getPendingUsers = async (req, res) => {
  try {
    // Get the current user's ID from the request
    let { communityId } = req.query;
    let hoaCommunityCode = req.user.hoaCommunityCode;
    let communityIds = [];

    console.log('🔍 DEBUG: getPendingUsers called with:', {
      communityId,
      hoaCommunityCode,
      userId: req.user._id,
      userRole: req.user.role
    });

    // Build base query for pending users
    const query = {
      isApproved: false,
      denied: false
    };

    // Handle community-based filtering
    if (req.user.role === 'company_admin') {
      console.log('🔍 DEBUG: Company admin access - can see all pending users');

      // Company admin can see all pending users, but can filter by community if requested
      if (communityId) {
        // Parse communityId parameter (handle multiple formats)
        if (typeof communityId === 'string') {
          try {
            // Try to parse as JSON array first
            communityId = JSON.parse(communityId);
          } catch (e) {
            // If not JSON, split by comma
            communityId = communityId.split(',').map(id => id.trim()).filter(id => id);
          }
        }

        if (Array.isArray(communityId) && communityId.length > 0) {
          console.log('🔍 DEBUG: Frontend provided community IDs for filtering');
          console.log('🔍 DEBUG: Processing frontend community IDs:', communityId);
          query.communityId = { $in: communityId };
          communityIds = communityId;
        }
      }
    }
    // For admin users, filter by their accessible communities
    else if (req.user.role === 'admin') {
      // If frontend provides specific community IDs, use those (filtered by user access)
      if (communityId) {
        // Parse communityId parameter (handle multiple formats)
        if (typeof communityId === 'string') {
          try {
            // Try to parse as JSON array first
            communityId = JSON.parse(communityId);
          } catch (e) {
            // If not JSON, split by comma
            communityId = communityId.split(',').map(id => id.trim()).filter(id => id);
          }
        }

        if (Array.isArray(communityId) && communityId.length > 0) {
          console.log('🔍 DEBUG: Frontend provided community IDs for filtering');
          console.log('🔍 DEBUG: Processing frontend community IDs:', communityId);

          // Find communities that match the requested IDs and verify user has access
          const communities = await Community.find({
            _id: { $in: communityId }
          }).populate('hoaId', 'hoaCommunityCode');

          console.log('🔍 DEBUG: Found communities:', communities.length);

          // Filter communities by user's HOA access
          const accessibleCommunities = communities.filter(community => {
            if (community.hoaId && community.hoaId.hoaCommunityCode) {
              const hasAccess = hoaCommunityCode && hoaCommunityCode.includes(community.hoaId.hoaCommunityCode);
              if (hasAccess) {
                console.log(`🔍 DEBUG: Found community ${community.name} with HOA ${community.hoaId.hoaCommunityName || 'Unknown'}`);
              }
              return hasAccess;
            }
            return false;
          });

          if (accessibleCommunities.length > 0) {
            const accessibleCommunityIds = accessibleCommunities.map(c => c._id.toString());
            console.log('🔍 DEBUG: Filtering pending users by specific communities:', accessibleCommunityIds);
            query.communityId = { $in: accessibleCommunityIds };
            communityIds = accessibleCommunityIds;
          } else {
            // User doesn't have access to any of the requested communities
            console.log('🔍 DEBUG: User has no access to requested communities');
            return res.json({
              users: [],
              total: 0,
              selectedCommunityIds: null
            });
          }
        }
      }
      // Fallback to user's HOA community codes
      else if (req.user.hoaCommunityCode) {
        hoaCommunityCode = req.user.hoaCommunityCode;
        query.hoaCommunityCode = { $in: Array.isArray(hoaCommunityCode) ? hoaCommunityCode : [hoaCommunityCode] };
        console.log(`Admin restricted to HOA hoaCommunityCode: ${hoaCommunityCode}`);
      }
    }
    // For members, they can only see users from their own HOA and hide company_admin users
    else if (req.user.role === 'member') {
      // Hide company_admin users from members
      query.role = { $ne: 'company_admin' };

      if (req.user.hoaCommunityCode) {
        hoaCommunityCode = req.user.hoaCommunityCode;
        query.hoaCommunityCode = { $in: Array.isArray(hoaCommunityCode) ? hoaCommunityCode : [hoaCommunityCode] };
        console.log(`Member restricted to HOA codes: ${hoaCommunityCode}`);
      } else {
        // If member doesn't have an HOA, they can't see any users
        return res.json({
          users: [],
          total: 0,
          selectedCommunityIds: null
        });
      }
    }

    console.log('🔍 DEBUG: Final pending users query:', JSON.stringify(query, null, 2));

    const pendingUsers = await User.find(query)
      .select('-password')
      .populate('communityId', 'name communityCode')
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
      .lean();

    console.log('🔍 DEBUG: Found pending users:', pendingUsers.length);

    res.json({
      success: true,
      count: pendingUsers.length,
      users: pendingUsers,
      selectedCommunityIds: communityIds.length > 0 ? communityIds : null
    });
  } catch (err) {
    console.error('Error fetching pending users:', err);
    res.status(500).json({
      error: 'Failed to fetch pending users',
      details: err.message
    });
  }
};

exports.approveUser = async (req, res) => {
  try {
    console.log('Approving user:', req.params.id);
    console.log('Admin role:', req.user.role);

    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only administrators can approve users'
      });
    }

    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        error: 'Not found',
        message: 'User not found'
      });
    }

    // Update user approval status
    user.isApproved = true;
    user.denied = false; // Make sure denied is set to false
    user.verifiedBy = req.user._id;
    user.verifiedAt = new Date();

    // Only determine role if user doesn't already have a role set (or has 'pending' role)
    if (!user.role || user.role === 'pending') {
      console.log('User has no role or pending role, determining appropriate role based on registration codes');

      // Determine the appropriate role based on registration codes
      // If the user registered with community codes, they should be assigned the admin role
      if (user.hoaCommunityCode && Array.isArray(user.hoaCommunityCode) && user.hoaCommunityCode.length > 0) {
        // Check if any of the codes are community codes (which means admin role)
        let isAdmin = false;

        for (const code of user.hoaCommunityCode) {
          // First, try to find a community with this code
          const community = await Community.findOne({ communityCode: code });

          if (community) {
            // This is a community code, so the user should be an admin
            isAdmin = true;
            console.log(`User registered with community code ${code}, assigning admin role`);

            // Make sure the communityId is in the array
            if (Array.isArray(user.communityId) && !user.communityId.includes(community._id)) {
              user.communityId.push(community._id);
            } else if (!Array.isArray(user.communityId)) {
              user.communityId = [community._id];
            }

            // Make sure the hoaId is in the array
            if (community.hoaId) {
              if (Array.isArray(user.hoaId) && !user.hoaId.includes(community.hoaId)) {
                user.hoaId.push(community.hoaId);
              } else if (!Array.isArray(user.hoaId)) {
                user.hoaId = [community.hoaId];
              }
            }
            break; // Found at least one community code, no need to check others for role assignment
          } else {
            // Check if it's an HOA code (legacy)
            const hoa = await HOA.findOne({ hoaCommunityCode: code });

            if (hoa) {
              // Make sure the hoaId is in the array
              if (Array.isArray(user.hoaId) && !user.hoaId.includes(hoa._id)) {
                user.hoaId.push(hoa._id);
              } else if (!Array.isArray(user.hoaId)) {
                user.hoaId = [hoa._id];
              }

              // Try to find a default community for this HOA
              const defaultCommunity = await Community.findOne({ hoaId: hoa._id });
              if (defaultCommunity) {
                if (Array.isArray(user.communityId) && !user.communityId.includes(defaultCommunity._id)) {
                  user.communityId.push(defaultCommunity._id);
                } else if (!Array.isArray(user.communityId)) {
                  user.communityId = [defaultCommunity._id];
                }
              }
            }
          }
        }

        if (isAdmin) {
          user.role = 'admin';
        } else {
          user.role = 'member';
          console.log('User registered with HOA codes only, assigning member role');
        }
      } else {
        // No code provided, assign member role
        user.role = 'member';
        console.log('User registered without community code, assigning member role');
      }
    } else {
      console.log(`User already has role '${user.role}', preserving existing role`);

      // Still update HOA and community associations even if role is preserved
      if (user.hoaCommunityCode && Array.isArray(user.hoaCommunityCode) && user.hoaCommunityCode.length > 0) {
        for (const code of user.hoaCommunityCode) {
          // First, try to find a community with this code
          const community = await Community.findOne({ communityCode: code });

          if (community) {
            // Make sure the communityId is in the array
            if (Array.isArray(user.communityId) && !user.communityId.includes(community._id)) {
              user.communityId.push(community._id);
            } else if (!Array.isArray(user.communityId)) {
              user.communityId = [community._id];
            }

            // Make sure the hoaId is in the array
            if (community.hoaId) {
              if (Array.isArray(user.hoaId) && !user.hoaId.includes(community.hoaId)) {
                user.hoaId.push(community.hoaId);
              } else if (!Array.isArray(user.hoaId)) {
                user.hoaId = [community.hoaId];
              }
            }
          } else {
            // Check if it's an HOA code (legacy)
            const hoa = await HOA.findOne({ hoaCommunityCode: code });

            if (hoa) {
              // Make sure the hoaId is in the array
              if (Array.isArray(user.hoaId) && !user.hoaId.includes(hoa._id)) {
                user.hoaId.push(hoa._id);
              } else if (!Array.isArray(user.hoaId)) {
                user.hoaId = [hoa._id];
              }

              // Try to find a default community for this HOA
              const defaultCommunity = await Community.findOne({ hoaId: hoa._id });
              if (defaultCommunity) {
                if (Array.isArray(user.communityId) && !user.communityId.includes(defaultCommunity._id)) {
                  user.communityId.push(defaultCommunity._id);
                } else if (!Array.isArray(user.communityId)) {
                  user.communityId = [defaultCommunity._id];
                }
              }
            }
          }
        }
      }
    }

    // IMPORTANT: We're preserving the user's original password from registration
    // No temporary password or password reset required

    console.log('Updating user with new status:', {
      username: user.username,
      isApproved: user.isApproved,
      role: user.role,
      denied: user.denied,
      hoaId: user.hoaId,
      communityId: user.communityId
    });

    await user.save();

    // Verify the user was updated correctly
    const updatedUser = await User.findById(user._id);
    console.log('User after update:', {
      username: updatedUser.username,
      isApproved: updatedUser.isApproved,
      role: updatedUser.role,
      denied: updatedUser.denied,
      hoaId: updatedUser.hoaId,
      communityId: updatedUser.communityId
    });

    // Send approval email with login link
    try {
      console.log('Sending approval email to:', user.email);
      const frontendUrl = process.env.FRONTEND_URL || 'https://hoa-front.vercel.app';
      const loginUrl = `${frontendUrl}/login`;
      console.log('Generated login URL for approval email:', loginUrl);

      // Send email
      const emailBodyContent = `
        <p>Hi ${user.username},</p>
        <p>Thank you for registering with HOA MANAGEMENT APP by Pelican App Solutions.</p>
        <p>Your account has been approved and is now ready to use.</p>
        <p>You can log in using the following link:</p>
        <a href="${loginUrl}" class="button">Log In to Your Account</a>
        <p>Thank you for choosing HOA MANAGEMENT APP by Pelican App Solutions.</p>
        <p>Best regards,<br>HOA MANAGEMENT APP Team<br>Pelican App Solutions</p>
      `;
      const emailResult = await sendEmail(user.email, 'Account Approved', emailBodyContent);

      if (emailResult.success) {
        console.log('Approval email sent successfully:', emailResult.messageId || 'No message ID');
      } else {
        console.error('Failed to send approval email:', emailResult.error);
      }
    } catch (emailError) {
      console.error('Exception sending approval email:', emailError);
    }

    res.json({
      message: 'User approved successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        isApproved: user.isApproved,
        hoaId: user.hoaId,
        communityId: user.communityId
      }
    });
  } catch (err) {
    console.error('Error approving user:', err);
    res.status(500).json({
      error: 'Server error',
      message: 'Failed to approve user'
    });
  }
};

exports.denyUser = async (req, res) => {
  try {
    console.log('Denying user:', req.params.id);
    console.log('Admin role:', req.user.role);

    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only administrators can deny users'
      });
    }

    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        error: 'Not found',
        message: 'User not found'
      });
    }

    user.denied = true;
    user.role = 'denied';
    await user.save();

    // Send denial email
    try {
      console.log('Sending denial email to:', user.email);

      const emailResult = await sendEmail(user.email, 'accountDenied', {
        username: user.username
      });

      if (emailResult.success) {
        console.log('Denial email sent successfully:', emailResult.messageId || 'No message ID');
      } else {
        console.error('Failed to send denial email:', emailResult.error);
      }
    } catch (emailError) {
      console.error('Exception sending denial email:', emailError);
    }

    res.json({
      message: 'User denied successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        denied: user.denied
      }
    });
  } catch (err) {
    console.error('Error denying user:', err);
    res.status(500).json({
      error: 'Server error',
      message: 'Failed to deny user'
    });
  }
};

/**
 * @desc    Get denied users, optionally filtered by community IDs
 * @route   GET /api/auth/denied
 * @access  Private (Admin only)
 * @query   communityId - Array of community IDs or single community ID to filter users
 */
exports.getDeniedUsers = async (req, res) => {
  try {
    // Get the current user's ID from the request
    let { communityId } = req.query;
    let hoaCommunityCode = req.user.hoaCommunityCode;
    let communityIds = [];

    console.log('🔍 DEBUG: getDeniedUsers called with:', {
      communityId,
      hoaCommunityCode,
      userId: req.user._id,
      userRole: req.user.role
    });

    // Build base query for denied users
    const query = {
      denied: true
    };

    // Handle community-based filtering
    if (req.user.role === 'company_admin') {
      console.log('🔍 DEBUG: Company admin access - can see all denied users');

      // Company admin can see all denied users, but can filter by community if requested
      if (communityId) {
        // Parse communityId parameter (handle multiple formats)
        if (typeof communityId === 'string') {
          try {
            // Try to parse as JSON array first
            communityId = JSON.parse(communityId);
          } catch (e) {
            // If not JSON, split by comma
            communityId = communityId.split(',').map(id => id.trim()).filter(id => id);
          }
        }

        if (Array.isArray(communityId) && communityId.length > 0) {
          console.log('🔍 DEBUG: Frontend provided community IDs for filtering');
          console.log('🔍 DEBUG: Processing frontend community IDs:', communityId);
          query.communityId = { $in: communityId };
          communityIds = communityId;
        }
      }
    }
    // For admin users, filter by their accessible communities
    else if (req.user.role === 'admin') {
      // If frontend provides specific community IDs, use those (filtered by user access)
      if (communityId) {
        // Parse communityId parameter (handle multiple formats)
        if (typeof communityId === 'string') {
          try {
            // Try to parse as JSON array first
            communityId = JSON.parse(communityId);
          } catch (e) {
            // If not JSON, split by comma
            communityId = communityId.split(',').map(id => id.trim()).filter(id => id);
          }
        }

        if (Array.isArray(communityId) && communityId.length > 0) {
          console.log('🔍 DEBUG: Frontend provided community IDs for filtering');
          console.log('🔍 DEBUG: Processing frontend community IDs:', communityId);

          // Find communities that match the requested IDs and verify user has access
          const communities = await Community.find({
            _id: { $in: communityId }
          }).populate('hoaId', 'hoaCommunityCode');

          console.log('🔍 DEBUG: Found communities:', communities.length);

          // Filter communities by user's HOA access
          const accessibleCommunities = communities.filter(community => {
            if (community.hoaId && community.hoaId.hoaCommunityCode) {
              const hasAccess = hoaCommunityCode && hoaCommunityCode.includes(community.hoaId.hoaCommunityCode);
              if (hasAccess) {
                console.log(`🔍 DEBUG: Found community ${community.name} with HOA ${community.hoaId.hoaCommunityName || 'Unknown'}`);
              }
              return hasAccess;
            }
            return false;
          });

          if (accessibleCommunities.length > 0) {
            const accessibleCommunityIds = accessibleCommunities.map(c => c._id.toString());
            console.log('🔍 DEBUG: Filtering denied users by specific communities:', accessibleCommunityIds);
            query.communityId = { $in: accessibleCommunityIds };
            communityIds = accessibleCommunityIds;
          } else {
            // User doesn't have access to any of the requested communities
            console.log('🔍 DEBUG: User has no access to requested communities');
            return res.json({
              users: [],
              total: 0,
              selectedCommunityIds: null
            });
          }
        }
      }
      // Fallback to user's HOA community codes
      else if (req.user.hoaCommunityCode) {
        hoaCommunityCode = req.user.hoaCommunityCode;
        query.hoaCommunityCode = { $in: Array.isArray(hoaCommunityCode) ? hoaCommunityCode : [hoaCommunityCode] };
        console.log(`Admin restricted to HOA hoaCommunityCode: ${hoaCommunityCode}`);
      }
    }
    // For members, they can only see users from their own HOA and hide company_admin users
    else if (req.user.role === 'member') {
      // Hide company_admin users from members
      query.role = { $ne: 'company_admin' };

      if (req.user.hoaCommunityCode) {
        hoaCommunityCode = req.user.hoaCommunityCode;
        query.hoaCommunityCode = { $in: Array.isArray(hoaCommunityCode) ? hoaCommunityCode : [hoaCommunityCode] };
        console.log(`Member restricted to HOA codes: ${hoaCommunityCode}`);
      } else {
        // If member doesn't have an HOA, they can't see any users
        return res.json({
          users: [],
          total: 0,
          selectedCommunityIds: null
        });
      }
    }

    console.log('🔍 DEBUG: Final denied users query:', JSON.stringify(query, null, 2));

    const deniedUsers = await User.find(query)
      .select('-password')
      .populate('communityId', 'name communityCode')
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
      .lean();

    console.log('🔍 DEBUG: Found denied users:', deniedUsers.length);

    res.json({
      success: true,
      count: deniedUsers.length,
      users: deniedUsers,
      selectedCommunityIds: communityIds.length > 0 ? communityIds : null
    });
  } catch (err) {
    console.error('Error fetching denied users:', err);
    res.status(500).json({
      error: 'Error fetching denied users',
      details: err.message
    });
  }
};

exports.requestPasswordReset = async (req, res) => {
  try {
    const { email } = req.body;
    const user = await User.findOne({ email });

    if (!user) {
      // For security, don't reveal if email exists
      return res.json({ message: 'If an account exists with this email, a password reset link will be sent.' });
    }

    // Generate and store reset token
    const resetToken = generateResetToken();
    const resetExpiry = Date.now() + 3600000; // 1 hour expiry
    passwordResetTokens.set(resetToken, {
      userId: user._id,
      expiry: resetExpiry
    });

    // Create reset link with production URL fallback
    const frontendUrl = process.env.FRONTEND_URL || 'https://hoa-front.vercel.app';
    const resetLink = `${frontendUrl}/reset-password/${resetToken}`;
    console.log('Generated reset link:', resetLink);

    // Send reset email
    await sendEmail(email, 'passwordReset', { resetLink });

    res.json({
      message: 'Password reset instructions have been sent to your email.'
    });
  } catch (error) {
    console.error('Password reset request error:', error);
    res.status(500).json({ error: 'Failed to process password reset request' });
  }
};

exports.resetPassword = async (req, res) => {
  try {
    const { token, newPassword } = req.body;
    const resetData = passwordResetTokens.get(token);

    if (!resetData || Date.now() > resetData.expiry) {
      return res.status(400).json({ error: 'Invalid or expired reset token' });
    }

    // Get user and update password
    const user = await User.findById(resetData.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    user.password = hashedPassword;
    await user.save();

    // Remove used token
    passwordResetTokens.delete(token);

    res.json({ message: 'Password has been reset successfully' });
  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({ error: 'Failed to reset password' });
  }
};

exports.logout = async (req, res) => {
  try {
    const userId = req.user._id;
    console.log('Logout attempt for user:', userId);

    // Update user's online status
    await User.findByIdAndUpdate(
      userId,
      { isOnline: false },
      { new: true, runValidators: true }
    );

    console.log('Logout successful for user:', userId);
    res.status(200).json({ message: 'Logged out successfully' });
  } catch (err) {
    console.error('Logout error:', err);
    res.status(500).json({ message: 'Server error during logout' });
  }
};

exports.changePassword = async (req, res) => {
  // Implementation of changePassword function
};

exports.approveMember = async (req, res) => {
  try {
    const { userId } = req.params;
    // First get the user to check if they have an HOA code
    const userToApprove = await User.findById(userId);

    if (!userToApprove) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Determine the role based on whether they have a community code
    // If they have a community code, they should be an admin
    const role = userToApprove.hoaCommunityCode ? 'admin' : 'member';
    console.log(`User ${userToApprove.username} has community code: ${userToApprove.hoaCommunityCode}, assigning role: ${role}`);

    const user = await User.findByIdAndUpdate(
      userId,
      {
        $set: {
          isApproved: true,
          role: role,
          denied: false
        }
      },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get all admin users to notify
    const adminUsers = await User.find({ role: 'admin' });
    const adminIds = adminUsers.map(user => user._id);

    // Create notification for admins
    await notificationController.createInfoNotification(
      'New Member Approved',
      `${user.username} has been approved as a new member`,
      adminIds
    );

    res.json({ message: 'Member approved successfully', user });
  } catch (err) {
    console.error('Error approving member:', err);
    res.status(500).json({ message: 'Error approving member' });
  }
};

exports.denyMember = async (req, res) => {
  try {
    const { userId } = req.params;
    const user = await User.findByIdAndUpdate(
      userId,
      {
        $set: {
          denied: true,
          isApproved: false
        }
      },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get all admin users to notify
    const adminUsers = await User.find({ role: 'admin' });
    const adminIds = adminUsers.map(user => user._id);

    // Create notification for admins
    await notificationController.createInfoNotification(
      'Member Application Denied',
      `${user.username}'s membership application has been denied`,
      adminIds
    );

    res.json({ message: 'Member denied successfully', user });
  } catch (err) {
    console.error('Error denying member:', err);
    res.status(500).json({ message: 'Error denying member' });
  }
};



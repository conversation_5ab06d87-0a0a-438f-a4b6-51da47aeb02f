/**
 * Stripe Connect Controller
 * Handles Stripe Connect integration for HOA admins
 */
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const HOA = require('../../models/hoa');
const User = require('../../models/user');

/**
 * Create Stripe Connect Express account for HOA
 */
exports.createConnectAccount = async (req, res) => {
  try {
    const { hoaId } = req.body;
    const userId = req.user._id;

    // Verify user is admin of this HOA
    const hoa = await HOA.findById(hoaId);
    if (!hoa) {
      return res.status(404).json({ error: 'HOA not found' });
    }

    // Check if user is HOA admin
    const user = await User.findById(userId);
    if (user.role !== 'admin' && user.role !== 'hoa_admin') {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    // Create Stripe Express account
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US',
      email: hoa.contactEmail,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true }
      },
      business_profile: {
        name: hoa.hoaCommunityName,
        product_description: 'HOA Management Services',
        support_email: hoa.contactEmail
      },
      metadata: {
        hoaId: hoaId.toString(),
        userId: userId.toString()
      }
    });

    // Save Stripe account ID to HOA
    hoa.stripeConnectAccountId = account.id;
    await hoa.save();

    res.status(200).json({
      success: true,
      accountId: account.id,
      message: 'Stripe Connect account created successfully'
    });

  } catch (error) {
    console.error('Error creating Stripe Connect account:', error);
    res.status(500).json({ error: 'Failed to create Stripe Connect account' });
  }
};

/**
 * Create account link for onboarding
 */
exports.createAccountLink = async (req, res) => {
  try {
    const { hoaId } = req.body;
    
    const hoa = await HOA.findById(hoaId);
    if (!hoa || !hoa.stripeConnectAccountId) {
      return res.status(404).json({ error: 'Stripe Connect account not found' });
    }

    const accountLink = await stripe.accountLinks.create({
      account: hoa.stripeConnectAccountId,
      refresh_url: `${process.env.FRONTEND_URL}/hoa/stripe-connect/refresh`,
      return_url: `${process.env.FRONTEND_URL}/hoa/stripe-connect/success`,
      type: 'account_onboarding'
    });

    res.status(200).json({
      success: true,
      url: accountLink.url
    });

  } catch (error) {
    console.error('Error creating account link:', error);
    res.status(500).json({ error: 'Failed to create account link' });
  }
};

/**
 * Get account status
 */
exports.getAccountStatus = async (req, res) => {
  try {
    const { hoaId } = req.params;
    
    const hoa = await HOA.findById(hoaId);
    if (!hoa || !hoa.stripeConnectAccountId) {
      return res.status(404).json({ error: 'Stripe Connect account not found' });
    }

    const account = await stripe.accounts.retrieve(hoa.stripeConnectAccountId);

    res.status(200).json({
      success: true,
      account: {
        id: account.id,
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        details_submitted: account.details_submitted,
        requirements: account.requirements
      }
    });

  } catch (error) {
    console.error('Error getting account status:', error);
    res.status(500).json({ error: 'Failed to get account status' });
  }
};

/**
 * Create payment intent with application fee (platform fee)
 */
exports.createPaymentIntentWithFee = async (req, res) => {
  try {
    const { amount, hoaId, description } = req.body;
    
    const hoa = await HOA.findById(hoaId);
    if (!hoa || !hoa.stripeConnectAccountId) {
      return res.status(404).json({ error: 'HOA Stripe account not found' });
    }

    // Calculate platform fee (e.g., 2.9% + $0.30)
    const platformFeePercent = 0.029; // 2.9%
    const platformFeeFixed = 30; // $0.30 in cents
    const platformFee = Math.round(amount * platformFeePercent) + platformFeeFixed;

    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount * 100, // Convert to cents
      currency: 'usd',
      application_fee_amount: platformFee,
      transfer_data: {
        destination: hoa.stripeConnectAccountId
      },
      metadata: {
        hoaId: hoaId.toString(),
        userId: req.user._id.toString(),
        description: description || 'HOA Payment'
      }
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      platformFee: platformFee / 100 // Convert back to dollars
    });

  } catch (error) {
    console.error('Error creating payment intent with fee:', error);
    res.status(500).json({ error: 'Failed to create payment intent' });
  }
};

/**
 * Pay vendor through platform
 */
exports.payVendor = async (req, res) => {
  try {
    const { amount, vendorEmail, description, hoaId } = req.body;
    
    const hoa = await HOA.findById(hoaId);
    if (!hoa || !hoa.stripeConnectAccountId) {
      return res.status(404).json({ error: 'HOA Stripe account not found' });
    }

    // Create transfer to vendor (this would require vendor to have Stripe account)
    // For now, we'll create a payment intent that HOA admin can use to pay vendor
    
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount * 100,
      currency: 'usd',
      payment_method_types: ['card'],
      metadata: {
        hoaId: hoaId.toString(),
        vendorEmail,
        description,
        type: 'vendor_payment'
      }
    }, {
      stripeAccount: hoa.stripeConnectAccountId
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    });

  } catch (error) {
    console.error('Error creating vendor payment:', error);
    res.status(500).json({ error: 'Failed to create vendor payment' });
  }
};

module.exports = exports;

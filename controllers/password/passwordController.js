const bcrypt = require('bcryptjs');
const User = require('../../models/user');
const { sendEmail } = require('../../services/emailService');
const crypto = require('crypto');

// Store reset tokens (in memory)
const passwordResetTokens = new Map();

// Track server start time
const SERVER_START_TIME = Date.now();

// Request password reset
exports.requestPasswordReset = async (req, res) => {
  try {
    const { email } = req.body;

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      // For security, don't reveal if email exists
      return res.json({ message: 'If an account exists with this email, a password reset link will be sent.' });
    }

    // Delete any existing tokens for this user
    for (const [existingToken, data] of passwordResetTokens.entries()) {
      if (data.userId.toString() === user._id.toString()) {
        passwordResetTokens.delete(existingToken);
      }
    }

    // Generate reset token
    const token = crypto.randomBytes(32).toString('hex');
    const expires = Date.now() + 3600000; // 1 hour from now

    // Store token with user ID and expiry
    passwordResetTokens.set(token, {
      userId: user._id,
      expires: expires,
      email: user.email // Store email for better error messages
    });

    // Create reset URL
    // Ensure we're using the correct frontend URL for production
    const frontendUrl = process.env.NODE_ENV === 'production'
      ? 'https://hoa-front.vercel.app'
      : process.env.FRONTEND_URL || 'http://localhost:8080';

    const resetUrl = `${frontendUrl}/reset-password?token=${token}`;
    console.log('Generated reset URL:', resetUrl);

    // Send reset email
    try {
      const emailResult = await sendEmail(email, 'passwordReset', { resetLink: resetUrl });

      if (emailResult.success) {
        console.log('Reset email sent to:', email, 'with message ID:', emailResult.messageId);
        res.json({
          message: 'Password reset email sent',
          success: true,
          simulated: emailResult.simulated || false
        });
      } else {
        console.error('Failed to send reset email:', emailResult.error);
        // Keep the token but inform the user of the email issue
        res.json({
          message: 'Password reset link created, but there was an issue sending the email. Please contact support if you do not receive the email.',
          success: true,
          emailIssue: true
        });
      }
    } catch (error) {
      console.error('Error in reset email process:', error);
      // Delete the token if the entire process fails
      passwordResetTokens.delete(token);
      res.status(500).json({ message: 'Error processing password reset request' });
    }
  } catch (error) {
    console.error('Password reset request error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Reset password
exports.resetPassword = async (req, res) => {
  try {
    console.log('Reset password request received:', {
      hasToken: !!req.body.token,
      hasPassword: !!req.body.newPassword,
      tokenLength: req.body.token?.length
    });

    const { token, newPassword } = req.body;

    // Validate input
    if (!token || !newPassword) {
      console.log('Missing required fields:', { hasToken: !!token, hasPassword: !!newPassword });
      return res.status(400).json({
        message: 'Token and new password are required'
      });
    }

    if (newPassword.length < 8) {
      console.log('Password too short:', newPassword.length);
      return res.status(400).json({
        message: 'Password must be at least 8 characters long'
      });
    }

    // Find token in memory
    console.log('Searching for reset token...');
    const tokenData = passwordResetTokens.get(token);

    if (!tokenData) {
      console.log('Token not found');
      return res.status(400).json({
        message: 'The password reset link is invalid or has expired. Please request a new password reset link.',
        shouldRequestNew: true
      });
    }

    // Check if token expired
    if (Date.now() > tokenData.expires) {
      console.log('Token expired:', {
        now: new Date(),
        expires: new Date(tokenData.expires),
        email: tokenData.email
      });
      passwordResetTokens.delete(token);
      return res.status(400).json({
        message: 'The password reset link has expired. Please request a new password reset link.',
        shouldRequestNew: true
      });
    }

    // Find user
    console.log('Finding user with ID:', tokenData.userId);
    const user = await User.findById(tokenData.userId);
    if (!user) {
      console.log('User not found for ID:', tokenData.userId);
      return res.status(404).json({
        message: 'User account not found. Please contact support.'
      });
    }

    // Hash new password
    console.log('Hashing new password...');
    // Store the password exactly as provided by the user
    // This ensures the case sensitivity is preserved
    console.log('Password length:', newPassword.length);
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password using findByIdAndUpdate to avoid validation
    console.log('Updating user password...');
    await User.findByIdAndUpdate(
      user._id,
      { $set: { password: hashedPassword } },
      { new: true, runValidators: false }
    );
    console.log('Password updated successfully');

    // Delete used token
    console.log('Deleting used reset token...');
    passwordResetTokens.delete(token);
    console.log('Reset token deleted');

    // Send confirmation email
    try {
      console.log('Sending confirmation email...');
      await sendEmail(user.email, 'passwordChanged', { username: user.username });
      console.log('Confirmation email sent successfully');
      res.json({
        message: 'Password reset successful. You can now log in with your new password.'
      });
    } catch (emailError) {
      console.error('Error sending confirmation email:', emailError);
      res.json({
        message: 'Password reset successful. You can now log in with your new password.',
        warning: 'Confirmation email could not be sent.'
      });
    }
  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({
      message: 'An error occurred while resetting your password. Please try again.'
    });
  }
};

// Change password (for logged-in users)
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify current password
    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: 'Current password is incorrect' });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password using findByIdAndUpdate
    await User.findByIdAndUpdate(
      userId,
      { $set: { password: hashedPassword } },
      { new: true, runValidators: false }
    );

    // Send confirmation email
    try {
      await sendEmail(user.email, 'passwordChanged', { username: user.username });
      res.json({ message: 'Password changed successfully' });
    } catch (error) {
      console.error('Error sending confirmation email:', error);
      res.json({
        message: 'Password changed successfully',
        warning: 'Confirmation email could not be sent.'
      });
    }
  } catch (error) {
    console.error('Password change error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
/**
 * HOA Revenue Controller
 *
 * This controller provides endpoints for fetching revenue data for HOAs,
 * specifically designed for company admin dashboard views.
 */

const mongoose = require('mongoose');
const HOA = require('../../models/hoa');
const Finance = require('../../models/finance');
const Subscription = require('../../models/subscription');
const SubscriptionPayment = require('../../models/subscriptionPayment');

/**
 * Get revenue summary for all HOAs
 * @route GET /api/hoa-revenue/summary
 * @access Private (Company Admin only)
 */
exports.getHOARevenueSummary = async (req, res) => {
  try {
    // Get all approved HOAs
    const hoas = await HOA.find({
      verificationStatus: 'approved'
    }).select('_id hoaCommunityName hoaCommunityCode subscription');

    // Initialize results array
    const results = [];

    // For each HOA, calculate revenue metrics
    for (const hoa of hoas) {
      console.log(`Processing HOA: ${hoa.hoaCommunityName} (${hoa._id})`);

      // Convert hoa._id to ObjectId for proper comparison
      const hoaObjectId = new mongoose.Types.ObjectId(hoa._id);

      // Get subscription revenue
      const subscriptionPayments = await SubscriptionPayment.find({
        hoaId: hoaObjectId
      });

      console.log(`Found ${subscriptionPayments.length} subscription payments for HOA ${hoa.hoaCommunityName}`);

      const totalSubscriptionRevenue = subscriptionPayments.reduce(
        (sum, payment) => sum + payment.amount,
        0
      );

      // Get finance income entries for this HOA
      // Using the same hoaObjectId as above

      const financeEntries = await Finance.find({
        hoaId: hoaObjectId,
        type: 'income'
      });

      console.log(`Found ${financeEntries.length} finance entries for HOA ${hoa.hoaCommunityName} (${hoa._id})`);

      const totalFinanceRevenue = financeEntries.reduce(
        (sum, entry) => sum + entry.amount,
        0
      );

      // Calculate total revenue
      const totalRevenue = totalSubscriptionRevenue + totalFinanceRevenue;

      // Get current subscription status and tier
      const subscriptionStatus = hoa.subscription?.status || 'inactive';
      const subscriptionTier = hoa.subscription?.tier || 'none';

      // Add to results
      results.push({
        _id: hoa._id,
        name: hoa.hoaCommunityName,
        code: hoa.hoaCommunityCode,
        subscriptionRevenue: totalSubscriptionRevenue,
        financeRevenue: totalFinanceRevenue,
        totalRevenue: totalRevenue,
        subscriptionStatus: subscriptionStatus,
        subscriptionTier: subscriptionTier
      });
    }

    // Sort by total revenue (highest first)
    results.sort((a, b) => b.totalRevenue - a.totalRevenue);

    res.status(200).json({
      success: true,
      count: results.length,
      data: results
    });
  } catch (error) {
    console.error('Error fetching HOA revenue summary:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get detailed revenue data for a specific HOA
 * @route GET /api/hoa-revenue/:hoaId
 * @access Private (Company Admin only)
 */
/**
 * Debug endpoint to check finance entries for a specific HOA
 * @route GET /api/hoa-revenue/debug/:hoaId
 * @access Private (Company Admin only)
 */
exports.debugHOAFinances = async (req, res) => {
  try {
    const { hoaId } = req.params;

    // Validate hoaId
    if (!mongoose.Types.ObjectId.isValid(hoaId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid HOA ID format'
      });
    }

    // Get HOA details
    const hoa = await HOA.findById(hoaId);
    if (!hoa) {
      return res.status(404).json({
        success: false,
        message: 'HOA not found'
      });
    }

    // Get all finance entries for this HOA
    const financeEntries = await Finance.find({}).lean();

    // Filter entries manually to debug the issue
    const matchingEntries = financeEntries.filter(entry => {
      if (!entry.hoaId) return false;

      // Try different comparison methods
      const directMatch = entry.hoaId.toString() === hoaId.toString();
      const objectIdMatch = entry.hoaId.equals ? entry.hoaId.equals(new mongoose.Types.ObjectId(hoaId)) : false;

      return directMatch || objectIdMatch;
    });

    console.log(`Debug: Found ${matchingEntries.length} entries out of ${financeEntries.length} total entries`);

    res.status(200).json({
      success: true,
      data: {
        hoa: {
          _id: hoa._id,
          name: hoa.hoaCommunityName,
          code: hoa.hoaCommunityCode
        },
        totalEntries: financeEntries.length,
        matchingEntries: matchingEntries.length,
        entries: matchingEntries
      }
    });
  } catch (error) {
    console.error('Error in debug endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

exports.getHOARevenueDetail = async (req, res) => {
  try {
    const { hoaId } = req.params;

    // Validate hoaId
    if (!mongoose.Types.ObjectId.isValid(hoaId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid HOA ID format'
      });
    }

    // Get HOA details
    const hoa = await HOA.findById(hoaId);
    if (!hoa) {
      return res.status(404).json({
        success: false,
        message: 'HOA not found'
      });
    }

    // Get subscription payments - ensure consistent ObjectId usage
    const subscriptionPayments = await SubscriptionPayment.find({ hoaId: hoaObjectId })
      .sort({ createdAt: -1 })
      .limit(10);

    // Get finance income entries
    // Convert hoaId to ObjectId for proper comparison
    const hoaObjectId = new mongoose.Types.ObjectId(hoaId);

    const financeEntries = await Finance.find({
      hoaId: hoaObjectId,
      type: 'income'
    })
      .sort({ createdAt: -1 })
      .limit(10);

    console.log(`Found ${financeEntries.length} finance entries for HOA detail view (${hoaId})`);

    // Calculate monthly revenue for the past 12 months
    const monthlyRevenue = await Finance.aggregate([
      { $match: { hoaId: new mongoose.Types.ObjectId(hoaId), type: 'income' } },
      {
        $group: {
          _id: { $substr: ["$createdAt", 0, 7] }, // yyyy-mm
          revenue: { $sum: "$amount" }
        }
      },
      {
        $project: {
          month: "$_id",
          revenue: 1,
          _id: 0
        }
      },
      { $sort: { month: 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: {
        hoa: {
          _id: hoa._id,
          name: hoa.hoaCommunityName,
          code: hoa.hoaCommunityCode,
          subscriptionStatus: hoa.subscription?.status || 'inactive',
          subscriptionTier: hoa.subscription?.tier || 'none'
        },
        subscriptionPayments,
        financeEntries,
        monthlyRevenue
      }
    });
  } catch (error) {
    console.error('Error fetching HOA revenue detail:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

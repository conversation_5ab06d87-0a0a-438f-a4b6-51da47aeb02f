/**
 * Subscription Controller
 * Handles subscription management for HOAs
 */
const Subscription = require('../../models/subscription');
const SubscriptionPayment = require('../../models/subscriptionPayment');
const HOA = require('../../models/hoa');
const Community = require('../../models/community');
const User = require('../../models/user');
const notificationController = require('../notification/notificationController');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { sendEmail } = require('../../services/emailService');

// Stripe product IDs for different tiers
const STRIPE_PRODUCTS = {
  basic: process.env.STRIPE_PRODUCT_BASIC,
  standard: process.env.STRIPE_PRODUCT_STANDARD,
  premium: process.env.STRIPE_PRODUCT_PREMIUM,
  enterprise: process.env.STRIPE_PRODUCT_ENTERPRISE
};

// Pricing tiers (per unit)
const PRICING_TIERS = {
  basic: {
    maxUnits: 50,
    price: 150,
    pricePerUnit: 3, // $3 per unit
    stripePriceID: process.env.STRIPE_PRICE_BASIC
  },
  standard: {
    maxUnits: 150,
    price: 350,
    pricePerUnit: 2.33, // ~$2.33 per unit
    stripePriceID: process.env.STRIPE_PRICE_STANDARD
  },
  premium: {
    maxUnits: 300,
    price: 600,
    pricePerUnit: 2, // $2 per unit
    stripePriceID: process.env.STRIPE_PRICE_PREMIUM
  },
  enterprise: {
    maxUnits: Infinity,
    price: 800,
    pricePerUnit: 1.6, // ~$1.60 per unit for 500 units
    stripePriceID: process.env.STRIPE_PRICE_ENTERPRISE
  }
};

/**
 * Calculate subscription price based on tier and unit count
 * @param {string} tier - Subscription tier
 * @param {number} unitCount - Number of units
 * @returns {number} - Total price
 */
const calculateSubscriptionPrice = (tier, unitCount) => {
  const tierInfo = PRICING_TIERS[tier.toLowerCase()];
  if (!tierInfo) {
    throw new Error(`Invalid tier: ${tier}`);
  }

  // For simplicity, we're using flat pricing per tier
  return tierInfo.price;
};

/**
 * Create a new subscription
 * @route POST /api/subscriptions
 * @access Private/CompanyAdmin, HOA Admin
 */
exports.createSubscription = async (req, res) => {
  try {
    const { hoaId, tier, unitCount, paymentMethodId } = req.body;
    let accountLink = null;

    // Validate required fields
    if (!hoaId || !tier || !unitCount || !paymentMethodId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        details: {
          hoaId: !hoaId ? 'HOA ID is required' : null,
          tier: !tier ? 'Subscription tier is required' : null,
          unitCount: !unitCount ? 'Unit count is required' : null,
          paymentMethodId: !paymentMethodId ? 'Payment method ID is required' : null
        }
      });
    }

    // Check if HOA exists
    const hoa = await HOA.findById(hoaId);
    if (!hoa) {
      return res.status(404).json({
        success: false,
        message: 'HOA not found'
      });
    }

    // Check if user has permission to create subscription
    if (req.user.role !== 'company_admin' &&
        (req.user.role !== 'admin')) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to create a subscription for this HOA'
      });
    }

    // Check if subscription already exists
    const existingSubscription = await Subscription.findOne({ hoaId });
    if (existingSubscription) {
      return res.status(400).json({
        success: false,
        message: 'Subscription already exists for this HOA',
        subscriptionId: existingSubscription._id
      });
    }

    // Calculate price
    const totalPrice = calculateSubscriptionPrice(tier, unitCount);

    // Create or get Stripe customer
    let stripeCustomerId = hoa.subscription?.stripeCustomerId;
    let stripeAccountId = hoa.subscription?.stripeAccountId;

    if (!stripeCustomerId && !stripeAccountId) {
      const customer = await stripe.customers.create({
        name: hoa.hoaCommunityName,
        email: hoa.contactEmail,
        metadata: {
          hoaId: hoaId.toString()
        }
      });

      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US',
        email: hoa.contactEmail,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true }
        },
        business_type: 'company',
        business_profile: {
          name: hoa.hoaCommunityName
        },
        metadata: {
          hoaId: hoaId.toString()
        }
      })

      stripeCustomerId = customer.id;
      stripeAccountId = account.id;

      accountLink = await stripe.accountLinks.create({
        account: stripeAccountId,
        type: 'account_onboarding',
      });
    }

    // Attach payment method to customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: stripeCustomerId
    });

    // Set as default payment method
    await stripe.customers.update(stripeCustomerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId
      }
    });

    // Create Stripe subscription with 30-day free trial
    const stripeSubscription = await stripe.subscriptions.create({
      customer: stripeCustomerId,
      items: [
        {
          price_data: {
            currency: 'usd',
            product: STRIPE_PRODUCTS[tier.toLowerCase()],
            unit_amount: totalPrice * 100, // Convert to cents
            recurring: {
              interval: 'month'
            }
          },
          quantity: 1
        }
      ],
      payment_behavior: 'default_incomplete',
      payment_settings: {
        payment_method_types: ['card'],
        save_default_payment_method: 'on_subscription'
      },
      trial_period_days: 30, // Add 30-day free trial
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        hoaId: hoaId.toString(),
        tier,
        unitCount: unitCount.toString(),
        hasTrial: 'true'
      }
    });

    // Create subscription in database
    const subscription = new Subscription({
      hoaId,
      tier,
      status: stripeSubscription.status, // Will be 'trialing' for new subscriptions
      unitCount,
      pricePerUnit: totalPrice / unitCount,
      totalPrice,
      billingCycle: 'monthly',
      currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
      currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
      stripeCustomerId,
      stripeSubscriptionId: stripeSubscription.id,
      stripePaymentMethodId: paymentMethodId,
      metadata: new Map([
        ['trialEnd', stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000).toISOString() : null],
        ['hasTrial', 'true']
      ])
    });

    await subscription.save();

    // Update HOA with subscription info
    hoa.subscription = {
      tier,
      unitCount,
      pricePerUnit: totalPrice / unitCount,
      totalPrice,
      status: stripeSubscription.status, // Will be 'trialing' for new subscriptions
      stripeCustomerId,
      stripeAccountId,
      stripeSubscriptionId: stripeSubscription.id,
      currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
      currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
      trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null
    };

    await hoa.save();

    // Create notification for admins
    const adminUsers = await User.find({ role: 'admin', hoaId });
    const adminIds = adminUsers.map(user => user._id);

    await notificationController.createInfoNotification(
      'Subscription Created',
      `A ${tier} subscription has been created for ${hoa.hoaCommunityName}`,
      adminIds
    );

     // Send confirmation email
    try {
      const emailSubject = `[ACTION REQUIRED] Subscription Activated for ${hoa.hoaCommunityName}`;
      const emailHtml = `
        <h2>Your HOA Subscription is Now Active</h2>
        <p>Dear ${hoa.hoaCommunityName} Administrator,</p>
        <p>Your subscription has been successfully activated.</p>
        <p>We need to take a few final steps to complete the setup process.</p>
        <p>Please visit the following link to complete the setup:</p>
        <a href="${accountLink}" target="_blank">Complete Setup</a>
        <p>Thank you for choosing our HOA Management platform!</p>
        <p>Best regards,<br>The Street Harmony Team</p>
      `;

      await sendEmail(hoa.contactEmail, emailSubject, emailHtml);
    } catch (emailError) {
      console.error('Error sending email:', emailError);
    }

    res.status(201).json({
      success: true,
      subscription,
      clientSecret: stripeSubscription.latest_invoice?.payment_intent?.client_secret || null
    });
  } catch (error) {
    console.error('Error creating subscription:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create subscription',
      error: error.message
    });
  }
};

/**
 * Get subscription details
 * @route GET /api/subscriptions/:id?communityId=communityId
 * @access Private/CompanyAdmin, HOA Admin
 * @query communityId - Optional community ID to find HOA and then subscription
 */
exports.getSubscriptionDetails = async (req, res) => {
  try {
    const { id } = req.params;
    const { communityId } = req.query;
    let subscription;
    let hoaId;

    console.log('🔍 DEBUG: getSubscriptionDetails called with:', {
      subscriptionId: id,
      communityId,
      userId: req.user._id,
      userRole: req.user.role
    });

    // If communityId is provided, find the HOA through the community
    if (communityId) {
      console.log('🔍 DEBUG: Frontend provided community ID for subscription lookup');

      // Find the community and get its HOA
      const community = await Community.findById(communityId).populate('hoaId');
      if (!community) {
        return res.status(404).json({
          success: false,
          message: 'Community not found'
        });
      }

      console.log(`🔍 DEBUG: Found community ${community.name} with HOA ${community.hoaId?.hoaCommunityName || 'Unknown'}`);
      hoaId = community.hoaId._id;

      // Find subscription by HOA ID instead of subscription ID
      subscription = await Subscription.findOne({ hoaId });
      if (!subscription) {
        return res.status(404).json({
          success: false,
          message: 'Subscription not found for this community'
        });
      }
    } else {
      // Original logic: find subscription by ID
      subscription = await Subscription.findById(id);
      if (!subscription) {
        return res.status(404).json({
          success: false,
          message: 'Subscription not found'
        });
      }
      hoaId = subscription.hoaId;
    }

    console.log('🔍 DEBUG: Found subscription for HOA:', hoaId);

    // Check if user has permission to view subscription
    if (req.user.role !== 'company_admin') {
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to view this subscription'
        });
      }

      // For admin users, check if they have access to this HOA
      if (req.user.hoaId && req.user.hoaId.toString() !== hoaId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to view this subscription'
        });
      }

      // For admin users without hoaId, check hoaCommunityCode access
      if (!req.user.hoaId && req.user.hoaCommunityCode) {
        // Get the HOA to check its community code
        const hoa = await HOA.findById(hoaId);
        if (!hoa || !req.user.hoaCommunityCode.includes(hoa.hoaCommunityCode)) {
          return res.status(403).json({
            success: false,
            message: 'You do not have permission to view this subscription'
          });
        }
      }
    }

    // Get Stripe subscription details
    const stripeSubscription = await stripe.subscriptions.retrieve(
      subscription.stripeSubscriptionId,
      {
        expand: ['default_payment_method', 'latest_invoice']
      }
    );

    console.log('🔍 DEBUG: Successfully retrieved subscription details');

    res.status(200).json({
      success: true,
      subscription,
      selectedCommunityId: communityId || null,
      stripeDetails: {
        status: stripeSubscription.status,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
        defaultPaymentMethod: stripeSubscription.default_payment_method,
        latestInvoice: stripeSubscription.latest_invoice
      }
    });
  } catch (error) {
    console.error('Error getting subscription details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get subscription details',
      error: error.message
    });
  }
};

/**
 * Get subscription by HOA ID (supports single or multiple comma-separated HOA IDs)
 * @route GET /api/subscriptions/hoa/:hoaId
 * @access Private/CompanyAdmin, HOA Admin
 */
exports.getSubscriptionByHoaId = async (req, res) => {
  try {
    const { hoaId } = req.params;

    console.log('🔍 DEBUG: getSubscriptionByHoaId called with:', {
      hoaId,
      userId: req.user._id,
      userRole: req.user.role
    });

    // Handle multiple HOA IDs (comma-separated)
    const hoaIds = hoaId.includes(',') ? hoaId.split(',').map(id => id.trim()) : [hoaId];
    console.log('🔍 DEBUG: Processing HOA IDs:', hoaIds);

    // Check if user has permission to view subscriptions
    if (req.user.role !== 'company_admin' && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to view subscriptions'
      });
    }

    // For single HOA ID, return single subscription
    if (hoaIds.length === 1) {
      const singleHoaId = hoaIds[0];

      // Check if HOA exists
      const hoa = await HOA.findById(singleHoaId);
      if (!hoa) {
        return res.status(404).json({
          success: false,
          message: 'HOA not found'
        });
      }

      const subscription = await Subscription.findOne({ hoaId: singleHoaId });
      if (!subscription) {
        return res.status(404).json({
          success: false,
          message: 'No subscription found for this HOA'
        });
      }

      // Get Stripe subscription details
      const stripeSubscription = await stripe.subscriptions.retrieve(
        subscription.stripeSubscriptionId,
        {
          expand: ['default_payment_method', 'latest_invoice']
        }
      );

      console.log('🔍 DEBUG: Successfully retrieved single subscription');

      return res.status(200).json({
        success: true,
        subscription,
        stripeDetails: {
          status: stripeSubscription.status,
          currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
          currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
          cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
          defaultPaymentMethod: stripeSubscription.default_payment_method,
          latestInvoice: stripeSubscription.latest_invoice
        }
      });
    }

    // For multiple HOA IDs, return multiple subscriptions
    const subscriptions = await Subscription.find({
      hoaId: { $in: hoaIds }
    }).populate('hoaId', 'hoaCommunityName hoaCommunityCode');

    console.log(`🔍 DEBUG: Found ${subscriptions.length} subscriptions for ${hoaIds.length} HOAs`);

    // Get Stripe details for each subscription
    const subscriptionsWithStripeDetails = await Promise.all(
      subscriptions.map(async (subscription) => {
        try {
          const stripeSubscription = await stripe.subscriptions.retrieve(
            subscription.stripeSubscriptionId,
            {
              expand: ['default_payment_method', 'latest_invoice']
            }
          );

          return {
            ...subscription.toObject(),
            stripeDetails: {
              status: stripeSubscription.status,
              currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
              currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
              cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
              defaultPaymentMethod: stripeSubscription.default_payment_method,
              latestInvoice: stripeSubscription.latest_invoice
            }
          };
        } catch (stripeError) {
          console.error(`Error getting Stripe details for subscription ${subscription._id}:`, stripeError);
          return {
            ...subscription.toObject(),
            stripeDetails: null,
            stripeError: stripeError.message
          };
        }
      })
    );

    res.status(200).json({
      success: true,
      subscriptions: subscriptionsWithStripeDetails,
      count: subscriptionsWithStripeDetails.length,
      requestedHoaIds: hoaIds
    });
  } catch (error) {
    console.error('Error getting subscription by HOA ID:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get subscription',
      error: error.message
    });
  }
};

/**
 * Update subscription
 * @route PUT /api/subscriptions/:id
 * @access Private/CompanyAdmin, HOA Admin
 */
exports.updateSubscription = async (req, res) => {
  try {
    const { id } = req.params;
    const { tier, unitCount } = req.body;

    const subscription = await Subscription.findById(id);
    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Subscription not found'
      });
    }

    // Check if user has permission to update subscription
    if (req.user.role !== 'company_admin' &&
        (req.user.role !== 'admin' || req.user.hoaId.toString() !== subscription.hoaId.toString())) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to update this subscription'
      });
    }

    // Calculate new price
    const totalPrice = calculateSubscriptionPrice(tier || subscription.tier, unitCount || subscription.unitCount);

    // Update Stripe subscription
    const stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripeSubscriptionId);

    await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
      items: [
        {
          id: stripeSubscription.items.data[0].id,
          price_data: {
            currency: 'usd',
            product: STRIPE_PRODUCTS[(tier || subscription.tier).toLowerCase()],
            unit_amount: totalPrice * 100, // Convert to cents
            recurring: {
              interval: 'month'
            }
          },
          quantity: 1
        }
      ],
      metadata: {
        hoaId: subscription.hoaId.toString(),
        tier: tier || subscription.tier,
        unitCount: (unitCount || subscription.unitCount).toString()
      }
    });

    // Update subscription in database
    subscription.tier = tier || subscription.tier;
    subscription.unitCount = unitCount || subscription.unitCount;
    subscription.pricePerUnit = totalPrice / (unitCount || subscription.unitCount);
    subscription.totalPrice = totalPrice;

    await subscription.save();

    // Update HOA with subscription info
    const hoa = await HOA.findById(subscription.hoaId);
    if (hoa) {
      hoa.subscription.tier = tier || subscription.tier;
      hoa.subscription.unitCount = unitCount || subscription.unitCount;
      hoa.subscription.pricePerUnit = totalPrice / (unitCount || subscription.unitCount);
      hoa.subscription.totalPrice = totalPrice;

      await hoa.save();
    }

    // Create notification for admins
    const adminUsers = await User.find({ role: 'admin', hoaId: subscription.hoaId });
    const adminIds = adminUsers.map(user => user._id);

    await notificationController.createInfoNotification(
      'Subscription Updated',
      `The subscription for ${hoa.hoaCommunityName} has been updated to ${tier || subscription.tier}`,
      adminIds
    );

    res.status(200).json({
      success: true,
      subscription
    });
  } catch (error) {
    console.error('Error updating subscription:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update subscription',
      error: error.message
    });
  }
};

/**
 * Verify subscription activation token
 * @route GET /api/subscriptions/verify-token/:token
 * @access Public
 */
exports.verifyToken = async (req, res) => {
  try {
    const { token } = req.params;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'No token provided'
      });
    }

    // Find HOA with matching token that hasn't expired
    const hoa = await HOA.findOne({
      paymentToken: token,
      paymentTokenExpires: { $gt: Date.now() }
    });

    if (!hoa) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }

    // Return HOA details
    res.status(200).json({
      success: true,
      message: 'Token verified successfully',
      hoa: {
        _id: hoa._id,
        hoaCommunityName: hoa.hoaCommunityName,
        hoaCommunityCode: hoa.hoaCommunityCode,
        subscription: {
          tier: hoa.subscription.tier,
          unitCount: hoa.subscription.unitCount,
          status: hoa.subscription.status
        }
      }
    });
  } catch (error) {
    console.error('Error verifying token:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Create Stripe checkout session for subscription
 * @route POST /api/subscriptions/create-checkout
 * @access Public (with valid token)
 */
exports.createCheckoutSession = async (req, res) => {
  try {
    const { hoaId, token } = req.body;
    let subscriptionForNewCustomer = null;
    let accountLink = null;
    let stripeAccountId = null;

    if (!hoaId || !token) {
      return res.status(400).json({
        success: false,
        message: 'HOA ID and token are required'
      });
    }

    // Find HOA with matching token that hasn't expired
    const hoa = await HOA.findOne({
      _id: hoaId,
      paymentToken: token,
      paymentTokenExpires: { $gt: Date.now() }
    });

    if (!hoa) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }

    // Get the appropriate product ID based on the subscription tier
    const productId = STRIPE_PRODUCTS[hoa.subscription.tier.toLowerCase()];
    const priceId = PRICING_TIERS[hoa.subscription.tier.toLowerCase()];
    
    if (!productId || !priceId) {
      return res.status(400).json({
        success: false,
        message: `Invalid subscription tier: ${hoa.subscription.tier}`
      });
    }

    // Create a Stripe customer
    let customer;
    if (hoa.subscription.stripeCustomerId) {
      // Use existing customer
      customer = await stripe.customers.retrieve(hoa.subscription.stripeCustomerId, {
        expand: ['subscriptions']
      });
    } else {
      // Create new customer
      customer = await stripe.customers.create({
        email: hoa.contactEmail,
        name: hoa.hoaCommunityName,
        metadata: {
          hoaId: hoa._id.toString(),
          hoaCommunityCode: hoa.hoaCommunityCode
        }
      });

      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US',
        email: hoa.contactEmail,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true }
        },
        business_type: 'company',
        business_profile: {
          name: hoa.hoaCommunityName
        },
        metadata: {
          hoaId: hoaId.toString()
        }
      })

      stripeAccountId = account.id

      accountLink = await stripe.accountLinks.create({
        account: stripeAccountId,
        type: 'account_onboarding',
        refresh_url: `${process.env.FRONTEND_URL}/subscriptions/refresh?acctId=${stripeAccountId}`,
        return_url: `${process.env.FRONTEND_URL}/subscriptions/return`,
      });

      // Save customer ID to HOA
      hoa.subscription.stripeCustomerId = customer.id;
      hoa.subscription.stripeAccountId = stripeAccountId;
      hoa.subscription.status = 'trialing'; // Will be updated by subscription.created event
      hoa.paymentToken = token;
      hoa.paymentTokenExpires = Date.now() + 24 * 60 * 60 * 1000;

      await hoa.save();
    }

    // Create a checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId.stripePriceID,
          quantity: 1
        }
      ],
      mode: 'subscription',
      subscription_data: {
        trial_period_days: 30, // 30-day free trial
        metadata: {
          hoaId: hoa._id.toString(),
          tier: hoa.subscription.tier
        }
      },
      success_url: `${process.env.FRONTEND_URL}/subscriptions/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.FRONTEND_URL}/subscriptions/cancel`,
      metadata: {
        hoaId: hoa._id.toString(),
        token: token
      }
    });

    // Save Subscription data here
    const existingSubscription = await Subscription.findOne({ hoaId: hoa._id });
    if (!existingSubscription) {
      const subscription = new Subscription({
        hoaId: hoa._id,
        tier: hoa.subscription.tier,
        status: 'trialing', // Will be 'trialing' for new subscriptions
        unitCount: hoa.subscription.unitCount,
        pricePerUnit: hoa.subscription.pricePerUnit,
        totalPrice: hoa.subscription.totalPrice,
        billingCycle: 'monthly',
        paymentToken: hoa.paymentToken,
        paymentTokenExpires: hoa.paymentTokenExpires,
        currentPeriodStart: new Date(Date.now()),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        stripeCustomerId: customer.id,
        stripeAccountId: stripeAccountId,
        metadata: new Map([
          ['trialEnd', new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()],
          ['hasTrial', 'true']
        ])
      });

      await subscription.save();
    }

    console.log('Account link:', accountLink.url);

    // Send confirmation email
    try {
      const emailSubject = `[ACTION REQUIRED] Subscription Activated for ${hoa.hoaCommunityName}`;
      const emailHtml = `
        <h2>Your HOA Subscription is Now Active</h2>
        <p>Dear ${hoa.hoaCommunityName} Administrator,</p>
        <p>Your subscription has been successfully activated.</p>
        <p>We need to take a few final steps to complete the setup process.</p>
        <p>Please visit the following link to complete the setup:</p>
        <a href="${accountLink.url}" target="_blank">Complete Setup</a>
        <p>Thank you for choosing our HOA Management platform!</p>
        <p>Best regards,<br>The Street Harmony Team</p>
      `;

      await sendEmail(hoa.contactEmail, emailSubject, emailHtml);
    } catch (error) {
      console.error('Error sending confirmation email:', error);
    }

    // Return the session ID
    res.status(200).json({
      success: true,
      sessionId: session.id
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Cancel subscription
 * @route DELETE /api/subscriptions/:id
 * @access Private/CompanyAdmin, HOA Admin
 */
exports.cancelSubscription = async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await Subscription.findById(id);
    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Subscription not found'
      });
    }

    // Check if user has permission to cancel subscription
    if (req.user.role !== 'company_admin' && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to cancel this subscription'
      });
    }

    // Cancel Stripe subscription
    await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
      cancel_at_period_end: true
    });

    // Update subscription in database
    subscription.cancelAtPeriodEnd = true;

    await subscription.save();

    // Update HOA with subscription info
    const hoa = await HOA.findById(subscription.hoaId);
    if (hoa) {
      hoa.subscription.cancelAtPeriodEnd = true;

      await hoa.save();
    }

    // Create notification for admins
    const adminUsers = await User.find({ role: 'admin', hoaId: subscription.hoaId });
    const adminIds = adminUsers.map(user => user._id);

    await notificationController.createInfoNotification(
      'Subscription Canceled',
      `The subscription for ${hoa.hoaCommunityName} will be canceled at the end of the billing period`,
      adminIds
    );

    res.status(200).json({
      success: true,
      subscription,
      message: 'Subscription will be canceled at the end of the billing period'
    });
  } catch (error) {
    console.error('Error canceling subscription:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel subscription',
      error: error.message
    });
  }
};

/**
 * Handle Stripe webhook events
 * @route POST /api/subscriptions/webhook
 * @access Public
 */
exports.handleStripeWebhook = async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'checkout.session.completed':
      await handleCheckoutSessionCompleted(event.data.object);
      break;
    case 'invoice.payment_succeeded':
      await handleInvoicePaymentSucceeded(event.data.object);
      break;
    case 'invoice.payment_failed':
      await handleInvoicePaymentFailed(event.data.object);
      break;
    case 'customer.subscription.created':
      await handleSubscriptionCreated(event.data.object);
      break;
    case 'customer.subscription.updated':
      await handleSubscriptionUpdated(event.data.object);
      break;
    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(event.data.object);
      break;
    default:
      console.log(`Unhandled event type: ${event.type}`);
  }

  res.status(200).json({ received: true });
};

/**
 * Handle checkout session completed event
 * @param {Object} session - Stripe checkout session object
 */
async function handleCheckoutSessionCompleted(session) {
  try {
    // Get HOA ID from metadata
    const hoaId = session.metadata.hoaId;

    if (!hoaId) {
      console.error('No HOA ID found in session metadata');
      return;
    }

    // Update HOA with subscription info
    const hoa = await HOA.findById(hoaId);

    if (!hoa) {
      console.error(`HOA not found with ID: ${hoaId}`);
      return;
    }

    // Clear payment token after successful checkout
    hoa.paymentToken = undefined;
    hoa.paymentTokenExpires = undefined;

    // Save subscription ID if available
    if (session.subscription) {
      hoa.subscription.stripeSubscriptionId = session.subscription;
      hoa.subscription.status = 'trialing'; // Will be updated by subscription.created event
    }

    await hoa.save();

    // Send confirmation email
    try {
      const emailSubject = `Subscription Activated for ${hoa.hoaCommunityName}`;
      const emailHtml = `
        <h2>Your HOA Subscription is Now Active</h2>
        <p>Dear ${hoa.hoaCommunityName} Administrator,</p>
        <p>Your subscription has been successfully activated with a 30-day free trial.</p>
        <p>Subscription Details:</p>
        <ul>
          <li>HOA: ${hoa.hoaCommunityName}</li>
          <li>Community Code: ${hoa.hoaCommunityCode}</li>
          <li>Tier: ${hoa.subscription.tier}</li>
          <li>Status: Trial Period</li>
        </ul>
        <p>Thank you for choosing our HOA Management platform!</p>
        <p>Best regards,<br>The Street Harmony Team</p>
      `;

      await sendEmail(hoa.contactEmail, emailSubject, emailHtml);
    } catch (emailError) {
      console.error('Error sending confirmation email:', emailError);
    }
  } catch (error) {
    console.error('Error handling checkout.session.completed:', error);
  }
}

/**
 * Handle invoice payment succeeded event
 * @param {Object} invoice - Stripe invoice object
 */
async function handleInvoicePaymentSucceeded(invoice) {
  try {
    // Get subscription from invoice
    const stripeSubscription = await stripe.subscriptions.retrieve(invoice.subscription);
    const hoaId = stripeSubscription.metadata.hoaId;

    if (!hoaId) {
      console.error('No HOA ID found in subscription metadata');
      return;
    }

    // Find subscription in database
    const subscription = await Subscription.findOne({
      stripeSubscriptionId: invoice.subscription
    });

    if (!subscription) {
      console.error('Subscription not found in database');
      return;
    }

    // Create subscription payment record
    const subscriptionPayment = new SubscriptionPayment({
      hoaId,
      subscriptionId: subscription._id,
      amount: invoice.amount_paid / 100, // Convert from cents
      currency: invoice.currency,
      status: 'succeeded',
      billingPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
      billingPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
      stripeInvoiceId: invoice.id,
      stripePaymentIntentId: invoice.payment_intent,
      receiptUrl: invoice.hosted_invoice_url,
      receiptNumber: invoice.number
    });

    await subscriptionPayment.save();

    // Update subscription status
    subscription.status = 'active';
    subscription.currentPeriodStart = new Date(stripeSubscription.current_period_start * 1000);
    subscription.currentPeriodEnd = new Date(stripeSubscription.current_period_end * 1000);

    await subscription.save();

    // Update HOA subscription status
    const hoa = await HOA.findById(hoaId);
    if (hoa) {
      hoa.subscription.status = 'active';
      hoa.subscription.currentPeriodStart = new Date(stripeSubscription.current_period_start * 1000);
      hoa.subscription.currentPeriodEnd = new Date(stripeSubscription.current_period_end * 1000);

      await hoa.save();
    }

    // Create notification for admins
    const adminUsers = await User.find({ role: 'admin', hoaId });
    const adminIds = adminUsers.map(user => user._id);

    await notificationController.createInfoNotification(
      'Subscription Payment Successful',
      `Payment for ${hoa ? hoa.hoaCommunityName : 'your HOA'} subscription has been processed successfully`,
      adminIds
    );
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

/**
 * Handle invoice payment failed event
 * @param {Object} invoice - Stripe invoice object
 */
async function handleInvoicePaymentFailed(invoice) {
  try {
    // Get subscription from invoice
    const stripeSubscription = await stripe.subscriptions.retrieve(invoice.subscription);
    const hoaId = stripeSubscription.metadata.hoaId;

    if (!hoaId) {
      console.error('No HOA ID found in subscription metadata');
      return;
    }

    // Find subscription in database
    const subscription = await Subscription.findOne({
      stripeSubscriptionId: invoice.subscription
    });

    if (!subscription) {
      console.error('Subscription not found in database');
      return;
    }

    // Create subscription payment record
    const subscriptionPayment = new SubscriptionPayment({
      hoaId,
      subscriptionId: subscription._id,
      amount: invoice.amount_due / 100, // Convert from cents
      currency: invoice.currency,
      status: 'failed',
      billingPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
      billingPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
      stripeInvoiceId: invoice.id,
      stripePaymentIntentId: invoice.payment_intent
    });

    await subscriptionPayment.save();

    // Update subscription status
    subscription.status = 'past_due';

    await subscription.save();

    // Update HOA subscription status
    const hoa = await HOA.findById(hoaId);
    if (hoa) {
      hoa.subscription.status = 'past_due';

      await hoa.save();
    }

    // Create notification for admins
    const adminUsers = await User.find({ role: 'admin', hoaId });
    const adminIds = adminUsers.map(user => user._id);

    await notificationController.createInfoNotification(
      'Subscription Payment Failed',
      `Payment for ${hoa ? hoa.hoaCommunityName : 'your HOA'} subscription has failed. Please update your payment method.`,
      adminIds
    );
  } catch (error) {
    console.error('Error handling invoice payment failed:', error);
  }
}

/**
 * Handle subscription created event
 * @param {Object} stripeSubscription - Stripe subscription object
 */
async function handleSubscriptionCreated(stripeSubscription) {
  // This is handled by our createSubscription endpoint
  console.log('Subscription created:', stripeSubscription.id);
}

/**
 * Handle subscription updated event
 * @param {Object} stripeSubscription - Stripe subscription object
 */
async function handleSubscriptionUpdated(stripeSubscription) {
  try {
    const hoaId = stripeSubscription.metadata.hoaId;

    if (!hoaId) {
      console.error('No HOA ID found in subscription metadata');
      return;
    }

    // Find subscription in database
    const subscription = await Subscription.findOne({
      stripeSubscriptionId: stripeSubscription.id
    });

    if (!subscription) {
      console.error('Subscription not found in database');
      return;
    }

    // Update subscription status
    subscription.status = stripeSubscription.status;
    subscription.currentPeriodStart = new Date(stripeSubscription.current_period_start * 1000);
    subscription.currentPeriodEnd = new Date(stripeSubscription.current_period_end * 1000);
    subscription.cancelAtPeriodEnd = stripeSubscription.cancel_at_period_end;

    await subscription.save();

    // Update HOA subscription status
    const hoa = await HOA.findById(hoaId);
    if (hoa) {
      hoa.subscription.status = stripeSubscription.status;
      hoa.subscription.currentPeriodStart = new Date(stripeSubscription.current_period_start * 1000);
      hoa.subscription.currentPeriodEnd = new Date(stripeSubscription.current_period_end * 1000);
      hoa.subscription.cancelAtPeriodEnd = stripeSubscription.cancel_at_period_end;

      await hoa.save();
    }
  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

/**
 * Get subscription payments for an HOA
 * @route GET /api/subscriptions/hoa/:hoaId/payments
 * @access Private/CompanyAdmin, HOA Admin
 */
exports.getSubscriptionPayments = async (req, res) => {
  try {
    const { hoaId } = req.params;

    // Check if HOA exists
    const hoa = await HOA.findById(hoaId);
    if (!hoa) {
      return res.status(404).json({
        success: false,
        message: 'HOA not found'
      });
    }

    // Check if user has permission to view subscription payments
    if (req.user.role !== 'company_admin' && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to view subscription payments for this HOA'
      });
    }

    // Get subscription payments
    const payments = await SubscriptionPayment.find({ hoaId })
      .sort({ createdAt: -1 })
      .limit(12); // Limit to last 12 payments

    res.status(200).json({
      success: true,
      payments
    });
  } catch (error) {
    console.error('Error getting subscription payments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get subscription payments',
      error: error.message
    });
  }
};

/**
 * Handle subscription deleted event
 * @param {Object} stripeSubscription - Stripe subscription object
 */
async function handleSubscriptionDeleted(stripeSubscription) {
  try {
    const hoaId = stripeSubscription.metadata.hoaId;

    if (!hoaId) {
      console.error('No HOA ID found in subscription metadata');
      return;
    }

    // Find subscription in database
    const subscription = await Subscription.findOne({
      stripeSubscriptionId: stripeSubscription.id
    });

    if (!subscription) {
      console.error('Subscription not found in database');
      return;
    }

    // Update subscription status
    subscription.status = 'canceled';
    subscription.canceledAt = new Date();

    await subscription.save();

    // Update HOA subscription status
    const hoa = await HOA.findById(hoaId);
    if (hoa) {
      hoa.subscription.status = 'canceled';

      await hoa.save();
    }

    // Create notification for admins
    const adminUsers = await User.find({ role: 'admin', hoaId });
    const adminIds = adminUsers.map(user => user._id);

    await notificationController.createInfoNotification(
      'Subscription Canceled',
      `The subscription for ${hoa ? hoa.hoaCommunityName : 'your HOA'} has been canceled`,
      adminIds
    );
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}

exports.retrieveCheckoutSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    const subscription = session.subscription;

    await Subscription.updateOne(
      { hoaId: session.metadata.hoaId },
      { stripeSubscriptionId: subscription }
    )
    res.status(200).json({ status: 'success' });
  } catch (error) {
    console.error('Error retrieving checkout session:', error);
    res.status(500).json({ error: 'Failed to retrieve checkout session' });
  }
};

/**
 * Get subscription by community ID
 * @route GET /api/subscriptions/by-community/:communityId
 * @access Private/CompanyAdmin, HOA Admin
 */
exports.getSubscriptionByCommunityId = async (req, res) => {
  try {
    const { communityId } = req.params;

    console.log('🔍 DEBUG: getSubscriptionByCommunityId called with:', {
      communityId,
      userId: req.user._id,
      userRole: req.user.role
    });

    // Find the community and get its HOA
    const community = await Community.findById(communityId).populate('hoaId');
    if (!community) {
      return res.status(404).json({
        success: false,
        message: 'Community not found'
      });
    }

    console.log(`🔍 DEBUG: Found community ${community.name} with HOA ${community.hoaId?.hoaCommunityName || 'Unknown'}`);
    const hoaId = community.hoaId._id;

    // Find subscription by HOA ID
    const subscription = await Subscription.findOne({ hoaId });
    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Subscription not found for this community'
      });
    }

    console.log('🔍 DEBUG: Found subscription for HOA:', hoaId);

    // Check if user has permission to view subscription
    if (req.user.role !== 'company_admin') {
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to view this subscription'
        });
      }

      // For admin users, check if they have access to this HOA
      if (req.user.hoaId && req.user.hoaId.toString() !== hoaId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to view this subscription'
        });
      }

      // For admin users without hoaId, check hoaCommunityCode access
      if (!req.user.hoaId && req.user.hoaCommunityCode) {
        // Get the HOA to check its community code
        const hoa = await HOA.findById(hoaId);
        if (!hoa || !req.user.hoaCommunityCode.includes(hoa.hoaCommunityCode)) {
          return res.status(403).json({
            success: false,
            message: 'You do not have permission to view this subscription'
          });
        }
      }
    }

    // Get Stripe subscription details
    const stripeSubscription = await stripe.subscriptions.retrieve(
      subscription.stripeSubscriptionId,
      {
        expand: ['default_payment_method', 'latest_invoice']
      }
    );

    console.log('🔍 DEBUG: Successfully retrieved subscription details by community ID');

    res.status(200).json({
      success: true,
      subscription,
      selectedCommunityId: communityId,
      stripeDetails: {
        status: stripeSubscription.status,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
        defaultPaymentMethod: stripeSubscription.default_payment_method,
        latestInvoice: stripeSubscription.latest_invoice
      }
    });
  } catch (error) {
    console.error('Error getting subscription by community ID:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get subscription details',
      error: error.message
    });
  }
};

const Message = require('../../models/message');
const Conversation = require('../../models/conversation');
const User = require('../../models/user');
const mongoose = require('mongoose');
const { createNotification } = require('../notification/notificationController');

// Get all conversations for the current user
const getConversations = async (req, res) => {
  try {
    const userId = req.user._id;
    console.log('[GET CONVERSATIONS] Getting conversations for user:', userId);

    // Check database connection
    console.log('[GET CONVERSATIONS] MongoDB connection state:', mongoose.connection.readyState);

    // Convert userId to ObjectId if it's a string
    const userObjectId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;

    // First, check if there are any conversations with this user as a participant
    // using a more flexible approach that tries multiple formats
    console.log('[GET CONVERSATIONS] Trying flexible query first');

    let conversations = await Conversation.find({
      $or: [
        { participants: userObjectId },
        { participants: userId.toString() },
        { 'participants': { $in: [userObjectId] } },
        { 'participants': { $in: [userId.toString()] } }
      ],
      hiddenFor: {
        $not: {
          $elemMatch: {
            $in: [userObjectId, userId.toString()]
          }
        }
      }
    })
    .populate({
      path: 'participants',
      select: 'username fullName email profilePicture role'
    })
    .populate({
      path: 'lastMessage',
      select: 'content createdAt sender'
    })
    .sort({ updatedAt: -1 });

    console.log('[GET CONVERSATIONS] Found conversations with flexible query:', conversations.length);

    // If no conversations, check if there are any messages for this user
    if (conversations.length === 0) {
      console.log('[GET CONVERSATIONS] No conversations found with initial query, checking for messages...');

      // Check if there are any messages for this user
      const messages = await Message.find({
        $or: [
          { sender: userId },
          { recipient: userId }
        ]
      }).sort({ createdAt: -1 }).limit(20);

      console.log('[GET CONVERSATIONS] Found messages:', messages.length);

      // If there are messages but no conversations, we need to create the conversations
      if (messages.length > 0) {
        console.log('[GET CONVERSATIONS] Found messages but no conversations, creating conversations...');

        // Group messages by conversation ID
        const conversationMap = new Map();
        const directMessageMap = new Map();

        for (const msg of messages) {
          // If message has a conversation ID, group by that
          if (msg.conversation) {
            const convId = msg.conversation.toString();
            if (!conversationMap.has(convId)) {
              conversationMap.set(convId, []);
            }
            conversationMap.get(convId).push(msg);
          }
          // Otherwise, create a key based on the participants
          else {
            const senderId = msg.sender.toString();
            const recipientId = msg.recipient.toString();
            const otherUserId = senderId === userId.toString() ? recipientId : senderId;

            if (!directMessageMap.has(otherUserId)) {
              directMessageMap.set(otherUserId, []);
            }
            directMessageMap.get(otherUserId).push(msg);
          }
        }

        console.log('[GET CONVERSATIONS] Grouped messages into conversation groups:',
          conversationMap.size, 'and direct message groups:', directMessageMap.size);

        // Process messages with conversation IDs
        const newConversations = [];

        // First, handle messages with conversation IDs
        for (const [convId, msgs] of conversationMap.entries()) {
          console.log(`[GET CONVERSATIONS] Processing conversation ${convId} with ${msgs.length} messages`);

          // Find the other participant (not the current user)
          const otherParticipantIds = new Set();
          msgs.forEach(msg => {
            if (msg.sender && msg.sender.toString() !== userId.toString()) {
              otherParticipantIds.add(msg.sender.toString());
            }
            if (msg.recipient && msg.recipient.toString() !== userId.toString()) {
              otherParticipantIds.add(msg.recipient.toString());
            }
          });

          // Create or update the conversation
          try {
            let conversation = await Conversation.findById(convId);

            if (!conversation) {
              // Create a new conversation
              console.log(`[GET CONVERSATIONS] Creating new conversation with ID: ${convId}`);
              conversation = new Conversation({
                _id: convId,
                participants: [userId, ...Array.from(otherParticipantIds)],
                lastMessage: msgs[0]._id, // Most recent message (sorted by createdAt desc)
                unreadCount: new Map(),
                createdAt: msgs[msgs.length - 1].createdAt, // Oldest message
                updatedAt: msgs[0].createdAt // Newest message
              });
              await conversation.save();
              console.log(`[GET CONVERSATIONS] Created new conversation with ID: ${convId}`);
            } else {
              // Update the conversation participants if needed
              let updated = false;

              // Check if current user is in participants
              const userInParticipants = conversation.participants.some(p =>
                p.toString() === userId.toString()
              );

              if (!userInParticipants) {
                console.log(`[GET CONVERSATIONS] Adding current user ${userId} to conversation ${convId}`);
                conversation.participants.push(userId);
                updated = true;
              }

              // Add any missing participants
              for (const participantId of otherParticipantIds) {
                const participantInConversation = conversation.participants.some(p =>
                  p.toString() === participantId
                );

                if (!participantInConversation) {
                  console.log(`[GET CONVERSATIONS] Adding participant ${participantId} to conversation ${convId}`);
                  conversation.participants.push(participantId);
                  updated = true;
                }
              }

              if (updated) {
                await conversation.save();
                console.log(`[GET CONVERSATIONS] Updated conversation ${convId} with missing participants`);
              }
            }

            // Populate the conversation
            const populatedConversation = await Conversation.findById(convId)
              .populate({
                path: 'participants',
                select: 'username fullName email profilePicture role'
              })
              .populate({
                path: 'lastMessage',
                select: 'content createdAt sender deleted'
              });

            if (populatedConversation) {
              newConversations.push(populatedConversation);
            }
          } catch (error) {
            console.error(`[GET CONVERSATIONS] Error processing conversation ${convId}:`, error);
          }
        }

        // Then handle direct messages without conversation IDs
        for (const [otherUserId, msgs] of directMessageMap.entries()) {
          console.log(`[GET CONVERSATIONS] Processing direct messages with ${otherUserId}, ${msgs.length} messages`);

          try {
            // Create a new conversation ID
            const newConvId = new mongoose.Types.ObjectId();
            console.log(`[GET CONVERSATIONS] Creating new conversation with ID: ${newConvId} for direct messages`);

            // Create the conversation
            const conversation = new Conversation({
              _id: newConvId,
              participants: [userId, otherUserId],
              lastMessage: msgs[0]._id, // Most recent message
              unreadCount: new Map(),
              createdAt: msgs[msgs.length - 1].createdAt, // Oldest message
              updatedAt: msgs[0].createdAt // Newest message
            });

            await conversation.save();
            console.log(`[GET CONVERSATIONS] Created new conversation for direct messages: ${newConvId}`);

            // Update all messages to reference this conversation
            await Message.updateMany(
              {
                $or: [
                  { sender: userId, recipient: otherUserId },
                  { sender: otherUserId, recipient: userId }
                ],
                conversation: { $exists: false }
              },
              { conversation: newConvId }
            );

            console.log(`[GET CONVERSATIONS] Updated messages to reference conversation: ${newConvId}`);

            // Populate the conversation
            const populatedConversation = await Conversation.findById(newConvId)
              .populate({
                path: 'participants',
                select: 'username fullName email profilePicture role'
              })
              .populate({
                path: 'lastMessage',
                select: 'content createdAt sender deleted'
              });

            if (populatedConversation) {
              newConversations.push(populatedConversation);
            }
          } catch (error) {
            console.error(`[GET CONVERSATIONS] Error processing direct messages with ${otherUserId}:`, error);
          }
        }

        console.log(`[GET CONVERSATIONS] Created/updated ${newConversations.length} conversations from messages`);
        if (newConversations.length > 0) {
          conversations = newConversations;
        }
      }
    }

    // Format the conversations for the frontend
    const formattedConversations = conversations.map(conversation => {
      try {
        // Filter out the current user from participants
        const otherParticipants = conversation.participants.filter(
          participant => participant && participant._id && participant._id.toString() !== userId.toString()
        );

        // Get unread count for current user
        const unreadCount = conversation.unreadCount ? (conversation.unreadCount.get(userId.toString()) || 0) : 0;

        // Check if we have a valid lastMessage
        let lastMessageData = conversation.lastMessage;

        // If lastMessage is deleted or missing, try to find another one
        if (!lastMessageData || (lastMessageData && lastMessageData.deleted)) {
          console.log(`[GET CONVERSATIONS] Last message for conversation ${conversation._id} is deleted or missing, finding another one`);
          // We'll handle this on the frontend by showing "Message deleted" or "No messages"
        }

        return {
          _id: conversation._id,
          participants: otherParticipants,
          lastMessage: lastMessageData,
          title: conversation.title || otherParticipants.map(p => p.fullName || p.username || 'Unknown User').join(', '),
          unreadCount,
          updatedAt: conversation.updatedAt
        };
      } catch (err) {
        console.error(`[GET CONVERSATIONS] Error formatting conversation ${conversation._id}:`, err);
        // Return a minimal valid conversation object
        return {
          _id: conversation._id,
          participants: [],
          lastMessage: null,
          title: 'Conversation',
          unreadCount: 0,
          updatedAt: conversation.updatedAt || new Date()
        };
      }
    }).filter(Boolean); // Remove any null/undefined values

    // Sort by updatedAt
    formattedConversations.sort((a, b) => {
      const dateA = new Date(a.updatedAt || 0);
      const dateB = new Date(b.updatedAt || 0);
      return dateB - dateA; // Most recent first
    });

    console.log('[GET CONVERSATIONS] Returning formatted conversations:', formattedConversations.length);
    res.status(200).json(formattedConversations);
  } catch (error) {
    console.error('[GET CONVERSATIONS] Error getting conversations:', error);
    res.status(500).json({
      error: 'Failed to get conversations',
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? undefined : error.stack
    });
  }
};

// Get conversation details
const getConversationDetails = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { otherUserId } = req.query; // Optional parameter to find conversation by other user ID
    const userId = req.user._id;

    console.log(`Getting conversation details for ID: ${conversationId}, user: ${userId}, otherUser: ${otherUserId}`);

    // If otherUserId is provided, try to find a conversation between the current user and the other user
    if (otherUserId && !conversationId) {
      console.log(`Looking for conversation between users ${userId} and ${otherUserId}`);

      // Try to find a direct conversation between these two users
      let directConversation = await Conversation.findOne({
        participants: { $all: [userId, otherUserId] },
        $expr: { $eq: [{ $size: "$participants" }, 2] } // Only direct conversations
      }).populate({
        path: 'participants',
        select: 'username fullName email profilePicture role'
      });

      if (directConversation) {
        console.log(`Found direct conversation: ${directConversation._id}`);
        return res.status(200).json(directConversation);
      }

      // If no conversation found, check if there are any messages between these users
      console.log('No direct conversation found, checking for messages between users');
      const messages = await Message.find({
        $or: [
          { sender: userId, recipient: otherUserId },
          { sender: otherUserId, recipient: userId }
        ]
      }).sort({ createdAt: -1 }).limit(10);

      if (messages.length > 0) {
        console.log(`Found ${messages.length} messages between users`);

        // If any of these messages has a conversation ID, use that
        const messageWithConversation = messages.find(msg => msg.conversation);
        if (messageWithConversation) {
          console.log(`Found message with conversation ID: ${messageWithConversation.conversation}`);

          // Get that conversation
          const existingConversation = await Conversation.findById(messageWithConversation.conversation).populate({
            path: 'participants',
            select: 'username fullName email profilePicture role'
          });

          if (existingConversation) {
            console.log(`Found existing conversation: ${existingConversation._id}`);
            return res.status(200).json(existingConversation);
          }
        }

        // Create a new conversation
        const newConversationId = new mongoose.Types.ObjectId();
        console.log(`Creating new conversation with ID: ${newConversationId}`);

        const newConversation = new Conversation({
          _id: newConversationId,
          participants: [userId, otherUserId],
          lastMessage: messages[0]._id,
          unreadCount: new Map(),
          createdAt: messages[messages.length - 1].createdAt,
          updatedAt: messages[0].createdAt,
          hiddenFor: [] // Initialize with empty array
        });

        await newConversation.save();
        console.log(`Created new conversation: ${newConversationId}`);

        // Update all messages to reference this conversation
        await Message.updateMany(
          {
            $or: [
              { sender: userId, recipient: otherUserId },
              { sender: otherUserId, recipient: userId }
            ],
            conversation: { $exists: false }
          },
          { conversation: newConversationId }
        );

        // Return the populated conversation
        const populatedConversation = await Conversation.findById(newConversationId).populate({
          path: 'participants',
          select: 'username fullName email profilePicture role'
        });

        return res.status(200).json(populatedConversation);
      }

      // If no messages found, return 404
      return res.status(404).json({ error: 'No conversation or messages found between these users' });
    }

    // Regular flow - find by conversation ID
    // Verify the conversation exists and user is a participant
    let conversation = await Conversation.findOne({
      _id: conversationId,
      participants: userId
    }).populate({
      path: 'participants',
      select: 'username fullName email profilePicture role'
    });

    if (!conversation) {
      console.log('Conversation not found with user as participant, checking if it exists at all');

      // Check if the conversation exists but user is not a participant
      conversation = await Conversation.findById(conversationId).populate({
        path: 'participants',
        select: 'username fullName email profilePicture role'
      });

      if (!conversation) {
        console.log('Conversation not found at all, checking for messages with this conversation ID');

        // Check if there are messages with this conversation ID
        const messages = await Message.find({ conversation: conversationId })
          .sort({ createdAt: -1 })
          .limit(10);

        if (messages.length > 0) {
          console.log(`Found ${messages.length} messages for this conversation ID`);

          // Get unique participants from messages
          const participantIds = new Set();
          messages.forEach(msg => {
            if (msg.sender) participantIds.add(msg.sender.toString());
            if (msg.recipient) participantIds.add(msg.recipient.toString());
          });

          // Make sure current user is included
          participantIds.add(userId.toString());

          console.log('Creating conversation from messages with participants:', Array.from(participantIds));

          // Create the conversation
          conversation = new Conversation({
            _id: conversationId,
            participants: Array.from(participantIds),
            lastMessage: messages[0]._id,
            unreadCount: new Map(),
            createdAt: messages[messages.length - 1].createdAt,
            updatedAt: messages[0].createdAt
          });

          await conversation.save();
          console.log('Created conversation from messages');

          // Populate the participants
          conversation = await Conversation.findById(conversationId).populate({
            path: 'participants',
            select: 'username fullName email profilePicture role'
          });
        } else {
          console.log('No messages found for this conversation ID');
          return res.status(404).json({ error: 'Conversation not found' });
        }
      } else {
        console.log('Conversation exists but user is not a participant, adding user');

        // Add user to participants
        conversation.participants.push(userId);
        await conversation.save();

        // Reload with populated participants
        conversation = await Conversation.findById(conversationId).populate({
          path: 'participants',
          select: 'username fullName email profilePicture role'
        });
      }
    }

    console.log('Returning conversation details');
    res.status(200).json(conversation);
  } catch (error) {
    console.error('Error fetching conversation details:', error);
    res.status(500).json({ error: 'Failed to fetch conversation details' });
  }
};

// Get messages for a specific conversation
const getMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user._id;

    console.log(`Getting messages for conversation: ${conversationId}, user: ${userId}`);

    // Verify the conversation exists and user is a participant
    let conversation = await Conversation.findOne({
      _id: conversationId,
      participants: userId
    });

    if (!conversation) {
      console.log('Conversation not found with user as participant, checking if it exists at all');

      // Check if the conversation exists but user is not a participant
      conversation = await Conversation.findById(conversationId);

      if (!conversation) {
        console.log('Conversation not found at all, checking for messages with this conversation ID');

        // Check if there are messages with this conversation ID
        const messagesExist = await Message.exists({ conversation: conversationId });

        if (messagesExist) {
          console.log('Messages exist for this conversation ID, creating conversation');

          // Get all messages for this conversation to determine participants
          const allMessages = await Message.find({ conversation: conversationId })
            .sort({ createdAt: -1 });

          // Get unique participants from messages
          const participantIds = new Set();
          allMessages.forEach(msg => {
            if (msg.sender) participantIds.add(msg.sender.toString());
            if (msg.recipient) participantIds.add(msg.recipient.toString());
          });

          // Make sure current user is included
          participantIds.add(userId.toString());

          console.log('Creating conversation from messages with participants:', Array.from(participantIds));

          // Create the conversation
          conversation = new Conversation({
            _id: conversationId,
            participants: Array.from(participantIds),
            lastMessage: allMessages[0]._id,
            unreadCount: new Map(),
            createdAt: allMessages[allMessages.length - 1].createdAt,
            updatedAt: allMessages[0].createdAt
          });

          await conversation.save();
          console.log('Created conversation from messages');
        } else {
          console.log('No messages found for this conversation ID');
          return res.status(404).json({ error: 'Conversation not found' });
        }
      } else {
        console.log('Conversation exists but user is not a participant, adding user');

        // Add user to participants
        conversation.participants.push(userId);
        await conversation.save();
      }
    }

    // Get messages
    let messages = await Message.find({
      conversation: conversationId
    })
    .populate({
      path: 'sender',
      select: 'username fullName email profilePicture role'
    })
    .sort({ createdAt: 1 });

    console.log(`Found ${messages.length} messages for conversation ${conversationId}`);

    // If no messages found, try a more flexible query
    if (messages.length === 0) {
      console.log('No messages found with standard query, trying more flexible query');

      // Try to find messages by conversation ID as string
      messages = await Message.find({
        $or: [
          { conversation: conversationId },
          { conversation: conversationId.toString() }
        ]
      })
      .populate({
        path: 'sender',
        select: 'username fullName email profilePicture role'
      })
      .sort({ createdAt: 1 });

      console.log(`Found ${messages.length} messages with flexible query`);

      // If still no messages, check if there are any messages between these participants
      if (messages.length === 0 && conversation && conversation.participants.length > 0) {
        console.log('Still no messages found, checking for messages between participants');

        // Get all participants except current user
        const otherParticipants = conversation.participants
          .filter(p => p.toString() !== userId.toString())
          .map(p => p.toString());

        console.log('Looking for messages between current user and:', otherParticipants);

        if (otherParticipants.length > 0) {
          messages = await Message.find({
            $or: [
              { sender: userId, recipient: { $in: otherParticipants } },
              { recipient: userId, sender: { $in: otherParticipants } }
            ]
          })
          .populate({
            path: 'sender',
            select: 'username fullName email profilePicture role'
          })
          .sort({ createdAt: 1 });

          console.log(`Found ${messages.length} messages between participants`);

          // If we found messages, update their conversation ID
          if (messages.length > 0) {
            console.log('Updating conversation ID for these messages');
            await Message.updateMany(
              { _id: { $in: messages.map(m => m._id) } },
              { conversation: conversationId }
            );

            // Refresh the messages with updated conversation ID
            messages = await Message.find({
              conversation: conversationId
            })
            .populate({
              path: 'sender',
              select: 'username fullName email profilePicture role'
            })
            .sort({ createdAt: 1 });
          }
        }
      }
    }

    // Mark messages as read
    const updateResult = await Message.updateMany(
      {
        conversation: conversationId,
        recipient: userId,
        read: false
      },
      { read: true }
    );

    console.log(`Marked ${updateResult.modifiedCount} messages as read`);

    // Reset unread count for this user
    conversation.unreadCount.set(userId.toString(), 0);
    await conversation.save();
    console.log('Reset unread count for user');

    res.status(200).json(messages);
  } catch (error) {
    console.error('Error getting messages:', error);
    res.status(500).json({ error: 'Failed to get messages' });
  }
};

// Send a new message
const sendMessage = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { recipientId, content, conversationId } = req.body;
    const senderId = req.user._id;

    console.log('Send message request:', {
      senderId,
      recipientId,
      content: content ? content.substring(0, 30) + (content.length > 30 ? '...' : '') : null,
      conversationId
    });

    if (!content) {
      return res.status(400).json({ error: 'Message content is required' });
    }

    // Check if recipient exists
    const recipient = await User.findById(recipientId);
    if (!recipient) {
      console.log('Recipient not found:', recipientId);
      return res.status(404).json({ error: 'Recipient not found' });
    }

    console.log('Recipient found:', recipient.username || recipient.email);

    let conversation;

    // If conversationId is provided, use that conversation
    if (conversationId) {
      console.log('Looking for existing conversation:', conversationId);

      conversation = await Conversation.findOne({
        _id: conversationId,
        participants: { $all: [senderId, recipientId] }
      });

      if (!conversation) {
        console.log('Conversation not found with ID:', conversationId);

        // Try to find it without the participants check
        const anyConversation = await Conversation.findById(conversationId);
        if (anyConversation) {
          console.log('Found conversation but participants don\'t match. Current participants:', anyConversation.participants);

          // Check if both users are in the participants list
          const senderInParticipants = anyConversation.participants.some(p => p.toString() === senderId.toString());
          const recipientInParticipants = anyConversation.participants.some(p => p.toString() === recipientId.toString());

          if (!senderInParticipants) {
            console.log('Adding sender to participants');
            anyConversation.participants.push(senderId);
          }

          if (!recipientInParticipants) {
            console.log('Adding recipient to participants');
            anyConversation.participants.push(recipientId);
          }

          if (!senderInParticipants || !recipientInParticipants) {
            await anyConversation.save();
            conversation = anyConversation;
            console.log('Updated conversation participants');
          } else {
            console.log('Both users are in participants but query failed, this is unexpected');
            return res.status(404).json({ error: 'Conversation not found' });
          }
        } else {
          console.log('No conversation found with this ID at all');
          return res.status(404).json({ error: 'Conversation not found' });
        }
      } else {
        console.log('Found existing conversation');
      }
    } else {
      // Check if a conversation already exists between these users
      console.log('Looking for existing conversation between users');

      conversation = await Conversation.findOne({
        participants: { $all: [senderId, recipientId] },
        $expr: { $eq: [{ $size: "$participants" }, 2] } // Only direct conversations
      });

      // If no conversation exists, create a new one
      if (!conversation) {
        console.log('No existing conversation found, creating new one');

        conversation = new Conversation({
          participants: [senderId, recipientId],
          unreadCount: new Map()
        });

        // Save the conversation first to get an ID
        await conversation.save({ session });
        console.log('Created new conversation with ID:', conversation._id);
      } else {
        console.log('Found existing conversation:', conversation._id);
      }
    }

    // Create the new message
    console.log('Creating new message');
    const message = new Message({
      sender: senderId,
      recipient: recipientId,
      content,
      conversation: conversation._id
    });

    await message.save({ session });
    console.log('Saved new message with ID:', message._id);

    // Update the conversation with the last message
    conversation.lastMessage = message._id;

    // Increment unread count for recipient
    const currentCount = conversation.unreadCount.get(recipientId.toString()) || 0;
    conversation.unreadCount.set(recipientId.toString(), currentCount + 1);

    await conversation.save({ session });
    console.log('Updated conversation with new message');

    // Create a notification for the recipient
    try {
      await createNotification({
        user: recipientId,
        title: 'New Message',
        description: `${req.user.fullName || req.user.username || req.user.email} sent you a message`,
        type: 'message',
        metadata: {
          senderId: senderId.toString(),
          conversationId: conversation._id.toString(),
          messagePreview: content.substring(0, 50) + (content.length > 50 ? '...' : '')
        }
      }, { session });
      console.log('Created notification for recipient');
    } catch (notificationError) {
      console.error('Error creating notification:', notificationError);
      // Continue even if notification creation fails
    }

    await session.commitTransaction();
    session.endSession();
    console.log('Transaction committed successfully');

    // Populate sender info before returning
    const populatedMessage = await Message.findById(message._id)
      .populate({
        path: 'sender',
        select: 'username fullName email profilePicture role'
      });

    console.log('Populated message with sender info');

    // Emit socket event for real-time updates
    if (req.io) {
      console.log(`Emitting new_message event to recipient: ${recipientId.toString()}`);

      // Send to the specific recipient's room
      req.io.to(recipientId.toString()).emit('new_message', {
        message: populatedMessage,
        conversation: conversation._id
      });

      // Also send to the sender's room to ensure they see their own messages
      // This helps with production environments where socket events might be missed
      req.io.to(senderId.toString()).emit('new_message', {
        message: populatedMessage,
        conversation: conversation._id
      });

      // Also broadcast to all connected clients to ensure maximum delivery
      req.io.emit('conversation_updated', {
        conversationId: conversation._id.toString()
      });

      // Log the message details for debugging
      console.log('Emitted message details:', {
        messageId: populatedMessage._id,
        conversationId: conversation._id.toString(),
        senderId: senderId.toString(),
        recipientId: recipientId.toString(),
        content: populatedMessage.content.substring(0, 30) + (populatedMessage.content.length > 30 ? '...' : '')
      });
    } else {
      console.log('No socket.io instance available, skipping real-time updates');
    }

    // Return the response
    console.log('Sending success response to client');
    res.status(201).json({
      message: populatedMessage,
      conversationId: conversation._id
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    console.error('Error sending message:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
};

// Edit a message
const editMessage = async (req, res) => {
  try {
    const { messageId } = req.params;
    const { content } = req.body;
    const userId = req.user._id;

    if (!content || content.trim() === '') {
      return res.status(400).json({ error: 'Message content cannot be empty' });
    }

    // Find the message and check if the user is the sender
    const message = await Message.findOne({
      _id: messageId,
      sender: userId
    });

    if (!message) {
      return res.status(404).json({ error: 'Message not found or you do not have permission to edit it' });
    }

    // Check if the message is too old to edit (e.g., 24 hours)
    const messageAge = Date.now() - new Date(message.createdAt).getTime();
    const MAX_EDIT_AGE = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    if (messageAge > MAX_EDIT_AGE) {
      return res.status(403).json({ error: 'Message is too old to edit' });
    }

    // Update the message
    message.content = content;
    message.edited = true;
    message.editedAt = Date.now();
    await message.save();

    // Get the conversation to notify other participants
    const conversation = await Conversation.findById(message.conversation);

    // Emit socket event for real-time updates
    if (req.io) {
      // Notify all participants except the sender
      conversation.participants.forEach(participantId => {
        if (participantId.toString() !== userId.toString()) {
          req.io.to(participantId.toString()).emit('message_updated', {
            messageId: message._id,
            content: message.content,
            edited: message.edited,
            editedAt: message.editedAt,
            conversationId: message.conversation
          });
        }
      });
    }

    res.status(200).json({
      message: {
        _id: message._id,
        content: message.content,
        edited: message.edited,
        editedAt: message.editedAt
      }
    });
  } catch (error) {
    console.error('Error editing message:', error);
    res.status(500).json({ error: 'Failed to edit message' });
  }
};

// Delete a message
const deleteMessage = async (req, res) => {
  try {
    const { messageId } = req.params;
    const userId = req.user._id;

    console.log(`[DELETE MESSAGE] Request to delete message ${messageId} for user ${userId} (role: ${req.user.role})`);

    // Validate messageId format
    if (!mongoose.Types.ObjectId.isValid(messageId)) {
      console.error(`[DELETE MESSAGE] Invalid message ID format: ${messageId}`);
      return res.status(400).json({ error: 'Invalid message ID format' });
    }

    // Find the message and check if the user is the sender
    const message = await Message.findOne({
      _id: messageId
    });

    if (!message) {
      console.error(`[DELETE MESSAGE] Message ${messageId} not found in database`);
      return res.status(404).json({ error: 'Message not found', messageId });
    }

    console.log(`[DELETE MESSAGE] Found message: ${JSON.stringify({
      id: message._id,
      sender: message.sender,
      conversation: message.conversation,
      content: message.content.substring(0, 20) + (message.content.length > 20 ? '...' : ''),
      deleted: message.deleted,
      createdAt: message.createdAt
    })}`);

    // Check if message is already deleted
    if (message.deleted) {
      console.log(`[DELETE MESSAGE] Message ${messageId} is already marked as deleted`);
      return res.status(200).json({
        message: 'Message already deleted',
        messageId,
        deletedMessage: message
      });
    }

    // Check if user is the sender or an admin
    const isAdmin = req.user.role === 'admin' || req.user.role === 'company_admin';
    const isSender = message.sender.toString() === userId.toString();

    console.log(`[DELETE MESSAGE] Authorization check: isAdmin=${isAdmin}, isSender=${isSender}`);
    console.log(`[DELETE MESSAGE] Sender comparison: message.sender=${message.sender} (${typeof message.sender}), userId=${userId} (${typeof userId})`);

    if (!isSender && !isAdmin) {
      console.error(`[DELETE MESSAGE] User ${userId} is not the sender of message ${messageId} and is not an admin`);
      return res.status(403).json({
        error: 'You do not have permission to delete this message',
        messageId,
        userId,
        senderId: message.sender,
        userRole: req.user.role
      });
    }

    console.log(`[DELETE MESSAGE] User ${userId} is authorized to delete message ${messageId}`);
    const conversationId = message.conversation;

    // Mark as deleted - we'll filter these out on the frontend
    message.deleted = true;
    message.content = "This message has been deleted";  // Set a placeholder instead of empty string
    message.deletedAt = Date.now();

    try {
      // Use updateOne instead of save to bypass validation
      await Message.updateOne(
        { _id: messageId },
        {
          deleted: true,
          content: "This message has been deleted",
          deletedAt: Date.now()
        }
      );
      console.log(`[DELETE MESSAGE] Message ${messageId} marked as deleted successfully`);
    } catch (saveError) {
      console.error(`[DELETE MESSAGE] Error saving deleted message: ${saveError}`);
      return res.status(500).json({
        error: 'Failed to save deleted message state',
        details: saveError.message
      });
    }

    // Get the conversation to notify other participants
    const conversation = await Conversation.findById(conversationId);

    if (!conversation) {
      console.error(`[DELETE MESSAGE] Conversation ${conversationId} not found`);
      return res.status(200).json({
        message: 'Message deleted but conversation not found',
        messageId,
        conversationId
      });
    }

    console.log(`[DELETE MESSAGE] Found conversation with ${conversation.participants.length} participants`);

    // Check if this was the last message in the conversation and update if needed
    if (conversation.lastMessage && conversation.lastMessage.toString() === messageId) {
      console.log(`[DELETE MESSAGE] This was the last message in the conversation, finding new last message`);

      try {
        // Find the new last message that isn't deleted
        const newLastMessage = await Message.findOne({
          conversation: conversationId,
          deleted: { $ne: true },
          _id: { $ne: messageId }
        }).sort({ createdAt: -1 });

        if (newLastMessage) {
          console.log(`[DELETE MESSAGE] Found new last message: ${newLastMessage._id}`);
          conversation.lastMessage = newLastMessage._id;
          await conversation.save();
          console.log(`[DELETE MESSAGE] Updated conversation ${conversationId} last message to ${newLastMessage._id}`);
        } else {
          console.log(`[DELETE MESSAGE] No non-deleted messages found for conversation ${conversationId}`);
        }
      } catch (lastMessageError) {
        console.error(`[DELETE MESSAGE] Error updating last message: ${lastMessageError}`);
        // Continue execution - this is not a critical error
      }
    }

    // Emit socket event for real-time updates
    if (req.io) {
      console.log(`[DELETE MESSAGE] Emitting socket event for message deletion`);
      try {
        // Notify all participants except the sender
        conversation.participants.forEach(participantId => {
          if (participantId.toString() !== userId.toString()) {
            console.log(`[DELETE MESSAGE] Emitting to participant: ${participantId}`);
            req.io.to(participantId.toString()).emit('message_deleted', {
              messageId: message._id,
              conversationId
            });
          }
        });
      } catch (socketError) {
        console.error(`[DELETE MESSAGE] Error emitting socket event: ${socketError}`);
        // Continue execution - socket errors shouldn't prevent API response
      }
    } else {
      console.log(`[DELETE MESSAGE] Socket.io not available, skipping real-time notification`);
    }

    // Return the deleted message for immediate UI update
    try {
      const deletedMessage = await Message.findById(messageId)
        .populate({
          path: 'sender',
          select: 'username fullName email profilePicture role'
        });

      console.log(`[DELETE MESSAGE] Successfully retrieved deleted message for response`);

      // Send detailed response
      res.status(200).json({
        success: true,
        messageId: message._id,
        deletedMessage,
        timestamp: new Date().toISOString()
      });
    } catch (populateError) {
      console.error(`[DELETE MESSAGE] Error retrieving deleted message for response: ${populateError}`);
      // Still return success even if we can't populate the message
      res.status(200).json({
        success: true,
        messageId: message._id,
        error: 'Could not retrieve full message details',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error(`[DELETE MESSAGE] Unhandled error in delete message: ${error}`);
    console.error(error.stack);
    res.status(500).json({
      error: 'Failed to delete message',
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? undefined : error.stack
    });
  }
};

// Delete a conversation completely
const hideConversation = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user._id;

    // Find the conversation and check if the user is a participant
    const conversation = await Conversation.findOne({
      _id: conversationId,
      participants: userId
    });

    if (!conversation) {
      return res.status(404).json({ error: 'Conversation not found or you are not a participant' });
    }

    // Delete all messages in the conversation
    await Message.deleteMany({ conversation: conversationId });

    // Delete the conversation itself
    await Conversation.deleteOne({ _id: conversationId });

    res.status(200).json({ success: true, message: 'Conversation deleted successfully' });
  } catch (error) {
    console.error('Error deleting conversation:', error);
    res.status(500).json({ error: 'Failed to delete conversation' });
  }
};

// This function is no longer needed but we'll keep it for API compatibility
const unhideConversation = async (req, res) => {
  try {
    res.status(410).json({ error: 'This feature has been removed. Conversations are now permanently deleted.' });
  } catch (error) {
    console.error('Error in unhide conversation endpoint:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

module.exports = {
  getConversations,
  getConversationDetails,
  getMessages,
  sendMessage,
  editMessage,
  deleteMessage,
  hideConversation,
  unhideConversation
};

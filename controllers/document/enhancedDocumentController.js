/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// controllers/document/enhancedDocumentController.js
const Document = require('../../models/document');
const AnnouncementAttachment = require('../../models/announcementAttachment');
const { generateSignedUrl, deleteFile, generateS3Key } = require('../../config/s3Config');
const jwt = require('jsonwebtoken');

/**
 * Enhanced Document Controller with S3 Integration and Access Control
 * Provides comprehensive document management with HOA isolation
 */

// Generate a secure download token
const generateDownloadToken = (docId, userId) => {
  return jwt.sign(
    { docId, userId },
    process.env.JWT_SECRET,
    { expiresIn: '24h' } // Token expires in 24 hours
  );
};

// Get the appropriate API URL based on environment
const getApiUrl = () => {
  // Multiple checks to detect local development environment
  const isLocalPort = process.env.PORT === '5001';
  const isDevelopment = process.env.NODE_ENV === 'development';
  const noHerokuApp = !process.env.HEROKU_APP_NAME;
  const hasLocalEnv = process.env.FRONTEND_URL && process.env.FRONTEND_URL.includes('localhost');

  // Consider it local if any of these conditions are true
  const isLocal = isLocalPort || isDevelopment || (noHerokuApp && hasLocalEnv);

  if (isLocal) {
    // For local development - always use localhost
    return 'http://localhost:5001';
  }

  // For Heroku production environment
  return 'https://hoa-management-app-dad2f9d126ae.herokuapp.com';
};

// Upload document with enhanced features
const uploadDocument = async (req, res) => {
  try {
    console.log('Enhanced upload document request:', {
      user: req.user ? req.user._id : 'anonymous',
      userRole: req.user ? req.user.role : 'anonymous',
      userHoaId: req.user ? req.user.hoaId : null,
      userCommunityId: req.user ? req.user.communityId : null,
      userHoaCommunityCode: req.user ? req.user.hoaCommunityCode : null,
      file: req.file ? req.file.originalname : 'no file',
      body: req.body
    });

    // If user doesn't have hoaId in JWT token, fetch it from database
    if (req.user && !req.user.hoaId && req.user._id) {
      console.log('User missing hoaId in JWT token, fetching from database...');
      const User = require('../../models/user');
      const fullUser = await User.findById(req.user._id);
      if (fullUser) {
        req.user.hoaId = fullUser.hoaId;
        req.user.communityId = fullUser.communityId;
        req.user.hoaCommunityCode = fullUser.hoaCommunityCode;
        console.log('Updated user data from database:', {
          hoaId: req.user.hoaId,
          communityId: req.user.communityId,
          hoaCommunityCode: req.user.hoaCommunityCode
        });
      }
    }

    if (!req.file) {
      return res.status(400).json({ 
        success: false, 
        message: 'No file uploaded' 
      });
    }

    // Ensure HOA association
    let hoaId = req.body.hoaId || (req.user ? req.user.hoaId : null);

    // Handle case where hoaId is an array (take first element)
    if (Array.isArray(hoaId)) {
      hoaId = hoaId[0];
    }

    // If still no hoaId, try to find it using hoaCommunityCode
    if (!hoaId && req.user && req.user.hoaCommunityCode && req.user.hoaCommunityCode.length > 0) {
      const HOA = require('../../models/hoa');
      const hoa = await HOA.findOne({ hoaCommunityCode: req.user.hoaCommunityCode[0] });
      if (hoa) {
        hoaId = hoa._id;
        console.log('Found HOA by community code:', {
          communityCode: req.user.hoaCommunityCode[0],
          hoaId: hoaId
        });
      }
    }

    if (!hoaId) {
      console.error('HOA association failed:', {
        userHoaId: req.user ? req.user.hoaId : null,
        userHoaCommunityCode: req.user ? req.user.hoaCommunityCode : null,
        bodyHoaId: req.body.hoaId
      });
      return res.status(400).json({
        success: false,
        message: 'HOA association is required for document uploads'
      });
    }

    // Extract communityId from user data or request body
    let communityId = req.body.communityId;

    // If no communityId provided, try to get from user
    if (!communityId && req.user && req.user.communityId) {
      communityId = Array.isArray(req.user.communityId) ? req.user.communityId[0] : req.user.communityId;
    }

    console.log('Document upload associations:', {
      hoaId: hoaId,
      communityId: communityId,
      uploadedBy: req.user ? req.user._id : null,
      userRole: req.user ? req.user.role : null
    });

    // Validate user can upload to this HOA
    if (req.user && req.user.role !== 'company_admin') {
      // Handle case where user.hoaId might be an array (users can belong to multiple HOAs)
      const userHoaIds = Array.isArray(req.user.hoaId) ? req.user.hoaId : [req.user.hoaId];
      const targetHoaIdString = hoaId.toString();

      const hasHoaAccess = userHoaIds.some(userHoaId =>
        userHoaId && userHoaId.toString() === targetHoaIdString
      );

      if (!hasHoaAccess) {
        return res.status(403).json({
          success: false,
          message: 'You can only upload documents to your own HOA'
        });
      }
    }

    // Create document with enhanced schema
    const documentData = {
      title: req.body.title || req.file.originalname,
      description: req.body.description || '',
      category: req.body.category || 'miscellaneous',

      // Critical database associations
      hoaId: hoaId,
      communityId: communityId,
      uploadedBy: req.user ? req.user._id : null,

      isPublic: req.body.isPublic === 'true' || req.body.isPublic === true,
      
      // New standardized file schema
      file: {
        s3Key: req.file.key,
        s3Bucket: req.file.bucket,
        originalName: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        encoding: req.file.encoding,
        uploadedAt: new Date()
      },
      
      // Legacy fields for backward compatibility
      fileName: req.file.originalname,
      fileType: req.file.mimetype,
      fileSize: req.file.size,
      s3Key: req.file.key,
      s3Bucket: req.file.bucket,
      
      // Enhanced metadata
      metadata: {
        virusScanStatus: 'pending',
        processingStatus: 'completed',
        tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : []
      },
      
      // Version information
      version: {
        version: 1,
        changeLog: 'Initial upload',
        changedBy: req.user ? req.user._id : null,
        changedAt: new Date()
      }
    };

    console.log('Creating document with data:', JSON.stringify(documentData, null, 2));

    const document = new Document(documentData);
    console.log('Document created, attempting to save...');

    try {
      await document.save();
      console.log('Document saved successfully to database');

      // Verify the document was actually saved by querying it back
      const savedDoc = await Document.findById(document._id);
      if (savedDoc) {
        console.log('Document verification: Found in database with ID:', savedDoc._id);
      } else {
        console.error('Document verification: NOT found in database after save!');
      }
    } catch (saveError) {
      console.error('Error saving document to database:', saveError);
      throw saveError;
    }

    console.log('Document uploaded successfully with all associations:', {
      documentId: document._id,
      title: document.title,
      category: document.category,
      s3Key: req.file.key,
      s3Bucket: req.file.bucket,
      // Database associations verification
      hoaId: document.hoaId,
      communityId: document.communityId,
      uploadedBy: document.uploadedBy,
      // File metadata
      fileName: document.file.originalName,
      fileSize: document.file.size,
      mimetype: document.file.mimetype,
      isPublic: document.isPublic,
      status: document.status
    });

    res.status(201).json({
      success: true,
      message: 'Document uploaded successfully',
      document: {
        _id: document._id,
        title: document.title,
        fileName: document.file.originalName,
        fileSize: document.formattedFileSize,
        category: document.category,
        uploadedAt: document.createdAt,
        isPublic: document.isPublic
      }
    });

  } catch (error) {
    console.error('Enhanced upload error:', error);
    
    // Clean up uploaded file if document creation failed
    if (req.file && req.file.key) {
      try {
        await deleteFile(req.file.key);
        console.log('Cleaned up uploaded file after error:', req.file.key);
      } catch (cleanupError) {
        console.error('Error cleaning up file:', cleanupError);
      }
    }

    res.status(500).json({
      success: false,
      message: 'Error uploading document',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Get documents with enhanced access control
const getDocuments = async (req, res) => {
  try {
    const { page = 1, limit = 10, category, search, status = 'active' } = req.query;
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // If user doesn't have hoaId in JWT token, fetch it from database
    if (user && !user.hoaId && user._id) {
      console.log('getDocuments: User missing hoaId in JWT token, fetching from database...');
      const User = require('../../models/user');
      const fullUser = await User.findById(user._id);
      if (fullUser) {
        user.hoaId = fullUser.hoaId;
        user.communityId = fullUser.communityId;
        user.hoaCommunityCode = fullUser.hoaCommunityCode;
        console.log('getDocuments: Updated user data from database:', {
          hoaId: user.hoaId,
          communityId: user.communityId,
          hoaCommunityCode: user.hoaCommunityCode
        });
      }
    }

    // Build filters
    const filters = { status };
    if (category && category !== 'all') {
      filters.category = category;
    }

    // Add search functionality
    if (search) {
      filters.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { 'file.originalName': { $regex: search, $options: 'i' } },
        { fileName: { $regex: search, $options: 'i' } }
      ];
    }

    let documents;
    let totalDocuments;

    if (user.role === 'company_admin') {
      // Company admins can see all documents
      documents = await Document.findAllWithAccess(user._id, user.role, filters)
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);
      
      totalDocuments = await Document.countDocuments(filters);
    } else {
      // HOA admins and users see documents in their HOA
      // Handle case where hoaId is an array (use all HOAs the user has access to)
      const userHoaIds = Array.isArray(user.hoaId) ? user.hoaId : [user.hoaId];

      console.log('Fetching documents for HOA admin:', {
        userHoaIds,
        userId: user._id,
        userRole: user.role,
        filters,
        page,
        limit
      });

      // For users with multiple HOAs, we need to fetch documents from all their HOAs
      if (userHoaIds.length === 1) {
        documents = await Document.findByHOAWithAccess(userHoaIds[0], user._id, user.role, filters)
          .sort({ createdAt: -1 })
          .limit(limit * 1)
          .skip((page - 1) * limit);
      } else {
        // Handle multiple HOAs - fetch documents from all user's HOAs
        const multiHoaFilters = { ...filters, hoaId: { $in: userHoaIds } };

        // For HOA admins with multiple HOAs, use a direct query since findAllWithAccess expects company_admin
        documents = await Document.find({ status: 'active', ...multiHoaFilters })
          .populate(['uploadedBy', 'hoaId', 'communityId'])
          .sort({ createdAt: -1 })
          .limit(limit * 1)
          .skip((page - 1) * limit);
      }

      console.log(`Found ${documents.length} documents for user`);

      // Count documents with proper HOA filtering
      let countFilters = { ...filters };
      if (userHoaIds.length === 1) {
        countFilters.hoaId = userHoaIds[0];
      } else {
        countFilters.hoaId = { $in: userHoaIds };
      }

      if (user.role !== 'admin') {
        countFilters.isPublic = true;
      }
      totalDocuments = await Document.countDocuments(countFilters);

      console.log(`Total documents count: ${totalDocuments}`);
    }

    // Format response with download URLs
    const formattedDocuments = documents.map(doc => {
      // Generate download token and URL
      const token = generateDownloadToken(doc._id, user._id);
      const apiUrl = getApiUrl();
      // API URL detection working correctly
      const downloadUrl = `${apiUrl}/api/documents/download/${doc._id}?token=${token}`;

      // File size information ready for frontend

      return {
        _id: doc._id,
        title: doc.title,
        description: doc.description,
        fileName: doc.file?.originalName || doc.fileName,
        fileSize: doc.currentFileSize, // Send raw number, let frontend format it
        fileType: doc.file?.mimetype || doc.fileType,
        category: doc.category,
        isPublic: doc.isPublic,
        status: doc.status,
        uploadedBy: doc.uploadedBy,
        uploadedAt: doc.createdAt,
        downloadCount: doc.downloadCount,
        hoaId: doc.hoaId,
        communityId: doc.communityId,
        downloadUrl: downloadUrl
      };
    });

    res.json({
      success: true,
      documents: formattedDocuments,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalDocuments / limit),
        totalDocuments,
        hasNextPage: page < Math.ceil(totalDocuments / limit),
        hasPrevPage: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching documents:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching documents',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Download document with token verification
const downloadDocument = async (req, res) => {
  try {
    const { id } = req.params;
    let userId = null;

    // Download request initiated

    // Verify download token from query parameter
    if (req.query.token) {
      try {
        const decoded = jwt.verify(req.query.token, process.env.JWT_SECRET);
        // Check if the document ID in the token matches the requested document ID
        if (decoded.docId !== id) {
          return res.status(403).json({ message: 'Token does not match requested document' });
        }

        userId = decoded.userId;
      } catch (tokenErr) {
        return res.status(401).json({ message: 'Invalid or expired download token' });
      }
    } else {
      return res.status(401).json({ message: 'Download token required' });
    }

    // Find the document
    const document = await Document.findById(id);
    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }

    // Get S3 key (new or legacy format)
    const s3Key = document.file?.s3Key || document.s3Key;
    if (!s3Key) {
      return res.status(400).json({ message: 'Document not available for download' });
    }

    // Get the original filename for proper download
    const fileName = document.file?.originalName || document.fileName || 'download';

    // Generate signed URL for secure download with forced download headers
    const signedUrl = await generateSignedUrl(s3Key, 3600, fileName, true); // 1 hour expiration, force download

    // Track download
    await document.trackDownload(userId);

    // Set aggressive download headers
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Redirect to signed URL for download
    return res.redirect(signedUrl);

  } catch (error) {
    console.error('Error in downloadDocument:', error);
    res.status(500).json({
      message: 'Error downloading document',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Generate secure download URL
const generateDownloadUrl = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const document = await Document.findById(id);
    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    // Check access permissions
    if (!document.canUserAccess(user._id, user.role, user.hoaId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Get S3 key (new or legacy)
    const s3Key = document.file?.s3Key || document.s3Key;
    if (!s3Key) {
      return res.status(400).json({
        success: false,
        message: 'Document not available for download'
      });
    }

    // Generate signed URL
    const signedUrl = await generateSignedUrl(s3Key, 3600); // 1 hour expiration

    // Track download
    await document.trackDownload(user._id);

    res.json({
      success: true,
      downloadUrl: signedUrl,
      fileName: document.file?.originalName || document.fileName,
      expiresIn: 3600
    });

  } catch (error) {
    console.error('Error generating download URL:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating download URL',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Delete document with S3 cleanup
const deleteDocument = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // If user doesn't have hoaId in JWT token, fetch it from database
    if (user && !user.hoaId && user._id) {
      console.log('deleteDocument: User missing hoaId in JWT token, fetching from database...');
      const User = require('../../models/user');
      const fullUser = await User.findById(user._id);
      if (fullUser) {
        user.hoaId = fullUser.hoaId;
        user.communityId = fullUser.communityId;
        user.hoaCommunityCode = fullUser.hoaCommunityCode;
        console.log('deleteDocument: Updated user data from database:', {
          hoaId: user.hoaId,
          communityId: user.communityId,
          hoaCommunityCode: user.hoaCommunityCode
        });
      }
    }

    const document = await Document.findById(id);
    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    // Additional safety check for document status
    if (document.status === 'deleted') {
      return res.status(404).json({
        success: false,
        message: 'Document already deleted'
      });
    }

    // Use the established access control pattern from other operations
    // Check if user can access this document (same as download/view operations)
    if (!document.canUserAccess(user._id, user.role, user.hoaId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Additional permission check for deletion (only admins and document owner can delete)
    const isCompanyAdmin = user.role === 'company_admin';
    const isHoaAdmin = user.role === 'admin';
    const isDocumentOwner = document.uploadedBy && document.uploadedBy.toString() === user._id.toString();

    if (!isCompanyAdmin && !isHoaAdmin && !isDocumentOwner) {
      return res.status(403).json({
        success: false,
        message: 'Permission denied - insufficient privileges to delete this document'
      });
    }

    // Note: HOA access is already checked by canUserAccess() above
    // No need for duplicate HOA access check here

    // Get S3 key for cleanup
    const s3Key = document.file?.s3Key || document.s3Key;

    // Soft delete or hard delete based on query parameter
    const hardDelete = req.query.hard === 'true';

    if (hardDelete) {
      // Hard delete: remove from database and S3
      await Document.findByIdAndDelete(id);

      // Clean up S3 file
      if (s3Key) {
        try {
          await deleteFile(s3Key);
          console.log('File deleted from S3:', s3Key);
        } catch (s3Error) {
          console.error('Error deleting file from S3:', s3Error);
          // Continue with response even if S3 deletion fails
        }
      }

      res.json({
        success: true,
        message: 'Document permanently deleted'
      });
    } else {
      // Soft delete: mark as deleted
      await document.softDelete(user._id);

      res.json({
        success: true,
        message: 'Document deleted successfully'
      });
    }

  } catch (error) {
    console.error('Error deleting document:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting document',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Update document metadata
const updateDocument = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;
    const updates = req.body;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const document = await Document.findById(id);
    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    // Use the established access control pattern
    if (!document.canUserAccess(user._id, user.role, user.hoaId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Additional permission check for updates (only admins and document owner can update)
    const isCompanyAdmin = user.role === 'company_admin';
    const isHoaAdmin = user.role === 'admin';
    const isDocumentOwner = document.uploadedBy && document.uploadedBy.toString() === user._id.toString();

    if (!isCompanyAdmin && !isHoaAdmin && !isDocumentOwner) {
      return res.status(403).json({
        success: false,
        message: 'Permission denied - insufficient privileges to update this document'
      });
    }

    // Update allowed fields
    const allowedUpdates = ['title', 'description', 'category', 'isPublic'];
    const updateData = {};

    allowedUpdates.forEach(field => {
      if (updates[field] !== undefined) {
        updateData[field] = updates[field];
      }
    });

    // Update tags if provided
    if (updates.tags) {
      updateData['metadata.tags'] = Array.isArray(updates.tags)
        ? updates.tags
        : updates.tags.split(',').map(tag => tag.trim());
    }

    // Update version info
    updateData['version.changeLog'] = updates.changeLog || 'Document updated';
    updateData['version.changedBy'] = user._id;
    updateData['version.changedAt'] = new Date();

    const updatedDocument = await Document.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    res.json({
      success: true,
      message: 'Document updated successfully',
      document: {
        _id: updatedDocument._id,
        title: updatedDocument.title,
        description: updatedDocument.description,
        category: updatedDocument.category,
        isPublic: updatedDocument.isPublic,
        version: updatedDocument.version.version
      }
    });

  } catch (error) {
    console.error('Error updating document:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating document',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Get document statistics
const getDocumentStats = async (req, res) => {
  try {
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    let matchStage = { status: 'active' };

    // Apply HOA filtering for non-company admins
    if (user.role !== 'company_admin') {
      // Handle case where user.hoaId might be an array
      const userHoaIds = Array.isArray(user.hoaId) ? user.hoaId : [user.hoaId];

      if (userHoaIds.length === 1) {
        matchStage.hoaId = userHoaIds[0];
      } else {
        matchStage.hoaId = { $in: userHoaIds };
      }

      // Regular users can only see public documents
      if (user.role !== 'admin') {
        matchStage.isPublic = true;
      }
    }

    const stats = await Document.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalDocuments: { $sum: 1 },
          totalSize: { $sum: { $ifNull: ['$file.size', '$fileSize'] } },
          totalDownloads: { $sum: '$downloadCount' },
          categoryCounts: {
            $push: '$category'
          }
        }
      },
      {
        $project: {
          _id: 0,
          totalDocuments: 1,
          totalSize: 1,
          totalDownloads: 1,
          categoryCounts: 1
        }
      }
    ]);

    // Count by category
    const categoryStats = await Document.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalSize: { $sum: { $ifNull: ['$file.size', '$fileSize'] } }
        }
      }
    ]);

    const result = stats[0] || {
      totalDocuments: 0,
      totalSize: 0,
      totalDownloads: 0
    };

    res.json({
      success: true,
      stats: {
        ...result,
        categories: categoryStats,
        formattedTotalSize: formatFileSize(result.totalSize || 0)
      }
    });

  } catch (error) {
    console.error('Error getting document stats:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting document statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Update document category (specific endpoint for category updates)
const updateDocumentCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { category } = req.body;
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // If user doesn't have hoaId in JWT token, fetch it from database
    if (user && !user.hoaId && user._id) {
      console.log('updateDocumentCategory: User missing hoaId in JWT token, fetching from database...');
      const User = require('../../models/user');
      const fullUser = await User.findById(user._id);
      if (fullUser) {
        user.hoaId = fullUser.hoaId;
        user.communityId = fullUser.communityId;
        user.hoaCommunityCode = fullUser.hoaCommunityCode;
        console.log('updateDocumentCategory: Updated user data from database:', {
          hoaId: user.hoaId,
          communityId: user.communityId,
          hoaCommunityCode: user.hoaCommunityCode
        });
      }
    }

    // Validate category
    const validCategories = ['financial', 'property_information', 'miscellaneous', 'meetings', 'rules', 'bids'];
    if (!category || !validCategories.includes(category)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid category',
        validCategories
      });
    }

    const document = await Document.findById(id);
    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    // Use the established access control pattern
    if (!document.canUserAccess(user._id, user.role, user.hoaId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Additional permission check for updates (only admins and document owner can update)
    const isCompanyAdmin = user.role === 'company_admin';
    const isHoaAdmin = user.role === 'admin';
    const isDocumentOwner = document.uploadedBy && document.uploadedBy.toString() === user._id.toString();

    if (!isCompanyAdmin && !isHoaAdmin && !isDocumentOwner) {
      return res.status(403).json({
        success: false,
        message: 'Permission denied - insufficient privileges to update this document'
      });
    }

    // Update the category
    document.category = category;

    // Update version info
    document.version.changeLog = `Category updated to ${category}`;
    document.version.changedBy = user._id;
    document.version.changedAt = new Date();

    await document.save();

    res.json({
      success: true,
      message: 'Document category updated successfully',
      document: {
        _id: document._id,
        title: document.title,
        description: document.description,
        category: document.category,
        isPublic: document.isPublic,
        version: document.version.version
      }
    });

  } catch (error) {
    console.error('Error updating document category:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating document category',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

module.exports = {
  uploadDocument,
  getDocuments,
  downloadDocument,
  generateDownloadUrl,
  deleteDocument,
  updateDocument,
  updateDocumentCategory,
  getDocumentStats
};

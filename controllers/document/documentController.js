/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// controllers/documentController.js
const Document = require('../models/document');
const fs = require('fs');
const path = require('path');

// Ensure documents directory exists
const documentsDir = path.join(__dirname, '../uploads/documents');
if (!fs.existsSync(documentsDir)) {
  try {
    fs.mkdirSync(documentsDir, { recursive: true });
    console.log('Documents directory created at:', documentsDir);
  } catch (err) {
    console.error('Error creating documents directory:', err);
  }
}

// Generate a secure download token
const generateDownloadToken = (docId, userId) => {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    { docId, userId },
    process.env.JWT_SECRET,
    { expiresIn: '24h' } // Token expires in 24 hours to give users more time to download
  );
};

// Get the appropriate API URL based on environment
const getApiUrl = () => {
  // For Heroku or other production environments
  if (process.env.NODE_ENV === 'production') {
    // Always use the correct Heroku URL
    return 'https://hoa-management-app-dad2f9d126ae.herokuapp.com';
  }
  // For local development
  return process.env.API_URL || 'http://localhost:5001';
};

// Get all documents
exports.getAllDocuments = async (req, res) => {
  try {
    // Build query based on user role, HOA, and community
    let query = { isPublic: true };

    // If user is not a company_admin, filter by HOA and community
    if (req.user.role !== 'company_admin') {
      // Get the user's HOA ID
      let hoaId = null;
      if (req.user.hoaId) {
        hoaId = req.user.hoaId;
      } else if (req.user.hoaCommunityCode) {
        // Try to find HOA by community code
        const HOA = require('../models/hoa');
        const hoa = await HOA.findOne({ hoaCommunityCode: req.user.hoaCommunityCode });
        if (hoa) {
          hoaId = hoa._id;
        }
      }

      // If we have an HOA ID, filter by it
      if (hoaId) {
        // Create a query that includes HOA-wide documents and community-specific documents
        const orConditions = [{ hoaId: hoaId, communityId: { $exists: false } }];

        // If user has a communityId, include documents for that community
        if (req.user.communityId) {
          orConditions.push({ communityId: req.user.communityId });
        }

        query.$or = orConditions;
      }
    }

    console.log('Document query:', query);

    const documents = await Document.find(query)
      .sort({ uploadedAt: -1 })
      .populate('uploadedBy', 'username email');

    // Add download URLs to documents with token
    const docsWithUrls = documents.map(doc => {
      const docObj = doc.toObject();
      const token = generateDownloadToken(doc._id, req.user._id);
      const apiUrl = getApiUrl();
      docObj.downloadUrl = `${apiUrl}/api/documents/download/${doc._id}?token=${token}`;
      console.log(`Generated download URL: ${docObj.downloadUrl}`);
      return docObj;
    });

    res.json(docsWithUrls);
  } catch (err) {
    console.error('Error fetching documents:', err);
    res.status(500).json({ message: 'Error fetching documents' });
  }
};

// Get documents by category
exports.getDocumentsByCategory = async (req, res) => {
  try {
    const { category } = req.params;

    // Build query based on user role, HOA, and community
    let query = {
      category,
      isPublic: true
    };

    // If user is not a company_admin, filter by HOA and community
    if (req.user.role !== 'company_admin') {
      // Get the user's HOA ID
      let hoaId = null;
      if (req.user.hoaId) {
        hoaId = req.user.hoaId;
      } else if (req.user.hoaCommunityCode) {
        // Try to find HOA by community code
        const HOA = require('../models/hoa');
        const hoa = await HOA.findOne({ hoaCommunityCode: req.user.hoaCommunityCode });
        if (hoa) {
          hoaId = hoa._id;
        }
      }

      // If we have an HOA ID, filter by it
      if (hoaId) {
        // Create a query that includes HOA-wide documents and community-specific documents
        const orConditions = [{ hoaId: hoaId, communityId: { $exists: false } }];

        // If user has a communityId, include documents for that community
        if (req.user.communityId) {
          orConditions.push({ communityId: req.user.communityId });
        }

        query.$or = orConditions;
      }
    }

    console.log('Document category query:', query);

    const documents = await Document.find(query)
      .sort({ uploadedAt: -1 })
      .populate('uploadedBy', 'username email');

    // Add download URLs to documents with token
    const docsWithUrls = documents.map(doc => {
      const docObj = doc.toObject();
      const token = generateDownloadToken(doc._id, req.user._id);
      const apiUrl = getApiUrl();
      docObj.downloadUrl = `${apiUrl}/api/documents/download/${doc._id}?token=${token}`;
      console.log(`Generated category download URL: ${docObj.downloadUrl}`);
      return docObj;
    });

    res.json(docsWithUrls);
  } catch (err) {
    console.error('Error fetching documents by category:', err);
    res.status(500).json({ message: 'Error fetching documents' });
  }
};

// Upload a document (admin and company_admin only)
exports.uploadDocument = async (req, res) => {
  try {
    console.log('Document upload request received');

    // Check if user is admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      console.error('Non-admin user attempted to upload document:', req.user._id);
      return res.status(403).json({ message: 'Only administrators can upload documents' });
    }

    if (!req.file) {
      console.error('No file in upload request');
      return res.status(400).json({ message: 'No file uploaded' });
    }

    console.log('File received and uploaded to S3:', {
      key: req.file.key, // S3 key
      bucket: req.file.bucket, // S3 bucket
      location: req.file.location, // S3 URL
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size
    });

    const { title, description, category, isPublic } = req.body;

    // With S3, the file is already uploaded and we have the key
    const s3Key = req.file.key;
    const s3Location = req.file.location;

    console.log('File successfully uploaded to S3 with key:', s3Key);

    // Get the HOA ID and Community ID from the user
    let hoaId = null;
    let communityId = null;

    // Get HOA ID
    if (req.user.hoaId) {
      hoaId = req.user.hoaId;
    } else if (req.user.hoaCommunityCode) {
      // Try to find HOA by community code
      const HOA = require('../models/hoa');
      const hoa = await HOA.findOne({ hoaCommunityCode: req.user.hoaCommunityCode });
      if (hoa) {
        hoaId = hoa._id;
      }
    }

    // Get Community ID
    if (req.user.communityId) {
      communityId = req.user.communityId;
    } else if (req.body.communityId) {
      // Allow specifying a community ID in the request body
      communityId = req.body.communityId;
    }

    const newDocument = new Document({
      title: title || req.file.originalname,
      description: description || '',
      fileName: s3Key, // Store S3 key instead of local filename
      s3Key: s3Key, // Add explicit S3 key field for clarity
      s3Location: s3Location, // Store S3 URL
      fileType: req.file.mimetype,
      fileSize: req.file.size,
      category: category || 'miscellaneous',
      uploadedBy: req.user._id,
      isPublic: isPublic === 'false' ? false : true,
      hoaId: hoaId, // Associate document with HOA
      communityId: communityId // Associate document with Community if available
    });

    await newDocument.save();
    console.log('Document record saved to database with ID:', newDocument._id);

    // Generate token for download URL
    const token = generateDownloadToken(newDocument._id, req.user._id);
    const apiUrl = getApiUrl();
    const downloadUrl = `${apiUrl}/api/documents/download/${newDocument._id}?token=${token}`;

    console.log('Download URL generated:', downloadUrl);

    res.status(201).json({
      message: 'Document uploaded successfully',
      document: {
        ...newDocument.toObject(),
        downloadUrl: downloadUrl
      }
    });
  } catch (err) {
    console.error('Error uploading document:', err);
    res.status(500).json({ message: 'Error uploading document', error: err.message });
  }
};

// Download a document
exports.downloadDocument = async (req, res) => {
  try {
    console.log('Download request received for document ID:', req.params.id);

    // Get user ID from either token or auth middleware
    let userId = null;
    let isAdmin = false;

    // Check for token in query or auth header
    if (req.query.token) {
      // Using download token
      const jwt = require('jsonwebtoken');
      try {
        const decoded = jwt.verify(req.query.token, process.env.JWT_SECRET);
        userId = decoded.userId;
        console.log('Download token verified successfully for user:', userId);

        // Check if the document ID in the token matches the requested document ID
        if (decoded.docId !== req.params.id) {
          console.error('Token docId does not match requested document ID');
          return res.status(403).json({ message: 'Token does not match requested document' });
        }
      } catch (tokenErr) {
        console.error('Token verification failed:', tokenErr);
        return res.status(401).json({ message: 'Invalid or expired token' });
      }
    } else if (req.headers.authorization) {
      // Using standard auth header
      const authHeader = req.headers.authorization;
      if (authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const jwt = require('jsonwebtoken');
        try {
          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          // Check if the token has user or user.id format
          if (decoded.user && decoded.user._id) {
            userId = decoded.user._id;
          } else if (decoded.id) {
            userId = decoded.id;
          } else if (decoded._id) {
            userId = decoded._id;
          }

          console.log('Auth token verified for user:', userId);

          if (!userId) {
            console.error('Could not extract user ID from token:', decoded);
            return res.status(401).json({ message: 'Invalid token format' });
          }
        } catch (tokenErr) {
          console.error('Auth token verification failed:', tokenErr);
          return res.status(401).json({ message: 'Invalid or expired token' });
        }
      } else {
        console.error('Invalid authorization header format');
        return res.status(401).json({ message: 'Invalid authorization header format' });
      }
    } else if (req.user && req.user._id) {
      // User is already authenticated via middleware
      userId = req.user._id;
      isAdmin = req.user.role === 'admin';
      console.log('User already authenticated via middleware:', userId, 'isAdmin:', isAdmin);
    } else {
      console.error('No authentication provided for document download');
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Check if user exists and get role (if not already determined)
    if (userId && !isAdmin) {
      // If we already know the user is admin from middleware, skip this check
      if (req.user && req.user._id.toString() === userId.toString() && req.user.role === 'admin') {
        isAdmin = true;
        console.log('User is admin (from middleware)');
      } else {
        // Otherwise look up the user
        const User = require('../models/user');
        try {
          const user = await User.findById(userId);
          if (user) {
            isAdmin = user.role === 'admin';
            console.log('User found:', user.username || user.email, 'isAdmin:', isAdmin);
          } else {
            console.error('User not found with ID:', userId);
            return res.status(401).json({ message: 'User not found' });
          }
        } catch (err) {
          console.error('Error finding user:', err);
          return res.status(500).json({ message: 'Error finding user' });
        }
      }
    } else if (!userId) {
      console.error('No user ID available');
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Find the document
    const document = await Document.findById(req.params.id);
    if (!document) {
      console.error('Document not found with ID:', req.params.id);
      return res.status(404).json({ message: 'Document not found' });
    }

    console.log('Document found:', document.title);

    // Check if document is public or user is admin
    if (!document.isPublic && !isAdmin) {
      console.error('Access denied: non-public document requested by non-admin user');
      return res.status(403).json({ message: 'Access denied' });
    }

    // Handle S3-based file serving
    const { generateSignedUrl } = require('../config/s3Config');

    // Use S3 key if available, otherwise fall back to fileName for backward compatibility
    const s3Key = document.s3Key || document.fileName;
    console.log('Attempting to serve file from S3 with key:', s3Key);

    try {
      // Generate signed URL for secure download
      const signedUrl = await generateSignedUrl(s3Key, 3600); // 1 hour expiration
      console.log('Generated signed URL for document download');

      // Redirect to signed URL for download
      return res.redirect(signedUrl);

    } catch (s3Error) {
      console.error('Error accessing file from S3:', s3Error);

      // Fall back to local file system for backward compatibility during migration
      const filePath = path.join(__dirname, '../uploads/documents', document.fileName);
      console.log('Falling back to local file system:', filePath);

      if (!fs.existsSync(filePath)) {
        console.error('File not found in both S3 and local storage:', filePath);

        // Check if uploads directory exists
        const uploadsDir = path.join(__dirname, '../uploads');
        const documentsDir = path.join(uploadsDir, 'documents');

        if (!fs.existsSync(uploadsDir)) {
          console.error('Uploads directory does not exist');
          fs.mkdirSync(uploadsDir, { recursive: true, mode: 0o777 });
          console.log('Created uploads directory');
        }

        if (!fs.existsSync(documentsDir)) {
          console.error('Documents directory does not exist');
          fs.mkdirSync(documentsDir, { recursive: true, mode: 0o777 });
          console.log('Created documents directory');
        }

        // Check if the file exists in the root uploads directory (for backward compatibility)
        const altFilePath = path.join(__dirname, '../uploads', document.fileName);
      console.log('Checking alternative path:', altFilePath);

      if (fs.existsSync(altFilePath)) {
        console.log('File found at alternative path, serving from there');

        // Set appropriate headers
        res.setHeader('Content-Type', document.fileType);
        res.setHeader('Content-Disposition', `attachment; filename="${document.title || document.fileName}"`);

        // Get file stats
        const stats = fs.statSync(altFilePath);
        res.setHeader('Content-Length', stats.size);

        // Add CORS headers with improved origin handling
        const origin = req.headers.origin;
        const allowedOrigins = process.env.NODE_ENV === 'production'
          ? [
              'https://hoa-front.vercel.app',
              'https://www.hoa-front.vercel.app',
              'https://street-harmony.vercel.app',
              'https://www.street-harmony.vercel.app',
              'https://hoa-front-git-main-pelicanapps-projects.vercel.app',
              'https://hoa-front-pelicanapps-projects.vercel.app',
              'https://hoa-management-app-dad2f9d126ae.herokuapp.com',
              'https://hoa-management-app.herokuapp.com',
              'https://www.hoaflo.com',
              'https://hoaflo.com',
              'https://hoa-app-git-cloudstorage-pelicanapps-projects.vercel.app'
            ]
          : [
              'http://localhost:8080',
              'http://127.0.0.1:8080',
              'http://localhost:8082',
              'http://127.0.0.1:8082',
              'http://localhost:3000',
              'http://localhost:5173'
            ];

        // Allow requests with no origin (like mobile apps, curl requests, etc)
        if (!origin) {
          console.log('Download request with no origin allowed');
        } else if (allowedOrigins.includes(origin)) {
          // Origin is in the allowed list
          console.log('Download origin allowed:', origin);
          res.setHeader('Access-Control-Allow-Origin', origin);
        } else if (process.env.NODE_ENV !== 'production') {
          // In development, allow all origins
          console.log('Development mode: allowing all download origins');
          res.setHeader('Access-Control-Allow-Origin', origin);
        } else {
          // In production, use the first allowed origin as fallback
          console.log('Download origin not allowed, using fallback:', origin);
          res.setHeader('Access-Control-Allow-Origin', allowedOrigins[0]);
        }

        // Add Vary header to tell browsers that the response varies based on Origin
        res.setHeader('Vary', 'Origin');
        res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Origin, X-Requested-With, X-CSRF-Token');
        res.setHeader('Access-Control-Allow-Credentials', 'true');
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition, Content-Length');

        // Stream the file from alternative location
        const fileStream = fs.createReadStream(altFilePath);
        fileStream.on('error', (err) => {
          console.error('Error streaming file from alternative path:', err);
          if (!res.headersSent) {
            res.status(500).json({ message: 'Error streaming file' });
          }
        });

        return fileStream.pipe(res);
      }

        return res.status(404).json({
          message: 'File not found on server',
          details: {
            documentId: document._id,
            fileName: document.fileName,
            s3Key: s3Key,
            s3Error: s3Error.message
          }
        });
      }

      // If local file exists, serve it with proper headers
      const stats = fs.statSync(filePath);
      console.log('Serving local file, size:', stats.size, 'bytes');

      // Set appropriate headers
      res.setHeader('Content-Type', document.fileType);
      res.setHeader('Content-Disposition', `attachment; filename="${document.title || document.fileName}"`);
      res.setHeader('Content-Length', stats.size);

    // Add CORS headers with improved origin handling
    const origin = req.headers.origin;
    const allowedOrigins = process.env.NODE_ENV === 'production'
      ? [
          'https://hoa-front.vercel.app',
          'https://www.hoa-front.vercel.app',
          'https://street-harmony.vercel.app',
          'https://www.street-harmony.vercel.app',
          'https://hoa-front-git-main-pelicanapps-projects.vercel.app',
          'https://hoa-front-pelicanapps-projects.vercel.app',
          'https://hoa-management-app-dad2f9d126ae.herokuapp.com',
          'https://hoa-management-app.herokuapp.com',
          'https://www.hoaflo.com',
          'https://hoaflo.com',
          'https://hoa-app-git-cloudstorage-pelicanapps-projects.vercel.app'
        ]
      : [
          'http://localhost:8080',
          'http://127.0.0.1:8080',
          'http://localhost:8082',
          'http://127.0.0.1:8082',
          'http://localhost:3000',
          'http://localhost:5173'
        ];

    // Allow requests with no origin (like mobile apps, curl requests, etc)
    if (!origin) {
      console.log('Download request with no origin allowed');
    } else if (allowedOrigins.includes(origin)) {
      // Origin is in the allowed list
      console.log('Download origin allowed:', origin);
      res.setHeader('Access-Control-Allow-Origin', origin);
    } else if (process.env.NODE_ENV !== 'production') {
      // In development, allow all origins
      console.log('Development mode: allowing all download origins');
      res.setHeader('Access-Control-Allow-Origin', origin);
    } else {
      // In production, use the first allowed origin as fallback
      console.log('Download origin not allowed, using fallback:', origin);
      res.setHeader('Access-Control-Allow-Origin', allowedOrigins[0]);
    }

    // Add Vary header to tell browsers that the response varies based on Origin
    res.setHeader('Vary', 'Origin');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Origin, X-Requested-With, X-CSRF-Token');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition, Content-Length');

    console.log('Streaming file to client');

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.on('error', (err) => {
      console.error('Error streaming file:', err);
      if (!res.headersSent) {
        res.status(500).json({ message: 'Error streaming file' });
      }
    });

      fileStream.pipe(res);
    } // End of S3 catch block
  } catch (err) {
    console.error('Error downloading document:', err);
    res.status(500).json({
      message: 'Error downloading document',
      error: err.message,
      stack: process.env.NODE_ENV === 'production' ? undefined : err.stack
    });
  }
};

// Update document category (admin and company_admin only)
exports.updateDocumentCategory = async (req, res) => {
  try {
    // Check if user is admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({ message: 'Only administrators can update documents' });
    }

    const { category } = req.body;

    // Validate category
    const validCategories = ['financial', 'property_information', 'miscellaneous', 'meetings', 'rules', 'bids'];
    if (!validCategories.includes(category)) {
      return res.status(400).json({
        message: 'Invalid category',
        validCategories
      });
    }

    // Find and update the document
    const document = await Document.findById(req.params.id);

    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }

    // Update the category
    document.category = category;
    await document.save();

    // Generate token for download URL
    const token = generateDownloadToken(document._id, req.user._id);
    const apiUrl = getApiUrl();
    const downloadUrl = `${apiUrl}/api/documents/download/${document._id}?token=${token}`;

    // Return the updated document
    res.json({
      message: 'Document category updated successfully',
      document: {
        ...document.toObject(),
        downloadUrl: downloadUrl
      }
    });
  } catch (err) {
    console.error('Error updating document category:', err);
    res.status(500).json({ message: 'Error updating document category', error: err.message });
  }
};

// Delete a document (admin and company_admin only)
exports.deleteDocument = async (req, res) => {
  try {
    // Check if user is admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({ message: 'Only administrators can delete documents' });
    }

    const document = await Document.findById(req.params.id);

    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }

    // Delete the file
    const filePath = path.join(__dirname, '../uploads/documents', document.fileName);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Delete the document record
    await Document.findByIdAndDelete(req.params.id);

    res.json({ message: 'Document deleted successfully' });
  } catch (err) {
    console.error('Error deleting document:', err);
    res.status(500).json({ message: 'Error deleting document' });
  }
};

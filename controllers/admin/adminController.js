/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

const User = require('../../models/user');
const HOA = require('../../models/hoa');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { sendEmail } = require('../../services/emailService');
const Community = require('../../models/community');
const fs = require('fs');
const path = require('path');

/**
 * Create a new admin user
 * This endpoint is for super admins to create new HOA admin accounts
 */
exports.createAdmin = async (req, res) => {
  try {
    const { username, email, password, fullName, propertyAddress, hoaId, phoneNumber } = req.body;

    // Validate required fields
    if (!username || !email || !password || !fullName || !propertyAddress) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        details: {
          username: !username ? 'Username is required' : undefined,
          email: !email ? 'Email is required' : undefined,
          password: !password ? 'Password is required' : undefined,
          fullName: !fullName ? 'Full name is required' : undefined,
          propertyAddress: !propertyAddress ? 'Property address is required' : undefined
        }
      });
    }

    // Check if username or email already exists
    const existingUser = await User.findOne({
      $or: [{ username }, { email }]
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User already exists',
        details: {
          username: existingUser.username === username ? 'Username already taken' : undefined,
          email: existingUser.email === email ? 'Email already registered' : undefined
        }
      });
    }

    // If hoaId is provided, verify it exists
    let hoaCommunityCode = null;
    if (hoaId) {
      const hoa = await HOA.findById(hoaId);
      if (!hoa) {
        return res.status(404).json({
          success: false,
          message: 'HOA not found with the provided ID'
        });
      }
      hoaCommunityCode = hoa.hoaCommunityCode;
    }

    // Create the admin user
    const newAdmin = new User({
      username,
      email,
      password, // Will be hashed by the pre-save middleware
      fullName,
      propertyAddress,
      role: 'admin',
      isApproved: true,
      hoaId,
      hoaCommunityCode,
      phoneNumber
    });

    await newAdmin.save();

    // Send welcome email to the new admin

    const emailBodyContent = `
      <p>Hi ${fullName},</p>
      <p>Welcome to Street Harmony HOA Management System! Your account has been created successfully.</p>
      <p>Username: ${username}</p>
      <p>Password: ${password}</p>
      <p>Property Address: ${propertyAddress}</p>
      <p>HOA Community Code: ${hoaCommunityCode}</p>
      <p>Phone Number: ${phoneNumber}</p>
      <p>Best regards,<br>Street Harmony HOA Management System</p>
    `;

    try {
      await sendEmail(email, 'Welcome to Street Harmony HOA Management System', emailBodyContent);
    } catch (emailError) {
      console.error('Error sending admin welcome email:', emailError);
      // Continue even if email fails
    }

    res.status(201).json({
      success: true,
      message: 'Admin user created successfully',
      data: {
        _id: newAdmin._id,
        username: newAdmin.username,
        email: newAdmin.email,
        fullName: newAdmin.fullName,
        role: newAdmin.role,
        hoaId: newAdmin.hoaId,
        hoaCommunityCode: newAdmin.hoaCommunityCode
      }
    });
  } catch (error) {
    console.error('Error creating admin user:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Update an existing admin user
 */
exports.updateAdmin = async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, fullName, propertyAddress, hoaId, phoneNumber, password } = req.body;

    // Validate admin ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid admin ID format'
      });
    }

    // Find the admin user
    const admin = await User.findById(id);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin user not found'
      });
    }

    // Verify the user is an admin
    if (admin.role !== 'admin') {
      return res.status(400).json({
        success: false,
        message: 'User is not an admin'
      });
    }

    // Check if username or email is being changed and if they already exist
    if (username && username !== admin.username) {
      const existingUsername = await User.findOne({ username });
      if (existingUsername) {
        return res.status(409).json({
          success: false,
          message: 'Username already taken'
        });
      }
      admin.username = username;
    }

    if (email && email !== admin.email) {
      const existingEmail = await User.findOne({ email });
      if (existingEmail) {
        return res.status(409).json({
          success: false,
          message: 'Email already registered'
        });
      }
      admin.email = email;
    }

    // Update other fields if provided
    if (fullName) admin.fullName = fullName;
    if (propertyAddress) admin.propertyAddress = propertyAddress;
    if (phoneNumber) admin.phoneNumber = phoneNumber;

    // If hoaId is provided, verify it exists and update
    if (hoaId && hoaId !== admin.hoaId?.toString()) {
      const hoa = await HOA.findById(hoaId);
      if (!hoa) {
        return res.status(404).json({
          success: false,
          message: 'HOA not found with the provided ID'
        });
      }
      admin.hoaId = hoaId;
      admin.hoaCommunityCode = hoa.hoaCommunityCode;
    }

    // Update password if provided
    if (password) {
      const salt = await bcrypt.genSalt(10);
      admin.password = await bcrypt.hash(password, salt);
    }

    await admin.save();

    res.status(200).json({
      success: true,
      message: 'Admin user updated successfully',
      data: {
        _id: admin._id,
        username: admin.username,
        email: admin.email,
        fullName: admin.fullName,
        role: admin.role,
        hoaId: admin.hoaId,
        hoaCommunityCode: admin.hoaCommunityCode
      }
    });
  } catch (error) {
    console.error('Error updating admin user:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Delete an admin user
 */
exports.deleteAdmin = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate admin ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid admin ID format'
      });
    }

    // Find the admin user
    const admin = await User.findById(id);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin user not found'
      });
    }

    // Verify the user is an admin
    if (admin.role !== 'admin') {
      return res.status(400).json({
        success: false,
        message: 'User is not an admin'
      });
    }

    // Delete the admin user
    await User.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: 'Admin user deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting admin user:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get all admin users
 */
exports.getAllAdmins = async (req, res) => {
  try {
    // Build query based on user role
    let query = {};

    // Company admins can see all admin types
    if (req.user.role === 'company_admin') {
      query = { role: { $in: ['admin', 'company_admin'] } };
    }
    // Regular admins can only see other regular admins, not company admins
    else if (req.user.role === 'admin') {
      query = { role: 'admin' };

      // If admin has a specific HOA, only show admins from that HOA
      if (req.user.hoaId) {
        query.hoaId = req.user.hoaId;
      }
    }
    // Members shouldn't be able to access this endpoint, but just in case
    else {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin only.'
      });
    }

    const admins = await User.find(query)
      .select('-password')
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
      .populate('communityId', 'name communityCode streetAddress');

    res.status(200).json({
      success: true,
      count: admins.length,
      data: admins
    });
  } catch (error) {
    console.error('Error fetching admin users:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get pending HOA registrations
 * @route GET /api/admin/hoa/pending
 * @access Private (Company Admin only)
 */
exports.getPendingHOAs = async (req, res) => {
  try {
    const pendingHOAs = await HOA.find({ verificationStatus: 'pending' })
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: pendingHOAs.length,
      data: pendingHOAs
    });
  } catch (error) {
    console.error('Error fetching pending HOAs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get HOA verification documents
 * @route GET /api/admin/hoa/:id/documents
 * @access Private (Company Admin only)
 */
exports.getHOADocuments = async (req, res) => {
  try {
    const hoa = await HOA.findById(req.params.id);

    if (!hoa) {
      return res.status(404).json({
        success: false,
        message: 'HOA not found'
      });
    }

    // Check if documents exist
    const documents = {
      registrationDoc: null,
      einDoc: null
    };

    if (hoa.verificationDocuments?.registrationDoc?.filename) {
      const filePath = path.join(
        __dirname,
        '../uploads/hoa_documents',
        hoa.verificationDocuments.registrationDoc.filename
      );

      if (fs.existsSync(filePath)) {
        documents.registrationDoc = {
          filename: hoa.verificationDocuments.registrationDoc.filename,
          originalName: hoa.verificationDocuments.registrationDoc.originalName,
          mimeType: hoa.verificationDocuments.registrationDoc.mimeType,
          url: `/uploads/hoa_documents/${hoa.verificationDocuments.registrationDoc.filename}`
        };
      }
    }

    if (hoa.verificationDocuments?.einDoc?.filename) {
      const filePath = path.join(
        __dirname,
        '../uploads/hoa_documents',
        hoa.verificationDocuments.einDoc.filename
      );

      if (fs.existsSync(filePath)) {
        documents.einDoc = {
          filename: hoa.verificationDocuments.einDoc.filename,
          originalName: hoa.verificationDocuments.einDoc.originalName,
          mimeType: hoa.verificationDocuments.einDoc.mimeType,
          url: `/uploads/hoa_documents/${hoa.verificationDocuments.einDoc.filename}`
        };
      }
    }

    res.status(200).json({
      success: true,
      data: documents
    });
  } catch (error) {
    console.error('Error fetching HOA documents:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get admin dashboard statistics
 * @route GET /api/admin/stats
 * @access Private (Company Admin only)
 */
exports.getAdminStats = async (req, res) => {
  try {
    // Get counts
    const totalHOAs = await HOA.countDocuments();
    const pendingHOAs = await HOA.countDocuments({ verificationStatus: 'pending' });
    const approvedHOAs = await HOA.countDocuments({ verificationStatus: 'approved' });
    const rejectedHOAs = await HOA.countDocuments({ verificationStatus: 'rejected' });

    const totalCommunities = await Community.countDocuments();

    const totalUsers = await User.countDocuments();
    const pendingUsers = await User.countDocuments({ isApproved: false, denied: false });
    const approvedUsers = await User.countDocuments({ isApproved: true });
    const deniedUsers = await User.countDocuments({ denied: true });

    const adminUsers = await User.countDocuments({ role: 'admin' });
    const memberUsers = await User.countDocuments({ role: 'member' });

    // Get recent HOAs
    const recentHOAs = await HOA.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('hoaCommunityName verificationStatus createdAt');

    // Get recent users
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('username email role isApproved createdAt');

    res.status(200).json({
      success: true,
      data: {
        hoas: {
          total: totalHOAs,
          pending: pendingHOAs,
          approved: approvedHOAs,
          rejected: rejectedHOAs,
          recent: recentHOAs
        },
        communities: {
          total: totalCommunities
        },
        users: {
          total: totalUsers,
          pending: pendingUsers,
          approved: approvedUsers,
          denied: deniedUsers,
          admins: adminUsers,
          members: memberUsers,
          recent: recentUsers
        }
      }
    });
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get user verification documents
 * @route GET /api/admin/user/:id/documents
 * @access Private (Admin, Company Admin)
 */
exports.getUserDocuments = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if documents exist
    const documents = {
      homeOwnership: null,
      utilityBill: null,
      photoId: null,
      // Legacy fields
      licenseFront: null,
      licenseBack: null
    };

    // Check new document format
    if (user.verificationDocuments?.homeOwnership?.filename) {
      const filePath = path.join(
        __dirname,
        '../uploads',
        user.verificationDocuments.homeOwnership.filename
      );

      if (fs.existsSync(filePath)) {
        documents.homeOwnership = {
          filename: user.verificationDocuments.homeOwnership.filename,
          originalName: user.verificationDocuments.homeOwnership.originalName,
          mimeType: user.verificationDocuments.homeOwnership.mimeType,
          url: `/uploads/${user.verificationDocuments.homeOwnership.filename}`
        };
      }
    }

    if (user.verificationDocuments?.utilityBill?.filename) {
      const filePath = path.join(
        __dirname,
        '../uploads',
        user.verificationDocuments.utilityBill.filename
      );

      if (fs.existsSync(filePath)) {
        documents.utilityBill = {
          filename: user.verificationDocuments.utilityBill.filename,
          originalName: user.verificationDocuments.utilityBill.originalName,
          mimeType: user.verificationDocuments.utilityBill.mimeType,
          url: `/uploads/${user.verificationDocuments.utilityBill.filename}`
        };
      }
    }

    if (user.verificationDocuments?.photoId?.filename) {
      const filePath = path.join(
        __dirname,
        '../uploads',
        user.verificationDocuments.photoId.filename
      );

      if (fs.existsSync(filePath)) {
        documents.photoId = {
          filename: user.verificationDocuments.photoId.filename,
          originalName: user.verificationDocuments.photoId.originalName,
          mimeType: user.verificationDocuments.photoId.mimeType,
          url: `/uploads/${user.verificationDocuments.photoId.filename}`
        };
      }
    }

    // Check legacy document format
    if (user.licenseFront) {
      const filePath = path.join(__dirname, '../uploads', user.licenseFront);
      if (fs.existsSync(filePath)) {
        documents.licenseFront = {
          filename: user.licenseFront,
          url: `/uploads/${user.licenseFront}`
        };
      }
    }

    if (user.licenseBack) {
      const filePath = path.join(__dirname, '../uploads', user.licenseBack);
      if (fs.existsSync(filePath)) {
        documents.licenseBack = {
          filename: user.licenseBack,
          url: `/uploads/${user.licenseBack}`
        };
      }
    }

    res.status(200).json({
      success: true,
      data: documents
    });
  } catch (error) {
    console.error('Error fetching user documents:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * HOA Controller
 * Handles HOA-related operations including registration, approval, and management
 */
const HOA = require('../../models/hoa');
const Community = require('../../models/community');
const User = require('../../models/user');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { sendEmail } = require('../../services/emailService');

// Import S3 storage configuration
const { storageConfigs } = require('../../config/s3Config');

// Use S3 storage for HOA documents
const upload = storageConfigs.hoaDocuments;

/**
 * Get all HOAs
 * @route GET /api/hoa
 * @access Private (Company Admin for all HOAs, Admin/Member for their accessible HOAs)
 */
exports.getAllHOAs = async (req, res) => {
  try {
    console.log('🔍 DEBUG: Fetching HOAs for user:', {
      userId: req.user._id,
      role: req.user.role,
      hoaCommunityCode: req.user.hoaCommunityCode
    });

    // Get communityId from query parameter
    const { communityId, status } = req.query;
    console.log('🔍 DEBUG: Query parameters:', { communityId, status });

    // Build base query
    let query = {};

    // Add status filtering if provided
    if (status) {
      const statusMap = {
        'approved': 'approved',
        'rejected': 'rejected',
        'pending': 'pending'
      };

      const verificationStatus = statusMap[status];
      if (verificationStatus) {
        query.verificationStatus = verificationStatus;
      }
    }

    // Role-based filtering
    if (req.user.role === 'company_admin') {
      console.log('🔍 DEBUG: Company admin - showing all HOAs');
      // Company admin can see all HOAs (no additional filtering)
    } else if (req.user.role === 'admin' || req.user.role === 'member') {
      // For admin and member roles, filter based on their accessible HOAs

      if (communityId) {
        console.log('🔍 DEBUG: Community-based filtering for communityId:', communityId);

        // Find the community to get its HOA
        const Community = require('../../models/community');
        const community = await Community.findById(communityId);

        if (community) {
          console.log(`🔍 DEBUG: Found community ${community.name} with HOA ID: ${community.hoaId}`);

          // Verify user has access to this community's HOA
          if (req.user.hoaCommunityCode && req.user.hoaCommunityCode.includes(community.communityCode)) {
            query._id = community.hoaId;
            console.log('🔍 DEBUG: User has access to community, filtering by HOA ID:', community.hoaId);
          } else {
            console.log('🔍 DEBUG: User does not have access to this community');
            return res.status(200).json({
              success: true,
              count: 0,
              data: []
            });
          }
        } else {
          console.log('🔍 DEBUG: Community not found');
          return res.status(200).json({
            success: true,
            count: 0,
            data: []
          });
        }
      } else {
        // No communityId provided - show all HOAs user has access to
        if (req.user.hoaCommunityCode && req.user.hoaCommunityCode.length > 0) {
          console.log('🔍 DEBUG: Filtering by user HOA community codes:', req.user.hoaCommunityCode);
          query.hoaCommunityCode = { $in: req.user.hoaCommunityCode };
        } else {
          console.log('🔍 DEBUG: User has no accessible HOAs');
          return res.status(200).json({
            success: true,
            count: 0,
            data: []
          });
        }
      }
    }

    console.log('🔍 DEBUG: Final HOA query:', JSON.stringify(query, null, 2));

    const hoas = await HOA.find(query).sort({ createdAt: -1 });

    console.log('🔍 DEBUG: Found HOAs:', hoas.length);
    if (hoas.length > 0) {
      console.log('🔍 DEBUG: Sample HOA:', {
        id: hoas[0]._id,
        name: hoas[0].hoaCommunityName,
        code: hoas[0].hoaCommunityCode
      });
    }

    res.status(200).json({
      success: true,
      count: hoas.length,
      data: hoas
    });
  } catch (error) {
    console.error('Error fetching HOAs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get HOA by Community ID
 * @route GET /api/hoa/:id
 * @access Private (Admin for their HOA, Company Admin for any HOA)
 * @query communityId - Single community ID to find HOA
 */
exports.getHOAById = async (req, res) => {
  try {
    const { id, communityId: pathCommunityId } = req.params;
    const { communityId: queryCommunityId } = req.query;

    // Use path parameter if available, otherwise use query parameter
    const communityId = pathCommunityId || queryCommunityId;

    console.log('🔍 DEBUG: getHOAById called with:', {
      hoaId: id,
      pathCommunityId,
      queryCommunityId,
      communityId,
      userId: req.user._id,
      userRole: req.user.role
    });

    let hoa = null;
    let selectedCommunityId = null;

    // Handle communityId parameter (single value only)
    if (communityId) {
      console.log('🔍 DEBUG: Processing single community ID:', communityId);

      // Find community and its associated HOA
      const Community = require('../../models/community');
      const community = await Community.findById(communityId).populate('hoaId');

      console.log('🔍 DEBUG: Found community:', community ? community.name : 'Not found');

      if (!community) {
        console.log('🔍 DEBUG: Community not found for provided ID');
        return res.status(404).json({
          success: false,
          message: 'Community not found for the provided ID'
        });
      }

      if (!community.hoaId) {
        console.log('🔍 DEBUG: Community has no associated HOA');
        return res.status(404).json({
          success: false,
          message: 'Community has no associated HOA'
        });
      }

      // If a specific HOA ID is provided in the URL, verify it matches
      if (id && id !== 'undefined' && id !== 'null') {
        console.log('🔍 DEBUG: Verifying HOA ID matches community HOA:', id);

        if (community.hoaId._id.toString() === id) {
          hoa = community.hoaId;
          selectedCommunityId = community._id;
          console.log('🔍 DEBUG: HOA ID matches community HOA:', {
            communityId: selectedCommunityId,
            communityName: community.name,
            hoaId: hoa._id,
            hoaName: hoa.hoaCommunityName
          });
        } else {
          console.log('🔍 DEBUG: HOA ID does not match community HOA');
          return res.status(404).json({
            success: false,
            message: 'HOA ID does not match the community\'s HOA'
          });
        }
      } else {
        // No specific HOA ID provided, return the community's HOA
        hoa = community.hoaId;
        selectedCommunityId = community._id;
        console.log('🔍 DEBUG: Using community HOA:', {
          communityId: selectedCommunityId,
          communityName: community.name,
          hoaId: hoa._id,
          hoaName: hoa.hoaCommunityName
        });
      }
    } else if (id && id !== 'undefined' && id !== 'null') {
      // Traditional HOA ID lookup
      console.log('🔍 DEBUG: Finding HOA by ID only:', id);

      // Validate ObjectId format
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(id)) {
        console.log('🔍 DEBUG: Invalid ObjectId format:', id);
        return res.status(400).json({
          success: false,
          message: 'Invalid HOA ID format'
        });
      }

      hoa = await HOA.findById(id);
    } else {
      console.log('🔍 DEBUG: No valid HOA ID or community ID provided');
      return res.status(400).json({
        success: false,
        message: 'Valid HOA ID or community ID is required'
      });
    }

    if (!hoa) {
      console.log('🔍 DEBUG: HOA not found');
      return res.status(404).json({
        success: false,
        message: 'HOA not found'
      });
    }

    // Verify user has access to this HOA (for non-company admins)
    if (req.user.role !== 'company_admin') {
      const userHasCommunityAccess = req.user.hoaCommunityCode &&
        (Array.isArray(hoa.hoaCommunityCode)
          ? hoa.hoaCommunityCode.some(code => req.user.hoaCommunityCode.includes(code))
          : req.user.hoaCommunityCode.includes(hoa.hoaCommunityCode)
        );

      if (!userHasCommunityAccess) {
        console.log('🔍 DEBUG: User does not have access to this HOA:', {
          userCodes: req.user.hoaCommunityCode,
          hoaCode: hoa.hoaCommunityCode
        });
        return res.status(403).json({
          success: false,
          message: 'Access denied to this HOA'
        });
      }
    }

    console.log('🔍 DEBUG: Successfully returning HOA:', {
      id: hoa._id,
      name: hoa.hoaCommunityName,
      code: hoa.hoaCommunityCode,
      selectedCommunityId
    });

    res.status(200).json({
      success: true,
      data: hoa,
      selectedCommunityId // Include the community ID that was used to find this HOA
    });
  } catch (error) {
    console.error('Error fetching HOA:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Create a new HOA (Company Admin only)
 * @route POST /api/hoa
 * @access Private (Company Admin)
 */
exports.createHOA = async (req, res) => {
  try {
    const { hoaCommunityName, hoaStreetAddress, hoaCity, hoaState, hoaZipCode, hoaPaymentInfo } = req.body;

    // Generate a unique HOA community code
    const hoaCommunityCode = await HOA.generateHoaCommunityCode();

    const newHoa = new HOA({
      hoaCommunityName,
      hoaCommunityCode,
      hoaStreetAddress,
      hoaCity,
      hoaState,
      hoaZipCode,
      hoaPaymentInfo,
      verificationStatus: 'approved', // Auto-approve when created by company admin
      verifiedBy: req.user._id,
      verifiedAt: new Date()
    });

    const savedHoa = await newHoa.save();

    res.status(201).json({
      success: true,
      data: savedHoa
    });
  } catch (error) {
    console.error('Error creating HOA:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Update HOA
 * @route PUT /api/hoa/:id
 * @access Private (Admin for their HOA, Company Admin for any HOA)
 */
exports.updateHOA = async (req, res) => {
  try {
    // Don't allow updating the community code
    if (req.body.hoaCommunityCode) {
      delete req.body.hoaCommunityCode;
    }

    const updatedHoa = await HOA.findByIdAndUpdate(
      req.params.id,
      {
        ...req.body,
        updatedAt: Date.now()
      },
      { new: true, runValidators: true }
    );

    if (!updatedHoa) {
      return res.status(404).json({
        success: false,
        message: 'HOA not found'
      });
    }

    res.status(200).json({
      success: true,
      data: updatedHoa
    });
  } catch (error) {
    console.error('Error updating HOA:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get HOA by community code
 * @route GET /api/hoa/code/:code
 * @access Public (needed for registration)
 */
exports.getHOAByCode = async (req, res) => {
  try {
    // Add explicit CORS headers for this endpoint
    const origin = req.headers.origin;
    const allowedOrigins = [
      'https://hoa-front.vercel.app',
      'https://www.hoa-front.vercel.app',
      'https://street-harmony.vercel.app',
      'https://www.street-harmony.vercel.app',
      'http://localhost:8080',
      'http://localhost:8082'
    ];

    if (origin && allowedOrigins.includes(origin)) {
      res.setHeader('Access-Control-Allow-Origin', origin);
      res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      res.setHeader('Access-Control-Allow-Credentials', 'true');
    }

    const code = req.params.code.toUpperCase();
    const hoa = await HOA.findOne({ hoaCommunityCode: code });

    if (!hoa) {
      return res.status(404).json({
        success: false,
        message: 'HOA not found with the provided community code'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        _id: hoa._id,
        hoaCommunityName: hoa.hoaCommunityName,
        hoaCommunityCode: hoa.hoaCommunityCode
      }
    });
  } catch (error) {
    console.error('Error fetching HOA by code:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Register a new HOA (Public)
 * @route POST /api/hoa/register
 * @access Public
 */
exports.registerHOA = async (req, res) => {
  try {
    // Add explicit CORS headers for this endpoint
    const origin = req.headers.origin;
    const allowedOrigins = [
      'https://hoa-front.vercel.app',
      'https://www.hoa-front.vercel.app',
      'https://street-harmony.vercel.app',
      'https://www.street-harmony.vercel.app',
      'http://localhost:8080',
      'http://localhost:8082'
    ];

    if (origin && allowedOrigins.includes(origin)) {
      res.setHeader('Access-Control-Allow-Origin', origin);
      res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      res.setHeader('Access-Control-Allow-Credentials', 'true');
    }

    console.log('HOA registration request received:', req.body);

    // Create uploads directory if it doesn't exist
    try {
      const uploadsDir = path.join(__dirname, '../uploads');
      const hoaDocumentsDir = path.join(uploadsDir, 'hoa_documents');

      // Create directories recursively
      if (!fs.existsSync(uploadsDir)) {
        console.log('Creating uploads directory');
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      if (!fs.existsSync(hoaDocumentsDir)) {
        console.log('Creating hoa_documents directory');
        fs.mkdirSync(hoaDocumentsDir, { recursive: true });
      }

      console.log('Upload directories created or verified');
    } catch (dirError) {
      console.error('Error creating directories:', dirError);
      // Continue with registration even if directory creation fails
    }

    const {
      hoaCommunityName,
      hoaStreetAddress,
      hoaCity,
      hoaState,
      hoaZipCode,
      contactEmail,
      contactPhone,
      einNumber,
      subscription
    } = req.body;

    console.log('Generating HOA community code');
    // Generate a unique HOA community code
    const hoaCommunityCode = await HOA.generateHoaCommunityCode();
    console.log('Generated HOA community code:', hoaCommunityCode);

    console.log('Creating new HOA with data:', {
      hoaCommunityName,
      hoaCommunityCode,
      hoaStreetAddress,
      hoaCity,
      hoaState,
      hoaZipCode,
      contactEmail,
      contactPhone,
      einNumber,
      subscription
    });

    const subscriptionJSON = JSON.parse(subscription);

    // Create new HOA with pending verification status
    const newHoa = new HOA({
      hoaCommunityName,
      hoaCommunityCode,
      hoaStreetAddress,
      hoaCity,
      hoaState,
      hoaZipCode,
      contactEmail,
      contactPhone,
      einNumber,
      verificationStatus: 'pending',
      subscription: {
        tier: subscriptionJSON.tier,
        unitCount: subscriptionJSON.unitCount,
        totalPrice: subscriptionJSON.totalPrice,
        pricePerUnit: subscriptionJSON.totalPrice / subscriptionJSON.unitCount
      },
      hoaPaymentInfo: {
        paymentMethod: 'online',
        dueDate: 1
      }
    });

    // If documents were uploaded, add them to the HOA and create Document records
    if (req.files) {
      console.log('Documents uploaded:', Object.keys(req.files));

      // Import Document model
      const Document = require('../../models/document');

      // Create documents directory if it doesn't exist
      const documentsDir = path.join(__dirname, '../uploads/documents');
      if (!fs.existsSync(documentsDir)) {
        fs.mkdirSync(documentsDir, { recursive: true });
        console.log('Created documents directory');
      }

      if (req.files.registrationDoc) {
        const regDoc = req.files.registrationDoc[0];
        console.log('Registration document found:', regDoc.originalname);

        // Copy file to documents directory
        const sourceFile = regDoc.path;
        const targetFile = path.join(documentsDir, regDoc.filename);

        try {
          // Copy file to documents directory
          fs.copyFileSync(sourceFile, targetFile);
          console.log(`Copied registration document from ${sourceFile} to ${targetFile}`);

          // Create Document record
          const docRecord = new Document({
            title: `HOA Registration Document - ${hoaCommunityName}`,
            description: `Registration document for ${hoaCommunityName}`,
            fileName: regDoc.filename,
            fileType: regDoc.mimetype,
            fileSize: regDoc.size,
            category: 'property_information',
            uploadedBy: req.user ? req.user._id : null, // May be null for public registration
            isPublic: false,
            // Will associate with HOA after it's created
          });

          // Save document record
          const savedDoc = await docRecord.save();
          console.log('Registration document record saved with ID:', savedDoc._id);

          // Add reference to HOA
          newHoa.verificationDocuments.registrationDoc = {
            filename: regDoc.filename,
            originalName: regDoc.originalname,
            mimeType: regDoc.mimetype,
            uploadDate: new Date(),
            documentId: savedDoc._id // Reference to Document record
          };
        } catch (err) {
          console.error('Error handling registration document:', err);
          // Continue with registration even if document handling fails
        }
      }

      if (req.files.einDoc) {
        const einDoc = req.files.einDoc[0];
        console.log('EIN document found:', einDoc.originalname);

        // Copy file to documents directory
        const sourceFile = einDoc.path;
        const targetFile = path.join(documentsDir, einDoc.filename);

        try {
          // Copy file to documents directory
          fs.copyFileSync(sourceFile, targetFile);
          console.log(`Copied EIN document from ${sourceFile} to ${targetFile}`);

          // Create Document record
          const docRecord = new Document({
            title: `EIN Document - ${hoaCommunityName}`,
            description: `EIN document for ${hoaCommunityName}`,
            fileName: einDoc.filename,
            fileType: einDoc.mimetype,
            fileSize: einDoc.size,
            category: 'property_information',
            uploadedBy: req.user ? req.user._id : null, // May be null for public registration
            isPublic: false,
            // Will associate with HOA after it's created
          });

          // Save document record
          const savedDoc = await docRecord.save();
          console.log('EIN document record saved with ID:', savedDoc._id);

          // Add reference to HOA
          newHoa.verificationDocuments.einDoc = {
            filename: einDoc.filename,
            originalName: einDoc.originalname,
            mimeType: einDoc.mimetype,
            uploadDate: new Date(),
            documentId: savedDoc._id // Reference to Document record
          };
        } catch (err) {
          console.error('Error handling EIN document:', err);
          // Continue with registration even if document handling fails
        }
      }
    }

    console.log('Saving HOA to database');
    const savedHoa = await newHoa.save();
    console.log('HOA saved successfully with ID:', savedHoa._id);

    // Update Document records with HOA ID
    if (req.files) {
      try {
        const Document = require('../../models/document');

        // Update registration document if it exists
        if (savedHoa.verificationDocuments?.registrationDoc?.documentId) {
          await Document.findByIdAndUpdate(
            savedHoa.verificationDocuments.registrationDoc.documentId,
            { hoaId: savedHoa._id }
          );
          console.log('Updated registration document with HOA ID');
        }

        // Update EIN document if it exists
        if (savedHoa.verificationDocuments?.einDoc?.documentId) {
          await Document.findByIdAndUpdate(
            savedHoa.verificationDocuments.einDoc.documentId,
            { hoaId: savedHoa._id }
          );
          console.log('Updated EIN document with HOA ID');
        }
      } catch (err) {
        console.error('Error updating document records with HOA ID:', err);
        // Continue with registration even if document updates fail
      }
    }

    // Create a default community for this HOA
    console.log('Generating community code for HOA:', hoaCommunityCode);
    const communityCode = await Community.generateCommunityCode(hoaCommunityCode);
    console.log('Generated community code:', communityCode);

    console.log('Creating default community for HOA');
    const newCommunity = new Community({
      name: `${hoaCommunityName}`,
      description: `Default community for ${hoaCommunityName}`,
      streetAddress: hoaStreetAddress,
      city: hoaCity,
      state: hoaState,
      zipCode: hoaZipCode,
      communityCode,
      hoaId: savedHoa._id,
      type: 'mixed',
      status: 'active',
      visibility: 'public',
      permissions: {
        memberVisibility: true,
        financeVisibility: false,
        documentVisibility: true
      }
    });

    console.log('Saving community to database');
    const savedCommunity = await newCommunity.save();
    console.log('Community saved successfully with ID:', savedCommunity._id);

    console.log('HOA registration completed successfully');

    // Send notification email
    const bodyTemplate = `
      <h2>HOA Registration</h2>
      <p>Hello ${hoaCommunityName} Administrator,</p>
      <p>Thank you for registering your HOA with our platform.</p>
      <p>HOA details:</p>
      <ul>
        <li>Name: ${hoaCommunityName}</li>
        <li>Street Address: ${hoaStreetAddress}</li>
        <li>City: ${hoaCity}</li>
        <li>State: ${hoaState}</li>
        <li>Zip Code: ${hoaZipCode}</li>
      </ul>
      <p>Please wait while our team approve the registration.</p>
      <p>You will receive an email once the registration is approved.</p>
    `;

    try {
      await sendEmail(contactEmail, 'HOA Registration', bodyTemplate);
    } catch (err) {
      console.error('Error sending notification email:', err);
    }

    res.status(201).json({
      success: true,
      message: 'HOA registration submitted successfully. Pending approval by company admin.',
      data: {
        _id: savedHoa._id,
        hoaCommunityName: savedHoa.hoaCommunityName,
        hoaCommunityCode: savedHoa.hoaCommunityCode,
        verificationStatus: savedHoa.verificationStatus,
        defaultCommunity: {
          _id: savedCommunity._id,
          name: savedCommunity.name,
          communityCode: savedCommunity.communityCode
        }
      }
    });
  } catch (error) {
    console.error('Error registering HOA:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Upload HOA verification documents
 * @route POST /api/hoa/:id/documents
 * @access Private (Admin for their HOA, Company Admin for any HOA)
 */
exports.uploadHOADocuments = async (req, res) => {
  try {
    const hoa = await HOA.findById(req.params.id);

    if (!hoa) {
      return res.status(404).json({
        success: false,
        message: 'HOA not found'
      });
    }

    // Update document references
    if (req.files) {
      if (req.files.registrationDoc) {
        hoa.verificationDocuments.registrationDoc = {
          filename: req.files.registrationDoc[0].filename,
          originalName: req.files.registrationDoc[0].originalname,
          mimeType: req.files.registrationDoc[0].mimetype,
          uploadDate: new Date()
        };
      }

      if (req.files.einDoc) {
        hoa.verificationDocuments.einDoc = {
          filename: req.files.einDoc[0].filename,
          originalName: req.files.einDoc[0].originalname,
          mimeType: req.files.einDoc[0].mimetype,
          uploadDate: new Date()
        };
      }

      await hoa.save();

      res.status(200).json({
        success: true,
        message: 'Documents uploaded successfully',
        data: {
          registrationDoc: hoa.verificationDocuments.registrationDoc,
          einDoc: hoa.verificationDocuments.einDoc
        }
      });
    } else {
      return res.status(400).json({
        success: false,
        message: 'No documents uploaded'
      });
    }
  } catch (error) {
    console.error('Error uploading HOA documents:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Approve or reject HOA registration
 * @route PUT /api/hoa/:id/verify
 * @access Private (Company Admin only)
 */
exports.verifyHOA = async (req, res) => {
  try {
    const { action, verificationNotes } = req.body;
    const verificationStatus = action === 'approve' ? 'approved' : 'rejected';

    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid action. Must be "approve" or "reject".'
      });
    }

    const hoa = await HOA.findById(req.params.id);

    if (!hoa) {
      return res.status(404).json({
        success: false,
        message: 'HOA not found'
      });
    }

    // Update verification status
    hoa.verificationStatus = verificationStatus;
    hoa.verificationNotes = verificationNotes;
    hoa.verifiedBy = req.user._id;
    hoa.verifiedAt = new Date();

    // If approved, generate payment token and prepare for subscription
    if (action === 'approve') {
      // Generate a unique payment token
      const crypto = require('crypto');
      const paymentToken = crypto.randomBytes(32).toString('hex');

      // Set payment token and expiration (24 hours from now)
      hoa.paymentToken = paymentToken;
      hoa.paymentTokenExpires = new Date(Date.now() + 24 * 60 * 60 * 1000);

      // Keep subscription status as inactive until payment is processed
      hoa.subscription.status = 'pending';

      // Send email with payment link
      try {
        // Construct the payment URL
        const frontendUrl = process.env.FRONTEND_URL || 'https://hoa-front.vercel.app';
        const paymentUrl = `${frontendUrl}/subscription/activate/${paymentToken}`;

        // Email content
        const emailSubject = `Your HOA Registration for ${hoa.hoaCommunityName} has been approved`;
        const emailHtml = `
          <h2>Congratulations! Your HOA Registration has been approved</h2>
          <p>Dear ${hoa.hoaCommunityName} Administrator,</p>
          <p>We're pleased to inform you that your HOA registration has been approved by our team.</p>
          <p>To activate your subscription with a 30-day free trial, please click the button below:</p>
          <p style="text-align: center; margin: 30px 0;">
            <a href="${paymentUrl}" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
              Activate Your Subscription
            </a>
          </p>
          <p>This link will expire in 24 hours. If you don't activate your subscription within this time, you'll need to contact our support team.</p>
          <p>Your HOA Community Code: <strong>${hoa.hoaCommunityCode}</strong></p>
          <p>Please keep this code safe as it will be needed for your members to register.</p>
          <p>Thank you for choosing our HOA Management platform!</p>
          <p>Best regards,<br>The Street Harmony Team</p>
        `;

        // Send the email
        const { sendEmail } = require('../../services/emailService');
        await sendEmail(hoa.contactEmail, emailSubject, emailHtml);
        console.log(`Payment activation email sent to ${hoa.contactEmail}`);
      } catch (emailError) {
        console.error('Error sending payment email:', emailError);
        // Continue even if email fails
      }
    }

    await hoa.save();

    res.status(200).json({
      success: true,
      message: action === 'approve'
        ? `HOA registration approved. Payment link sent to ${hoa.contactEmail}.`
        : 'HOA registration rejected.',
      data: hoa
    });
  } catch (error) {
    console.error('Error verifying HOA:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Create a test HOA (for development only)
 * @route GET /api/hoa/test
 * @access Public (for testing only)
 */
exports.createTestHOA = async (req, res) => {
  try {
    console.log('Creating test HOA');

    // Generate a unique HOA community code
    const hoaCommunityCode = await HOA.generateHoaCommunityCode();
    console.log('Generated HOA community code:', hoaCommunityCode);

    // Create new HOA with approved status
    const newHoa = new HOA({
      hoaCommunityName: 'Test HOA 6',
      hoaCommunityCode,
      hoaStreetAddress: '123 Test Street',
      hoaCity: 'Test City',
      hoaState: 'TS',
      hoaZipCode: '12345',
      contactEmail: '<EMAIL>',
      contactPhone: '************',
      einNumber: '12-3456789',
      verificationStatus: 'approved',
      verificationNotes: 'Approved automatically for testing',
      verifiedAt: new Date(),
      subscription: {
        tier: 'basic',
        unitCount: 50,
        status: 'active'
      },
      hoaPaymentInfo: {
        paymentMethod: 'online',
        dueDate: 1
      }
    });

    console.log('Saving HOA to database');
    const savedHoa = await newHoa.save();
    console.log('HOA saved successfully with ID:', savedHoa._id);

    // Create a default community for this HOA
    console.log('Generating community code for HOA:', hoaCommunityCode);
    const communityCode = await Community.generateCommunityCode(hoaCommunityCode);
    console.log('Generated community code:', communityCode);

    console.log('Creating default community for HOA');
    const newCommunity = new Community({
      name: `${savedHoa.hoaCommunityName}`,
      description: `Default community for ${savedHoa.hoaCommunityName}`,
      streetAddress: savedHoa.hoaStreetAddress,
      city: savedHoa.hoaCity,
      state: savedHoa.hoaState,
      zipCode: savedHoa.hoaZipCode,
      communityCode,
      hoaId: savedHoa._id,
      type: 'mixed',
      status: 'active',
      visibility: 'public',
      permissions: {
        memberVisibility: true,
        financeVisibility: false,
        documentVisibility: true
      }
    });

    console.log('Saving community to database');
    const savedCommunity = await newCommunity.save();
    console.log('Community saved successfully with ID:', savedCommunity._id);

    res.status(201).json({
      success: true,
      message: 'Test HOA and community created successfully',
      data: {
        hoa: {
          _id: savedHoa._id,
          name: savedHoa.hoaCommunityName,
          code: savedHoa.hoaCommunityCode,
          status: savedHoa.verificationStatus
        },
        community: {
          _id: savedCommunity._id,
          name: savedCommunity.name,
          code: savedCommunity.communityCode
        }
      }
    });
  } catch (error) {
    console.error('Error creating test HOA:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

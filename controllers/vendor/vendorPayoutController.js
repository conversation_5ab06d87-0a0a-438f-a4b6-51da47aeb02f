/**
 * Vendor Payout Controller
 * Handles automated payments to vendors using Stripe payouts
 */
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const RecurringPayment = require('../../models/recurringPayment');
const VendorPayment = require('../../models/vendorPayment');
const HOA = require('../../models/hoa');
const User = require('../../models/user');
const notificationController = require('../notification/notificationController');

/**
 * Create recurring payment setup
 */
exports.createRecurringPayment = async (req, res) => {
  try {
    const {
      hoaId,
      vendorName,
      vendorEmail,
      vendorPhone,
      amount,
      description,
      category,
      frequency,
      startDate,
      endDate,
      paymentMethod,
      bankAccount
    } = req.body;

    // Verify HOA exists and user has permission
    const hoa = await HOA.findById(hoaId);
    if (!hoa) {
      return res.status(404).json({ error: 'HOA not found' });
    }

    // Calculate first payment date
    const firstPaymentDate = new Date(startDate);
    
    // Create recurring payment
    const recurringPayment = new RecurringPayment({
      hoaId,
      createdBy: req.user._id,
      vendor: {
        name: vendorName,
        email: vendorEmail,
        phone: vendorPhone,
        bankAccount: bankAccount // Should be encrypted in production
      },
      amount,
      description,
      category,
      schedule: {
        frequency,
        startDate: firstPaymentDate,
        endDate: endDate ? new Date(endDate) : null
      },
      nextPaymentDate: firstPaymentDate,
      paymentMethod,
      status: 'active'
    });

    await recurringPayment.save();

    res.status(201).json({
      success: true,
      recurringPayment,
      message: 'Recurring payment created successfully'
    });

  } catch (error) {
    console.error('Error creating recurring payment:', error);
    res.status(500).json({ error: 'Failed to create recurring payment' });
  }
};

/**
 * Process due recurring payments (called by cron job)
 */
exports.processDuePayments = async () => {
  try {
    console.log('Processing due recurring payments...');

    // Find all due payments
    const duePayments = await RecurringPayment.find({
      status: 'active',
      nextPaymentDate: { $lte: new Date() }
    }).populate('hoaId');

    console.log(`Found ${duePayments.length} due payments`);

    for (const recurringPayment of duePayments) {
      try {
        await processIndividualPayment(recurringPayment);
      } catch (error) {
        console.error(`Failed to process payment ${recurringPayment._id}:`, error);
        
        // Increment failure count
        recurringPayment.failureCount += 1;
        
        // If max failures reached, pause the recurring payment
        if (recurringPayment.failureCount >= recurringPayment.maxFailures) {
          recurringPayment.status = 'paused';
          
          // Notify HOA admin
          const hoaAdmins = await User.find({ 
            role: 'admin',
            // Add HOA filtering logic here
          });
          
          await notificationController.createInfoNotification(
            'Recurring Payment Paused',
            `Recurring payment to ${recurringPayment.vendor.name} has been paused due to repeated failures.`,
            hoaAdmins.map(admin => admin._id)
          );
        }
        
        await recurringPayment.save();
      }
    }

  } catch (error) {
    console.error('Error processing due payments:', error);
  }
};

/**
 * Process individual recurring payment
 */
async function processIndividualPayment(recurringPayment) {
  const hoa = recurringPayment.hoaId;
  
  if (!hoa.stripeConnectAccountId) {
    throw new Error('HOA does not have Stripe Connect account set up');
  }

  let paymentResult;

  if (recurringPayment.paymentMethod === 'stripe_transfer' && recurringPayment.vendor.stripeAccountId) {
    // Transfer to vendor's Stripe account
    paymentResult = await processStripeTransfer(recurringPayment, hoa);
  } else if (recurringPayment.paymentMethod === 'bank_payout') {
    // Payout to vendor's bank account
    paymentResult = await processBankPayout(recurringPayment, hoa);
  } else {
    throw new Error('Invalid payment method or missing vendor account info');
  }

  // Create vendor payment record
  const vendorPayment = new VendorPayment({
    hoaId: recurringPayment.hoaId._id,
    initiatedBy: recurringPayment.createdBy,
    vendorName: recurringPayment.vendor.name,
    vendorEmail: recurringPayment.vendor.email,
    vendorPhone: recurringPayment.vendor.phone,
    amount: recurringPayment.amount,
    description: recurringPayment.description,
    category: recurringPayment.category,
    status: 'completed',
    paymentMethod: recurringPayment.paymentMethod,
    stripePaymentIntentId: paymentResult.id,
    paymentDate: new Date(),
    notes: `Automated recurring payment - ${recurringPayment.schedule.frequency}`
  });

  await vendorPayment.save();

  // Update recurring payment
  recurringPayment.lastPaymentId = vendorPayment._id;
  recurringPayment.calculateNextPaymentDate();
  recurringPayment.failureCount = 0; // Reset failure count on success
  await recurringPayment.save();

  // Send notifications
  if (recurringPayment.notifyVendor && recurringPayment.vendor.email) {
    // Send email to vendor (implement email service)
    console.log(`Notifying vendor ${recurringPayment.vendor.email} of payment`);
  }

  if (recurringPayment.notifyHOA) {
    // Notify HOA admin
    const hoaAdmins = await User.find({ role: 'admin' });
    await notificationController.createInfoNotification(
      'Vendor Payment Processed',
      `Automated payment of $${recurringPayment.amount} sent to ${recurringPayment.vendor.name}`,
      hoaAdmins.map(admin => admin._id)
    );
  }

  console.log(`Successfully processed payment to ${recurringPayment.vendor.name}`);
}

/**
 * Process Stripe transfer to vendor's Stripe account
 */
async function processStripeTransfer(recurringPayment, hoa) {
  const transfer = await stripe.transfers.create({
    amount: recurringPayment.amount * 100, // Convert to cents
    currency: 'usd',
    destination: recurringPayment.vendor.stripeAccountId,
    description: `${recurringPayment.description} - ${recurringPayment.schedule.frequency} payment`,
    metadata: {
      hoaId: hoa._id.toString(),
      vendorName: recurringPayment.vendor.name,
      recurringPaymentId: recurringPayment._id.toString()
    }
  }, {
    stripeAccount: hoa.stripeConnectAccountId
  });

  return transfer;
}

/**
 * Process bank payout to vendor's bank account
 */
async function processBankPayout(recurringPayment, hoa) {
  // First, create external account for vendor if not exists
  const externalAccount = await stripe.accounts.createExternalAccount(
    hoa.stripeConnectAccountId,
    {
      external_account: {
        object: 'bank_account',
        country: 'US',
        currency: 'usd',
        account_number: recurringPayment.vendor.bankAccount.accountNumber,
        routing_number: recurringPayment.vendor.bankAccount.routingNumber,
        account_holder_name: recurringPayment.vendor.bankAccount.accountHolderName,
        account_holder_type: 'individual'
      }
    }
  );

  // Create payout
  const payout = await stripe.payouts.create({
    amount: recurringPayment.amount * 100, // Convert to cents
    currency: 'usd',
    destination: externalAccount.id,
    description: `${recurringPayment.description} - ${recurringPayment.schedule.frequency} payment`,
    metadata: {
      hoaId: hoa._id.toString(),
      vendorName: recurringPayment.vendor.name,
      recurringPaymentId: recurringPayment._id.toString()
    }
  }, {
    stripeAccount: hoa.stripeConnectAccountId
  });

  return payout;
}

/**
 * Get recurring payments for HOA
 */
exports.getRecurringPayments = async (req, res) => {
  try {
    const { hoaId } = req.params;
    
    const recurringPayments = await RecurringPayment.find({ hoaId })
      .populate('lastPaymentId')
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      recurringPayments
    });

  } catch (error) {
    console.error('Error fetching recurring payments:', error);
    res.status(500).json({ error: 'Failed to fetch recurring payments' });
  }
};

/**
 * Update recurring payment
 */
exports.updateRecurringPayment = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const recurringPayment = await RecurringPayment.findByIdAndUpdate(
      id,
      updates,
      { new: true, runValidators: true }
    );

    if (!recurringPayment) {
      return res.status(404).json({ error: 'Recurring payment not found' });
    }

    res.status(200).json({
      success: true,
      recurringPayment
    });

  } catch (error) {
    console.error('Error updating recurring payment:', error);
    res.status(500).json({ error: 'Failed to update recurring payment' });
  }
};

/**
 * Cancel recurring payment
 */
exports.cancelRecurringPayment = async (req, res) => {
  try {
    const { id } = req.params;

    const recurringPayment = await RecurringPayment.findById(id);
    if (!recurringPayment) {
      return res.status(404).json({ error: 'Recurring payment not found' });
    }

    await recurringPayment.cancel();

    res.status(200).json({
      success: true,
      message: 'Recurring payment cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling recurring payment:', error);
    res.status(500).json({ error: 'Failed to cancel recurring payment' });
  }
};

module.exports = exports;

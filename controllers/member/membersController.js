// controllers/membersController.js
const User = require('../../models/user');
const Payment = require('../../models/payment');
const Task = require('../../models/task');

/**
 * @desc    Get all members for an HOA or specific communities
 * @route   GET /api/members
 * @access  Private
 * @query   communityId - Array of community IDs or single community ID to filter members
 */
exports.getAllMembers = async (req, res) => {
  try {
    // Allow access to admins, company_admins, and regular users (members)
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin' && req.user.role !== 'member') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Access restricted to authenticated users'
      });
    }

    let { communityId } = req.query;
    let hoaId = req.query.hoaId || req.user.hoaId;
    let hoaCommunityCode = req.query.hoaCommunityCode || req.user.hoaCommunityCode;
    let hoaIds = [];
    let communityIds = [];

    console.log('🔍 DEBUG: getAllMembers called with:', {
      communityId,
      hoaId,
      hoaCommunityCode,
      userId: req.user._id,
      userRole: req.user.role
    });

    // Build query based on user role and community filtering
    let query = { role: 'member' };

    // For regular users (members), restrict to same street/community
    if (req.user.role === 'member') {
      console.log('🔍 DEBUG: Regular user accessing members - applying street-level filtering');

      // Regular users can only see members from their own community/street
      if (!req.user.communityId) {
        console.log('🔍 DEBUG: Regular user has no community assigned');
        return res.json({
          success: true,
          count: 0,
          data: [],
          message: 'No community assigned to your account'
        });
      }

      // Filter by same community (street) and same HOA
      query.communityId = req.user.communityId;

      // Also ensure they're in the same HOA if user has HOA info
      if (req.user.hoaId) {
        query.hoaId = req.user.hoaId;
      } else if (req.user.hoaCommunityCode) {
        query.hoaCommunityCode = { $in: Array.isArray(req.user.hoaCommunityCode) ? req.user.hoaCommunityCode : [req.user.hoaCommunityCode] };
      }

      console.log('🔍 DEBUG: Regular user member query:', JSON.stringify(query, null, 2));

      const members = await User.find(query)
        .select('-password')
        .populate('communityId', 'name communityCode')
        .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
        .lean();

      console.log('🔍 DEBUG: Found members for regular user:', members.length);

      return res.json({
        success: true,
        count: members.length,
        data: members,
        userCommunityId: req.user.communityId,
        restrictedView: true
      });
    }

    // For company_admin users, allow them to see all members
    if (req.user.role === 'company_admin') {
      // If specific community IDs are provided, filter by them
      if (communityId) {
        // Parse communityId if it's a string representation of an array
        if (typeof communityId === 'string') {
          try {
            // Try to parse as JSON array first
            if (communityId.startsWith('[') && communityId.endsWith(']')) {
              communityId = JSON.parse(communityId);
            } else if (communityId.includes(',')) {
              // Handle comma-separated values
              communityId = communityId.split(',').map(id => id.trim());
            } else {
              // Single community ID
              communityId = [communityId];
            }
          } catch (parseError) {
            // If parsing fails, treat as single ID
            communityId = [communityId];
          }
        }

        // Ensure communityId is an array
        if (!Array.isArray(communityId)) {
          communityId = [communityId];
        }

        console.log('🔍 DEBUG: Company admin filtering by community IDs:', communityId);
        query.communityId = { $in: communityId };

        const members = await User.find(query)
          .select('-password')
          .populate('communityId', 'name communityCode')
          .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
          .lean();

        return res.json({
          success: true,
          count: members.length,
          data: members,
          selectedCommunityIds: communityId
        });
      } else {
        // No community filter, show all members
        const members = await User.find(query)
          .select('-password')
          .populate('communityId', 'name communityCode')
          .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
          .lean();

        return res.json({
          success: true,
          count: members.length,
          data: members
        });
      }
    }

    // Handle community-based filtering from frontend for admin users
    if (communityId) {
      console.log('🔍 DEBUG: Frontend provided community IDs for filtering');

      // Parse communityId if it's a string representation of an array
      if (typeof communityId === 'string') {
        try {
          // Try to parse as JSON array first
          if (communityId.startsWith('[') && communityId.endsWith(']')) {
            communityId = JSON.parse(communityId);
          } else if (communityId.includes(',')) {
            // Handle comma-separated values
            communityId = communityId.split(',').map(id => id.trim());
          } else {
            // Single community ID
            communityId = [communityId];
          }
        } catch (parseError) {
          // If parsing fails, treat as single ID
          communityId = [communityId];
        }
      }

      // Ensure communityId is an array
      if (!Array.isArray(communityId)) {
        communityId = [communityId];
      }

      console.log('🔍 DEBUG: Processing frontend community IDs:', communityId);

      const Community = require('../../models/community');

      // Find communities and get their HOA IDs
      const communities = await Community.find({
        _id: { $in: communityId }
      }).populate('hoaId', '_id hoaCommunityName');

      console.log('🔍 DEBUG: Found communities:', communities.length);

      if (communities.length === 0) {
        console.log('🔍 DEBUG: No communities found for provided IDs');
        return res.status(404).json({
          success: false,
          message: 'No communities found for the provided IDs'
        });
      }

      // Extract HOA IDs and community IDs
      communities.forEach(community => {
        if (community.hoaId) {
          hoaIds.push(community.hoaId._id);
          communityIds.push(community._id);
          console.log(`🔍 DEBUG: Found community ${community.name} with HOA ${community.hoaId.hoaCommunityName}`);
        }
      });

      // Remove duplicates
      hoaIds = [...new Set(hoaIds.map(id => id.toString()))];
      communityIds = [...new Set(communityIds.map(id => id.toString()))];

      // Filter members by specific communities
      query.communityId = { $in: communityIds };
      console.log('🔍 DEBUG: Filtering members by specific communities:', communityIds);
    }
    // For admin users who have HOA associations (fallback to user's communities)
    else if (hoaCommunityCode && req.user.role === 'admin') {
      const User = require('../../models/user');
      const HOA = require('../../models/hoa');
      const Community = require('../../models/community');

      console.log('🔍 DEBUG: Using user HOA community codes for filtering');

      // Get the user's full profile to access HOA associations
      const user = await User.findById(req.user._id);

      if (user && user.hoaCommunityCode) {
        // Handle array of community codes
        const codes = Array.isArray(user.hoaCommunityCode) ? user.hoaCommunityCode : [user.hoaCommunityCode];

        console.log('🔍 DEBUG: Processing user community codes:', codes);

        // Process each community code to get HOA and community IDs
        for (const code of codes) {
          // First try to find a community with this code
          const community = await Community.findOne({ communityCode: code })
            .populate('hoaId', '_id hoaCommunityName');

          if (community && community.hoaId) {
            hoaIds.push(community.hoaId._id);
            communityIds.push(community._id);
            console.log(`🔍 DEBUG: Found community ${community.name} with HOA ${community.hoaId.hoaCommunityName}`);
          } else {
            // For backward compatibility, check if it's an HOA code
            const hoa = await HOA.findOne({ hoaCommunityCode: code });
            if (hoa) {
              hoaIds.push(hoa._id);
              console.log(`🔍 DEBUG: Found HOA ${hoa.hoaCommunityName} with code ${code}`);

              // Try to find communities for this HOA
              const hoaCommunities = await Community.find({ hoaId: hoa._id });
              hoaCommunities.forEach(comm => communityIds.push(comm._id));
            }
          }
        }

        // Remove duplicates
        hoaIds = [...new Set(hoaIds.map(id => id.toString()))];
        communityIds = [...new Set(communityIds.map(id => id.toString()))];

        // Filter members by user's communities
        if (communityIds.length > 0) {
          query.communityId = { $in: communityIds };
          console.log('🔍 DEBUG: Filtering members by user communities:', communityIds);
        } else if (hoaIds.length > 0) {
          query.hoaId = { $in: hoaIds };
          console.log('🔍 DEBUG: Filtering members by user HOAs:', hoaIds);
        }
      }
    }
    // Fallback to traditional HOA ID lookup
    else if (hoaId) {
      query.hoaId = hoaId;
    }

    console.log('🔍 DEBUG: Final member query:', JSON.stringify(query, null, 2));

    const members = await User.find(query)
      .select('-password')
      .populate('communityId', 'name communityCode')
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode')
      .lean();

    console.log('🔍 DEBUG: Found members:', members.length);

    res.json({
      success: true,
      count: members.length,
      data: members,
      selectedCommunityIds: communityIds.length > 0 ? communityIds : null
    });
  } catch (err) {
    console.error('Error fetching members:', err);
    res.status(500).json({ error: 'Failed to fetch members' });
  }
};

exports.addMember = async (req, res) => {
  try {
    // Check if user is admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only administrators can add members'
      });
    }

    // If admin with specific HOA, ensure the member is added to that HOA
    if (req.user.role === 'admin' && req.user.hoaId) {
      req.body.hoaId = req.user.hoaId;
    }

    const newMember = new User({
      ...req.body,
      role: 'member',
      isApproved: true
    });

    await newMember.save();
    res.status(201).json(newMember.userInfo);
  } catch (err) {
    console.error('Error adding member:', err);
    if (err.name === 'ValidationError') {
      return res.status(400).json({ error: err.message });
    }
    res.status(500).json({ error: 'Failed to add member' });
  }
};

exports.updateMember = async (req, res) => {
  try {
    // Check if user is admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only administrators can update members'
      });
    }

    // Find the member first to check permissions
    const member = await User.findById(req.params.id);

    if (!member) {
      return res.status(404).json({ error: 'Member not found' });
    }

    // If admin with specific HOA, ensure they can only update members from their HOA
    if (req.user.role === 'admin' && req.user.hoaId &&
        member.hoaId && member.hoaId.toString() !== req.user.hoaId.toString()) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only update members from your HOA'
      });
    }

    // Don't allow changing the hoaId if admin is HOA-specific
    if (req.user.role === 'admin' && req.user.hoaId && req.body.hoaId) {
      req.body.hoaId = req.user.hoaId;
    }

    const updatedMember = await User.findByIdAndUpdate(
      req.params.id,
      { ...req.body },
      { new: true, runValidators: true }
    ).select('-password');

    res.json(updatedMember);
  } catch (err) {
    console.error('Error updating member:', err);
    if (err.name === 'ValidationError') {
      return res.status(400).json({ error: err.message });
    }
    res.status(500).json({ error: 'Failed to update member' });
  }
};

exports.deleteMember = async (req, res) => {
  try {
    // Check if user is admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only administrators can delete members'
      });
    }

    // Find the member first to check permissions
    const member = await User.findById(req.params.id);

    if (!member) {
      return res.status(404).json({ error: 'Member not found' });
    }

    // If admin with specific HOA, ensure they can only delete members from their HOA
    if (req.user.role === 'admin' && req.user.hoaId &&
        member.hoaId && member.hoaId.toString() !== req.user.hoaId.toString()) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only delete members from your HOA'
      });
    }

    // Prevent deleting admins or company_admins
    if (member.role === 'admin' || member.role === 'company_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Cannot delete admin users through this endpoint'
      });
    }

    const deletedMember = await User.findByIdAndDelete(req.params.id);
    res.status(204).send();
  } catch (err) {
    console.error('Error deleting member:', err);
    res.status(500).json({ error: 'Failed to delete member' });
  }
};

/**
 * @desc    Get approved users for an HOA or specific communities
 * @route   GET /api/members/approved
 * @access  Private
 * @query   communityId - Array of community IDs or single community ID to filter users
 */
exports.getApprovedUsers = async (req, res) => {
  try {
    // Get the current user's ID from the request
    const currentUserId = req.user._id;
    let { communityId } = req.query;
    let hoaId = req.query.hoaId || req.user.hoaId;
    let hoaCommunityCode = req.query.hoaCommunityCode || req.user.hoaCommunityCode;
    let hoaIds = [];
    let communityIds = [];

    console.log('🔍 DEBUG: getApprovedUsers called with:', {
      communityId,
      hoaId,
      hoaCommunityCode,
      userId: req.user._id,
      userRole: req.user.role
    });

    // Build query based on filters
    const query = {
      isApproved: true,
      denied: { $ne: true }
    };

    // For company_admin, use the provided communityId filter if any
    // Company admins can see all users including other company admins
    if (req.user.role === 'company_admin') {
      if (communityId) {
        // Parse communityId if it's a string representation of an array
        if (typeof communityId === 'string') {
          try {
            // Try to parse as JSON array first
            if (communityId.startsWith('[') && communityId.endsWith(']')) {
              communityId = JSON.parse(communityId);
            } else if (communityId.includes(',')) {
              // Handle comma-separated values
              communityId = communityId.split(',').map(id => id.trim());
            } else {
              // Single community ID
              communityId = [communityId];
            }
          } catch (parseError) {
            // If parsing fails, treat as single ID
            communityId = [communityId];
          }
        }

        // Ensure communityId is an array
        if (!Array.isArray(communityId)) {
          communityId = [communityId];
        }

        console.log('🔍 DEBUG: Company admin filtering by community IDs:', communityId);
        query.communityId = { $in: communityId };
        communityIds = communityId;
      }
    }
    // For regular admin, restrict to their HOA and hide company_admin users
    else if (req.user.role === 'admin') {
      // Hide company_admin users from HOA admins
      query.role = { $ne: 'company_admin' };

      // Handle community-based filtering from frontend
      if (communityId) {
        console.log('🔍 DEBUG: Frontend provided community IDs for filtering');

        // Parse communityId if it's a string representation of an array
        if (typeof communityId === 'string') {
          try {
            // Try to parse as JSON array first
            if (communityId.startsWith('[') && communityId.endsWith(']')) {
              communityId = JSON.parse(communityId);
            } else if (communityId.includes(',')) {
              // Handle comma-separated values
              communityId = communityId.split(',').map(id => id.trim());
            } else {
              // Single community ID
              communityId = [communityId];
            }
          } catch (parseError) {
            // If parsing fails, treat as single ID
            communityId = [communityId];
          }
        }

        // Ensure communityId is an array
        if (!Array.isArray(communityId)) {
          communityId = [communityId];
        }

        console.log('🔍 DEBUG: Processing frontend community IDs:', communityId);
        query.communityId = { $in: communityId };
        communityIds = communityId;
      }
      // Fallback to user's HOA community codes
      else if (req.user.hoaCommunityCode) {
        hoaCommunityCode = req.user.hoaCommunityCode;
        query.hoaCommunityCode = { $in: Array.isArray(hoaCommunityCode) ? hoaCommunityCode : [hoaCommunityCode] };
        console.log(`Admin restricted to HOA hoaCommunityCode: ${hoaCommunityCode}`);
      }
    }
    // For members, they can only see users from their own street/community and hide company_admin users
    else if (req.user.role === 'member') {
      // Hide company_admin users from members
      query.role = { $ne: 'company_admin' };

      console.log('🔍 DEBUG: Regular user accessing approved users - applying street-level filtering');

      // Regular users can only see members from their own community/street
      if (!req.user.communityId) {
        console.log('🔍 DEBUG: Regular user has no community assigned');
        return res.json({
          success: true,
          users: [],
          total: 0,
          selectedCommunityIds: null,
          message: 'No community assigned to your account'
        });
      }

      // Filter by same community (street) - this is the most restrictive level
      query.communityId = req.user.communityId;
      communityIds = [req.user.communityId];

      // Also ensure they're in the same HOA if user has HOA info
      if (req.user.hoaId) {
        query.hoaId = req.user.hoaId;
      } else if (req.user.hoaCommunityCode) {
        hoaCommunityCode = req.user.hoaCommunityCode;
        query.hoaCommunityCode = { $in: Array.isArray(hoaCommunityCode) ? hoaCommunityCode : [hoaCommunityCode] };
      }

      console.log(`🔍 DEBUG: Member restricted to community: ${req.user.communityId} and HOA codes: ${hoaCommunityCode}`);
    }

    console.log('🔍 DEBUG: Final approved users query:', JSON.stringify(query, null, 2));

    const users = await User.find(query)
      .select('-password')
      .populate('communityId', 'name communityCode')
      .populate('hoaId', 'hoaCommunityName hoaCommunityCode');

    console.log('🔍 DEBUG: Found approved users:', users.length);

    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    const payments = await Payment.find({
      month: currentMonth,
      year: currentYear
    });

    const usersWithDueStatus = users.map(user => ({
      ...user.toObject(),
      profilePhoto: user.profilePhoto ?
        `${process.env.API_URL}/uploads/${user.profilePhoto}` : null,
      dueStatus: payments.some(p =>
        p.userId && p.userId.toString() === user._id.toString()
      ) ? 'paid' : 'pending',
      // Check if we have a current user ID before comparing
      isOnline: user._id.toString() === currentUserId.toString()
    }));

    res.json({
      success: true,
      users: usersWithDueStatus,
      total: usersWithDueStatus.length,
      selectedCommunityIds: communityIds.length > 0 ? communityIds : null
    });
  } catch (err) {
    console.error('Error fetching approved users:', err);
    res.status(500).json({
      error: 'Failed to fetch approved users',
      details: err.message
    });
  }
};

exports.updateTaskVotes = async (req, res) => {
  const { taskId, userId, direction } = req.body;

  try {
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    const updatedTask = await Task.findOneAndUpdate(
      { _id: taskId },
      { $set: { votes: task.votes } },
      { new: true }
    );

    if (!updatedTask) {
      return res.status(500).json({ error: 'Failed to update task votes' });
    }

    res.json(updatedTask);
  } catch (err) {
    console.error('Error updating task votes:', err);
    res.status(500).json({ error: 'Failed to update task votes' });
  }
};



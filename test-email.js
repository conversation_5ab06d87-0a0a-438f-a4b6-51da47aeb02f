/**
 * Test Email Functionality for HOAFLO Announcements
 * Tests Azure/Outlook integration and email templates
 */

require('dotenv').config({ path: '.env.local.active' });
const { sendTemplateEmail, sendEmail, testAzureEmailConfig } = require('./services/emailService');

async function testEmailConfiguration() {
  console.log('🧪 TESTING HOAFLO EMAIL CONFIGURATION');
  console.log('=====================================');
  
  // Check environment variables
  console.log('\n1️⃣ CHECKING ENVIRONMENT VARIABLES:');
  console.log('-----------------------------------');
  console.log('OUTLOOK_EMAIL_PASSWORD:', !!process.env.OUTLOOK_EMAIL_PASSWORD);
  console.log('OUTLOOK_EMAIL_USER:', process.env.OUTLOOK_EMAIL_USER);
  console.log('AZURE_CLIENT_ID:', !!process.env.AZURE_CLIENT_ID);
  console.log('AZURE_CLIENT_SECRET:', !!process.env.AZURE_CLIENT_SECRET);
  console.log('AZURE_TENANT_ID:', !!process.env.AZURE_TENANT_ID);
  console.log('EMAIL_SERVICE:', process.env.EMAIL_SERVICE);
  console.log('EMAIL_FROM_ADDRESS:', process.env.EMAIL_FROM_ADDRESS);

  // Test Azure configuration
  console.log('\n🔧 TESTING AZURE CONFIGURATION:');
  console.log('--------------------------------');

  try {
    const azureTest = await testAzureEmailConfig();
    console.log('Azure configuration test result:', azureTest);
  } catch (error) {
    console.error('Azure configuration test failed:', error);
  }

  // Test basic email sending
  console.log('\n2️⃣ TESTING BASIC EMAIL SENDING:');
  console.log('--------------------------------');
  
  try {
    const testResult = await sendEmail(
      '<EMAIL>',
      'HOAFLO Email Test - Basic',
      '<h1>Test Email</h1><p>This is a test email from HOAFLO to verify email configuration.</p>'
    );
    
    console.log('Basic email test result:', testResult);
  } catch (error) {
    console.error('Basic email test failed:', error);
  }
  
  // Test announcement template
  console.log('\n3️⃣ TESTING ANNOUNCEMENT TEMPLATE:');
  console.log('----------------------------------');
  
  try {
    const templateResult = await sendTemplateEmail(
      '<EMAIL>',
      'streetAnnouncement',
      {
        username: 'Test Admin 4',
        subject: 'Test Announcement from HOAFLO',
        message: 'This is a test announcement to verify the email template and Azure/Outlook integration is working correctly.',
        streetName: 'Derany Lane - Main',
        senderName: 'HOAFLO Support',
        hoaName: 'Derany Lane HOA',
        attachments: [],
        actionLink: 'http://localhost:8080/notifications'
      },
      {
        senderType: 'company_admin'
      }
    );
    
    console.log('Template email test result:', templateResult);
  } catch (error) {
    console.error('Template email test failed:', error);
  }
  
  // Test HOA announcement template
  console.log('\n4️⃣ TESTING HOA ANNOUNCEMENT TEMPLATE:');
  console.log('-------------------------------------');
  
  try {
    const hoaTemplateResult = await sendTemplateEmail(
      '<EMAIL>',
      'hoaAnnouncement',
      {
        username: 'Test Admin 4',
        subject: 'HOA-Wide Test Announcement',
        message: 'This is a test HOA-wide announcement to verify the email template is working correctly.',
        hoaName: 'Derany Lane HOA',
        senderName: 'HOAFLO Support',
        attachments: [],
        actionLink: 'http://localhost:8080/notifications'
      },
      {
        senderType: 'company_admin'
      }
    );
    
    console.log('HOA template email test result:', hoaTemplateResult);
  } catch (error) {
    console.error('HOA template email test failed:', error);
  }
  
  console.log('\n✅ EMAIL TESTING COMPLETE');
  console.log('=========================');
}

// Run the test
testEmailConfiguration().then(() => {
  console.log('Email test completed');
  process.exit(0);
}).catch((error) => {
  console.error('Email test failed:', error);
  process.exit(1);
});

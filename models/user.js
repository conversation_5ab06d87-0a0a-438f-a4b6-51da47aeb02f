const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Schema = mongoose.Schema;

const userSchema = new Schema({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    minlength: [3, 'Username must be at least 3 characters long']
  },
  fullName: {
    type: String,
    required: [true, 'Full name is required'],
    trim: true
  },
  propertyAddress: {
    type: String,
    required: [true, 'Property address is required'],
    trim: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters long']
    // TODO: Increase minimum length to 8 and add complexity requirements in the future
  },
  autoPayment: {
    enabled: { type: Boolean, default: false },
    stripeCustomerId: String,
    stripeSubscriptionId: String
  },
  role: {
    type: String,
    enum: {
      values: ['pending', 'member', 'admin', 'denied', 'company_admin'],
      message: '{VALUE} is not a valid role'
    },
    default: 'pending'
  },
  profilePhoto: String,
  // Verification documents
  verificationDocuments: {
    homeOwnership: {
      filename: String,
      originalName: String,
      mimeType: String,
      uploadDate: {
        type: Date,
        default: Date.now
      }
    },
    utilityBill: {
      filename: String,
      originalName: String,
      mimeType: String,
      uploadDate: {
        type: Date,
        default: Date.now
      }
    },
    photoId: {
      filename: String,
      originalName: String,
      mimeType: String,
      uploadDate: {
        type: Date,
        default: Date.now
      }
    }
  },
  // Legacy fields (maintained for backward compatibility)
  licenseFront: String,
  licenseBack: String,
  // Approval status
  isApproved: { type: Boolean, default: false },
  denied: { type: Boolean, default: false },
  verificationNotes: { type: String, trim: true },
  verifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  verifiedAt: {
    type: Date
  },
  // Property relationship
  propertyType: {
    type: String,
    enum: ['owner', 'tenant', 'property_manager', 'other'],
    default: 'owner'
  },
  isOnline: { type: Boolean, default: false }, // Added field to track online status
  passwordResetRequired: { type: Boolean, default: false }, // Flag to indicate if user needs to reset password
  tempPassword: { type: String }, // Store temporary password for newly approved users
  // HOA and Community relationships
  hoaId: [{
    type: Schema.Types.ObjectId,
    ref: 'HOA'
  }],
  communityId: [{
    type: Schema.Types.ObjectId,
    ref: 'Community'
  }],
  hoaCommunityCode: [
    {
      type: String,
      trim: true,
      uppercase: true
    }
  ],
  phoneNumber: {
    type: String,
    trim: true
  },
  mfa: {
    enabled: { type: Boolean, default: false },
    verificationCode: { type: String },
    verificationCodeExpires: { type: Date },
    lastVerified: { type: Date }
  },
  // Soft deletion fields to preserve historical data
  isDeleted: { type: Boolean, default: false },
  deletedAt: { type: Date },
  deletedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  originalUsername: { type: String },
  originalFullName: { type: String },
  originalEmail: { type: String },
  createdAt: { type: Date, default: Date.now }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create indexes for efficient queries
userSchema.index({ hoaId: 1 });
userSchema.index({ communityId: 1 });
userSchema.index({ hoaCommunityCode: 1 });
userSchema.index({ isApproved: 1 });
userSchema.index({ role: 1 });
userSchema.index({ propertyType: 1 });

// Pre-save middleware to validate member requirements
userSchema.pre('save', function(next) {
  if (this.role === 'member') {
    if (!this.fullName || !this.propertyAddress) {
      const error = new Error('Full name and property address are required for members');
      error.name = 'ValidationError';
      return next(error);
    }
  }
  next();
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function(candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw error;
  }
};

// Virtual for full user info
userSchema.virtual('userInfo').get(function() {
  return {
    id: this._id,
    username: this.username,
    fullName: this.fullName,
    email: this.email,
    role: this.role,
    isApproved: this.isApproved,
    profilePhoto: this.profilePhoto,
    hoaId: this.hoaId,
    communityId: this.communityId,
    hoaCommunityCode: this.hoaCommunityCode,
    phoneNumber: this.phoneNumber,
    propertyAddress: this.propertyAddress,
    propertyType: this.propertyType,
    hasVerifiedDocuments: !!(
      this.verificationDocuments?.homeOwnership?.filename ||
      this.verificationDocuments?.utilityBill?.filename ||
      this.verificationDocuments?.photoId?.filename
    )
  };
});

module.exports = mongoose.model('User', userSchema);
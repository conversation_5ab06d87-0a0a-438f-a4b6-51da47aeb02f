const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Maintenance Request Schema
 * This schema represents a maintenance request for a property within an HOA
 */
const maintenanceRequestSchema = new Schema({
  property: {
    type: Schema.Types.ObjectId,
    ref: 'Property',
    required: [true, 'Property ID is required']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  dateSubmitted: {
    type: Date,
    default: Date.now
  },
  resident: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Resident ID is required']
  },
  assignedTo: {
    type: String,
    trim: true,
    default: null
  },
  estimatedCompletion: {
    type: Date,
    default: null
  },
  notes: {
    type: String,
    trim: true
  },
  images: [{
    type: String,
    trim: true
  }],
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA',
    required: [true, 'HOA ID is required']
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create text indexes for searching
maintenanceRequestSchema.index({ 
  description: 'text', 
  notes: 'text',
  assignedTo: 'text'
});

const MaintenanceRequest = mongoose.model('MaintenanceRequest', maintenanceRequestSchema);

module.exports = MaintenanceRequest;

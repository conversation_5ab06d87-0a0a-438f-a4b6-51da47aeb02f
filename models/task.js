const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const taskSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  status: {
    type: String,
    enum: ['Not Started', 'In Progress', 'Completed', 'Closed'],
    default: 'Not Started',
    validate: {
      validator: async function(status) {
        // Skip validation for new documents
        if (this.isNew) return true;

        // For direct document operations
        if (this instanceof mongoose.Document) {
          const currentStatus = this._original?.status || this.status;
          const validTransitions = {
            'Not Started': ['In Progress', 'Closed'],
            'In Progress': ['Completed', 'Closed'],
            'Completed': ['Closed'],
            'Closed': [] // No transitions allowed from Closed
          };
          return validTransitions[currentStatus]?.includes(status) ?? false;
        }

        // For query operations (findOneAndUpdate)
        try {
          const doc = await mongoose.model('Task').findOne(this._conditions);
          if (!doc) return true;

          const validTransitions = {
            'Not Started': ['In Progress', 'Closed'],
            'In Progress': ['Completed', 'Closed'],
            'Completed': ['Closed'],
            'Closed': [] // No transitions allowed from Closed
          };
          return validTransitions[doc.status]?.includes(status) ?? false;
        } catch (err) {
          return false;
        }
      },
      message: props => `Invalid status transition to ${props.value}`
    }
  },
  priority: {
    type: String,
    enum: ['Low', 'Medium', 'High'],
    required: true
  },
  dueDate: {
    type: Date,
    required: true
  },
  budget: {
    type: Number,
    min: 0
  },
  createdBy: {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  },
  votes: {
    up: {
      type: Number,
      default: 0,
      min: 0
    },
    down: {
      type: Number,
      default: 0,
      min: 0
    },
    voters: [{
      userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      vote: {
        type: String,
        enum: ['up', 'down'],
        required: true
      },
      _id: false
    }]
  },
  comments: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    username: String,
    message: {
      type: String,
      required: true
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  progress: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  assignee: {
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    username: { type: String },
    email: { type: String }
  },
  permissions: {
    canView: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    canEdit: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    canDelete: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
  },
  dueSoonNotified: {
    type: Boolean,
    default: false,
  },
  isArchived: {
    type: Boolean,
    default: false
  },
  closedAt: {
    type: Date,
    default: null
  },
  closedBy: {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date
    }
  },
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA'
    // Not required by default to maintain backward compatibility with existing tasks
  },
  communityId: {
    type: Schema.Types.ObjectId,
    ref: 'Community'
    // Not required to allow HOA-wide tasks
  },
}, { timestamps: true });

// Pre-save middleware to store the original status for transition validation
taskSchema.pre('save', function(next) {
  if (this.isNew) {
    next();
    return;
  }

  this._original = {
    status: this.status
  };
  next();
});

// Pre-findOneAndUpdate middleware to validate status transitions
taskSchema.pre('findOneAndUpdate', async function(next) {
  const update = this.getUpdate();
  if (!update.$set?.status) {
    next();
    return;
  }

  const doc = await this.model.findOne(this.getQuery());
  if (!doc) {
    next();
    return;
  }

  const validTransitions = {
    'Not Started': ['In Progress', 'Closed'],
    'In Progress': ['Completed', 'Closed'],
    'Completed': ['Closed'],
    'Closed': [] // No transitions allowed from Closed
  };

  if (!validTransitions[doc.status]?.includes(update.$set.status)) {
    next(new Error('Invalid status transition'));
    return;
  }

  next();
});

// Index for efficient queries
taskSchema.index({ status: 1, createdAt: -1 });
taskSchema.index({ 'createdBy.userId': 1 });
taskSchema.index({ hoaId: 1 });

module.exports = mongoose.model('Task', taskSchema);

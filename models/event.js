const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const eventSchema = new Schema({
  title: {
    type: String,
    required: [true, 'Event title is required'],
    trim: true,
    minlength: [1, 'Title cannot be empty']
  },
  date: {
    type: String,
    required: [true, 'Event date is required'],
    validate: {
      validator: function(v) {
        // Validate ISO date string format
        return /^\d{4}-\d{2}-\d{2}$/.test(v);
      },
      message: props => `${props.value} is not a valid date format! Use YYYY-MM-DD`
    }
  },
  createdBy: {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    username: String
  },
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA'
    // Not required by default to maintain backward compatibility with existing events
  },
  communityId: {
    type: Schema.Types.ObjectId,
    ref: 'Community'
    // Not required to allow HOA-wide events
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index for efficient queries
eventSchema.index({ date: 1 });
eventSchema.index({ hoaId: 1 });

module.exports = mongoose.model('Event', eventSchema);

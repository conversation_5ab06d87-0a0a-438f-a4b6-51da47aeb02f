const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const paymentSchema = new mongoose.Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  hoaCommunityCode: {
    type: String,
    required: true
  },
  // HOA and Community references
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA'
  },
  communityId: {
    type: Schema.Types.ObjectId,
    ref: 'Community'
  },
  // Payment details
  amount: {
    type: Number,
    required: true
  },
  month: {
    type: Number,
    required: true,
    min: 1,
    max: 12
  },
  year: {
    type: Number,
    required: true
  },
  paymentDate: {
    type: Date,
    default: Date.now
  },
  dueDate: {
    type: Date
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'check', 'credit', 'debit', 'bank_transfer'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed'],
    default: 'completed'
  },
  // Payment type
  paymentType: {
    type: String,
    enum: ['dues', 'special_assessment', 'fee', 'other'],
    default: 'dues'
  },
  // Stripe payment details
  stripePaymentIntentId: String,
  stripeChargeId: String,
  // Additional info
  notes: String,
  receiptNumber: String
}, {
  timestamps: true
});

// Indexes for efficient queries
paymentSchema.index({ userId: 1, month: 1, year: 1 });
paymentSchema.index({ hoaId: 1 });
paymentSchema.index({ communityId: 1 });
paymentSchema.index({ paymentType: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ stripePaymentIntentId: 1 });

// Ensure one payment per user per month per payment type
paymentSchema.index(
  { userId: 1, month: 1, year: 1, paymentType: 1 },
  { unique: true }
);

const Payment = mongoose.model('Payment', paymentSchema);

module.exports = Payment;
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * HOA Entity Schema
 * This schema represents a Homeowners Association entity
 * It stores information about the HOA community and serves as a central
 * reference point for all HOA-related data
 */
const hoaSchema = new Schema({
  hoaCommunityName: {
    type: String,
    required: [true, 'HOA community name is required'],
    trim: true
  },
  hoaCommunityCode: {
    type: String,
    required: [true, 'HOA community code is required'],
    unique: true,
    trim: true,
    uppercase: true
  },
  hoaStreetAddress: {
    type: String,
    required: [true, 'HOA street address is required'],
    trim: true
  },
  hoaCity: {
    type: String,
    required: [true, 'HOA city is required'],
    trim: true
  },
  hoaState: {
    type: String,
    required: [true, 'HOA state is required'],
    trim: true
  },
  hoaZipCode: {
    type: String,
    required: [true, 'HOA zip code is required'],
    trim: true
  },
  // Contact information
  contactEmail: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  contactPhone: {
    type: String,
    trim: true
  },
  // Verification fields
  verificationStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  verificationDocuments: {
    registrationDoc: {
      filename: String,
      originalName: String,
      mimeType: String,
      uploadDate: {
        type: Date,
        default: Date.now
      },
      documentId: {
        type: Schema.Types.ObjectId,
        ref: 'Document'
      }
    },
    einDoc: {
      filename: String,
      originalName: String,
      mimeType: String,
      uploadDate: {
        type: Date,
        default: Date.now
      },
      documentId: {
        type: Schema.Types.ObjectId,
        ref: 'Document'
      }
    }
  },
  einNumber: {
    type: String,
    trim: true
  },
  verificationNotes: {
    type: String,
    trim: true
  },
  verifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  verifiedAt: {
    type: Date
  },
  // Payment activation token
  paymentToken: {
    type: String,
    trim: true
  },
  paymentTokenExpires: {
    type: Date
  },
  // Subscription information
  subscription: {
    tier: {
      type: String,
      enum: ['basic', 'standard', 'premium', 'enterprise'],
      default: 'basic'
    },
    unitCount: {
      type: Number,
      default: 0,
      min: 0
    },
    pricePerUnit: {
      type: Number,
      default: 0
    },
    totalPrice: {
      type: Number,
      default: 0
    },
    status: {
      type: String,
      enum: ['inactive', 'active', 'past_due', 'canceled', 'trialing', 'pending'],
      default: 'inactive'
    },
    stripeCustomerId: String,
    stripeSubscriptionId: String,
    stripeAccountId: String,
    currentPeriodStart: Date,
    currentPeriodEnd: Date,
    trialEnd: Date,
    cancelAtPeriodEnd: {
      type: Boolean,
      default: false
    }
  },
  // Stripe Connect information for HOA's own payment processing
  stripeConnectAccountId: {
    type: String,
    trim: true
  },
  stripeConnectStatus: {
    type: String,
    enum: ['not_started', 'pending', 'active', 'restricted', 'rejected'],
    default: 'not_started'
  },
  stripeConnectOnboardingCompleted: {
    type: Boolean,
    default: false
  },
  // Original payment info (maintained for backward compatibility)
  hoaPaymentInfo: {
    paymentMethod: {
      type: String,
      enum: ['check', 'bankTransfer', 'online', 'other'],
      default: 'check'
    },
    paymentInstructions: {
      type: String,
      trim: true
    },
    dueDate: {
      type: Number,
      min: 1,
      max: 31,
      default: 1
    },
    paymentAmount: {
      type: Number,
      min: 0
    }
  },

  // Email configuration for HOA-specific announcements
  emailConfig: {
    // Whether HOA has configured their own email
    configured: {
      type: Boolean,
      default: false
    },
    // Email provider (gmail, outlook, smtp)
    provider: {
      type: String,
      enum: ['gmail', 'outlook', 'smtp'],
      default: 'gmail'
    },
    // HOA's email address for sending announcements
    email: {
      type: String,
      trim: true,
      lowercase: true,
      sparse: true, // Allows multiple null values, but unique non-null values
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    // App password for the email (encrypted)
    password: {
      type: String,
      // This will be encrypted before storage
    },
    // Display name for emails (e.g., "Oak Street HOA")
    displayName: {
      type: String,
      trim: true,
      default: function() {
        return this.hoaCommunityName || 'HOA Administration';
      }
    },
    // SMTP configuration for custom email providers
    smtpConfig: {
      host: {
        type: String,
        trim: true
      },
      port: {
        type: Number,
        default: 587
      },
      secure: {
        type: Boolean,
        default: false
      }
    },
    // Email verification status
    verificationStatus: {
      type: String,
      enum: ['not_verified', 'verified', 'failed'],
      default: 'not_verified'
    },
    // Last time email configuration was verified
    lastVerified: {
      type: Date
    },
    // When email configuration was last updated
    configuredAt: {
      type: Date
    },
    // Who configured the email (admin user ID)
    configuredBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create a text index for searching
hoaSchema.index({
  hoaCommunityName: 'text',
  hoaCommunityCode: 'text',
  hoaStreetAddress: 'text',
  contactEmail: 'text',
  einNumber: 'text'
});

// Create indexes for efficient queries
hoaSchema.index({ verificationStatus: 1 });
hoaSchema.index({ 'subscription.status': 1 });
hoaSchema.index({ 'subscription.tier': 1 });

// Method to generate a unique HOA community code
hoaSchema.statics.generateHoaCommunityCode = async function() {
  // Generate a random 6-character alphanumeric code
  const generateCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < 6; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
  };

  // Keep generating codes until we find a unique one
  let code;
  let isUnique = false;
  while (!isUnique) {
    code = generateCode();
    // Check if this code already exists
    const existingHoa = await this.findOne({ hoaCommunityCode: code });
    if (!existingHoa) {
      isUnique = true;
    }
  }

  return code;
};

const HOA = mongoose.model('HOA', hoaSchema);

module.exports = HOA;

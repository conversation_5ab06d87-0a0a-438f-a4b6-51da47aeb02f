const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Vendor Payment Schema
 * Tracks payments made to vendors (grass guy, maintenance, etc.)
 */
const vendorPaymentSchema = new Schema({
  // HOA making the payment
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA',
    required: true
  },
  // User who initiated the payment (HOA admin)
  initiatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Vendor information
  vendorName: {
    type: String,
    required: true,
    trim: true
  },
  vendorEmail: {
    type: String,
    trim: true,
    lowercase: true
  },
  vendorPhone: {
    type: String,
    trim: true
  },
  // Payment details
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    enum: ['landscaping', 'maintenance', 'security', 'utilities', 'legal', 'insurance', 'other'],
    default: 'other'
  },
  // Payment status
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'refunded'],
    default: 'pending'
  },
  // Stripe information
  stripePaymentIntentId: {
    type: String,
    trim: true
  },
  stripeChargeId: {
    type: String,
    trim: true
  },
  // Payment method used
  paymentMethod: {
    type: String,
    enum: ['card', 'bank_transfer', 'check', 'cash'],
    default: 'card'
  },
  // Dates
  paymentDate: {
    type: Date,
    default: Date.now
  },
  dueDate: {
    type: Date
  },
  // Additional metadata
  invoiceNumber: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true
  },
  // Attachments (receipts, invoices, etc.)
  attachments: [{
    filename: String,
    originalName: String,
    mimeType: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Indexes for efficient queries
vendorPaymentSchema.index({ hoaId: 1, paymentDate: -1 });
vendorPaymentSchema.index({ status: 1 });
vendorPaymentSchema.index({ category: 1 });
vendorPaymentSchema.index({ vendorEmail: 1 });
vendorPaymentSchema.index({ stripePaymentIntentId: 1 });

// Virtual for formatted amount
vendorPaymentSchema.virtual('formattedAmount').get(function() {
  return `$${this.amount.toFixed(2)}`;
});

// Method to mark payment as completed
vendorPaymentSchema.methods.markCompleted = function(stripeChargeId) {
  this.status = 'completed';
  this.stripeChargeId = stripeChargeId;
  this.paymentDate = new Date();
  return this.save();
};

// Method to mark payment as failed
vendorPaymentSchema.methods.markFailed = function(reason) {
  this.status = 'failed';
  this.notes = this.notes ? `${this.notes}\nFailure reason: ${reason}` : `Failure reason: ${reason}`;
  return this.save();
};

const VendorPayment = mongoose.model('VendorPayment', vendorPaymentSchema);

module.exports = VendorPayment;

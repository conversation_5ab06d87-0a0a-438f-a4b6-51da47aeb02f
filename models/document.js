/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// models/document.js
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Standardized file reference schema
const fileSchema = new Schema({
  s3Key: {
    type: String,
    required: true,
    index: true
  },
  s3Bucket: {
    type: String,
    required: true,
    default: process.env.AWS_S3_BUCKET_NAME || 'hoaflo-files-prod'
  },
  originalName: {
    type: String,
    required: true
  },
  mimetype: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true,
    min: 0
  },
  encoding: {
    type: String
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

// Enhanced metadata schema
const metadataSchema = new Schema({
  virusScanStatus: {
    type: String,
    enum: ['pending', 'clean', 'infected', 'error'],
    default: 'pending'
  },
  virusScanDate: {
    type: Date
  },
  processingStatus: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  },
  tags: [{
    type: String,
    trim: true
  }],
  customFields: {
    type: Map,
    of: Schema.Types.Mixed
  }
}, { _id: false });

// Document version schema
const versionSchema = new Schema({
  version: {
    type: Number,
    required: true,
    default: 1
  },
  changeLog: {
    type: String,
    trim: true
  },
  changedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  changedAt: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

const documentSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },

  // Standardized file reference (new schema)
  file: fileSchema,

  // Legacy fields for backward compatibility
  fileName: {
    type: String
  },
  fileType: {
    type: String
  },
  fileSize: {
    type: Number
  },
  s3Key: {
    type: String
  },
  s3Bucket: {
    type: String
  },

  category: {
    type: String,
    enum: ['financial', 'property_information', 'miscellaneous', 'meetings', 'rules', 'receipts', 'bids', 'property'],
    default: 'miscellaneous',
    index: true
  },

  // Enhanced status management
  status: {
    type: String,
    enum: ['active', 'archived', 'deleted'],
    default: 'active',
    index: true
  },

  // Access control and associations
  uploadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  uploadedAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA',
    required: true,
    index: true
  },
  communityId: {
    type: Schema.Types.ObjectId,
    ref: 'Community',
    index: true
  },

  // Permissions
  isPublic: {
    type: Boolean,
    default: false,
    index: true
  },
  permissions: {
    read: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    write: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    delete: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }]
  },

  // Enhanced metadata and versioning
  metadata: metadataSchema,
  version: versionSchema,

  // Audit trail
  downloadCount: {
    type: Number,
    default: 0
  },
  lastDownloadedAt: {
    type: Date
  },
  lastDownloadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for efficient queries
documentSchema.index({ hoaId: 1, category: 1 });
documentSchema.index({ hoaId: 1, status: 1 });
documentSchema.index({ uploadedAt: -1 });
documentSchema.index({ isPublic: 1, status: 1 });
documentSchema.index({ 'file.s3Key': 1 });

// Virtual for backward compatibility (using different names to avoid conflicts)
documentSchema.virtual('currentFileName').get(function() {
  return this.file?.s3Key || this.fileName;
});

documentSchema.virtual('currentFileType').get(function() {
  return this.file?.mimetype || this.fileType;
});

documentSchema.virtual('currentFileSize').get(function() {
  return this.file?.size || this.fileSize;
});

// Virtual for formatted file size
documentSchema.virtual('formattedFileSize').get(function() {
  const size = this.file?.size || this.fileSize || 0;
  if (size === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(size) / Math.log(k));

  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Virtual for file extension
documentSchema.virtual('fileExtension').get(function() {
  const fileName = this.file?.originalName || this.fileName || '';
  const lastDot = fileName.lastIndexOf('.');
  return lastDot !== -1 ? fileName.substring(lastDot + 1).toLowerCase() : '';
});

// Pre-save middleware for validation and defaults
documentSchema.pre('save', function(next) {
  // Ensure HOA association for new documents
  if (this.isNew && !this.hoaId) {
    return next(new Error('HOA association is required for new documents'));
  }

  // Auto-increment version on updates
  if (!this.isNew && this.isModified() && !this.isModified('version.version')) {
    this.version.version += 1;
    this.version.changedAt = new Date();
  }

  // Validate file schema consistency
  if (this.file && this.file.s3Key) {
    // New schema - ensure required fields
    if (!this.file.originalName || !this.file.mimetype || !this.file.size) {
      return next(new Error('Incomplete file information in new schema'));
    }
  } else if (this.fileName && this.fileType && this.fileSize) {
    // Legacy schema - allow but warn
    console.warn(`Document ${this._id} using legacy file schema`);
  } else {
    return next(new Error('Either new file schema or legacy fields are required'));
  }

  next();
});

// Static method: Find documents by HOA with access control
documentSchema.statics.findByHOAWithAccess = function(hoaId, userId, userRole, filters = {}) {
  const baseQuery = { hoaId, status: 'active', ...filters };

  console.log('Document query - findByHOAWithAccess:', {
    hoaId,
    userId,
    userRole,
    filters,
    baseQuery
  });

  if (userRole === 'company_admin') {
    // Company admins can see all documents across all HOAs
    delete baseQuery.hoaId;
    console.log('Company admin query:', baseQuery);
    return this.find(baseQuery).populate(['uploadedBy', 'hoaId', 'communityId']);
  } else if (userRole === 'admin') {
    // HOA admins can see all documents in their HOA
    console.log('HOA admin query:', baseQuery);
    return this.find(baseQuery).populate(['uploadedBy', 'communityId']);
  } else {
    // Regular users can only see public documents in their HOA
    baseQuery.isPublic = true;
    console.log('Regular user query:', baseQuery);
    return this.find(baseQuery).populate(['uploadedBy', 'communityId']);
  }
};

// Static method: Find all documents with access control
documentSchema.statics.findAllWithAccess = function(userId, userRole, filters = {}) {
  const baseQuery = { status: 'active', ...filters };

  if (userRole === 'company_admin') {
    // Company admins can see all documents
    return this.find(baseQuery).populate(['uploadedBy', 'hoaId', 'communityId']);
  } else {
    // Others are restricted to their HOA scope
    throw new Error('Use findByHOAWithAccess for non-company admin users');
  }
};

// Instance method: Check if user can access this document
documentSchema.methods.canUserAccess = function(userId, userRole, userHoaId) {
  // Company admins can access all documents
  if (userRole === 'company_admin') {
    return true;
  }

  // Handle null/undefined cases
  if (!this.hoaId || !userHoaId) {
    return false;
  }

  // Handle case where userHoaId might be an array (users can belong to multiple HOAs)
  const userHoaIds = Array.isArray(userHoaId) ? userHoaId : [userHoaId];
  const documentHoaIdString = this.hoaId.toString();

  // Check if document's HOA matches any of the user's HOAs
  const hasHoaAccess = userHoaIds.some(hoaId =>
    hoaId && hoaId.toString() === documentHoaIdString
  );

  if (!hasHoaAccess) {
    return false;
  }

  // HOA admins can access all documents in their HOA
  if (userRole === 'admin') {
    return true;
  }

  // Regular users can only access public documents
  return this.isPublic;
};

// Instance method: Soft delete
documentSchema.methods.softDelete = function(deletedBy) {
  this.status = 'deleted';
  this.version.version += 1;
  this.version.changeLog = 'Document deleted';
  this.version.changedBy = deletedBy;
  this.version.changedAt = new Date();
  return this.save();
};

// Instance method: Archive document
documentSchema.methods.archive = function(archivedBy, reason) {
  this.status = 'archived';
  this.version.version += 1;
  this.version.changeLog = reason || 'Document archived';
  this.version.changedBy = archivedBy;
  this.version.changedAt = new Date();
  return this.save();
};

// Instance method: Track download
documentSchema.methods.trackDownload = function(downloadedBy) {
  this.downloadCount += 1;
  this.lastDownloadedAt = new Date();
  this.lastDownloadedBy = downloadedBy;
  return this.save();
};

module.exports = mongoose.model('Document', documentSchema);

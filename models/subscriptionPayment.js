const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Subscription Payment Schema
 * This schema represents a payment for an HOA subscription
 * It tracks individual payments made for subscriptions
 */
const subscriptionPaymentSchema = new Schema({
  // Reference to the HOA and subscription
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA',
    required: true
  },
  subscriptionId: {
    type: Schema.Types.ObjectId,
    ref: 'Subscription',
    required: true
  },
  // Payment details
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'usd'
  },
  status: {
    type: String,
    enum: ['pending', 'succeeded', 'failed', 'refunded'],
    default: 'pending'
  },
  // Billing period
  billingPeriodStart: {
    type: Date,
    required: true
  },
  billingPeriodEnd: {
    type: Date,
    required: true
  },
  // Stripe details
  stripeInvoiceId: String,
  stripePaymentIntentId: String,
  stripeChargeId: String,
  // Receipt
  receiptUrl: String,
  receiptNumber: String,
  // Refund details
  refundedAt: Date,
  refundAmount: Number,
  refundReason: String,
  // Metadata
  metadata: {
    type: Map,
    of: String
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
subscriptionPaymentSchema.index({ hoaId: 1 });
subscriptionPaymentSchema.index({ subscriptionId: 1 });
subscriptionPaymentSchema.index({ status: 1 });
subscriptionPaymentSchema.index({ stripeInvoiceId: 1 });
subscriptionPaymentSchema.index({ stripePaymentIntentId: 1 });
subscriptionPaymentSchema.index({ billingPeriodStart: 1, billingPeriodEnd: 1 });

const SubscriptionPayment = mongoose.model('SubscriptionPayment', subscriptionPaymentSchema);

module.exports = SubscriptionPayment;

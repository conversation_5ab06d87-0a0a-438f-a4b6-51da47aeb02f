/**
 * Street Harmony HOA Management System
 * Budget Model
 */
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Budget Line Item Schema
 * Represents a single line item in a budget
 */
const budgetLineItemSchema = new Schema({
  // Line item details
  category: {
    type: String,
    required: [true, 'Category is required'],
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  type: {
    type: String,
    enum: ['income', 'expense'],
    required: [true, 'Type is required']
  },
  // Budget amounts
  budgetedAmount: {
    type: Number,
    required: [true, 'Budgeted amount is required'],
    min: 0
  },
  // Actual amounts (updated as transactions occur)
  actualAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  // Variance (calculated field)
  variance: {
    type: Number,
    default: 0
  },
  // Notes
  notes: {
    type: String,
    trim: true
  }
}, { _id: true });

/**
 * Budget Schema
 * Represents a budget for an HOA for a specific year
 */
const budgetSchema = new Schema({
  // Budget identification
  name: {
    type: String,
    required: [true, 'Budget name is required'],
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  // Time period
  year: {
    type: Number,
    required: [true, 'Budget year is required'],
    min: 2000,
    max: 2100
  },
  // Budget status
  status: {
    type: String,
    enum: ['draft', 'active', 'archived'],
    default: 'draft'
  },
  // References
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA',
    required: [true, 'HOA ID is required']
  },
  communityId: {
    type: Schema.Types.ObjectId,
    ref: 'Community'
    // Not required to allow HOA-wide budgets
  },
  // Created by
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Creator ID is required']
  },
  // Last updated by
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  // Budget line items
  lineItems: [budgetLineItemSchema],
  // Budget totals (calculated fields)
  totalBudgetedIncome: {
    type: Number,
    default: 0
  },
  totalBudgetedExpenses: {
    type: Number,
    default: 0
  },
  totalActualIncome: {
    type: Number,
    default: 0
  },
  totalActualExpenses: {
    type: Number,
    default: 0
  },
  // Notes
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Pre-save middleware to calculate totals
budgetSchema.pre('save', function(next) {
  // Calculate totals
  this.totalBudgetedIncome = this.lineItems
    .filter(item => item.type === 'income')
    .reduce((sum, item) => sum + item.budgetedAmount, 0);
  
  this.totalBudgetedExpenses = this.lineItems
    .filter(item => item.type === 'expense')
    .reduce((sum, item) => sum + item.budgetedAmount, 0);
  
  this.totalActualIncome = this.lineItems
    .filter(item => item.type === 'income')
    .reduce((sum, item) => sum + item.actualAmount, 0);
  
  this.totalActualExpenses = this.lineItems
    .filter(item => item.type === 'expense')
    .reduce((sum, item) => sum + item.actualAmount, 0);
  
  // Calculate variance for each line item
  this.lineItems.forEach(item => {
    item.variance = item.actualAmount - item.budgetedAmount;
  });
  
  next();
});

// Indexes for efficient queries
budgetSchema.index({ hoaId: 1, year: 1 });
budgetSchema.index({ communityId: 1 });
budgetSchema.index({ status: 1 });
budgetSchema.index({ createdBy: 1 });
budgetSchema.index({ 'lineItems.category': 1 });
budgetSchema.index({ 'lineItems.type': 1 });

const Budget = mongoose.model('Budget', budgetSchema);

module.exports = Budget;

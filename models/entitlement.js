const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Entitlement Entity Schema
 * This schema represents an Entitlement entity
 * It stores information about the Entitlement and serves as a central
 * reference point for all Entitlement-related data
 */
const entitlementSchema = new Schema({
    role: {
        type: String,
        required: [true, 'Role is required'],
        trim: true
    },
    features: [
        {
            type: String,
            enum: {
                values: [
                    'Finances', 
                    'Make Payment', 
                    'Members', 
                    'Tasks',
                    'Calendar',
                    'Annoucements', 
                    'Events', 
                    'Documents', 
                    'Admin', 
                    'Property Management', 
                    'Approval', 
                    'Admin Managements',
                    'HOA Office',
                    'HOA Registration',
                    'Master'
                ],
            },
            required: true
        }
    ]
});

module.exports = mongoose.model('Entitlement', entitlementSchema);
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Subscription Schema
 * This schema represents a subscription for an HOA
 * It tracks payment history and subscription status
 */
const subscriptionSchema = new Schema({
  // Reference to the HOA
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA',
    required: true
  },
  // Subscription details
  tier: {
    type: String,
    enum: ['basic', 'standard', 'premium', 'enterprise'],
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'past_due', 'canceled', 'incomplete', 'incomplete_expired', 'trialing', 'unpaid'],
    default: 'active'
  },
  // Billing details
  unitCount: {
    type: Number,
    required: true,
    min: 0
  },
  pricePerUnit: {
    type: Number,
    required: true,
    min: 0
  },
  totalPrice: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'usd'
  },
  // Billing cycle
  billingCycle: {
    type: String,
    enum: ['monthly', 'quarterly', 'annual'],
    default: 'monthly'
  },
   // Payment activation token
  paymentToken: {
    type: String,
    trim: true
  },
  paymentTokenExpires: {
    type: Date
  },
  currentPeriodStart: {
    type: Date,
    required: true
  },
  currentPeriodEnd: {
    type: Date,
    required: true
  },
  // Stripe details
  stripeCustomerId: {
    type: String
  },
  stripeSubscriptionId: {
    type: String
  },
  stripePaymentMethodId: {
    type: String
  },
  stripeAccountId: String,
  // Cancellation details
  cancelAtPeriodEnd: {
    type: Boolean,
    default: false
  },
  canceledAt: {
    type: Date
  },
  cancelReason: {
    type: String
  },
  // Metadata
  metadata: {
    type: Map,
    of: String
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
subscriptionSchema.index({ hoaId: 1 }, { unique: true });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ tier: 1 });
subscriptionSchema.index({ stripeSubscriptionId: 1 });
subscriptionSchema.index({ stripeCustomerId: 1 });

const Subscription = mongoose.model('Subscription', subscriptionSchema);

module.exports = Subscription;

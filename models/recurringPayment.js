const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Recurring Payment Schema
 * Manages automated recurring payments to vendors (grass guy, etc.)
 */
const recurringPaymentSchema = new Schema({
  // HOA making the payment
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA',
    required: true
  },
  // User who set up the recurring payment
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Vendor information
  vendor: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      trim: true,
      lowercase: true
    },
    phone: {
      type: String,
      trim: true
    },
    // Bank account info for payouts
    bankAccount: {
      accountNumber: String, // Encrypted
      routingNumber: String,
      accountHolderName: String,
      bankName: String
    },
    // Stripe Connect account (if vendor has one)
    stripeAccountId: String
  },
  // Payment details
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    enum: ['landscaping', 'maintenance', 'security', 'utilities', 'legal', 'insurance', 'other'],
    default: 'landscaping'
  },
  // Recurring schedule
  schedule: {
    frequency: {
      type: String,
      enum: ['weekly', 'biweekly', 'monthly', 'quarterly'],
      required: true
    },
    dayOfWeek: {
      type: Number, // 0 = Sunday, 1 = Monday, etc.
      min: 0,
      max: 6
    },
    dayOfMonth: {
      type: Number, // For monthly payments
      min: 1,
      max: 31
    },
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date // Optional - if not set, runs indefinitely
    }
  },
  // Payment status
  status: {
    type: String,
    enum: ['active', 'paused', 'cancelled', 'completed'],
    default: 'active'
  },
  // Payment method
  paymentMethod: {
    type: String,
    enum: ['stripe_transfer', 'bank_payout', 'check'],
    default: 'bank_payout'
  },
  // Next payment date (calculated)
  nextPaymentDate: {
    type: Date,
    required: true
  },
  // Payment history reference
  lastPaymentId: {
    type: Schema.Types.ObjectId,
    ref: 'VendorPayment'
  },
  // Failure handling
  failureCount: {
    type: Number,
    default: 0
  },
  maxFailures: {
    type: Number,
    default: 3
  },
  // Notifications
  notifyVendor: {
    type: Boolean,
    default: true
  },
  notifyHOA: {
    type: Boolean,
    default: true
  },
  // Additional settings
  notes: {
    type: String,
    trim: true
  },
  contractReference: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Indexes
recurringPaymentSchema.index({ hoaId: 1, status: 1 });
recurringPaymentSchema.index({ nextPaymentDate: 1, status: 1 });
recurringPaymentSchema.index({ 'vendor.email': 1 });
recurringPaymentSchema.index({ category: 1 });

// Method to calculate next payment date
recurringPaymentSchema.methods.calculateNextPaymentDate = function() {
  const current = this.nextPaymentDate || this.schedule.startDate;
  let next = new Date(current);

  switch (this.schedule.frequency) {
    case 'weekly':
      next.setDate(next.getDate() + 7);
      break;
    case 'biweekly':
      next.setDate(next.getDate() + 14);
      break;
    case 'monthly':
      next.setMonth(next.getMonth() + 1);
      if (this.schedule.dayOfMonth) {
        next.setDate(this.schedule.dayOfMonth);
      }
      break;
    case 'quarterly':
      next.setMonth(next.getMonth() + 3);
      break;
  }

  this.nextPaymentDate = next;
  return next;
};

// Method to check if payment is due
recurringPaymentSchema.methods.isDue = function() {
  const now = new Date();
  return this.status === 'active' && this.nextPaymentDate <= now;
};

// Method to pause recurring payment
recurringPaymentSchema.methods.pause = function() {
  this.status = 'paused';
  return this.save();
};

// Method to resume recurring payment
recurringPaymentSchema.methods.resume = function() {
  this.status = 'active';
  return this.save();
};

// Method to cancel recurring payment
recurringPaymentSchema.methods.cancel = function() {
  this.status = 'cancelled';
  return this.save();
};

const RecurringPayment = mongoose.model('RecurringPayment', recurringPaymentSchema);

module.exports = RecurringPayment;

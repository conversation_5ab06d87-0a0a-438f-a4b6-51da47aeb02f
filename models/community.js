const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Community Schema
 * This schema represents a community or street within an HOA
 * Each community has its own unique registration code for users
 */
const communitySchema = new Schema({
  name: {
    type: String,
    required: [true, 'Community name is required'],
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  streetAddress: {
    type: String,
    trim: true
  },
  city: {
    type: String,
    trim: true
  },
  state: {
    type: String,
    trim: true
  },
  zipCode: {
    type: String,
    trim: true
  },
  // The unique code users will use to register for this community
  communityCode: {
    type: String,
    required: [true, 'Community code is required'],
    unique: true,
    trim: true,
    uppercase: true
  },
  // Reference to the parent HOA
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA',
    required: [true, 'HOA ID is required']
  },
  // Community type
  type: {
    type: String,
    enum: ['single-family', 'townhouse', 'condo', 'apartment', 'mixed', 'other'],
    default: 'single-family'
  },
  // Number of units/homes in this community
  unitCount: {
    type: Number,
    default: 0,
    min: 0
  },
  // Community-specific rules or notes
  rules: {
    type: String,
    trim: true
  },
  // Status of the community
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  // Optional document for community-specific rules
  rulesDocument: {
    filename: String,
    originalName: String,
    mimeType: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  },
  // Visibility and access settings
  visibility: {
    type: String,
    enum: ['public', 'private', 'hoa_only'],
    default: 'private',
    description: {
      public: 'Visible to all users in the HOA',
      private: 'Visible only to members of this community and HOA admins',
      hoa_only: 'Visible only to HOA admins'
    }
  },
  // Permissions for this community
  permissions: {
    // Users who can manage this community (in addition to HOA admins)
    managers: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    // Whether members can see other members in this community
    memberVisibility: {
      type: Boolean,
      default: true
    },
    // Whether members can see finances for this community
    financeVisibility: {
      type: Boolean,
      default: false
    },
    // Whether members can see documents for this community
    documentVisibility: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true
});

// Create text index for searching
communitySchema.index({
  name: 'text',
  communityCode: 'text',
  streetAddress: 'text'
});

// Create indexes for efficient queries
communitySchema.index({ hoaId: 1 });
communitySchema.index({ status: 1 });
communitySchema.index({ communityCode: 1 }, { unique: true });

// Method to generate a unique community code
communitySchema.statics.generateCommunityCode = async function(hoaCode) {
  // Generate a random 4-character alphanumeric code and append to HOA code
  const generateCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = hoaCode ? `${hoaCode}-` : '';
    for (let i = 0; i < 4; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
  };

  // Keep generating codes until we find a unique one
  let code;
  let isUnique = false;
  while (!isUnique) {
    code = generateCode();
    // Check if this code already exists
    const existingCommunity = await this.findOne({ communityCode: code });
    if (!existingCommunity) {
      isUnique = true;
    }
  }

  return code;
};

const Community = mongoose.model('Community', communitySchema);

module.exports = Community;

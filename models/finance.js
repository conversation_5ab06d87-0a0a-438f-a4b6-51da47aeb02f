const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const financeSchema = new Schema({
    type: {
      type: String,
      enum: ['income', 'expense'],
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    note: {
      type: String,
    },
    status: {
        type: String,
        enum: ['active', 'archived'],
        default: 'active'
    },
    // References
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    hoaCommunityCode: {
        type: String,
        required: true,
        // Not required by default to maintain backward compatibility with existing finance records
    },
    communityId: {
        type: Schema.Types.ObjectId,
        ref: 'Community'
        // Not required to allow HOA-wide finances
    },
    // Related payment or subscription payment (if applicable)
    relatedPaymentId: {
        type: Schema.Types.ObjectId,
        refPath: 'relatedPaymentModel'
    },
    relatedPaymentModel: {
        type: String,
        enum: ['Payment', 'SubscriptionPayment']
    },
    // Additional metadata
    paymentMethod: {
        type: String,
        enum: ['cash', 'check', 'credit', 'debit', 'bank_transfer', 'other'],
    },
    receiptNumber: String,
    dueDate: Date,
    paidDate: Date
  }, {
    timestamps: true // ✅ Adds createdAt & updatedAt automatically
  });

// Indexes for efficient queries
financeSchema.index({ type: 1 });
financeSchema.index({ category: 1 });
financeSchema.index({ hoaId: 1 });
financeSchema.index({ communityId: 1 });
financeSchema.index({ createdBy: 1 });
financeSchema.index({ relatedPaymentId: 1 });
financeSchema.index({ dueDate: 1 });
financeSchema.index({ paidDate: 1 });

module.exports = mongoose.model('Finance', financeSchema);

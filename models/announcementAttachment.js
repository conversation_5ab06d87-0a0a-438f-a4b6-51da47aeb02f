/**
 * Street Harmony HOA Management System
 *
 * Copyright (c) 2025 Pelican App Solutions L.L.C. All rights reserved.
 *
 * This software and associated documentation files are the proprietary property
 * of Pelican App Solutions L.L.C., registered in Louisiana, USA. Unauthorized copying,
 * distribution, modification, public display, or public performance of this proprietary
 * software is strictly prohibited. All other rights reserved.
 *
 * Violators will be prosecuted to the fullest extent of the law.
 */

// models/announcementAttachment.js
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Standardized file reference schema (consistent with Document model)
const fileSchema = new Schema({
  s3Key: {
    type: String,
    required: true,
    index: true
  },
  s3Bucket: {
    type: String,
    required: true,
    default: process.env.AWS_S3_BUCKET_NAME || 'hoaflo-files-prod'
  },
  originalName: {
    type: String,
    required: true
  },
  mimetype: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true,
    min: 0
  },
  encoding: {
    type: String
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

const announcementAttachmentSchema = new Schema({
  // File information using standardized schema
  file: {
    type: fileSchema,
    required: true
  },

  // Announcement association
  announcementId: {
    type: Schema.Types.ObjectId,
    ref: 'Announcement',
    required: true,
    index: true
  },

  // HOA isolation and access control
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA',
    required: true,
    index: true
  },

  // Upload tracking
  uploadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },

  // Status management
  status: {
    type: String,
    enum: ['active', 'deleted'],
    default: 'active',
    index: true
  },

  // Download tracking
  downloadCount: {
    type: Number,
    default: 0
  },
  lastDownloadedAt: {
    type: Date
  },
  lastDownloadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },

  // Metadata
  description: {
    type: String,
    trim: true,
    maxlength: 500
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for efficient queries
announcementAttachmentSchema.index({ hoaId: 1, announcementId: 1 });
announcementAttachmentSchema.index({ hoaId: 1, status: 1 });
announcementAttachmentSchema.index({ uploadedBy: 1, createdAt: -1 });

// Virtual for formatted file size
announcementAttachmentSchema.virtual('formattedFileSize').get(function() {
  const size = this.file?.size || 0;
  if (size === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(size) / Math.log(k));

  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Virtual for file extension
announcementAttachmentSchema.virtual('fileExtension').get(function() {
  const fileName = this.file?.originalName || '';
  const lastDot = fileName.lastIndexOf('.');
  return lastDot !== -1 ? fileName.substring(lastDot + 1).toLowerCase() : '';
});

// Pre-save middleware
announcementAttachmentSchema.pre('save', function(next) {
  // Ensure HOA association for new attachments
  if (this.isNew && !this.hoaId) {
    return next(new Error('HOA association is required for announcement attachments'));
  }

  // Validate file information
  if (!this.file || !this.file.s3Key || !this.file.originalName) {
    return next(new Error('Complete file information is required'));
  }

  next();
});

// Static method: Find attachments by announcement with access control
announcementAttachmentSchema.statics.findByAnnouncementWithAccess = function(announcementId, userId, userRole, userHoaId) {
  const baseQuery = {
    announcementId,
    status: 'active'
  };

  if (userRole === 'company_admin') {
    // Company admins can see all attachments
    return this.find(baseQuery).populate(['uploadedBy', 'hoaId']);
  } else {
    // Others must be in the same HOA
    baseQuery.hoaId = userHoaId;
    return this.find(baseQuery).populate(['uploadedBy']);
  }
};

// Static method: Find attachments by HOA with access control
announcementAttachmentSchema.statics.findByHOAWithAccess = function(hoaId, userId, userRole) {
  const baseQuery = {
    hoaId,
    status: 'active'
  };

  if (userRole === 'company_admin') {
    // Company admins can see all attachments across all HOAs
    delete baseQuery.hoaId;
    return this.find(baseQuery).populate(['uploadedBy', 'hoaId', 'announcementId']);
  } else if (userRole === 'admin') {
    // HOA admins can see all attachments in their HOA
    return this.find(baseQuery).populate(['uploadedBy', 'announcementId']);
  } else {
    // Regular users can see attachments for announcements they have access to
    return this.find(baseQuery).populate(['uploadedBy', 'announcementId']);
  }
};

// Instance method: Check if user can access this attachment
announcementAttachmentSchema.methods.canUserAccess = function(userId, userRole, userHoaId) {
  // Company admins can access all attachments
  if (userRole === 'company_admin') {
    return true;
  }

  // Attachment must be in user's HOA
  if (this.hoaId.toString() !== userHoaId.toString()) {
    return false;
  }

  // HOA admins and regular users can access attachments in their HOA
  // (Additional announcement-level access control should be checked separately)
  return true;
};

// Instance method: Soft delete
announcementAttachmentSchema.methods.softDelete = function() {
  this.status = 'deleted';
  return this.save();
};

// Instance method: Track download
announcementAttachmentSchema.methods.trackDownload = function(downloadedBy) {
  this.downloadCount += 1;
  this.lastDownloadedAt = new Date();
  this.lastDownloadedBy = downloadedBy;
  return this.save();
};

module.exports = mongoose.model('AnnouncementAttachment', announcementAttachmentSchema);

const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['task', 'system', 'user', 'event', 'payment', 'alert', 'info', 'announcement']
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: 'bell'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed
  },
  hoaId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'HOA'
    // Not required to maintain backward compatibility with existing notifications
  },
  communityId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Community'
    // Not required to allow HOA-wide notifications
  },
  recipients: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    read: {
      type: Boolean,
      default: false
    },
    readAt: {
      type: Date,
      default: null
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Index for efficient queries
notificationSchema.index({ createdAt: -1 });
notificationSchema.index({ type: 1, createdAt: -1 });
notificationSchema.index({ 'recipients.userId': 1, createdAt: -1 });
notificationSchema.index({ hoaId: 1, createdAt: -1 });
notificationSchema.index({ communityId: 1, createdAt: -1 });
notificationSchema.index({ hoaId: 1, communityId: 1, createdAt: -1 });

module.exports = mongoose.model('Notification', notificationSchema);
const mongoose = require('mongoose');

const dueDateSchema = new mongoose.Schema({
  day: {
    type: Number,
    required: true,
    min: 1,
    max: 31,
    default: 15
  },
  reminderDays: {
    type: Number,
    required: true,
    min: 1,
    max: 14,
    default: 7
  },
  gracePeriod: {
    type: Number,
    required: true,
    min: 0,
    max: 14,
    default: 3
  }
});

const settingsSchema = new mongoose.Schema({
  dueDate: {
    type: dueDateSchema,
    required: true,
    default: () => ({
      day: 15,
      reminderDays: 7,
      gracePeriod: 3
    })
  },
  lastModifiedBy: {
    type: String,
    required: true,
    default: 'System'
  },
  lastModifiedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('Settings', settingsSchema); 
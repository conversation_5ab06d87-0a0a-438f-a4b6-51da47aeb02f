const { required } = require('joi');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Property Schema
 * This schema represents a property within an HOA
 */
const propertySchema = new Schema({
  address: {
    type: String,
    required: [true, 'Property address is required'],
    trim: true
  },
  type: {
    type: String,
    required: [true, 'Property type is required'],
    enum: ['Single Family', 'Townhouse', 'Condo', 'Apartment', 'Other'],
    default: 'Single Family'
  },
  status: {
    type: String,
    enum: ['occupied', 'vacant', 'maintenance'],
    default: 'vacant'
  },
  resident: {
    _id: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    fullName: {
      type: String,
      required: true,
    }
  },
  nextInspection: {
    type: Date,
    default: null
  },
  yearBuilt: {
    type: String,
    trim: true
  },
  squareFeet: {
    type: Number,
    min: 0
  },
  bedrooms: {
    type: Number,
    min: 0
  },
  bathrooms: {
    type: Number,
    min: 0
  },
  amenities: [{
    type: String,
    trim: true
  }],
  documents: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    fileType: {
      type: String,
      trim: true
    },
    fileUrl: {
      type: String,
      trim: true
    },
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  maintenanceHistory: [{
    description: {
      type: String,
      required: true,
      trim: true
    },
    status: {
      type: String,
      enum: ['pending', 'in_progress', 'completed'],
      default: 'pending'
    },
    cost: {
      type: Number,
      min: 0,
      default: 0
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  hoaId: {
    type: Schema.Types.ObjectId,
    ref: 'HOA',
    required: [true, 'HOA ID is required']
  },
  communityId: {
    type: Schema.Types.ObjectId,
    ref: 'Community',
    // Not required to maintain backward compatibility with existing properties
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Creator ID is required']
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create text indexes for searching
propertySchema.index({
  address: 'text',
  type: 'text',
  yearBuilt: 'text'
});

const Property = mongoose.model('Property', propertySchema);

module.exports = Property;

{"name": "street-harmony-api", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "NODE_ENV=production node server.js", "dev": "NODE_ENV=development nodemon server.js", "dev:prod": "NODE_ENV=production nodemon server.js", "generate-jwt": "node scripts/utilities/generate-jwt-secret.js", "local:mongodb": "cp .env.local.mongodb .env.local.active && NODE_ENV=development nodemon server.js", "local:production": "cp .env.local.production .env.local.active && NODE_ENV=development nodemon server.js", "local:seed": "cp .env.local.production .env.local.active && node scripts/utilities/seedLocalDatabase.js", "local:test": "node scripts/testing/testLocalSetup.js", "local:setup": "npm run local:seed && npm run local:test", "local:start": "node scripts/utilities/startLocal.js", "local:quick": "npm run local:production", "local:dev": "cp .env.local .env.local.active && NODE_ENV=development nodemon server.js", "local:check": "node scripts/testing/quickTest.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.0.0", "@aws-sdk/s3-request-presigner": "^3.0.0", "@azure/msal-node": "3.6.3", "@microsoft/microsoft-graph-client": "3.0.7", "@sendgrid/mail": "8.1.5", "axios": "1.10.0", "bcryptjs": "^3.0.2", "cors": "2.8.5", "dotenv": "^16.4.7", "express": "4.17.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.1", "multer": "^1.4.5-lts.2", "multer-s3": "^3.0.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.9", "plaid": "33.0.0", "socket.io": "^4.8.1", "stripe": "^14.22.0"}, "devDependencies": {"nodemon": "^3.1.9"}, "engines": {"node": "22.x"}}
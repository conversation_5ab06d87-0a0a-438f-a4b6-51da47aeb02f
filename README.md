# HOAFLO - HOA Management Application

## 🏘️ Project Overview

**HOAFLO** is a comprehensive HOA (Homeowners Association) management platform that streamlines community administration, financial management, and resident communication.

**Live URL**: https://www.hoaflo.com

## ✨ Latest Features (Updated: July 14, 2025)

### 🎯 Enhanced Announcement System
- **Role-based targeting**: HOA admins can target specific streets, Company admins can target entire HOAs
- **File attachments**: Support for images, PDFs, and documents via AWS S3 integration
- **Smart recipient selection**: Street dropdown → Resident checkboxes with "Select All" functionality
- **Professional email delivery**: <NAME_EMAIL> with attachment links
- **Real email delivery**: Azure OAuth2 integration for actual email sending (no more simulation)

### 📧 Advanced Email System
- **Azure OAuth2 Integration**: Professional email delivery via Microsoft Graph API
- **Outlook Business integration**: Professional email <NAME_EMAIL>
- **Application permissions**: Mail.Send permissions for service account authentication
- **Production ready**: Successfully tested with real email delivery including attachments

### 🎨 Streamlined UI/UX (Latest)
- **Clean HOA Admin Navigation**: Hidden duplicate "Announcements" menu for HOA admins
- **Single Announcement Interface**: HOA admins now only see "Admin Announcements" for both viewing and creating
- **Improved User Experience**: Eliminated UI confusion while preserving all functionality
- **Smart Navigation**: Cancel buttons now navigate appropriately based on user role

### 📁 Streamlined Document Management
- **Consolidated receipt management**: All financial documents now managed under Finances section only
- **Removed duplicate functionality**: Eliminated separate Receipts tab from Documents
- **AWS S3 cloud storage**: Secure, scalable file storage with signed URL access

### 🔧 Technical Improvements
- **Files Modified**:
  - `src/components/Sidebar.tsx`: Conditional rendering of Announcements menu for HOA admins
  - `src/pages/AdminAnnouncements.tsx`: Updated cancel button navigation
- **UI/UX Impact**:
  - Cleaner navigation for HOA admins
  - No functional loss - all announcement features remain accessible
  - Better user experience with streamlined menu structure

## 🛠️ Development

**Use your preferred IDE**

Clone this repo and push changes. All changes are automatically deployed to Vercel.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/f99f78ec-9ab4-4e51-b3f4-bc3dbc64ef12) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes it is!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
# hoa-frontend
# hoa-frontend
=======
>>>>>>> 6d6febac2c8fd3e7442867de5cd62bd1eb3417e4
# HOA-FRONT
# S3 Integration Ready - Thu Jul 10 20:44:28 CDT 2025

## 🚀 S3 Cloud Storage Integration Complete!

✅ Files now upload directly to AWS S3 cloud storage
✅ Secure signed URLs for downloads
✅ Organized by HOA hierarchy: `hoa-{hoaId}/documents/`
✅ No more Heroku file persistence issues

**Ready for live testing!** 🎯

Last updated: $(date)

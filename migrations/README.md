# HOAFLO Database Migrations

This directory contains database migration scripts for the HOAFLO application.

## Receipt Documents Migration

### Overview
The `migrate-receipt-documents.js` script migrates all documents with category 'receipts' to 'financial' category. This consolidates receipt management under the Finances section and eliminates duplicate functionality.

### When to Run
Run this migration **before** deploying the code changes that remove the receipts category from the document model.

### How to Run

#### Production Environment:
```bash
cd HOA-BACKEND
NODE_ENV=production node migrations/migrate-receipt-documents.js
```

#### Development Environment:
```bash
cd HOA-BACKEND
NODE_ENV=development node migrations/migrate-receipt-documents.js
```

### What the Migration Does

1. **Finds all documents** with `category: 'receipts'`
2. **Updates their category** to `'financial'`
3. **Adds migration metadata** to track the change:
   - `migratedFromReceipts: true`
   - `migrationDate: [current date]`
4. **Logs the results** for verification

### Safety Features

- **Read-only check first**: Counts documents before making changes
- **Detailed logging**: Shows exactly what documents are being migrated
- **Metadata tracking**: Adds fields to track the migration
- **Error handling**: Stops on any errors and provides clear messages

### Expected Results

After running the migration:
- ✅ All receipt documents will be categorized as 'financial'
- ✅ Receipt documents will be accessible in the Finances section
- ✅ No data loss - all document content and metadata preserved
- ✅ Migration is tracked with metadata fields

### Verification

After migration, you can verify the results by:

1. **Check document counts**:
   ```javascript
   // In MongoDB shell or application
   db.documents.countDocuments({ category: 'receipts' }) // Should be 0
   db.documents.countDocuments({ category: 'financial', migratedFromReceipts: true }) // Shows migrated count
   ```

2. **View migrated documents**:
   ```javascript
   db.documents.find({ migratedFromReceipts: true })
   ```

### Rollback (if needed)

If you need to rollback the migration:
```javascript
// In MongoDB shell
db.documents.updateMany(
  { migratedFromReceipts: true },
  { 
    $set: { category: 'receipts' },
    $unset: { migratedFromReceipts: 1, migrationDate: 1 }
  }
)
```

## Notes

- This migration is **idempotent** - safe to run multiple times
- The migration preserves all existing document data
- Receipt documents will still be fully accessible, just under the Financial category
- The Finances section already handles financial documents appropriately

/**
 * Migration Script: Convert Receipt Documents to Financial Category
 * 
 * This script migrates all documents with category 'receipts' to 'financial'
 * to consolidate receipt management under the Finances section.
 * 
 * Run this script after removing the receipts category from the document model.
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
const environment = process.env.NODE_ENV || 'production';
const envFile = environment === 'production' ? '.env.production' : '.env.development';
dotenv.config({ path: envFile });

console.log(`Running migration in ${environment} environment`);

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for migration');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

// Migration function
const migrateReceiptDocuments = async () => {
  try {
    console.log('Starting receipt documents migration...');

    // Find all documents with category 'receipts'
    const Document = mongoose.model('Document', new mongoose.Schema({}, { strict: false }));
    
    const receiptDocuments = await Document.find({ category: 'receipts' });
    console.log(`Found ${receiptDocuments.length} receipt documents to migrate`);

    if (receiptDocuments.length === 0) {
      console.log('No receipt documents found. Migration complete.');
      return;
    }

    // Update all receipt documents to financial category
    const updateResult = await Document.updateMany(
      { category: 'receipts' },
      { 
        $set: { 
          category: 'financial',
          // Add a note to the description to indicate this was migrated
          migratedFromReceipts: true,
          migrationDate: new Date()
        }
      }
    );

    console.log(`Migration completed successfully:`);
    console.log(`- Documents found: ${receiptDocuments.length}`);
    console.log(`- Documents updated: ${updateResult.modifiedCount}`);
    console.log(`- All receipt documents are now categorized as 'financial'`);

    // Log the migrated documents for reference
    console.log('\nMigrated documents:');
    receiptDocuments.forEach((doc, index) => {
      console.log(`${index + 1}. ${doc.title} (ID: ${doc._id})`);
    });

    console.log('\n✅ Receipt documents migration completed successfully!');
    console.log('📋 All receipt documents are now accessible in the Finances section under Financial category.');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
};

// Main execution
const runMigration = async () => {
  try {
    await connectDB();
    await migrateReceiptDocuments();
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('You can now safely deploy the updated code without the receipts category.');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed.');
    process.exit(0);
  }
};

// Run the migration
if (require.main === module) {
  runMigration();
}

module.exports = { migrateReceiptDocuments };

# 🏘️ HOAFLO Developer Guide
**Complete Development Reference - Frontend & Backend**

---

## 📋 **Project Overview**

**HOAFLO** is a comprehensive HOA (Homeowners Association) management platform with:
- **Backend**: Node.js/Express API with MongoDB and AWS S3 integration
- **Frontend**: React/TypeScript with Vite and Tailwind CSS
- **Live URL**: https://www.hoaflo.com
- **Current Branch**: `feature/members_update/docuemnts_update/announcements_update/responsivness_update/sidebar_upadate/fileupload_update`

---

## 🚀 **Latest Updates (July 14, 2025)**

### ✅ **Azure OAuth2 Email System - PRODUCTION READY**
- **Real Email Delivery**: Replaced email simulation with actual Microsoft Graph API integration
- **Professional Sender**: All emails sent from `<EMAIL>` (HOAFLO Support)
- **Azure Configuration**:
  - Tenant ID: `8cae2d00-6f54-4211-9e39-9a239124b6b5`
  - Client ID: `86068cd6-10f0-4d7b-a142-de1371ba45e4`
  - Application Permission: `Mail.Send` (granted)
- **Files Modified**:
  - `HOA-BACKEND/.env`: Updated Azure credentials
  - `HOA-BACKEND/services/azureEmailService.js`: Enhanced debugging and error handling

### ✅ **Streamlined UI/UX Navigation**
- **Hidden Duplicate Menu**: "Announcements" menu hidden for HOA admins to eliminate confusion
- **Single Interface**: HOA admins now only see "Admin Announcements" for both viewing and creating
- **Files Modified**:
  - `HOA-FRONT/src/components/Sidebar.tsx`: Conditional rendering based on user role
  - `HOA-FRONT/src/pages/AdminAnnouncements.tsx`: Updated cancel button navigation

---

## 🎯 **Core Features**

### **📧 Email System**
- **Azure OAuth2**: Microsoft Graph API for professional email delivery
- **Announcement Emails**: Real emails with PDF attachments via S3 signed URLs
- **Role-Based Sending**: HOA admins (street-level), Company admins (HOA-level)
- **Template System**: Professional HTML email templates

### **📢 Announcement System**
- **Street-Level Targeting**: HOA admins can target specific streets and residents
- **HOA-Level Targeting**: Company admins can target entire HOAs or specific streets
- **File Attachments**: PDF, images, documents via AWS S3 integration
- **Smart Selection**: Street dropdown → Resident checkboxes with "Select All"

### **📁 Document Management**
- **AWS S3 Integration**: Secure cloud storage with signed URLs
- **File Hierarchy**: `hoa-{hoaId}/documents/`, `hoa-{hoaId}/announcement_attachments/`
- **Role-Based Access**: Company admins see all, HOA admins see own HOA only
- **Consolidated Receipts**: All financial documents under Finances section

### **👥 User Management**
- **Three-Tier Roles**: user/member, admin (HOA), company_admin (platform owner)
- **Access Control**: Street-level filtering for members, HOA-level for admins
- **Authentication**: JWT tokens with role-based middleware

---

## 🛠️ **Development Setup**

### **Prerequisites**
- Node.js & npm (install with [nvm](https://github.com/nvm-sh/nvm))
- MongoDB connection (Atlas cloud)
- AWS S3 bucket access
- Azure OAuth2 app registration

### **Backend Setup (HOA-BACKEND)**
```bash
cd HOA-BACKEND
npm install
npm run local:production  # Uses .env.local.active
```

### **Frontend Setup (HOA-FRONT)**
```bash
cd HOA-FRONT
npm install
npm run dev  # Development server on localhost:8080
```

### **Environment Configuration**
- **Backend**: Uses `.env.local.active` (copied from `.env.local.production`)
- **Frontend**: Automatic environment detection (localhost vs production)
- **Database**: MongoDB Atlas (`street-harmony` database)
- **Storage**: AWS S3 bucket `hoaflo-files-prod` (us-east-2)

---

## 🔧 **Technical Architecture**

### **Backend Structure**
```
HOA-BACKEND/
├── controllers/          # API logic (announcement, auth, document, etc.)
├── middleware/          # Auth, admin, file upload, security
├── models/             # MongoDB schemas (user, hoa, community, etc.)
├── routes/             # Express routes
├── services/           # Email, SMS, external APIs
├── utils/              # Helpers, logging, error handling
└── uploads/            # Local file storage (legacy)
```

### **Frontend Structure**
```
HOA-FRONT/src/
├── components/         # Reusable UI components
├── pages/             # Route components (Dashboard, Announcements, etc.)
├── hooks/             # Custom React hooks (useAuth, etc.)
├── lib/               # Utilities (axios, utils)
├── context/           # React context (SocketProvider)
└── styles/            # CSS and styling
```

### **Key Technologies**
- **Backend**: Node.js, Express, MongoDB, Mongoose, JWT, Multer, AWS SDK
- **Frontend**: React, TypeScript, Vite, Tailwind CSS, shadcn/ui
- **Email**: Azure OAuth2, Microsoft Graph API
- **Storage**: AWS S3 with signed URLs
- **Real-time**: Socket.io for notifications

---

## 📊 **Database Schema**

### **Key Collections**
- **users**: User accounts with role-based access
- **hoas**: HOA organizations with community codes
- **communities**: Streets/neighborhoods within HOAs
- **notifications**: Announcements and system notifications
- **documents**: File metadata with S3 references
- **announcementAttachments**: Persistent attachment records

### **Role Hierarchy**
1. **user/member**: HOA residents (street-level access)
2. **admin**: HOA administrators (HOA-level access)
3. **company_admin**: Platform owners (master access)

---

## 🚀 **Deployment**

### **Current Deployment**
- **Backend**: Heroku (`hoa-management-app`)
- **Frontend**: Vercel (auto-deploy from GitHub)
- **Database**: MongoDB Atlas (cloud)
- **Storage**: AWS S3 (`hoaflo-files-prod`)

### **Branch Strategy**
- **Main Branch**: Production-ready code
- **Feature Branch**: `feature/members_update/docuemnts_update/announcements_update/responsivness_update/sidebar_upadate/fileupload_update`
- **Deployment**: Push to feature branch for testing, merge to main for production

---

## 🔍 **Testing & Debugging**

### **Test Users**
- **HOA Admin**: `<EMAIL>` (manages Derany Lane community)
- **Test Recipient**: `<EMAIL>` (receives announcement emails)

### **Testing Checklist**
1. **Authentication**: Login with different roles
2. **Announcements**: Send emails with attachments
3. **File Uploads**: Upload documents to S3
4. **Access Control**: Verify role-based restrictions
5. **Email Delivery**: Check real email delivery (not simulation)

### **Common Issues**
- **Email Simulation**: Check Azure permissions and environment variables
- **File Upload Errors**: Verify S3 credentials and bucket permissions
- **Access Denied**: Check user roles and HOA associations

---

## 📝 **Recent Changes Summary**

### **Email System Fixes**
- ✅ Fixed Azure environment variable loading
- ✅ Changed from Delegated to Application permissions
- ✅ Granted admin consent for Mail.Send
- ✅ Real email delivery working with attachments

### **UI/UX Improvements**
- ✅ Hidden duplicate "Announcements" menu for HOA admins
- ✅ Streamlined navigation with single announcement interface
- ✅ Fixed cancel button navigation in AdminAnnouncements

### **Files Modified**
- `HOA-BACKEND/.env`: Azure credentials
- `HOA-BACKEND/services/azureEmailService.js`: Enhanced debugging
- `HOA-FRONT/src/components/Sidebar.tsx`: Conditional menu rendering
- `HOA-FRONT/src/pages/AdminAnnouncements.tsx`: Navigation fixes

---

## 🎯 **Next Steps for Developer**

1. **Review Code**: Check recent commits on feature branch
2. **Test Email System**: Verify Azure OAuth2 email delivery
3. **Test UI Changes**: Confirm HOA admin navigation improvements
4. **Production Deployment**: Merge feature branch to main when ready
5. **Monitor**: Check email delivery and user feedback

---

**Last Updated**: July 14, 2025  
**Status**: Production Ready ✅  
**Contact**: For questions about this implementation, refer to recent commits and this guide.
